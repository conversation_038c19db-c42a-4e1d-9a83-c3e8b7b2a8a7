<template>
  <view class="order-detail">
    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-icon" :class="getStatusClass(orderInfo.status)">
        <uni-icons :type="getStatusIcon(orderInfo.status)" size="32" color="#fff"></uni-icons>
      </view>
      <view class="status-info">
        <view class="status-text">{{ getStatusText(orderInfo.status) }}</view>
        <view class="status-desc">{{ getStatusDescription(orderInfo.status) }}</view>
      </view>
    </view>

    <!-- 咨询师信息 -->
    <view class="consultant-section">
      <view class="section-title">咨询师信息</view>
      <view class="consultant-info">
        <image :src="orderInfo.consultantAvatar || defaultAvatar" class="consultant-avatar"></image>
        <view class="consultant-details">
          <view class="consultant-name">{{ orderInfo.consultantName }}</view>
          <view class="consultant-title">{{ orderInfo.consultantTitle }}</view>
          <view class="consultant-rating">
            <uni-rate :value="orderInfo.consultantRating || 0" readonly size="16"></uni-rate>
            <text class="rating-text">{{ orderInfo.consultantRating || 0 }}分</text>
          </view>
        </view>
        <view class="contact-btn" @click="contactConsultant">
          <uni-icons type="chat" size="20" color="#ff6b35"></uni-icons>
          <text>联系</text>
        </view>
      </view>
    </view>

    <!-- 预约信息 -->
    <view class="appointment-section">
      <view class="section-title">预约信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">咨询类型</text>
          <text class="value">{{ getConsultationTypeName(orderInfo.consultationType) }}</text>
        </view>
        <view class="info-item">
          <text class="label">预约时间</text>
          <text class="value">{{ formatDateTime(orderInfo.appointmentTime) }}</text>
        </view>
        <view class="info-item">
          <text class="label">咨询时长</text>
          <text class="value">{{ orderInfo.duration }}分钟</text>
        </view>
        <view class="info-item">
          <text class="label">问题描述</text>
          <text class="value description">{{ orderInfo.problemDescription }}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-title">订单信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">订单编号</text>
          <text class="value">{{ orderInfo.orderNo }}</text>
        </view>
        <view class="info-item">
          <text class="label">创建时间</text>
          <text class="value">{{ formatDateTime(orderInfo.createTime) }}</text>
        </view>
        <view class="info-item">
          <text class="label">支付金额</text>
          <text class="value amount">¥{{ orderInfo.paymentAmount }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.paymentTime">
          <text class="label">支付时间</text>
          <text class="value">{{ formatDateTime(orderInfo.paymentTime) }}</text>
        </view>
        <view class="info-item" v-if="orderInfo.cancelReason">
          <text class="label">取消原因</text>
          <text class="value">{{ orderInfo.cancelReason }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section" v-if="showActions">
      <button 
        v-if="orderInfo.status === 'pending'" 
        class="action-btn cancel-btn" 
        @click="cancelOrder"
      >
        取消预约
      </button>
      <button 
        v-if="orderInfo.status === 'pending'" 
        class="action-btn pay-btn" 
        @click="payOrder"
      >
        立即支付
      </button>
      <button 
        v-if="orderInfo.status === 'confirmed'" 
        class="action-btn join-btn" 
        @click="joinConsultation"
      >
        进入咨询室
      </button>
    </view>

    <!-- 支付弹框 -->
    <PaymentModal 
      ref="paymentModal"
      :order-info="paymentOrderInfo"
      @close="onPaymentClose"
      @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getOrderDetails, cancelOrder as cancelOrderApi } from '@/api/consultation'
import { useUserStore } from '@/stores/user'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'

// 响应式数据
const orderInfo = ref({})
const orderId = ref(null)
const paymentModal = ref(null)
const paymentOrderInfo = ref({})

// 默认头像
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 计算属性
const showActions = computed(() => {
  return ['pending', 'confirmed'].includes(orderInfo.value.status)
})

// 方法
const loadOrderDetails = async () => {
  try {
    const res = await getOrderDetails(orderId.value)
    if (res.code === 200) {
      orderInfo.value = res.data
    } else {
      uni.showToast({
        title: res.msg || '获取订单详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const cancelOrder = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个预约吗？取消后费用将原路退回。',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '取消中...' })
          
          const result = await cancelOrderApi(orderId.value, '用户主动取消')
          
          uni.hideLoading()
          
          if (result.code === 200) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            loadOrderDetails()
          } else {
            uni.showToast({
              title: result.msg || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          uni.hideLoading()
          console.error('取消订单失败:', error)
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

const payOrder = () => {
  // 设置支付订单信息并打开支付弹框
  paymentOrderInfo.value = {
    orderNo: orderInfo.value.orderNo,
    orderId: orderInfo.value.id,
    amount: orderInfo.value.paymentAmount,
    product: {
      title: `${orderInfo.value.consultantName} - ${getConsultationTypeName(orderInfo.value.consultationType)}`,
      description: `${formatDateTime(orderInfo.value.appointmentTime)} (${orderInfo.value.duration}分钟)`
    }
  }
  paymentModal.value?.open()
}

const joinConsultation = () => {
  // TODO: 实现进入咨询室逻辑
  uni.showToast({
    title: '咨询室功能开发中',
    icon: 'none'
  })
}

const contactConsultant = () => {
  // TODO: 实现联系咨询师逻辑
  uni.showToast({
    title: '联系功能开发中',
    icon: 'none'
  })
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 显示支付成功提示
  uni.showToast({
    title: '支付成功',
    icon: 'success'
  })

  // 延迟跳转到基本信息表单页面
  setTimeout(() => {
    uni.navigateTo({
      url: `/pages/consultation/basic-info/index?orderId=${paymentData.orderId || orderInfo.value.id}`
    })
  }, 1500)
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 工具方法
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const getConsultationTypeName = (type) => {
  const types = {
    'psychological': '心理咨询',
    'emotional': '情感咨询',
    'career': '职场咨询',
    'family': '家庭咨询'
  }
  return types[type] || type
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'confirmed': '已确认',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getStatusDescription = (status) => {
  const descMap = {
    'pending': '请尽快完成支付，超时订单将自动取消',
    'paid': '支付成功，等待咨询师确认',
    'confirmed': '咨询师已确认，请按时参加咨询',
    'completed': '咨询已完成，感谢您的使用',
    'cancelled': '订单已取消，如有疑问请联系客服'
  }
  return descMap[status] || ''
}

const getStatusClass = (status) => {
  return `status-${status}`
}

const getStatusIcon = (status) => {
  const iconMap = {
    'pending': 'time',
    'paid': 'checkmarkempty',
    'confirmed': 'checkmarkempty',
    'completed': 'checkbox',
    'cancelled': 'close'
  }
  return iconMap[status] || 'help'
}

// 生命周期
onLoad((options) => {
  orderId.value = options.id
  if (orderId.value) {
    loadOrderDetails()
  }
})
</script>

<style lang="scss" scoped>
.order-detail {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.status-section {
  display: flex;
  align-items: center;
  padding: 40rpx 32rpx;
  background-color: #fff;
  margin-bottom: 16rpx;

  .status-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;

    &.status-pending {
      background-color: #ff9800;
    }

    &.status-paid, &.status-confirmed {
      background-color: #2196f3;
    }

    &.status-completed {
      background-color: #4caf50;
    }

    &.status-cancelled {
      background-color: #f44336;
    }
  }

  .status-info {
    flex: 1;

    .status-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }

    .status-desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.4;
    }
  }
}

.consultant-section, .appointment-section, .order-section {
  background-color: #fff;
  margin-bottom: 16rpx;
  padding: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.consultant-info {
  display: flex;
  align-items: center;

  .consultant-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }

  .consultant-details {
    flex: 1;

    .consultant-name {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }

    .consultant-title {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 12rpx;
    }

    .consultant-rating {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .rating-text {
        font-size: 24rpx;
        color: #ff6b35;
      }
    }
  }

  .contact-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx;

    text {
      font-size: 24rpx;
      color: #ff6b35;
    }
  }
}

.info-list {
  .info-item {
    display: flex;
    padding: 20rpx 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .label {
      width: 160rpx;
      font-size: 28rpx;
      color: #666;
      flex-shrink: 0;
    }

    .value {
      flex: 1;
      font-size: 28rpx;
      color: #333;

      &.description {
        line-height: 1.6;
      }

      &.amount {
        color: #ff6b35;
        font-weight: 600;
      }
    }
  }
}

.action-section {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;

  .action-btn {
    flex: 1;
    padding: 24rpx;
    border: none;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 600;

    &.cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    &.pay-btn {
      background-color: #ff6b35;
      color: #fff;
    }

    &.join-btn {
      background-color: #4caf50;
      color: #fff;
    }
  }
}
</style>
