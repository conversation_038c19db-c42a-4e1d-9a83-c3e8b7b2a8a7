<template>
	<view class="audio-speed-test">
		<view class="header">
			<text class="title">音频倍速测试</text>
		</view>
		
		<view class="player-section">
			<view class="audio-info">
				<text class="audio-title">测试音频</text>
				<text class="audio-subtitle">倍速播放功能测试</text>
			</view>
			
			<view class="controls">
				<button @click="togglePlay" class="play-btn">
					{{ isPlaying ? '暂停' : '播放' }}
				</button>
				
				<button @click="setSpeed(0.5)" :class="{ active: currentSpeed === 0.5 }" class="speed-btn">
					0.5x
				</button>
				
				<button @click="setSpeed(1.0)" :class="{ active: currentSpeed === 1.0 }" class="speed-btn">
					1.0x
				</button>
				
				<button @click="setSpeed(1.5)" :class="{ active: currentSpeed === 1.5 }" class="speed-btn">
					1.5x
				</button>
				
				<button @click="setSpeed(2.0)" :class="{ active: currentSpeed === 2.0 }" class="speed-btn">
					2.0x
				</button>
			</view>
			
			<view class="progress-info">
				<text>当前时间: {{ formatTime(currentTime) }}</text>
				<text>总时长: {{ formatTime(duration) }}</text>
				<text>播放速度: {{ currentSpeed }}x</text>
				<text>音频模式: {{ useBackgroundAudio ? '背景音频' : '内部音频' }}</text>
			</view>
			
			<view class="mode-switch">
				<button @click="switchMode" class="mode-btn">
					切换到{{ useBackgroundAudio ? '内部音频' : '背景音频' }}模式
				</button>
			</view>
		</view>
		
		<view class="tips">
			<text class="tip-title">使用说明：</text>
			<text class="tip-text">• 背景音频模式支持真实倍速播放</text>
			<text class="tip-text">• 内部音频模式不支持倍速（小程序限制）</text>
			<text class="tip-text">• 背景音频模式可能需要用户授权</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { createAudioPlayer, formatTime } from '@/utils/audioPlayer'

// 测试音频URL（可以替换为实际的音频地址）
const testAudioUrl = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/test-audio.mp3'

// 响应式数据
const audioPlayer = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const currentSpeed = ref(1.0)
const useBackgroundAudio = ref(false)

// 初始化音频播放器
const initPlayer = () => {
	if (audioPlayer.value) {
		audioPlayer.value.destroy()
	}

	console.log('初始化播放器，背景音频模式:', useBackgroundAudio.value)

	audioPlayer.value = createAudioPlayer(testAudioUrl, {
		useBackgroundAudio: useBackgroundAudio.value,
		title: '倍速测试音频',
		singer: '喜欢心理',
		coverImgUrl: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
	})
	
	// 绑定事件
	audioPlayer.value
		.onPlay(() => {
			isPlaying.value = true
		})
		.onPause(() => {
			isPlaying.value = false
		})
		.onTimeUpdate((data) => {
			currentTime.value = data.currentTime
			duration.value = data.duration
		})
		.onEnded(() => {
			isPlaying.value = false
		})
		.onError((error) => {
			console.error('播放错误:', error)
			uni.showToast({
				title: '播放失败',
				icon: 'none'
			})
		})
		.onPlaybackRateChange((rate) => {
			currentSpeed.value = rate
		})
}

// 方法
const togglePlay = () => {
	if (audioPlayer.value) {
		audioPlayer.value.toggle()
	}
}

const setSpeed = (speed) => {
	console.log('设置倍速:', speed)
	if (audioPlayer.value) {
		console.log('播放器状态:', audioPlayer.value.getState())
		audioPlayer.value.setPlaybackRate(speed)
		currentSpeed.value = speed

		// 验证设置结果
		setTimeout(() => {
			const actualSpeed = audioPlayer.value.getPlaybackRate()
			console.log('实际倍速:', actualSpeed)
		}, 200)
	} else {
		console.error('播放器未初始化')
	}
}

const switchMode = () => {
	useBackgroundAudio.value = !useBackgroundAudio.value
	initPlayer()
	
	uni.showToast({
		title: `已切换到${useBackgroundAudio.value ? '背景音频' : '内部音频'}模式`,
		icon: 'success'
	})
}

// 初始化
initPlayer()

// 生命周期
onUnmounted(() => {
	if (audioPlayer.value) {
		audioPlayer.value.destroy()
	}
})
</script>

<style lang="scss" scoped>
.audio-speed-test {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

.player-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	
	.audio-info {
		text-align: center;
		margin-bottom: 40rpx;
		
		.audio-title {
			display: block;
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 12rpx;
		}
		
		.audio-subtitle {
			display: block;
			font-size: 26rpx;
			color: #666;
		}
	}
	
	.controls {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		justify-content: center;
		margin-bottom: 40rpx;
		
		.play-btn {
			width: 100%;
			padding: 24rpx;
			background-color: #ff6b35;
			color: #fff;
			border: none;
			border-radius: 12rpx;
			font-size: 30rpx;
			margin-bottom: 20rpx;
		}
		
		.speed-btn {
			flex: 1;
			min-width: 120rpx;
			padding: 20rpx;
			background-color: #f8f8f8;
			color: #666;
			border: none;
			border-radius: 8rpx;
			font-size: 26rpx;
			
			&.active {
				background-color: #ff6b35;
				color: #fff;
			}
		}
	}
	
	.progress-info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
		margin-bottom: 40rpx;
		
		text {
			font-size: 26rpx;
			color: #666;
		}
	}
	
	.mode-switch {
		.mode-btn {
			width: 100%;
			padding: 24rpx;
			background-color: #007aff;
			color: #fff;
			border: none;
			border-radius: 12rpx;
			font-size: 28rpx;
		}
	}
}

.tips {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	
	.tip-title {
		display: block;
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.tip-text {
		display: block;
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 12rpx;
	}
}
</style>
