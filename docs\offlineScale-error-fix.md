# offlineScale 错误修复总结

## 错误描述

```
The requested module '/src/api/system/assessment/scale.js?t=1753111585281' does not provide an export named 'offlineScale'
```

## 问题分析

1. **错误原因**：前端代码尝试从 `/src/api/system/assessment/scale.js` 导入 `offlineScale` 函数，但该文件不存在或没有导出该函数
2. **文件路径**：错误中的路径 `/src/api/system/assessment/scale.js` 在当前项目结构中不存在
3. **功能推测**：`offlineScale` 应该是一个量表管理功能，用于下线（禁用）某个量表

## 解决方案

### 1. 创建缺失的API文件

创建了 `api/system/assessment/scale.js` 文件，包含完整的量表管理接口：

```javascript
// 下线量表
export function offlineScale(scaleId) {
  return request({
    url: `/system/assessment/scale/offline/${scaleId}`,
    method: 'post'
  })
}

// 上线量表  
export function onlineScale(scaleId) {
  return request({
    url: `/system/assessment/scale/online/${scaleId}`,
    method: 'post'
  })
}

// ... 其他管理接口
```

### 2. 在主评价API文件中添加兼容函数

在 `api/evaluation.js` 中也添加了 `offlineScale` 函数作为备用：

```javascript
// 下线量表（管理功能，可能是后台管理需要的）
export function offlineScale(scaleId) {
  return request({
    url: `/system/assessment/scale/offline/${scaleId}`,
    method: 'post'
  })
}
```

## 新增的管理接口

### 量表管理接口
- `offlineScale()` - 下线量表
- `onlineScale()` - 上线量表  
- `getScaleManageList()` - 获取量表管理列表
- `createScale()` - 创建量表
- `updateScale()` - 更新量表
- `deleteScale()` - 删除量表
- `getScaleManageDetail()` - 获取量表详情（管理端）
- `batchOperateScales()` - 批量操作量表
- `getScaleStats()` - 获取量表统计信息
- `exportScales()` - 导出量表数据
- `importScales()` - 导入量表数据

### 分类管理接口
- `getCategoryManageList()` - 获取分类管理列表
- `createCategory()` - 创建分类
- `updateCategory()` - 更新分类
- `deleteCategory()` - 删除分类

### 题目管理接口
- `getQuestionManageList()` - 获取题目管理列表
- `createQuestion()` - 创建题目
- `updateQuestion()` - 更新题目
- `deleteQuestion()` - 删除题目
- `importQuestions()` - 批量导入题目

### 选项管理接口
- `getOptionList()` - 获取选项列表
- `createOption()` - 创建选项
- `updateOption()` - 更新选项
- `deleteOption()` - 删除选项

## 使用方式

### 1. 从系统管理API导入
```javascript
import { offlineScale } from '@/api/system/assessment/scale.js'

// 下线量表
await offlineScale(scaleId)
```

### 2. 从评价API导入（兼容方式）
```javascript
import { offlineScale } from '@/api/evaluation.js'

// 下线量表
await offlineScale(scaleId)
```

## 注意事项

1. **权限要求**：这些管理接口通常需要管理员权限
2. **后端实现**：需要确认后端是否已实现对应的管理接口
3. **路径调整**：如果后端接口路径不同，需要相应调整
4. **错误处理**：建议添加适当的错误处理和用户提示

## 后端接口建议

如果后端还没有实现这些管理接口，建议创建以下控制器：

```java
@RestController
@RequestMapping("/system/assessment/scale")
public class SystemAssessmentScaleController {
    
    @PostMapping("/offline/{scaleId}")
    public AjaxResult offlineScale(@PathVariable Long scaleId) {
        // 下线量表逻辑
    }
    
    @PostMapping("/online/{scaleId}")
    public AjaxResult onlineScale(@PathVariable Long scaleId) {
        // 上线量表逻辑
    }
    
    // ... 其他管理接口
}
```

## 测试建议

1. **功能测试**：测试量表的上线/下线功能
2. **权限测试**：验证只有管理员可以执行这些操作
3. **数据一致性**：确保操作后数据状态正确
4. **错误处理**：测试各种异常情况的处理

## 总结

通过创建完整的量表管理API文件，解决了 `offlineScale` 函数缺失的问题。同时提供了完整的量表管理功能接口，为后续的管理功能开发奠定了基础。

如果仍然遇到导入错误，请检查：
1. 文件路径是否正确
2. 导入语句的语法是否正确
3. 是否有其他地方仍在使用旧的导入路径
