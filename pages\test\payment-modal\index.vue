<template>
  <view class="test-page">
    <view class="header">
      <text class="title">支付弹框测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试功能</text>
        <view class="test-buttons">
          <button class="test-btn" @click="testCoursePayment">测试课程支付</button>
          <button class="test-btn" @click="testMeditationPayment">测试冥想支付</button>
          <button class="test-btn" @click="testAssessmentPayment">测试测评支付</button>
          <button class="test-btn" @click="testLongContentPayment">测试长内容支付</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">修复内容</text>
        <view class="fix-list">
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>添加滚动容器，支持上下滑动</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>提高z-index层级，避免被购物车遮挡</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>固定底部支付按钮，始终可见</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>美化界面，添加渐变和阴影效果</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>适配安全区域，支持全面屏</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">使用说明</text>
        <view class="description">
          <text>1. 点击测试按钮打开支付弹框</text>
          <text>2. 尝试上下滑动查看所有内容</text>
          <text>3. 底部支付按钮应始终可见</text>
          <text>4. 弹框层级应高于购物车组件</text>
        </view>
      </view>
    </view>
    
    <!-- 支付弹框 -->
    <PaymentModal
      ref="paymentModal"
      :order-info="orderInfo"
      @close="handlePaymentClose"
      @pay-success="handlePaySuccess"
      @pay-fail="handlePayFail"
    />
    
    <!-- 模拟购物车组件 -->
    <view class="mock-goods-nav">
      <text>模拟购物车组件 (z-index: 999)</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'

// 响应式数据
const paymentModal = ref(null)
const orderInfo = ref({})

// 测试方法
const testCoursePayment = () => {
  orderInfo.value = {
    orderNo: 'CO' + Date.now(),
    product: {
      title: '情绪管理入门课程',
      description: '学会识别和调节自己的情绪，提升心理健康水平',
      price: 199,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
    }
  }
  paymentModal.value?.open()
}

const testMeditationPayment = () => {
  orderInfo.value = {
    orderNo: 'ME' + Date.now(),
    product: {
      title: '深度放松冥想',
      description: '通过专业的冥想指导，帮助您快速进入放松状态',
      price: 29,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
    }
  }
  paymentModal.value?.open()
}

const testAssessmentPayment = () => {
  orderInfo.value = {
    orderNo: 'AS' + Date.now(),
    product: {
      title: '心理健康评估',
      description: '专业的心理健康测评，了解您的心理状态',
      price: 39,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
    }
  }
  paymentModal.value?.open()
}

const testLongContentPayment = () => {
  orderInfo.value = {
    orderNo: 'LC' + Date.now(),
    product: {
      title: '超长标题的心理学专业课程：从基础理论到实践应用的全面学习体系',
      description: '这是一个非常详细的课程描述，包含了大量的信息。本课程涵盖了心理学的各个方面，从基础理论到实践应用，从个体心理到社会心理，从发展心理学到临床心理学。通过系统的学习，您将掌握心理学的核心概念和方法，能够运用心理学知识解决实际问题。课程内容丰富，包括理论讲解、案例分析、实践练习等多种形式。',
      price: 999,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
    }
  }
  paymentModal.value?.open()
}

// 事件处理
const handlePaymentClose = () => {
  console.log('支付弹框关闭')
}

const handlePaySuccess = (result) => {
  console.log('支付成功:', result)
  uni.showToast({
    title: '支付成功',
    icon: 'success'
  })
}

const handlePayFail = (error) => {
  console.log('支付失败:', error)
  uni.showToast({
    title: '支付失败',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.header {
  background-color: #fff;
  padding: 40rpx 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;
  }
}

.fix-list {
  .fix-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.mock-goods-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b35, #ff8a50);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
  }
}
</style>
