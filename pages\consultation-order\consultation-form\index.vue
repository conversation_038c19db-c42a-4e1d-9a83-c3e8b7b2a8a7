<template>
  <view class="consultation-form-container">
    <!-- 表单头部 -->
    <view class="form-header">
      <view class="client-info">
        <image class="avatar" :src="orderInfo.clientAvatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
        <view class="info">
          <text class="name">{{ orderInfo.clientName }}</text>
          <text class="time">{{ formatDateTime(orderInfo.appointmentTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 咨询记录表单 -->
    <view class="form-content">
      <view class="form-section">
        <view class="section-title">咨询记录</view>
        
        <!-- 主要症状 -->
        <view class="form-item">
          <view class="item-label">
            <text class="required">*</text>
            <text>主要症状</text>
          </view>
          <textarea 
            class="textarea-input"
            v-model="formData.symptoms"
            placeholder="请详细描述来访者的主要症状和表现"
            maxlength="500"
            :show-count="true"
          ></textarea>
        </view>

        <!-- 咨询内容 -->
        <view class="form-item">
          <view class="item-label">
            <text class="required">*</text>
            <text>咨询内容</text>
          </view>
          <textarea 
            class="textarea-input"
            v-model="formData.content"
            placeholder="请记录本次咨询的主要内容和过程"
            maxlength="1000"
            :show-count="true"
          ></textarea>
        </view>

        <!-- 咨询方法 -->
        <view class="form-item">
          <view class="item-label">
            <text>咨询方法</text>
          </view>
          <textarea 
            class="textarea-input"
            v-model="formData.methods"
            placeholder="请记录使用的咨询技术和方法"
            maxlength="300"
            :show-count="true"
          ></textarea>
        </view>

        <!-- 建议方案 -->
        <view class="form-item">
          <view class="item-label">
            <text class="required">*</text>
            <text>建议方案</text>
          </view>
          <textarea 
            class="textarea-input"
            v-model="formData.suggestions"
            placeholder="请提供后续的建议和治疗方案"
            maxlength="500"
            :show-count="true"
          ></textarea>
        </view>

        <!-- 效果评估 -->
        <view class="form-item">
          <view class="item-label">
            <text>效果评估</text>
          </view>
          <view class="radio-group">
            <label 
              class="radio-item" 
              v-for="item in effectOptions" 
              :key="item.value"
              @click="selectEffect(item.value)"
            >
              <view class="radio" :class="{ checked: formData.effect === item.value }">
                <view class="radio-inner" v-if="formData.effect === item.value"></view>
              </view>
              <text>{{ item.label }}</text>
            </label>
          </view>
        </view>

        <!-- 下次预约建议 -->
        <view class="form-item">
          <view class="item-label">
            <text>下次预约建议</text>
          </view>
          <view class="radio-group">
            <label 
              class="radio-item" 
              v-for="item in nextAppointmentOptions" 
              :key="item.value"
              @click="selectNextAppointment(item.value)"
            >
              <view class="radio" :class="{ checked: formData.nextAppointment === item.value }">
                <view class="radio-inner" v-if="formData.nextAppointment === item.value"></view>
              </view>
              <text>{{ item.label }}</text>
            </label>
          </view>
        </view>

        <!-- 备注 -->
        <view class="form-item">
          <view class="item-label">
            <text>备注</text>
          </view>
          <textarea 
            class="textarea-input"
            v-model="formData.remarks"
            placeholder="其他需要记录的信息"
            maxlength="300"
            :show-count="true"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-footer">
      <button class="btn-submit" @click="submitForm" :disabled="submitting">
        {{ submitting ? '提交中...' : '提交记录' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getConsultantOrderDetail, createConsultationRecord, updateConsultationRecord } from '@/api/consultant-app.js'

// 响应式数据
const orderId = ref('')
const orderInfo = ref({})
const submitting = ref(false)

// 表单数据
const formData = ref({
  symptoms: '',
  content: '',
  methods: '',
  suggestions: '',
  effect: '',
  nextAppointment: '',
  remarks: ''
})

// 效果评估选项
const effectOptions = ref([
  { label: '显著改善', value: 'excellent' },
  { label: '有所改善', value: 'good' },
  { label: '基本稳定', value: 'stable' },
  { label: '需要继续', value: 'continue' }
])

// 下次预约建议选项
const nextAppointmentOptions = ref([
  { label: '建议1周后', value: '1week' },
  { label: '建议2周后', value: '2weeks' },
  { label: '建议1个月后', value: '1month' },
  { label: '暂不需要', value: 'none' }
])

// 生命周期
onLoad((options) => {
  orderId.value = options.orderId
  loadOrderInfo()
})

// 加载订单信息
const loadOrderInfo = async () => {
  try {
    const res = await getConsultantOrderDetail(orderId.value)
    if (res.code === 200) {
      orderInfo.value = res.data
      
      // 如果已有咨询记录，填充表单
      if (res.data.consultationRecord) {
        const record = res.data.consultationRecord
        formData.value = {
          symptoms: record.symptoms || '',
          content: record.content || '',
          methods: record.methods || '',
          suggestions: record.suggestions || '',
          effect: record.effect || '',
          nextAppointment: record.nextAppointment || '',
          remarks: record.remarks || ''
        }
      }
    }
  } catch (error) {
    console.error('加载订单信息失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${month}/${day} ${hour}:${minute}`
}

// 选择效果评估
const selectEffect = (value) => {
  formData.value.effect = value
}

// 选择下次预约建议
const selectNextAppointment = (value) => {
  formData.value.nextAppointment = value
}

// 表单验证
const validateForm = () => {
  if (!formData.value.symptoms.trim()) {
    uni.showToast({
      title: '请填写主要症状',
      icon: 'none'
    })
    return false
  }
  
  if (!formData.value.content.trim()) {
    uni.showToast({
      title: '请填写咨询内容',
      icon: 'none'
    })
    return false
  }
  
  if (!formData.value.suggestions.trim()) {
    uni.showToast({
      title: '请填写建议方案',
      icon: 'none'
    })
    return false
  }
  
  return true
}

// 提交表单
const submitForm = async () => {
  if (!validateForm()) return
  
  submitting.value = true
  
  try {
    const data = {
      orderId: orderId.value,
      ...formData.value
    }
    
    let res
    if (orderInfo.value.consultationRecord) {
      // 更新记录
      res = await updateConsultationRecord(orderInfo.value.consultationRecord.id, data)
    } else {
      // 创建记录
      res = await createConsultationRecord(data)
    }
    
    if (res.code === 200) {
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    console.error('提交失败:', error)
    uni.showToast({
      title: '提交失败',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.consultation-form-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.form-header {
  background-color: #fff;
  padding: 30rpx;
  
  .client-info {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .info {
      flex: 1;
      
      .name {
        display: block;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .time {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.form-content {
  padding: 20rpx;
}

.form-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
}

.form-item {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .item-label {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .required {
      color: #ff4d4f;
      margin-right: 4rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
  
  .textarea-input {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 1rpx solid #d9d9d9;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    box-sizing: border-box;
    
    &:focus {
      border-color: #1890ff;
    }
  }
  
  .radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 30rpx;
    
    .radio-item {
      display: flex;
      align-items: center;
      
      .radio {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #d9d9d9;
        border-radius: 50%;
        margin-right: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.checked {
          border-color: #1890ff;
          
          .radio-inner {
            width: 16rpx;
            height: 16rpx;
            background-color: #1890ff;
            border-radius: 50%;
          }
        }
      }
      
      text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

.form-footer {
  padding: 30rpx;
  
  .btn-submit {
    width: 100%;
    padding: 28rpx 0;
    background-color: #1890ff;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 30rpx;
    font-weight: bold;
    
    &:disabled {
      background-color: #d9d9d9;
      color: #999;
    }
  }
}
</style>
