<template>
	<view class="time-table-container">
		<!-- 日期选择器 -->
		<view class="date-selector" v-if="showDateSelector">
			<scroll-view class="scroll-view_H" scroll-x @scroll="scroll">
				<view
					:class="{ 'is-active': item.isToday }"
					class="temp-box"
					v-for="(item, index) in internalDateInfo"
					:key="item.data"
					@click="handleSelectTime(item, index)">
					<view class="time-date-week">
						{{ item.weekDay }}
					</view>
					<view class="time-date-temp" />
					<view class="border-box">
						<view class="time-date-month">{{ item.shortDate }}</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 时间段选择器 -->
		<view class="time-grid" v-if="mode === 'appointment'">
			<view class="time-period" v-for="item in internalTimeInfo" :key="item.id">
				<view class="period-title">
					<image
						mode="scaleToFill"
						:src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/${encodeURIComponent(
							item.rangeName
						)}.png`"></image>
					{{ item.rangeName }}
				</view>
				<view class="time-row">
					<view
						v-for="slot in item.slots"
						:key="slot.id"
						:class="{
							'time-slot': true,
							'is-active': internalSelectTime.indexOf(slot.fullDate + slot.time) !== -1,
							'is-disabled': slot.isDisabled || slot.timeStatus === '已约满',
							'is-start': isIntervalStart(slot.fullDate + slot.time),
							'is-end': isIntervalEnd(slot.fullDate + slot.time),
							'is-middle': isIntervalMiddle(slot.fullDate + slot.time)
						}"
						@click="handleHour(slot)">
						<view class="time-text">{{ slot.startTime || slot.time }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 简化版时间选择器（用于弹框） -->
		<view class="time-table" v-else-if="mode === 'popup'">
			<view class="time-period" v-for="item in internalTimeInfo" :key="item.id">
				<view class="period-header">
					<image
						class="period-icon"
						mode="scaleToFill"
						:src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/${encodeURIComponent(item.rangeName)}.png`"
					/>
					<text class="period-name">{{ item.rangeName }}</text>
				</view>
				<view class="time-slots">
					<view
						v-for="(hour, hourIndex) in item.slots"
						:key="hourIndex"
						:class="{
							'time-slot': true,
							'selected': internalSelectTime.includes(hour.fullDate + ' ' + hour.time),
							'disabled': hour.timeStatus === '已约满' || hour.timeStatus === '已过期'
						}"
						@click="handleHour(hour)"
					>
						<text class="time-text">{{ hour.time }}</text>
						<text class="status-text" v-if="hour.timeStatus">{{ hour.timeStatus }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 旧版时间选择器（用于兼容） -->
		<view class="hour-box" v-else>
			<view class="morning" v-for="item in internalTimeInfo" :key="item.id">
				<view class="morning-box">
					<image mode="scaleToFill" :src="`../../../static/icon/${item.rangeName}.png`"></image>
					{{ item.rangeName }}
				</view>
				<view class="morning-time-box">
					<view
						v-for="item1 in item.slots"
						:key="item1.id"
						:class="{
							'is-active': internalSelectTime.indexOf(item1.fullDate + item1.time) !== -1,
							'is-disabled': item1.timeStatus == '已过期',
						}"
						class="time"
						@click="handleHour(item1)">
						{{ item1.time }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import { getFormattedTimeSlots } from '@/api/timeSlot.js'

// Props
const props = defineProps({
	// 时间信息
	timeInfo: {
		type: Array,
		default: () => []
	},
	// 日期信息
	dateInfo: {
		type: Array,
		default: () => []
	},
	// 已选择的时间
	selectTime: {
		type: Array,
		default: () => []
	},
	// 时间区间
	timeIntervals: {
		type: Array,
		default: () => []
	},
	// 显示模式：appointment(预约页面), popup(弹框), legacy(旧版), enhanced(增强版)
	mode: {
		type: String,
		default: 'legacy'
	},
	// 是否显示日期选择器
	showDateSelector: {
		type: Boolean,
		default: false
	},
	// 是否启用自动获取时间数据
	autoFetch: {
		type: Boolean,
		default: false
	},
	// 获取时间数据的天数范围
	dayRange: {
		type: Number,
		default: 6
	}
})

// Emits
const emit = defineEmits(['timeSelect', 'dateSelect', 'scroll', 'timeChange', 'intervalChange'])

// 内部状态
const internalDateInfo = ref([])
const internalTimeInfo = ref([])
const internalSelectTime = ref([])
const internalTimeIntervals = ref([])
const selectedHours = ref([])

// 常量
const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

// 初始化内部状态
const initializeInternalState = () => {
	if (props.autoFetch) {
		// 如果启用自动获取，使用内部状态
		internalDateInfo.value = []
		internalTimeInfo.value = []
		internalSelectTime.value = []
		internalTimeIntervals.value = []
	} else {
		// 否则使用传入的props
		internalDateInfo.value = [...props.dateInfo]
		internalTimeInfo.value = [...props.timeInfo]
		internalSelectTime.value = [...props.selectTime]
		internalTimeIntervals.value = [...props.timeIntervals]
	}
}

// 监听props变化
watch(() => props.dateInfo, (newVal) => {
	if (!props.autoFetch) {
		internalDateInfo.value = [...newVal]
	}
}, { deep: true })

watch(() => props.timeInfo, (newVal) => {
	if (!props.autoFetch) {
		internalTimeInfo.value = [...newVal]
	}
}, { deep: true })

watch(() => props.selectTime, (newVal) => {
	if (!props.autoFetch) {
		internalSelectTime.value = [...newVal]
	}
}, { deep: true })

watch(() => props.timeIntervals, (newVal) => {
	if (!props.autoFetch) {
		internalTimeIntervals.value = [...newVal]
	}
}, { deep: true })

// 格式化日期字符串为 iOS 兼容格式
const formatDateForIOS = (dateStr, timeStr) => {
    const formattedDate = dateStr.replace(/-/g, '/');
    return `${formattedDate} ${timeStr}:00`;
};

// 获取时间列表
const getTimeList = async () => {
    if (!props.autoFetch) return;

    try {
        // 获取今天和未来指定天数的日期范围
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + props.dayRange);

        // 格式化日期为 YYYY-MM-DD
        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        const res = await getFormattedTimeSlots(
            formatDate(startDate),
            formatDate(endDate)
        );

        if (res.code === 200) {
            // 处理日期信息
            internalDateInfo.value = res.data.dates.map(dateData => {
                const currentDate = new Date(dateData.date.replace(/-/g, '/'));
                const month = String(currentDate.getMonth() + 1).padStart(2, '0');
                const day = String(currentDate.getDate()).padStart(2, '0');

                return {
                    data: dateData.date,
                    shortDate: `${month}月${day}日`,
                    weekDay: weekDays[currentDate.getDay()],
                    isToday: dateData.isToday,
                    timeRanges: dateData.timeRanges
                };
            });

            // 处理时间段信息
            if (internalDateInfo.value.length > 0) {
                const firstDateData = internalDateInfo.value[0];
                internalTimeInfo.value = firstDateData.timeRanges.map(range => ({
                    id: range.endHour,
                    rangeName: range.rangeName,
                    slots: range.slots.map(slot => ({
                        id: slot.slotId,
                        time: slot.startTime.substring(0, 5),
                        fullDate: firstDateData.data,
                        timeStatus: slot.status === 2 ? '已过期' : (slot.hasAvailable ? '可预约' : '已约满'),
                        isDisabled: slot.status === 2 || !slot.hasAvailable,
                        availabilityText: slot.availabilityText,
                        timeDisplay: slot.timeDisplay
                    }))
                }));
            }
        } else {
            uni.showToast({
                title: '获取时间列表失败',
                icon: 'none'
            });
        }
    } catch (error) {
        console.error('获取时间列表错误：', error);
        uni.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
        });
    }
};

// 获取时间的分钟表示，用于计算时间差
const getMinutes = (timeStr) => {
	const [hours, minutes] = timeStr.split(':').map(Number);
	return hours * 60 + minutes;
};

// 根据分钟数生成时间字符串
const getTimeString = (minutes) => {
	const hours = Math.floor(minutes / 60);
	const mins = minutes % 60;
	return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

// 获取指定时间段内的所有时间点
const getAllTimesBetween = (startTime, date) => {
	const startMinutes = getMinutes(startTime);
	const endMinutes = startMinutes + 60; // 一小时后
	const times = [];

	// 获取所有时间段的slots
	const allSlots = internalTimeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	// 生成一小时内的5个时间点（包括起始和结束时间）
	for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
		const timeStr = getTimeString(mins);
		const slot = allSlots.find(s => s.time === timeStr && s.fullDate === date);
		if (slot && slot.timeStatus !== '已过期') {
			times.push(date + timeStr);
		}
	}

	return times;
};

// 检查是否可以创建区间
const canCreateInterval = (startTime, date) => {
	const startMinutes = getMinutes(startTime);
	const endMinutes = startMinutes + 60; // 一小时后

	// 获取所有时间段的slots
	const allSlots = internalTimeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	let availableSlots = 0;

	// 检查一小时内的所有时间点（包括起始和结束时间）
	for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
		const timeStr = getTimeString(mins);
		const fullTime = date + timeStr;

		// 在所有时间段中查找该时间点
		const slot = allSlots.find(s => s.time === timeStr && s.fullDate === date);
		if (slot && !slot.isDisabled && slot.timeStatus !== '已约满' && !isTimeInInterval(fullTime)) {
			availableSlots++;
		}
	}

	// 必须有5个可用的时间点（完整的一小时，包括起始和结束时间）
	return availableSlots === 5;
};

// 检查时间段是否与已选时间段重叠
const isTimeOverlapping = (startTime, date) => {
	const startMinutes = getMinutes(startTime);
	const endMinutes = startMinutes + 60; // 一小时后

	// 获取所有时间段的slots
	const allSlots = internalTimeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	// 检查所有已选择的时间点
	for (const timePoint of internalSelectTime.value) {
		// 只检查同一天的时间点
		if (timePoint.startsWith(date)) {
			const pointTime = timePoint.substring(10); // 提取时间部分
			const pointMinutes = getMinutes(pointTime);

			// 如果新时间段的任何部分与已选时间点重叠
			if (pointMinutes >= startMinutes && pointMinutes < endMinutes) {
				return true;
			}

			// 如果新时间段的开始时间落在已有时间段内
			for (const hour of selectedHours.value) {
				const hourStartTime = hour.startTime.substring(10);
				const hourStartMinutes = getMinutes(hourStartTime);
				const hourEndMinutes = hourStartMinutes + 60;

				if ((startMinutes >= hourStartMinutes && startMinutes < hourEndMinutes) ||
					(endMinutes > hourStartMinutes && endMinutes <= hourEndMinutes) ||
					(startMinutes <= hourStartMinutes && endMinutes >= hourEndMinutes)) {
					return true;
				}
			}
		}
	}

	// 检查路径上是否有不可用的时间点
	for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
		const timeStr = getTimeString(mins);
		const slot = allSlots.find(s => s.time === timeStr && s.fullDate === date);
		if (slot && (slot.isDisabled || slot.timeStatus === '已约满')) {
			return true;
		}
	}

	return false;
};

// 检查时间点是否在任何已选区间内
const isTimeInInterval = (fullTime) => {
	return internalTimeIntervals.value.some(interval => {
		return interval.timePoints.includes(fullTime);
	});
};

// 获取时间点所属的区间
const getTimeInterval = (fullTime) => {
	return internalTimeIntervals.value.find(interval =>
		interval.timePoints.includes(fullTime)
	);
};

// 创建新的时间区间
const createTimeInterval = (startTime, timePoints) => {
	return {
		startTime,
		timePoints,
		duration: 60 // 默认1小时（单位：分钟）
	};
};

// 检查时间点是否与已选时间段有冲突
const checkTimeConflict = (date, startMinutes, endMinutes) => {
	// 获取所有已选时间区间的范围
	const selectedRanges = internalTimeIntervals.value.map(interval => {
		const firstTime = interval.timePoints[0].slice(-5);
		const lastTime = interval.timePoints[interval.timePoints.length - 1].slice(-5);
		return {
			date: interval.timePoints[0].slice(0, 10),
			start: getMinutes(firstTime),
			end: getMinutes(lastTime) + 15 // 加上最后一个时间点的持续时间
		};
	});

	// 检查是否与任何已选时间段重叠
	return selectedRanges.some(range => {
		// 只检查同一天的时间段
		if (range.date !== date) {
			return false;
		}

		// 如果新时间段正好连接到已有时间段（结束时间等于已有时间段的开始时间，或开始时间等于已有时间段的结束时间），则不算冲突
		if (endMinutes === range.start || startMinutes === range.end) {
			return false;
		}

		// 检查时间段是否重叠
		return (startMinutes < range.end && endMinutes > range.start);
	});
};

// 更新时间点的禁用状态
const updateDisabledStatus = () => {
	// 获取所有时间段的slots
	const allSlots = internalTimeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	internalTimeInfo.value.forEach(range => {
		range.slots.forEach(slot => {
			// 重置状态
			slot.isDisabled = false;

			// 如果时间已过期或已约满，则禁用
			if (slot.timeStatus === '已过期' || slot.timeStatus === '已约满') {
				slot.isDisabled = true;
				return;
			}

			// 检查是否在任何已选区间内
			const fullTime = slot.fullDate + slot.time;
			if (isTimeInInterval(fullTime)) {
				return; // 如果在已选区间内，保持可选状态
			}

			// 获取当前时间段的范围
			const startMinutes = getMinutes(slot.time);
			const endMinutes = startMinutes + 60; // 一小时后

			// 检查是否与已选时间段重叠
			if (checkTimeConflict(slot.fullDate, startMinutes, endMinutes)) {
				slot.isDisabled = true;
				return;
			}

			// 检查这个时间段内的所有时间点是否都可用
			let allPointsAvailable = true;
			for (let mins = startMinutes; mins < endMinutes; mins += 15) {
				const timeStr = getTimeString(mins);
				const timeSlot = allSlots.find(s => s.time === timeStr && s.fullDate === slot.fullDate);
				if (!timeSlot || timeSlot.timeStatus === '已约满' || timeSlot.timeStatus === '已过期') {
					allPointsAvailable = false;
					break;
				}
			}

			slot.isDisabled = !allPointsAvailable;
		});
	});
};

// 查找相邻的时间区间
const findAdjacentInterval = (data) => {
	const currentMinutes = getMinutes(data.time);
	const currentDate = data.fullDate;

	return internalTimeIntervals.value.find(interval => {
		const firstPoint = interval.timePoints[0];
		const lastPoint = interval.timePoints[interval.timePoints.length - 1];

		const firstTime = firstPoint.slice(-5); // 获取时间部分 HH:mm
		const lastTime = lastPoint.slice(-5);
		const firstMinutes = getMinutes(firstTime);
		const lastMinutes = getMinutes(lastTime);

		// 检查是否紧邻现有区间（前后都检查）
		return (currentDate === firstPoint.slice(0, 10) && // 同一天
			(Math.abs(currentMinutes - firstMinutes) === 15 || // 前后相差15分钟
			Math.abs(currentMinutes - (lastMinutes + 15)) === 0 || // 正好接上末尾
			Math.abs((currentMinutes + 60) - firstMinutes) === 0)); // 正好接上开头
	});
};

// 查找下一个相邻的区间
const findNextInterval = (data) => {
	const currentMinutes = getMinutes(data.time);
	const currentDate = data.fullDate;

	return internalTimeIntervals.value.find(interval => {
		const firstPoint = interval.timePoints[0];
		const firstTime = firstPoint.slice(-5);
		const firstMinutes = getMinutes(firstTime);

		// 检查是否是同一天的后续区间
		return currentDate === firstPoint.slice(0, 10) &&
			   firstMinutes > currentMinutes;
	});
};

// 检查时间点是否在其他区间内（排除当前区间）
const isTimeInOtherInterval = (fullTime, currentInterval) => {
	return internalTimeIntervals.value.some(interval =>
		interval !== currentInterval &&
		interval.timePoints.includes(fullTime)
	);
};

// 扩展现有区间
const extendExistingInterval = (data, adjacentInterval) => {
	const currentMinutes = getMinutes(data.time);
	const firstPoint = adjacentInterval.timePoints[0];
	const lastPoint = adjacentInterval.timePoints[adjacentInterval.timePoints.length - 1];
	const firstTime = firstPoint.slice(-5);
	const lastTime = lastPoint.slice(-5);
	const firstMinutes = getMinutes(firstTime);
	const lastMinutes = getMinutes(lastTime);

	// 获取所有时间段的slots
	const allSlots = internalTimeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	let newTimePoints = [];

	if (currentMinutes < firstMinutes) {
		// 向前扩展
		const startMinutes = currentMinutes;
		const endMinutes = lastMinutes;
		for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
			const timeStr = getTimeString(mins);
			const slot = allSlots.find(s =>
				s.time === timeStr &&
				s.fullDate === data.fullDate &&
				s.timeStatus !== '已过期' &&
				!isTimeInOtherInterval(data.fullDate + timeStr, adjacentInterval)
			);
			if (slot) {
				newTimePoints.push(data.fullDate + timeStr);
			}
		}
	} else {
		// 向后扩展
		// 检查是否有其他相邻的区间
		const nextInterval = findNextInterval(data);
		if (nextInterval) {
			// 如果有下一个区间，扩展到下一个区间的开始时间
			const nextStartTime = nextInterval.timePoints[0].slice(-5);
			const nextStartMinutes = getMinutes(nextStartTime);

			for (let mins = firstMinutes; mins <= nextStartMinutes; mins += 15) {
				const timeStr = getTimeString(mins);
				const slot = allSlots.find(s =>
					s.time === timeStr &&
					s.fullDate === data.fullDate &&
					s.timeStatus !== '已过期' &&
					!isTimeInOtherInterval(data.fullDate + timeStr, adjacentInterval)
				);
				if (slot) {
					newTimePoints.push(data.fullDate + timeStr);
				}
			}
		} else {
			// 如果没有下一个区间，扩展一小时
			const endMinutes = currentMinutes + 45; // 只扩展45分钟，因为当前点已经选中
			for (let mins = firstMinutes; mins <= endMinutes; mins += 15) {
				const timeStr = getTimeString(mins);
				const slot = allSlots.find(s =>
					s.time === timeStr &&
					s.fullDate === data.fullDate &&
					s.timeStatus !== '已过期' &&
					!isTimeInOtherInterval(data.fullDate + timeStr, adjacentInterval)
				);
				if (slot) {
					newTimePoints.push(data.fullDate + timeStr);
				}
			}
		}
	}

	if (newTimePoints.length >= 5) {
		// 更新区间
		const index = internalTimeIntervals.value.indexOf(adjacentInterval);
		internalTimeIntervals.value[index] = createTimeInterval(newTimePoints[0], newTimePoints);
		internalSelectTime.value = internalSelectTime.value.filter(time =>
			!adjacentInterval.timePoints.includes(time)
		);
		internalSelectTime.value = [...internalSelectTime.value, ...newTimePoints];
	} else {
		uni.showToast({
			title: "无法扩展时间区间，请检查时间是否充足",
			icon: "none",
			duration: 2000
		});
	}
};

// 创建新区间
const createNewInterval = (data) => {
	if (canCreateInterval(data.time, data.fullDate)) {
		const hourTimePoints = getAllTimesBetween(data.time, data.fullDate);

		if (hourTimePoints.length === 5) {
			const newInterval = createTimeInterval(data.fullDate + data.time, hourTimePoints);
			internalTimeIntervals.value.push(newInterval);
			internalSelectTime.value = [...internalSelectTime.value, ...hourTimePoints];
		} else {
			uni.showToast({
				title: "无法创建完整的一小时区间",
				icon: "none",
				duration: 2000
			});
		}
	} else {
		uni.showToast({
			title: "无法创建时间区间，请检查时间是否充足或是否与其他区间冲突",
			icon: "none",
			duration: 2000
		});
	}
};

// 处理时间点选择的核心逻辑
const handleTimeSelection = (data) => {
	const fullTime = data.fullDate + data.time;

	// 检查时间槽是否已禁用
	if (data.isDisabled || data.timeStatus === '已约满' || data.timeStatus === '已过期') {
		uni.showToast({
			title: "该时段已约满或不可选",
			icon: "none",
			duration: 2000
		});
		return;
	}

	// 检查是否点击了已选区间内的时间点
	const existingInterval = getTimeInterval(fullTime);
	if (existingInterval) {
		// 取消整个区间
		const index = internalTimeIntervals.value.indexOf(existingInterval);
		internalTimeIntervals.value.splice(index, 1);
		internalSelectTime.value = internalSelectTime.value.filter(time =>
			!existingInterval.timePoints.includes(time)
		);
		// 重新计算所有时间点的状态
		nextTick(() => {
			updateDisabledStatus();
			emitChanges();
		});
		return;
	}

	// 检查新时间段是否与已有时间段冲突
	const startMinutes = getMinutes(data.time);
	const endMinutes = startMinutes + 60;
	if (checkTimeConflict(data.fullDate, startMinutes, endMinutes)) {
		uni.showToast({
			title: "该时段与已选时间冲突",
			icon: "none",
			duration: 2000
		});
		return;
	}

	// 查找是否有相邻的区间
	const adjacentInterval = findAdjacentInterval(data);
	if (adjacentInterval) {
		// 扩展现有区间
		extendExistingInterval(data, adjacentInterval);
	} else {
		// 创建新区间
		createNewInterval(data);
	}

	// 更新禁用状态
	updateDisabledStatus();
	emitChanges();
};

// 发送变化事件
const emitChanges = () => {
	emit('timeChange', internalSelectTime.value);
	emit('intervalChange', internalTimeIntervals.value);
};

// Methods
const handleHour = (hour) => {
	if (props.mode === 'enhanced' || props.autoFetch) {
		// 使用增强模式的时间选择逻辑
		handleTimeSelection(hour);
	} else {
		// 使用原有的简单模式
		emit('timeSelect', hour);
	}
}

const handleSelectTime = (item, index) => {
	if (props.autoFetch) {
		// 自动获取模式下的日期选择逻辑
		internalDateInfo.value.forEach((dateItem) => (dateItem.isToday = false));
		internalDateInfo.value[index].isToday = true;

		// 更新时间段信息
		const selectedDateData = internalDateInfo.value[index];
		internalTimeInfo.value = selectedDateData.timeRanges.map(range => ({
			id: range.endHour,
			rangeName: range.rangeName,
			slots: range.slots.map(slot => ({
				id: slot.slotId,
				time: slot.startTime.substring(0, 5),
				fullDate: selectedDateData.data,
				timeStatus: slot.status === 2 ? '已过期' : (slot.hasAvailable ? '可预约' : '已约满'),
				isDisabled: slot.status === 2 || !slot.hasAvailable,
				availabilityText: slot.availabilityText,
				timeDisplay: slot.timeDisplay
			}))
		}));

		// 切换日期时清空已选择的时间
		internalSelectTime.value = [];
		internalTimeIntervals.value = [];

		// 重新计算禁用状态
		nextTick(() => {
			updateDisabledStatus();
			emitChanges();
		});
	}

	emit('dateSelect', item, index)
}

const scroll = (e) => {
	emit('scroll', e)
}

// 检查时间点是否是区间的开始
const isIntervalStart = (fullTime) => {
	return internalTimeIntervals.value.some(interval => interval.timePoints && interval.timePoints[0] === fullTime)
}

// 检查时间点是否是区间的结束
const isIntervalEnd = (fullTime) => {
	return internalTimeIntervals.value.some(interval =>
		interval.timePoints && interval.timePoints[interval.timePoints.length - 1] === fullTime
	)
}

// 检查时间点是否是区间的中间点
const isIntervalMiddle = (fullTime) => {
	return internalTimeIntervals.value.some(interval => {
		if (!interval.timePoints) return false
		const index = interval.timePoints.indexOf(fullTime)
		return index > 0 && index < interval.timePoints.length - 1
	})
}

// 暴露方法给父组件
const getSelectedTimes = () => {
	return internalSelectTime.value;
};

const getTimeIntervals = () => {
	return internalTimeIntervals.value;
};

const clearSelection = () => {
	internalSelectTime.value = [];
	internalTimeIntervals.value = [];
	updateDisabledStatus();
	emitChanges();
};

// 初始化
onMounted(() => {
	initializeInternalState();
	if (props.autoFetch) {
		getTimeList();
	}
	nextTick(() => {
		updateDisabledStatus();
	});
});

// 暴露给父组件的方法
defineExpose({
	getSelectedTimes,
	getTimeIntervals,
	clearSelection,
	getTimeList
})
</script>

<style lang="scss" scoped>
.time-table-container {
	width: 100%;
}

/* 日期选择器样式 */
.date-selector {
	.scroll-view_H {
		white-space: nowrap;

		.temp-box {
			width: 140rpx;
			height: 100rpx;
			display: inline-block;
			margin: 0 10rpx 10rpx;
			position: relative;

			.border-box {
				border: 1px solid #ccc;
				position: absolute;
				bottom: 0;
				height: 80rpx;
				z-index: 1;
				background-color: #fff;
				width: 100%;
				display: flex;
				align-items: flex-end;
				justify-content: center;
				font-size: 26rpx;
				border-radius: 10rpx;
			}

			.time-date-temp {
				width: 100rpx;
				height: 20rpx;
				position: absolute;
				border-radius: 10rpx;
				top: 8rpx;
				left: 50%;
				background-color: #cacaca;
				transform: translateX(-50%);
			}

			.time-date-week {
				background-color: #f7f7f7;
				position: absolute;
				border-radius: 6rpx;
				top: 8rpx;
				left: 50%;
				transform: translateX(-50%);
				z-index: 2;
				padding: 0 16rpx;
				color: #424242;
				font-size: 26rpx;
				border: 1px solid #e1e1e1;
			}

			.time-date-month {
				z-index: 2;
				text-align: center;
				margin-bottom: 10rpx;
			}
		}

		.is-active {
			.border-box {
				border: 1px solid #5fbaf9;
			}

			.time-date-temp,
			.time-date-week {
				background-color: #52b5f9;
				border: 1px solid #52b5f9;
				color: #fff;
			}
		}
	}
}

/* 预约页面时间网格样式 */
.time-grid {
	width: 100%;
	border: 1px solid #eee;
	border-radius: 10rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
	
	.time-period {
		padding: 20rpx;
		border-bottom: 1px solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
		
		.period-title {
			display: flex;
			align-items: center;
			color: #262626;
			font-size: 28rpx;
			margin-bottom: 20rpx;
			
			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}
		
		.time-row {
			display: flex;
			flex-wrap: wrap;
			.time-slot {
				width: 20%;
				height: 70rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				position: relative;
				cursor: pointer;
				
				.time-text {
					font-size: 26rpx;
					color: #333;
					margin-bottom: 6rpx;
				}
				
				&.is-disabled {
					background-color: #f5f5f5;
					cursor: not-allowed;
					
					.time-text {
						color: #999;
					}
				}
				
				&.is-active {
					background-color: #52b5f9;
					border-color: #1890ff;
					
					.time-text {
						color: #fff;
					}
				}
				
				&.is-start {
					border-top-right-radius: 0;
					border-bottom-right-radius: 0;
				}
				
				&.is-middle {
					border-radius: 0;
					border-left: none;
					border-right: none;
					background-color: #52b5f9;
					
					&:after {
						content: '';
						position: absolute;
						left: 0;
						right: 0;
						top: -1px;
						bottom: -1px;
						background-color: #52b5f9;
						z-index: -1;
					}
				}
				
				&.is-end {
					border-top-left-radius: 0;
					border-bottom-left-radius: 0;
				}
			}
		}
	}
}

/* 弹框时间表样式 */
.time-table {
	.time-period {
		margin-bottom: 32rpx;

		.period-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			.period-icon {
				width: 32rpx;
				height: 32rpx;
				margin-right: 12rpx;
			}

			.period-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;
			}
		}

		.time-slots {
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;
		}

		.time-slot {
			position: relative;
			padding: 16rpx 24rpx;
			background-color: #f8f8f8;
			border-radius: 8rpx;
			border: 2rpx solid transparent;
			min-width: 120rpx;
			text-align: center;

			&.selected {
				background-color: #e8f6ff;
				border-color: #20a3f3;
			}

			&.disabled {
				background-color: #f0f0f0;
				color: #ccc;
				opacity: 0.6;
			}

			.time-text {
				font-size: 26rpx;
				color: #333;
				display: block;
			}

			.status-text {
				font-size: 20rpx;
				color: #999;
				display: block;
				margin-top: 4rpx;
			}
		}
	}
}

/* 旧版时间选择器样式 */
.hour-box {
	width: calc(100% - 80rpx);
	margin: 40rpx;
	border: 1px solid #ccc;
	border-radius: 10rpx;
	background-color: #f9f9f9;

	image {
		margin-right: 10rpx;
		width: 40rpx;
		height: 40rpx;
	}

	.morning {
		padding: 20rpx;

		.morning-box {
			color: #262626;
			font-size: 28rpx;
			margin-bottom: 10rpx;
			display: flex;
			align-items: center;
		}

		.morning-time-box {
			display: flex;
			flex-wrap: wrap;
			width: 100%;

			.time {
				width: calc(33.333% - 52rpx);
				font-size: 28rpx;
				background-color: #fff;
				padding: 10rpx;
				margin-right: 20rpx;
				margin-bottom: 10rpx;
				text-align: center;
				border-radius: 10rpx;
				border: 1px solid #f9f9f9;
			}

			.is-disabled {
				border: 1px solid #efefef;
				color: #dcdada;
				background-color: #f7f7f7;
			}

			.is-active {
				border: 1px solid #52b5f9;
				position: relative;

				&:after {
					content: "";
					position: absolute;
					bottom: 0;
					right: 0;
					width: 30rpx;
					height: 20rpx;
					background-color: #fff;
					background-image: url(https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%AF%B9%E5%8B%BE.png);
					background-size: 120% 120%;
					border-bottom-right-radius: 10rpx;
				}
			}
		}
	}
}
</style>
