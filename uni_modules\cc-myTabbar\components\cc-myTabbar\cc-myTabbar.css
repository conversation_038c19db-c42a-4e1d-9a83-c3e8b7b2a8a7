@charset "UTF-8";
/* 主要颜色 */
.page-total {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
}

.tab-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 120rpx;
  background-color: #FFFFFF;
}

.tab-list .list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 20%;
  height: 120rpx;
}

.tab-list .list .tab-item-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-list .list .tab-item-container image {
  width: 48rpx;
  height: 48rpx;
  background-color: white;
}

.tab-list .list .tab-item-container .message-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ff4757;
  color: white;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  padding: 0 8rpx;
  box-sizing: border-box;
  border: 2rpx solid #fff;
  z-index: 10;
}

.tab-list .list text {
  color: #333333;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.tab-list .list .action {
  color: #fe3b0f;
}
