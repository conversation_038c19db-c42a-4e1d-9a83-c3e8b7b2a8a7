# 后端搜索接口需求文档

## 概述

为了实现真实的搜索功能，需要后端提供一套完整的搜索接口。根据前端的实现，建议后端新增以下接口来支持统一搜索功能。

## 🔍 核心搜索接口需求

### **1. 全局搜索接口**
```
GET /miniapp/search/global
```

**功能**: 统一搜索所有类型的内容（咨询师、课程、冥想、测评）

**请求参数**:
```json
{
  "keyword": "心理咨询",        // 必需：搜索关键词
  "type": "all",               // 可选：搜索类型 all|consultant|course|meditation|assessment
  "pageNum": 1,                // 可选：页码，默认1
  "pageSize": 20               // 可选：每页数量，默认20
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "type": "consultant",      // 类型：consultant|course|meditation|assessment
      "title": "张医生",         // 标题/名称
      "description": "专业心理咨询师", // 描述
      "imageUrl": "avatar.jpg",  // 图片URL
      "price": 200,              // 价格（如果有）
      "tags": ["焦虑", "抑郁"],  // 标签
      "score": 0.95              // 搜索相关度评分
    }
  ],
  "total": 100
}
```

### **2. 搜索建议接口**
```
GET /miniapp/search/suggestions
```

**功能**: 根据输入提供搜索建议

**请求参数**:
```json
{
  "keyword": "心理"            // 必需：输入的关键词
}
```

**响应格式**:
```json
{
  "code": 200,
  "data": [
    {
      "keyword": "心理咨询",
      "count": 120              // 搜索次数（可选）
    },
    {
      "keyword": "心理测评",
      "count": 89
    }
  ]
}
```

### **3. 热门搜索接口**
```
GET /miniapp/search/hot
```

**功能**: 获取热门搜索关键词

**请求参数**:
```json
{
  "type": "all"               // 可选：类型筛选
}
```

**响应格式**:
```json
{
  "code": 200,
  "data": [
    {
      "name": "热门搜索",
      "icon": "/static/images/hot.png",
      "items": [
        {
          "keyword": "焦虑咨询",
          "count": 1520,
          "rank": 1
        },
        {
          "keyword": "心理测评",
          "count": 1200,
          "rank": 2
        }
      ]
    }
  ]
}
```

## 📊 分类搜索接口

### **现有接口评估**

#### **咨询师搜索**
```
GET /system/consultant/search
```
**状态**: ✅ 已存在，可直接使用
**建议**: 增加关键词高亮和相关度排序

#### **课程搜索**
```
GET /miniapp/course/search
```
**状态**: ❓ 需要确认是否存在
**建议**: 如不存在，需要新增

#### **冥想搜索**
```
GET /miniapp/meditation/search
```
**状态**: ❓ 需要确认是否存在
**建议**: 如不存在，需要新增

#### **测评搜索**
```
GET /miniapp/assessment/search
```
**状态**: ❓ 需要确认是否存在
**建议**: 如不存在，需要新增

## 🔧 辅助功能接口

### **1. 搜索记录接口**
```
POST /miniapp/search/record
```

**功能**: 保存用户搜索记录

**请求参数**:
```json
{
  "keyword": "心理咨询",
  "type": "consultant",
  "userId": 123              // 可选：用户ID
}
```

### **2. 搜索历史接口**
```
GET /miniapp/search/history
```

**功能**: 获取用户搜索历史

**请求参数**:
```json
{
  "limit": 10               // 可选：限制数量
}
```

### **3. 相关搜索接口**
```
GET /miniapp/search/related
```

**功能**: 获取相关搜索建议

**请求参数**:
```json
{
  "keyword": "心理咨询"
}
```

## 🎯 实现建议

### **方案A: 新增统一搜索接口（推荐）**

**优势**:
- 统一的搜索体验
- 便于搜索结果排序和去重
- 支持跨类型搜索
- 便于搜索统计和分析

**实现方式**:
```java
@RestController
@RequestMapping("/miniapp/search")
public class MiniAppSearchController {
    
    @GetMapping("/global")
    public Result globalSearch(@RequestParam String keyword,
                              @RequestParam(defaultValue = "all") String type,
                              @RequestParam(defaultValue = "1") Integer pageNum,
                              @RequestParam(defaultValue = "20") Integer pageSize) {
        // 实现统一搜索逻辑
    }
    
    @GetMapping("/suggestions")
    public Result getSearchSuggestions(@RequestParam String keyword) {
        // 实现搜索建议逻辑
    }
    
    @GetMapping("/hot")
    public Result getHotSearches(@RequestParam(defaultValue = "all") String type) {
        // 实现热门搜索逻辑
    }
}
```

### **方案B: 使用现有分类接口**

**优势**:
- 利用现有接口，开发量小
- 各模块独立，便于维护

**劣势**:
- 需要前端调用多个接口
- 搜索结果难以统一排序
- 用户体验相对较差

## 📋 数据库设计建议

### **搜索记录表**
```sql
CREATE TABLE search_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    keyword VARCHAR(255) NOT NULL,
    search_type VARCHAR(50),
    result_count INT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_keyword (user_id, keyword),
    INDEX idx_keyword_time (keyword, created_time)
);
```

### **热门搜索表**
```sql
CREATE TABLE hot_searches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(255) NOT NULL,
    search_count INT DEFAULT 0,
    search_type VARCHAR(50),
    rank_score DECIMAL(10,2),
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type_score (search_type, rank_score DESC)
);
```

## 🚀 实现优先级

### **第一阶段（必需）**
1. ✅ 全局搜索接口 `/miniapp/search/global`
2. ✅ 搜索建议接口 `/miniapp/search/suggestions`
3. ✅ 热门搜索接口 `/miniapp/search/hot`

### **第二阶段（优化）**
1. 搜索记录保存 `/miniapp/search/record`
2. 搜索历史查询 `/miniapp/search/history`
3. 相关搜索推荐 `/miniapp/search/related`

### **第三阶段（高级）**
1. 智能搜索排序
2. 搜索结果高亮
3. 搜索统计分析

## 📝 总结

**推荐方案**: 新增统一搜索接口

**理由**:
1. **用户体验更好**: 一次搜索获取所有相关结果
2. **开发效率高**: 前端只需调用一个接口
3. **扩展性强**: 便于后续添加新的内容类型
4. **数据统计完整**: 便于分析用户搜索行为

**最小可行方案**:
- 实现 `/miniapp/search/global` 接口
- 支持基本的关键词搜索
- 返回统一格式的搜索结果

这样既能满足前端的搜索需求，又为后续功能扩展留下了空间。
