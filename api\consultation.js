import request from '../utils/request'

// 创建咨询订单
export function createConsultationOrder(data) {
  return request({
    url: '/miniapp/consultation/order',
    method: 'post',
    data
  })
}

// 获取用户订单列表
export function getUserOrders() {
  return request({
    url: '/miniapp/consultation/orders',
    method: 'get'
  })
}

// 获取订单详情
export function getOrderDetails(id) {
  return request({
    url: `/miniapp/consultation/order/${id}`,
    method: 'get'
  })
}

// 取消订单
export function cancelOrder(id, cancelReason) {
  return request({
    url: `/miniapp/consultation/order/cancel/${id}`,
    method: 'post',
    params: { cancelReason }
  })
}

// 检查时间段是否可预约
export function checkTimeAvailable(params) {
  return request({
    url: '/miniapp/consultation/checkAvailable',
    method: 'get',
    params
  })
}

// 提交咨询登记表
export function submitRegistrationForm(data) {
  return request({
    url: '/miniapp/consultation/registrationForm',
    method: 'post',
    data
  })
}

// 获取用户咨询记录列表
export function getUserRecords() {
  return request({
    url: '/miniapp/consultation/records',
    method: 'get'
  })
}

// 获取咨询记录详情
export function getRecordDetails(id) {
  return request({
    url: `/miniapp/consultation/record/${id}`,
    method: 'get'
  })
}

// 提交咨询评价
export function submitConsultationReview(data) {
  return request({
    url: '/miniapp/consultation/review',
    method: 'post',
    data
  })
}

// 获取用户评价列表
export function getUserReviews() {
  return request({
    url: '/miniapp/consultation/reviews',
    method: 'get'
  })
}

// 获取咨询师评价列表
export function getConsultantReviews(consultantId) {
  return request({
    url: `/miniapp/consultation/consultant/reviews/${consultantId}`,
    method: 'get'
  })
}

// 获取用户咨询统计
export function getUserStatistics() {
  return request({
    url: '/miniapp/consultation/statistics',
    method: 'get'
  })
}

// 获取与咨询师的咨询历史
export function getConsultationHistory(consultantId) {
  return request({
    url: `/miniapp/consultation/history/${consultantId}`,
    method: 'get'
  })
}

// 检查是否可以评价咨询记录
export function checkCanRate(recordId) {
  return request({
    url: `/miniapp/consultation/canRate/${recordId}`,
    method: 'get'
  })
}
