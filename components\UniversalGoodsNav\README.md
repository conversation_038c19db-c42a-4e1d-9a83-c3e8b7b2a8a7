# UniversalGoodsNav 通用购物车组件

## 概述

UniversalGoodsNav 是一个通用的底部导航购物车组件，可以在测评、冥想、课程和咨询师详情页面中使用。该组件提供了收藏、客服、转发和主要操作功能。

## 功能特性

- ✅ 支持多种页面类型：assessment（测评）、meditation（冥想）、course（课程）、counselor（咨询师）
- ✅ 统一的收藏功能，支持添加/取消收藏
- ✅ 客服功能，自动连接客服会话
- ✅ 转发功能，支持微信小程序、H5、App多端分享
- ✅ 动态主要操作按钮，根据页面类型和状态显示不同文案
- ✅ 响应式设计，支持深色模式
- ✅ 安全区域适配

## 使用方法

### 1. 导入组件

```vue
<script setup>
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'
</script>
```

### 2. 在模板中使用

```vue
<template>
  <UniversalGoodsNav
    page-type="assessment"
    :detail-data="detailData"
    :purchased="isPurchased"
    :price="price"
    :favorited="isFavorited"
    :favorite-id="favoriteId"
    @favorite="handleFavorite"
    @contact-service="handleContactService"
    @share="handleShare"
    @main-action="handleMainAction"
  />
</template>
```

## Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| pageType | String | 是 | - | 页面类型：'assessment'、'meditation'、'course'、'counselor' |
| detailData | Object | 否 | {} | 详情数据对象 |
| purchased | Boolean | 否 | false | 是否已购买/已完成 |
| price | Number/String | 否 | 0 | 价格 |
| favorited | Boolean | 否 | false | 是否已收藏 |
| favoriteId | String/Number | 否 | null | 收藏ID |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| favorite | { favorited: Boolean, favoriteId: String/Number } | 收藏状态变化 |
| contact-service | - | 点击客服按钮 |
| share | shareConfig | 点击转发按钮 |
| main-action | { index: Number, button: Object, pageType: String } | 点击主要操作按钮 |

## 不同页面类型的按钮文案

### 测评页面 (assessment)
- 未购买：`开始测评 ¥{price}`
- 已购买：`查看报告`

### 冥想页面 (meditation)
- 未购买且非免费：`立即购买 ¥{price}`
- 已购买或免费：`开始冥想`

### 课程页面 (course)
- 未购买：`立即购买 ¥{price}`
- 已购买：`开始学习`

### 咨询师页面 (counselor)
- 始终显示：`预约咨询`

## 示例代码

```vue
<template>
  <view class="page">
    <!-- 页面内容 -->
    
    <UniversalGoodsNav
      page-type="course"
      :detail-data="courseDetail"
      :purchased="courseDetail.purchased"
      :price="courseDetail.price"
      :favorited="courseDetail.favorited"
      :favorite-id="courseDetail.favoriteId"
      @favorite="handleFavorite"
      @contact-service="handleContactService"
      @share="handleShare"
      @main-action="handleMainAction"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'

const courseDetail = ref({
  id: 1,
  title: '心理健康课程',
  price: 99,
  purchased: false,
  favorited: false,
  favoriteId: null
})

// 处理收藏事件
const handleFavorite = (favoriteData) => {
  courseDetail.value.favorited = favoriteData.favorited
  courseDetail.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
  console.log('联系客服')
}

// 处理分享事件
const handleShare = (shareConfig) => {
  console.log('分享配置:', shareConfig)
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
  if (!courseDetail.value.purchased) {
    // 购买逻辑
    console.log('购买课程')
  } else {
    // 开始学习逻辑
    console.log('开始学习')
  }
}
</script>
```

## 注意事项

1. 确保已正确配置收藏、客服等相关API
2. 转发功能需要在页面中配置 `onShareAppMessage` 方法
3. 组件会自动处理不同平台的分享逻辑
4. 收藏功能需要用户登录状态
5. 客服功能使用固定的客服ID（137）

## 更新日志

- v1.0.0: 初始版本，支持基本的购物车功能
- v1.1.0: 添加转发功能和多端适配
- v1.2.0: 优化收藏功能和客服功能
