/**
 * 收藏功能API接口
 * 基于新的收藏系统重构，与后端MiniAppFavoriteController保持一致
 */
import request from '@/utils/request.js'

// ==================== 核心收藏接口 ====================

/**
 * 添加收藏
 * @param {Object} data - 收藏数据
 * @param {Number} data.targetType - 收藏目标类型 (1:咨询师, 2:课程, 3:冥想, 4:测评)
 * @param {Number} data.targetId - 目标对象ID
 * @param {String} data.targetTitle - 目标标题（可选）
 * @param {String} data.targetImage - 目标图片（可选）
 * @param {String} data.tags - 用户自定义标签（可选）
 * @param {String} data.notes - 用户备注（可选）
 * @param {Number} data.isPublic - 是否公开收藏，默认0私有（可选）
 * @param {Number} data.groupId - 收藏分组ID（可选）
 */
export function addFavorite(data) {
  return request({
    url: '/miniapp/favorite/add',
    method: 'POST',
    data
  })
}

/**
 * 取消收藏
 * @param {Number} favoriteId - 收藏ID
 */
export function removeFavorite(favoriteId) {
  return request({
    url: `/miniapp/favorite/remove/${favoriteId}`,
    method: 'DELETE'
  })
}

/**
 * 批量取消收藏
 * @param {Array} favoriteIds - 收藏ID数组
 */
export function batchRemoveFavorite(favoriteIds) {
  return request({
    url: '/miniapp/favorite/batchRemove',
    method: 'DELETE',
    data: favoriteIds
  })
}

/**
 * 查询收藏列表
 * @param {Object} params - 查询参数
 * @param {Number} params.targetType - 收藏类型筛选（可选）
 */
export function getFavoriteList(params = {}) {
  return request({
    url: '/miniapp/favorite/list',
    method: 'GET',
    params
  })
}

/**
 * 查询收藏详情
 * @param {Number} favoriteId - 收藏ID
 */
export function getFavoriteDetail(favoriteId) {
  return request({
    url: `/miniapp/favorite/detail/${favoriteId}`,
    method: 'GET'
  })
}

/**
 * 检查是否已收藏
 * @param {Object} params - 查询参数
 * @param {Number} params.targetType - 目标类型
 * @param {Number} params.targetId - 目标ID
 */
export function checkFavorite(params) {
  return request({
    url: '/miniapp/favorite/check',
    method: 'GET',
    params
  })
}

/**
 * 查询收藏统计
 */
export function getFavoriteStats() {
  return request({
    url: '/miniapp/favorite/stats',
    method: 'GET'
  })
}

// ==================== 收藏分组接口 ====================

/**
 * 查询收藏分组列表
 */
export function getFavoriteGroups() {
  return request({
    url: '/miniapp/favorite/group/list',
    method: 'GET'
  })
}

/**
 * 创建收藏分组
 * @param {Object} data - 分组数据
 * @param {String} data.groupName - 分组名称
 * @param {String} data.groupIcon - 分组图标（可选）
 * @param {String} data.groupColor - 分组颜色（可选）
 * @param {String} data.description - 分组描述（可选）
 * @param {Number} data.isPublic - 是否公开，默认0私有（可选）
 */
export function createFavoriteGroup(data) {
  return request({
    url: '/miniapp/favorite/group/create',
    method: 'POST',
    data
  })
}

/**
 * 更新收藏分组
 * @param {Object} data - 分组数据
 * @param {Number} data.groupId - 分组ID
 * @param {String} data.groupName - 分组名称
 * @param {String} data.groupIcon - 分组图标（可选）
 * @param {String} data.groupColor - 分组颜色（可选）
 * @param {String} data.description - 分组描述（可选）
 */
export function updateFavoriteGroup(data) {
  return request({
    url: '/miniapp/favorite/group/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除收藏分组
 * @param {Number} groupId - 分组ID
 */
export function deleteFavoriteGroup(groupId) {
  return request({
    url: `/miniapp/favorite/group/delete/${groupId}`,
    method: 'DELETE'
  })
}

/**
 * 添加收藏到分组
 * @param {Number} favoriteId - 收藏ID
 * @param {Number} groupId - 分组ID
 */
export function addFavoriteToGroup(favoriteId, groupId) {
  return request({
    url: '/miniapp/favorite/group/addFavorite',
    method: 'POST',
    params: { favoriteId, groupId }
  })
}

/**
 * 从分组中移除收藏
 * @param {Number} favoriteId - 收藏ID
 * @param {Number} groupId - 分组ID
 */
export function removeFavoriteFromGroup(favoriteId, groupId) {
  return request({
    url: '/miniapp/favorite/group/removeFavorite',
    method: 'DELETE',
    params: { favoriteId, groupId }
  })
}

/**
 * 查询分组内收藏列表
 * @param {Number} groupId - 分组ID
 */
export function getGroupFavorites(groupId) {
  return request({
    url: `/miniapp/favorite/group/favorites/${groupId}`,
    method: 'GET'
  })
}

// ==================== 收藏类型常量 ====================

export const FAVORITE_TYPES = {
  CONSULTANT: 1,    // 咨询师
  COURSE: 2,        // 课程
  MEDITATION: 3,    // 冥想
  ASSESSMENT: 4     // 测评
}

export const FAVORITE_TYPE_NAMES = {
  [FAVORITE_TYPES.CONSULTANT]: '咨询师',
  [FAVORITE_TYPES.COURSE]: '课程',
  [FAVORITE_TYPES.MEDITATION]: '冥想',
  [FAVORITE_TYPES.ASSESSMENT]: '测评'
}

// ==================== 工具函数 ====================

/**
 * 获取收藏类型名称
 * @param {Number} type - 收藏类型
 * @returns {String} 类型名称
 */
export function getFavoriteTypeName(type) {
  return FAVORITE_TYPE_NAMES[type] || '未知类型'
}

/**
 * 根据页面类型获取收藏类型
 * @param {String} pageType - 页面类型 (consultant, course, meditation, assessment)
 * @returns {Number} 收藏类型
 */
export function getTargetTypeByPage(pageType) {
  const pageTypeMap = {
    'consultant': FAVORITE_TYPES.CONSULTANT,
    'course': FAVORITE_TYPES.COURSE,
    'meditation': FAVORITE_TYPES.MEDITATION,
    'assessment': FAVORITE_TYPES.ASSESSMENT
  }
  return pageTypeMap[pageType] || FAVORITE_TYPES.CONSULTANT
}

/**
 * 构建收藏数据
 * @param {String} pageType - 页面类型
 * @param {Number} targetId - 目标ID
 * @param {Object} targetInfo - 目标信息（可选）
 * @returns {Object} 收藏数据
 */
export function buildFavoriteData(pageType, targetId, targetInfo = {}) {
  return {
    targetType: getTargetTypeByPage(pageType),
    targetId,
    targetTitle: targetInfo.title || targetInfo.name,
    targetImage: targetInfo.image || targetInfo.coverImage || targetInfo.avatar,
    tags: targetInfo.tags,
    notes: targetInfo.notes,
    isPublic: targetInfo.isPublic || 0
  }
}
