<template>
  <view class="templates-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">排班模板管理</text>
      <text class="page-desc">创建和管理常用的排班模板</text>
    </view>

    <!-- 模板列表 -->
    <view class="templates-section">
      <view class="section-header">
        <text class="section-title">我的模板</text>
        <button class="add-template-btn" @click="showCreateModal">+ 新建模板</button>
      </view>

      <view class="templates-list">
        <view class="template-item" v-for="template in templates" :key="template.id">
          <view class="template-content">
            <view class="template-header">
              <text class="template-name">{{ template.name }}</text>
              <view class="template-tags">
                <text class="tag" v-for="tag in template.tags" :key="tag">{{ tag }}</text>
              </view>
            </view>

            <view class="template-desc">
              <text>{{ template.description }}</text>
            </view>

            <view class="template-preview">
              <view class="time-slots">
                <view class="time-slot" v-for="slot in template.timeSlots" :key="slot.id">
                  <text class="time-range">{{ slot.startTime }} - {{ slot.endTime }}</text>
                  <view class="consult-types">
                    <text class="type-tag" v-for="type in slot.consultTypes" :key="type">
                      {{ getConsultTypeLabel(type) }}
                    </text>
                  </view>
                </view>
              </view>

              <view class="break-times" v-if="template.breakTimes?.length">
                <text class="break-label">休息时间：</text>
                <text class="break-time" v-for="breakTime in template.breakTimes" :key="breakTime.id">
                  {{ breakTime.startTime }}-{{ breakTime.endTime }}
                </text>
              </view>
            </view>
          </view>

          <view class="template-actions">
            <button class="action-btn apply-btn" @click="applyTemplate(template)">应用</button>
            <button class="action-btn edit-btn" @click="editTemplate(template)">编辑</button>
            <button class="action-btn delete-btn" @click="deleteTemplate(template)">删除</button>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="templates.length === 0">
          <image src="/static/icon/empty-template.png" mode="aspectFit"></image>
          <text class="empty-text">暂无排班模板</text>
          <text class="empty-desc">创建模板可以快速应用到多个日期</text>
        </view>
      </view>
    </view>

    <!-- 预设模板 -->
    <view class="preset-section">
      <view class="section-header">
        <text class="section-title">预设模板</text>
      </view>

      <view class="preset-list">
        <view class="preset-item" v-for="preset in presetTemplates" :key="preset.id" @click="usePresetTemplate(preset)">
          <view class="preset-icon">
            <image :src="preset.icon" mode="aspectFit"></image>
          </view>
          <view class="preset-content">
            <text class="preset-name">{{ preset.name }}</text>
            <text class="preset-desc">{{ preset.description }}</text>
          </view>
          <view class="preset-arrow">
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 创建/编辑模板弹窗 -->
    <uni-popup ref="templateModal" type="bottom">
      <view class="template-modal">
        <view class="modal-header">
          <text class="modal-title">{{ isEditing ? '编辑模板' : '新建模板' }}</text>
          <text class="modal-close" @click="closeTemplateModal">×</text>
        </view>

        <view class="modal-content">
          <!-- 基本信息 -->
          <view class="form-section">
            <view class="form-item">
              <text class="form-label">模板名称</text>
              <input class="form-input" v-model="templateForm.name" placeholder="请输入模板名称" maxlength="20" />
            </view>

            <view class="form-item">
              <text class="form-label">模板描述</text>
              <textarea class="form-textarea" v-model="templateForm.description" placeholder="请输入模板描述"
                maxlength="100" />
            </view>

            <view class="form-item">
              <text class="form-label">标签</text>
              <view class="tag-input">
                <view class="selected-tags">
                  <view class="selected-tag" v-for="(tag, index) in templateForm.tags" :key="index">
                    {{ tag }}
                    <text class="remove-tag" @click="removeTag(index)">×</text>
                  </view>
                </view>
                <input class="tag-input-field" v-model="newTag" placeholder="输入标签后按回车" @confirm="addTag" />
              </view>
            </view>
          </view>

          <!-- 时间段设置 -->
          <view class="form-section">
            <view class="section-title">工作时间段</view>
            <view class="time-slots-form">
              <view class="time-slot-form" v-for="(slot, index) in templateForm.timeSlots" :key="index">
                <view class="time-range-form">
                  <picker mode="time" :value="slot.startTime"
                    @change="(e) => updateFormSlotTime(index, 'startTime', e.detail.value)">
                    <view class="time-picker">{{ slot.startTime }}</view>
                  </picker>
                  <text class="time-separator">-</text>
                  <picker mode="time" :value="slot.endTime"
                    @change="(e) => updateFormSlotTime(index, 'endTime', e.detail.value)">
                    <view class="time-picker">{{ slot.endTime }}</view>
                  </picker>
                </view>

                <view class="consult-types-form">
                  <view class="type-checkbox" v-for="type in consultTypes" :key="type.value">
                    <checkbox :value="type.value" :checked="slot.consultTypes.includes(type.value)"
                      @change="(e) => toggleFormConsultType(index, type.value, e.detail.value)" />
                    <text class="checkbox-label">{{ type.label }}</text>
                  </view>
                </view>

                <button class="remove-slot-btn" @click="removeFormTimeSlot(index)"
                  v-if="templateForm.timeSlots.length > 1">
                  删除
                </button>
              </view>

              <button class="add-slot-btn" @click="addFormTimeSlot">+ 添加时间段</button>
            </view>
          </view>

          <!-- 休息时间设置 -->
          <view class="form-section">
            <view class="section-title">休息时间</view>
            <view class="break-times-form">
              <view class="break-time-form" v-for="(breakTime, index) in templateForm.breakTimes" :key="index">
                <view class="break-time-range">
                  <picker mode="time" :value="breakTime.startTime"
                    @change="(e) => updateFormBreakTime(index, 'startTime', e.detail.value)">
                    <view class="time-picker">{{ breakTime.startTime }}</view>
                  </picker>
                  <text class="time-separator">-</text>
                  <picker mode="time" :value="breakTime.endTime"
                    @change="(e) => updateFormBreakTime(index, 'endTime', e.detail.value)">
                    <view class="time-picker">{{ breakTime.endTime }}</view>
                  </picker>
                </view>

                <input class="break-label-input" v-model="breakTime.label" placeholder="休息时间说明" />

                <button class="remove-break-btn" @click="removeFormBreakTime(index)">删除</button>
              </view>

              <button class="add-break-btn" @click="addFormBreakTime">+ 添加休息时间</button>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn-cancel" @click="closeTemplateModal">取消</button>
          <button class="btn-save" @click="saveTemplate">保存模板</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const templates = ref([])
const templateModal = ref(null)
const isEditing = ref(false)
const editingId = ref(null)
const newTag = ref('')

// 咨询类型选项
const consultTypes = ref([
  { label: '线上咨询', value: 'online' },
  { label: '线下咨询', value: 'offline' },
  { label: '电话咨询', value: 'phone' }
])

// 预设模板
const presetTemplates = ref([
  {
    id: 'preset_1',
    name: '标准工作日',
    description: '9:00-17:00，中午休息1小时',
    icon: '/static/icon/template-workday.png'
  },
  {
    id: 'preset_2',
    name: '弹性工作',
    description: '上午9:00-12:00，下午14:00-18:00',
    icon: '/static/icon/template-flexible.png'
  },
  {
    id: 'preset_3',
    name: '周末排班',
    description: '10:00-16:00，适合周末咨询',
    icon: '/static/icon/template-weekend.png'
  }
])

// 模板表单
const templateForm = ref({
  name: '',
  description: '',
  tags: [],
  timeSlots: [{
    startTime: '09:00',
    endTime: '17:00',
    consultTypes: ['online', 'offline']
  }],
  breakTimes: []
})

// 生命周期
onMounted(() => {
  loadTemplates()
})

// 方法
const loadTemplates = async () => {
  try {
    // 这里调用API加载模板数据
    // const res = await getScheduleTemplates()
    // templates.value = res.data || []

    // 临时模拟数据
    templates.value = [
      {
        id: 1,
        name: '标准工作日',
        description: '朝九晚五标准工作时间',
        tags: ['工作日', '标准'],
        timeSlots: [
          {
            id: 1,
            startTime: '09:00',
            endTime: '17:00',
            consultTypes: ['online', 'offline']
          }
        ],
        breakTimes: [
          {
            id: 1,
            startTime: '12:00',
            endTime: '13:00',
            label: '午休时间'
          }
        ]
      }
    ]
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

const getConsultTypeLabel = (type) => {
  const typeMap = {
    'online': '线上',
    'offline': '线下',
    'phone': '电话'
  }
  return typeMap[type] || type
}

// 模板操作
const showCreateModal = () => {
  isEditing.value = false
  editingId.value = null
  resetTemplateForm()
  templateModal.value?.open()
}

const editTemplate = (template) => {
  isEditing.value = true
  editingId.value = template.id
  templateForm.value = JSON.parse(JSON.stringify(template))
  templateModal.value?.open()
}

const deleteTemplate = (template) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除模板"${template.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        // 调用API删除模板
        const index = templates.value.findIndex(t => t.id === template.id)
        if (index > -1) {
          templates.value.splice(index, 1)
        }
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

const applyTemplate = (template) => {
  uni.showModal({
    title: '应用模板',
    content: `确定要应用模板"${template.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        // 这里可以跳转到灵活排班页面并应用模板
        uni.navigateTo({
          url: `/pages/schedule/flexible/index?templateId=${template.id}`
        })
      }
    }
  })
}

const usePresetTemplate = (preset) => {
  // 使用预设模板创建新模板
  uni.showToast({
    title: '预设模板功能开发中',
    icon: 'none'
  })
}

// 表单操作
const resetTemplateForm = () => {
  templateForm.value = {
    name: '',
    description: '',
    tags: [],
    timeSlots: [{
      startTime: '09:00',
      endTime: '17:00',
      consultTypes: ['online', 'offline']
    }],
    breakTimes: []
  }
}

const closeTemplateModal = () => {
  templateModal.value?.close()
}

const saveTemplate = async () => {
  // 验证表单
  if (!templateForm.value.name.trim()) {
    uni.showToast({
      title: '请输入模板名称',
      icon: 'none'
    })
    return
  }

  try {
    if (isEditing.value) {
      // 更新模板
      const index = templates.value.findIndex(t => t.id === editingId.value)
      if (index > -1) {
        templates.value[index] = { ...templateForm.value, id: editingId.value }
      }
    } else {
      // 创建新模板
      const newTemplate = {
        ...templateForm.value,
        id: Date.now()
      }
      templates.value.push(newTemplate)
    }

    uni.showToast({
      title: isEditing.value ? '更新成功' : '创建成功',
      icon: 'success'
    })

    closeTemplateModal()
  } catch (error) {
    console.error('保存模板失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 标签操作
const addTag = () => {
  if (newTag.value.trim() && !templateForm.value.tags.includes(newTag.value.trim())) {
    templateForm.value.tags.push(newTag.value.trim())
    newTag.value = ''
  }
}

const removeTag = (index) => {
  templateForm.value.tags.splice(index, 1)
}

// 时间段表单操作
const addFormTimeSlot = () => {
  templateForm.value.timeSlots.push({
    startTime: '09:00',
    endTime: '17:00',
    consultTypes: ['online']
  })
}

const removeFormTimeSlot = (index) => {
  if (templateForm.value.timeSlots.length > 1) {
    templateForm.value.timeSlots.splice(index, 1)
  }
}

const updateFormSlotTime = (index, field, value) => {
  templateForm.value.timeSlots[index][field] = value
}

const toggleFormConsultType = (slotIndex, typeValue, checked) => {
  const types = templateForm.value.timeSlots[slotIndex].consultTypes
  if (checked) {
    if (!types.includes(typeValue)) {
      types.push(typeValue)
    }
  } else {
    const index = types.indexOf(typeValue)
    if (index > -1) {
      types.splice(index, 1)
    }
  }
}

// 休息时间表单操作
const addFormBreakTime = () => {
  templateForm.value.breakTimes.push({
    startTime: '12:00',
    endTime: '13:00',
    label: '休息时间'
  })
}

const removeFormBreakTime = (index) => {
  templateForm.value.breakTimes.splice(index, 1)
}

const updateFormBreakTime = (index, field, value) => {
  templateForm.value.breakTimes[index][field] = value
}
</script>

<style scoped lang="scss">
.templates-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

.page-header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  text-align: center;
  margin-bottom: 20rpx;

  .page-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .page-desc {
    font-size: 24rpx;
    color: #666;
  }
}

.templates-section,
.preset-section {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .add-template-btn {
      padding: 12rpx 20rpx;
      background-color: #1890ff;
      color: #fff;
      border: none;
      border-radius: 20rpx;
      font-size: 24rpx;
    }
  }
}

.templates-list {
  .template-item {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .template-content {
      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15rpx;

        .template-name {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }

        .template-tags {
          display: flex;
          gap: 8rpx;

          .tag {
            padding: 4rpx 12rpx;
            background-color: #f0f0f0;
            color: #666;
            font-size: 20rpx;
            border-radius: 12rpx;
          }
        }
      }

      .template-desc {
        margin-bottom: 20rpx;

        text {
          font-size: 24rpx;
          color: #666;
        }
      }

      .template-preview {
        .time-slots {
          margin-bottom: 15rpx;

          .time-slot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15rpx;
            background-color: #f8f9fa;
            border-radius: 8rpx;
            margin-bottom: 10rpx;

            .time-range {
              font-size: 26rpx;
              color: #333;
              font-weight: 500;
            }

            .consult-types {
              display: flex;
              gap: 8rpx;

              .type-tag {
                padding: 4rpx 8rpx;
                background-color: #1890ff;
                color: #fff;
                font-size: 20rpx;
                border-radius: 10rpx;
              }
            }
          }
        }

        .break-times {
          display: flex;
          align-items: center;
          gap: 10rpx;

          .break-label {
            font-size: 22rpx;
            color: #666;
          }

          .break-time {
            font-size: 22rpx;
            color: #52c41a;
          }
        }
      }
    }

    .template-actions {
      display: flex;
      gap: 15rpx;
      margin-top: 20rpx;

      .action-btn {
        flex: 1;
        padding: 16rpx 0;
        border: none;
        border-radius: 8rpx;
        font-size: 24rpx;
      }

      .apply-btn {
        background-color: #1890ff;
        color: #fff;
      }

      .edit-btn {
        background-color: #f0f0f0;
        color: #666;
      }

      .delete-btn {
        background-color: #fff2f0;
        color: #ff4d4f;
      }
    }
  }

  .empty-state {
    padding: 80rpx 30rpx;
    text-align: center;

    image {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 30rpx;
    }

    .empty-text {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .empty-desc {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.preset-list {
  .preset-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .preset-icon {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .preset-content {
      flex: 1;

      .preset-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
      }

      .preset-desc {
        font-size: 24rpx;
        color: #666;
      }
    }

    .preset-arrow {
      width: 24rpx;
      height: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.template-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .modal-close {
      font-size: 40rpx;
      color: #999;
    }
  }

  .modal-content {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;

    .form-section {
      margin-bottom: 40rpx;

      .section-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 20rpx;
      }

      .form-item {
        margin-bottom: 30rpx;

        .form-label {
          display: block;
          font-size: 26rpx;
          color: #333;
          margin-bottom: 15rpx;
        }

        .form-input,
        .form-textarea {
          width: 100%;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          font-size: 26rpx;
          box-sizing: border-box;
        }

        .form-input {
          height: 60rpx;
        }

        .form-textarea {
          height: 120rpx;
          resize: none;
        }

        .tag-input {
          .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10rpx;
            margin-bottom: 15rpx;

            .selected-tag {
              display: flex;
              align-items: center;
              padding: 8rpx 12rpx;
              background-color: #1890ff;
              color: #fff;
              border-radius: 16rpx;
              font-size: 22rpx;

              .remove-tag {
                margin-left: 8rpx;
                font-size: 20rpx;
                cursor: pointer;
              }
            }
          }

          .tag-input-field {
            width: 100%;
            padding: 15rpx;
            border: 1rpx solid #d9d9d9;
            border-radius: 8rpx;
            font-size: 24rpx;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .btn-cancel,
    .btn-save {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }

    .btn-cancel {
      background-color: #f5f5f5;
      color: #666;
    }

    .btn-save {
      background-color: #1890ff;
      color: #fff;
    }
  }
}
</style>
