<script>
import { useChatStore } from "@/stores/chat";
import { useUserStore } from "@/stores/user";
import {
	getToken
} from "@/utils/auth.js";
export default {
	onLaunch: function () {
		const userStore = useUserStore();
		const token = userStore.token || getToken();
		console.log("App Launch - Token:", !!token, "UserId:", userStore.userId);

		// 只有在有token且有userId的情况下才初始化WebSocket
		if (token && userStore.userId) {
			const chatStore = useChatStore();
			console.log("App Launch - 初始化WebSocket");
			chatStore.initWebSocket(userStore.userId);
		}
		console.log("App Launch");
	},
	onShow: function () {
		console.log("App Show");
		// 应用回到前台时，检查WebSocket连接状态
		const userStore = useUserStore();
		const token = userStore.token || getToken();
		console.log("App Show - Token:", !!token, "UserId:", userStore.userId, "WS Connected:", useChatStore().wsConnected);

		if (token && userStore.userId) {
			const chatStore = useChatStore();
			// 如果连接已断开，重新连接
			if (!chatStore.wsConnected) {
				console.log("App Show - 重新连接WebSocket");
				chatStore.initWebSocket(userStore.userId);
			}
		}
	},
	onHide: function () {
		console.log("App Hide");
		// 应用进入后台时，WebSocket连接保持不关闭，依靠心跳机制维持
	},
	onUnload: function () {
		console.log("App Unload");
		// 应用退出时，关闭WebSocket连接
		const chatStore = useChatStore();
		chatStore.closeWebSocket();
	}
};
</script>

<style></style>
