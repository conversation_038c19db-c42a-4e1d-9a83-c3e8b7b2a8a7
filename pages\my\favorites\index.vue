<template>
  <view class="favorites-page">
    <!-- 顶部统计 -->
    <view class="stats-header">
      <view class="stats-item">
        <text class="stats-number">{{ stats.totalCount || 0 }}</text>
        <text class="stats-label">总收藏</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ stats.consultantCount || 0 }}</text>
        <text class="stats-label">咨询师</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ stats.courseCount || 0 }}</text>
        <text class="stats-label">课程</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ stats.meditationCount || 0 }}</text>
        <text class="stats-label">冥想</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ stats.assessmentCount || 0 }}</text>
        <text class="stats-label">测评</text>
      </view>
    </view>
    
    <!-- 分组列表 -->
    <view class="groups-section">
      <view class="section-header">
        <text class="section-title">收藏分组</text>
        <button class="add-group-btn" @click="showCreateGroupModal">
          <uni-icons type="plus" size="16" color="#667eea"></uni-icons>
          <text>新建分组</text>
        </button>
      </view>
      
      <view class="groups-list">
        <view 
          v-for="group in groupList" 
          :key="group.groupId"
          class="group-item"
          @click="goToGroupDetail(group)"
        >
          <view class="group-icon" :style="{ backgroundColor: group.groupColor }">
            <text class="icon-text">{{ group.groupIcon || '📁' }}</text>
          </view>
          <view class="group-info">
            <text class="group-name">{{ group.groupName }}</text>
            <text class="group-desc">{{ group.description || '暂无描述' }}</text>
          </view>
          <view class="group-count">
            <text class="count-number">{{ group.favoriteCount }}</text>
            <text class="count-label">项</text>
          </view>
          <view class="group-arrow">
            <uni-icons type="right" size="14" color="#c8c9cc"></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 最近收藏 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">最近收藏</text>
        <text class="more-btn" @click="goToAllFavorites">查看全部</text>
      </view>
      
      <view class="recent-list">
        <view 
          v-for="item in recentFavorites" 
          :key="item.favoriteId"
          class="favorite-item"
          @click="goToFavoriteDetail(item)"
        >
          <image 
            class="favorite-image" 
            :src="item.actualImage || item.targetImage || defaultImage" 
            mode="aspectFill"
          />
          <view class="favorite-info">
            <text class="favorite-title">{{ item.actualTitle || item.targetTitle }}</text>
            <text class="favorite-type">{{ getFavoriteTypeName(item.targetType) }}</text>
            <text class="favorite-time">{{ formatTime(item.favoriteTime) }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 创建分组弹框 -->
    <uni-popup ref="createGroupPopup" type="center">
      <view class="create-group-modal">
        <view class="modal-header">
          <text class="modal-title">新建收藏分组</text>
        </view>
        <view class="modal-content">
          <uni-forms ref="groupFormRef" :model="groupForm" :rules="groupRules">
            <uni-forms-item label="分组名称" name="groupName" required>
              <uni-easyinput v-model="groupForm.groupName" placeholder="请输入分组名称" />
            </uni-forms-item>
            <uni-forms-item label="分组图标" name="groupIcon">
              <view class="icon-selector">
                <view 
                  v-for="icon in iconOptions" 
                  :key="icon"
                  class="icon-option"
                  :class="{ active: groupForm.groupIcon === icon }"
                  @click="groupForm.groupIcon = icon"
                >
                  <text>{{ icon }}</text>
                </view>
              </view>
            </uni-forms-item>
            <uni-forms-item label="分组颜色" name="groupColor">
              <view class="color-selector">
                <view 
                  v-for="color in colorOptions" 
                  :key="color"
                  class="color-option"
                  :class="{ active: groupForm.groupColor === color }"
                  :style="{ backgroundColor: color }"
                  @click="groupForm.groupColor = color"
                />
              </view>
            </uni-forms-item>
            <uni-forms-item label="分组描述" name="description">
              <uni-easyinput 
                v-model="groupForm.description" 
                type="textarea" 
                placeholder="请输入分组描述（可选）"
                :maxlength="100"
              />
            </uni-forms-item>
          </uni-forms>
        </view>
        <view class="modal-actions">
          <button class="cancel-btn" @click="hideCreateGroupModal">取消</button>
          <button class="confirm-btn" @click="handleCreateGroup" :loading="creating">
            {{ creating ? '创建中...' : '确定' }}
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { 
  getFavoriteList, 
  getFavoriteStats, 
  getFavoriteGroups,
  createFavoriteGroup,
  getFavoriteTypeName,
  FAVORITE_TYPES 
} from '@/api/favorite.js'

// 响应式数据
const stats = ref({})
const groupList = ref([])
const recentFavorites = ref([])
const createGroupPopup = ref(null)
const groupFormRef = ref(null)
const creating = ref(false)

// 表单数据
const groupForm = reactive({
  groupName: '',
  groupIcon: '📁',
  groupColor: '#007AFF',
  description: ''
})

// 表单验证规则
const groupRules = {
  groupName: {
    rules: [
      { required: true, errorMessage: '请输入分组名称' },
      { minLength: 1, maxLength: 20, errorMessage: '分组名称长度应为1-20个字符' }
    ]
  }
}

// 图标选项
const iconOptions = ['📁', '⭐', '❤️', '📚', '🎯', '💡', '🔖', '🎨', '🌟', '💎']

// 颜色选项
const colorOptions = [
  '#007AFF', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
  '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE'
]

// 默认图片
const defaultImage = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-cover.png'

// 加载统计数据
const loadStats = async () => {
  try {
    const res = await getFavoriteStats()
    if (res.code === 200) {
      stats.value = res.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载分组列表
const loadGroups = async () => {
  try {
    const res = await getFavoriteGroups()
    if (res.code === 200) {
      groupList.value = res.data || []
    }
  } catch (error) {
    console.error('加载分组列表失败:', error)
  }
}

// 加载最近收藏
const loadRecentFavorites = async () => {
  try {
    const res = await getFavoriteList()
    if (res.code === 200) {
      // 取最近的5个收藏
      recentFavorites.value = (res.data || []).slice(0, 5)
    }
  } catch (error) {
    console.error('加载最近收藏失败:', error)
  }
}

// 显示创建分组弹框
const showCreateGroupModal = () => {
  // 重置表单
  Object.assign(groupForm, {
    groupName: '',
    groupIcon: '📁',
    groupColor: '#007AFF',
    description: ''
  })
  createGroupPopup.value?.open()
}

// 隐藏创建分组弹框
const hideCreateGroupModal = () => {
  createGroupPopup.value?.close()
}

// 创建分组
const handleCreateGroup = async () => {
  try {
    await groupFormRef.value.validate()
    
    creating.value = true
    
    const res = await createFavoriteGroup(groupForm)
    if (res.code === 200) {
      uni.showToast({
        title: '创建成功',
        icon: 'success'
      })
      
      hideCreateGroupModal()
      loadGroups() // 重新加载分组列表
    } else {
      uni.showToast({
        title: res.msg || '创建失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('创建分组失败:', error)
    if (!error.errorFields) {
      uni.showToast({
        title: '创建失败',
        icon: 'none'
      })
    }
  } finally {
    creating.value = false
  }
}

// 跳转到分组详情
const goToGroupDetail = (group) => {
  uni.navigateTo({
    url: `/pages/my/favorites/group-detail/index?groupId=${group.groupId}&groupName=${encodeURIComponent(group.groupName)}`
  })
}

// 跳转到全部收藏
const goToAllFavorites = () => {
  uni.navigateTo({
    url: '/pages/my/favorites/all/index'
  })
}

// 跳转到收藏详情
const goToFavoriteDetail = (item) => {
  // 根据收藏类型跳转到对应的详情页面
  const pageMap = {
    [FAVORITE_TYPES.CONSULTANT]: '/pages/consultant/detail/index',
    [FAVORITE_TYPES.COURSE]: '/pages/course/detail/index',
    [FAVORITE_TYPES.MEDITATION]: '/pages/meditation/detail/index',
    [FAVORITE_TYPES.ASSESSMENT]: '/pages/evaluation/detail/index'
  }
  
  const url = pageMap[item.targetType]
  if (url) {
    uni.navigateTo({
      url: `${url}?id=${item.targetId}`
    })
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return `${Math.floor(diff / 86400000)}天前`
  }
}

// 生命周期
onMounted(() => {
  loadStats()
  loadGroups()
  loadRecentFavorites()
})
</script>

<style lang="scss" scoped>
.favorites-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.stats-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  display: flex;
  justify-content: space-around;
  
  .stats-item {
    text-align: center;
    
    .stats-number {
      display: block;
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.groups-section,
.recent-section {
  margin: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
  
  .add-group-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1rpx solid #667eea;
    border-radius: 20rpx;
    font-size: 24rpx;
  }
  
  .more-btn {
    color: #667eea;
    font-size: 26rpx;
  }
}

.groups-list {
  .group-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .group-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      
      .icon-text {
        font-size: 32rpx;
      }
    }
    
    .group-info {
      flex: 1;
      
      .group-name {
        display: block;
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .group-desc {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .group-count {
      text-align: center;
      margin-right: 16rpx;
      
      .count-number {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #667eea;
      }
      
      .count-label {
        font-size: 20rpx;
        color: #999;
      }
    }
  }
}

.recent-list {
  .favorite-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .favorite-image {
      width: 100rpx;
      height: 100rpx;
      border-radius: 12rpx;
      margin-right: 24rpx;
    }
    
    .favorite-info {
      flex: 1;
      
      .favorite-title {
        display: block;
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .favorite-type {
        display: inline-block;
        font-size: 22rpx;
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        margin-right: 16rpx;
      }
      
      .favorite-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.create-group-modal {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  
  .modal-header {
    padding: 32rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }
  
  .modal-content {
    padding: 32rpx;
  }
  
  .icon-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    
    .icon-option {
      width: 80rpx;
      height: 80rpx;
      border: 2rpx solid #f0f0f0;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      
      &.active {
        border-color: #667eea;
        background-color: rgba(102, 126, 234, 0.1);
      }
    }
  }
  
  .color-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    
    .color-option {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      border: 3rpx solid transparent;
      
      &.active {
        border-color: #333;
      }
    }
  }
  
  .modal-actions {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 88rpx;
      border: none;
      font-size: 30rpx;
    }
    
    .cancel-btn {
      background-color: #f8f8f8;
      color: #666;
    }
    
    .confirm-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
    }
  }
}
</style>
