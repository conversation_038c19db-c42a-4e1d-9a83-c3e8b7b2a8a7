<template>
  <view class="section">
    <text class="section-title">{{ title }}</text>
    <scroll-view scroll-x="true" class="horizontal-scroll-list">
      <view class="scroll-item" v-for="(item, index) in items" :key="index" @click="handleItemClick(item)">
        <view class="item-left">
          <image :src="getItemImage(item)" mode="aspectFill" class="item-avatar" @error="handleImageError"></image>
        </view>
        <view class="item-right">
          <text class="item-title">{{ getItemTitle(item) }}</text>
          <text class="item-description">{{ getItemDescription(item) }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  items: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'assessment', // 'assessment', 'consultant', 'course', 'meditation'
    validator: (value) => ['assessment', 'consultant', 'course', 'meditation'].includes(value)
  }
})

// Emits
const emit = defineEmits(['item-click'])

// 获取默认图片
const getDefaultImage = () => {
  const defaultImages = {
    consultant: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
    meditation: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
    course: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    assessment: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
  }
  return defaultImages[props.type] || '/static/evaluation/default.png'
}

// 获取项目图片
const getItemImage = (item) => {
  // 根据类型获取相应的图片字段
  const imageFields = {
    consultant: ['avatar', 'headImage', 'imageUrl'],
    assessment: ['imageUrl', 'coverImage', 'cover'],
    course: ['coverImage', 'imageUrl', 'cover'],
    meditation: ['coverImage', 'imageUrl', 'cover']
  }

  const fields = imageFields[props.type] || ['imageUrl', 'coverImage', 'avatar', 'cover']

  for (const field of fields) {
    if (item[field]) {
      return item[field]
    }
  }

  return getDefaultImage()
}

// 获取项目标题
const getItemTitle = (item) => {
  const titleFields = {
    consultant: ['name', 'realName', 'title'],
    assessment: ['name', 'title', 'scaleName'],
    course: ['title', 'name', 'courseName'],
    meditation: ['title', 'name']
  }

  const fields = titleFields[props.type] || ['name', 'title']

  for (const field of fields) {
    if (item[field]) {
      return item[field]
    }
  }

  return '未知'
}

// 获取项目描述
const getItemDescription = (item) => {
  const descriptionFields = {
    consultant: ['personalIntro', 'introduction', 'description', 'summary'],
    assessment: ['description', 'introduction', 'summary'],
    course: ['description', 'introduction', 'summary'],
    meditation: ['description', 'introduction', 'summary']
  }

  const defaultDescriptions = {
    consultant: '专业心理咨询师，为您提供专业的心理健康服务',
    assessment: '专业心理测评，帮助您更好地了解自己',
    course: '专业心理课程，提升您的心理健康水平',
    meditation: '专业冥想练习，帮助您放松身心'
  }

  const fields = descriptionFields[props.type] || ['description', 'introduction', 'summary']

  for (const field of fields) {
    if (item[field]) {
      return item[field]
    }
  }

  return defaultDescriptions[props.type] || '暂无描述'
}

// 处理图片加载错误
const handleImageError = (e) => {
  e.target.src = getDefaultImage()
}

// 处理项目点击
const handleItemClick = (item) => {
  emit('item-click', item)
}
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }

  .horizontal-scroll-list {
    width: 100%;
    white-space: nowrap;

    /* 确保scroll-view内部的文本可以正常换行 */

    .scroll-item {
      display: inline-flex;
      width: 580rpx;
      height: 176rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      padding: 32rpx;
      margin-right: 20rpx;
      align-items: center;
      box-sizing: border-box;
      vertical-align: top;
      white-space: normal;
      /* 重置white-space，允许内部文本换行 */

      .item-left {
        width: 114rpx;
        height: 114rpx;
        margin-right: 24rpx;
        flex-shrink: 0;

        .item-avatar {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }
      }

      .item-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 114rpx;
        min-width: 0; // 确保flex子元素可以收缩
        overflow: hidden;

        .item-title {
          font-size: 28rpx;
          font-weight: 400;
          color: #000000;
          margin-bottom: 8rpx;
          word-wrap: break-word;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .item-description {
          font-size: 24rpx;
          font-weight: 400;
          color: #8A8788;
          line-height: 36rpx;
          max-height: 72rpx;
          /* 2行的高度: 36rpx * 2 */
          width: 100%;
          word-wrap: break-word;
          word-break: break-all;
          white-space: normal;
          overflow: hidden;
          text-overflow: ellipsis;
          /* 多行省略号 - 兼容性更好的写法 */
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          /* 备用方案 */
          /* 如果-webkit-line-clamp不生效，使用传统方法 */
        }
      }
    }
  }
}
</style>
