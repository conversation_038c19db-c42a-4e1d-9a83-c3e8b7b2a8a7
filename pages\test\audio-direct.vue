<template>
	<view class="audio-test">
		<view class="header">
			<text class="title">直接音频API测试</text>
		</view>
		
		<view class="player">
			<view class="info">
				<text>播放状态: {{ isPlaying ? '播放中' : '已暂停' }}</text>
				<text>当前时间: {{ formatTime(currentTime) }}</text>
				<text>总时长: {{ formatTime(duration) }}</text>
				<text>当前倍速: {{ currentSpeed }}x</text>
			</view>
			
			<view class="controls">
				<button @click="initAudio" class="btn">初始化音频</button>
				<button @click="togglePlay" class="btn">{{ isPlaying ? '暂停' : '播放' }}</button>
			</view>
			
			<view class="seek-controls">
				<button @click="seekBackward" class="btn">快退5秒</button>
				<button @click="seekForward" class="btn">快进5秒</button>
			</view>
			
			<view class="speed-controls">
				<text class="label">倍速控制:</text>
				<button 
					v-for="speed in speeds" 
					:key="speed"
					@click="setSpeed(speed)"
					:class="['speed-btn', { active: currentSpeed === speed }]"
				>
					{{ speed }}x
				</button>
			</view>
			
			<view class="debug-info">
				<text class="debug-title">调试日志:</text>
				<text v-for="(log, index) in debugLogs" :key="index" class="debug-log">
					{{ log }}
				</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'

// 测试音频URL
const testAudioUrl = 'https://music.163.com/song/media/outer/url?id=25906124.mp3'

// 响应式数据
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const currentSpeed = ref(1.0)
const debugLogs = ref([])
const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]

// 音频管理器
let audioManager = null

// 格式化时间
const formatTime = (seconds) => {
	if (isNaN(seconds) || seconds < 0) return '00:00'
	const mins = Math.floor(seconds / 60)
	const secs = Math.floor(seconds % 60)
	return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 添加调试日志
const addLog = (message) => {
	const timestamp = new Date().toLocaleTimeString()
	debugLogs.value.unshift(`[${timestamp}] ${message}`)
	if (debugLogs.value.length > 15) {
		debugLogs.value.pop()
	}
	console.log(message)
}

// 初始化音频
const initAudio = () => {
	try {
		if (audioManager) {
			audioManager.stop()
		}
		
		addLog('初始化背景音频管理器')
		audioManager = uni.getBackgroundAudioManager()
		
		// 设置音频信息
		audioManager.title = '测试音频'
		audioManager.singer = '测试'
		audioManager.src = testAudioUrl
		
		// 绑定事件
		audioManager.onPlay(() => {
			isPlaying.value = true
			addLog('音频开始播放')
		})
		
		audioManager.onPause(() => {
			isPlaying.value = false
			addLog('音频暂停')
		})
		
		audioManager.onTimeUpdate(() => {
			currentTime.value = audioManager.currentTime || 0
			duration.value = audioManager.duration || 0
		})
		
		audioManager.onEnded(() => {
			isPlaying.value = false
			addLog('音频播放结束')
		})
		
		audioManager.onError((error) => {
			addLog('音频错误: ' + JSON.stringify(error))
		})
		
		// 检查倍速支持
		if ('playbackRate' in audioManager) {
			addLog('支持倍速功能')
		} else {
			addLog('不支持倍速功能')
		}
		
		addLog('音频初始化完成')
		
	} catch (error) {
		addLog('初始化失败: ' + error.message)
	}
}

// 切换播放
const togglePlay = () => {
	try {
		if (!audioManager) {
			addLog('请先初始化音频')
			return
		}
		
		if (isPlaying.value) {
			audioManager.pause()
		} else {
			audioManager.play()
		}
	} catch (error) {
		addLog('播放控制失败: ' + error.message)
	}
}

// 快退5秒
const seekBackward = () => {
	try {
		if (!audioManager) {
			addLog('请先初始化音频')
			return
		}
		
		const newTime = Math.max(0, currentTime.value - 5)
		addLog(`快退5秒: ${currentTime.value} -> ${newTime}`)
		
		const currentSrc = audioManager.src
		audioManager.src = ''
		audioManager.startTime = newTime
		audioManager.src = currentSrc
		currentTime.value = newTime
		
	} catch (error) {
		addLog('快退失败: ' + error.message)
	}
}

// 快进5秒
const seekForward = () => {
	try {
		if (!audioManager) {
			addLog('请先初始化音频')
			return
		}
		
		const newTime = Math.min(duration.value, currentTime.value + 5)
		addLog(`快进5秒: ${currentTime.value} -> ${newTime}`)
		
		const currentSrc = audioManager.src
		audioManager.src = ''
		audioManager.startTime = newTime
		audioManager.src = currentSrc
		currentTime.value = newTime
		
	} catch (error) {
		addLog('快进失败: ' + error.message)
	}
}

// 设置倍速
const setSpeed = (speed) => {
	try {
		if (!audioManager) {
			addLog('请先初始化音频')
			return
		}
		
		currentSpeed.value = speed
		addLog(`设置倍速: ${speed}x`)
		
		if ('playbackRate' in audioManager) {
			audioManager.playbackRate = speed
			addLog(`倍速设置为: ${speed}x`)
			
			// 验证设置结果
			setTimeout(() => {
				const actualRate = audioManager.playbackRate
				addLog(`实际倍速: ${actualRate}x`)
				if (Math.abs(actualRate - speed) < 0.01) {
					addLog('✅ 倍速设置成功!')
				} else {
					addLog('❌ 倍速设置失败!')
				}
			}, 100)
		} else {
			addLog('❌ 不支持倍速功能')
		}
		
	} catch (error) {
		addLog('设置倍速失败: ' + error.message)
	}
}

// 页面加载时自动初始化
initAudio()

// 清理
onUnmounted(() => {
	if (audioManager) {
		try {
			audioManager.stop()
		} catch (e) {
			// 忽略清理错误
		}
	}
})
</script>

<style lang="scss" scoped>
.audio-test {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

.player {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	
	.info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
		margin-bottom: 40rpx;
		
		text {
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.controls, .seek-controls {
		display: flex;
		gap: 20rpx;
		margin-bottom: 40rpx;
		
		.btn {
			flex: 1;
			padding: 24rpx;
			background-color: #007aff;
			color: #fff;
			border: none;
			border-radius: 12rpx;
			font-size: 28rpx;
		}
	}
	
	.speed-controls {
		margin-bottom: 40rpx;
		
		.label {
			display: block;
			font-size: 28rpx;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.speed-btn {
			display: inline-block;
			margin: 0 16rpx 16rpx 0;
			padding: 16rpx 24rpx;
			background-color: #f8f8f8;
			color: #666;
			border: none;
			border-radius: 8rpx;
			font-size: 26rpx;
			
			&.active {
				background-color: #ff6b35;
				color: #fff;
			}
		}
	}
	
	.debug-info {
		.debug-title {
			display: block;
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.debug-log {
			display: block;
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
			margin-bottom: 8rpx;
			padding: 8rpx;
			background-color: #f8f8f8;
			border-radius: 4rpx;
		}
	}
}
</style>
