<template>
  <view class="payment-success">
    <view class="success-icon">
      <uni-icons type="checkbox-filled" size="80" color="#4CAF50"></uni-icons>
    </view>
    
    <view class="success-title">支付成功</view>
    
    <view class="order-info">
      <view class="order-item">
        <text class="label">订单号</text>
        <text class="value">{{ orderNo }}</text>
      </view>
      <view class="order-item">
        <text class="label">支付金额</text>
        <text class="value price">¥{{ amount }}</text>
      </view>
      <view class="order-item">
        <text class="label">支付方式</text>
        <text class="value">{{ getPaymentMethodText(paymentMethod) }}</text>
      </view>
      <view class="order-item">
        <text class="label">支付时间</text>
        <text class="value">{{ paymentTime }}</text>
      </view>
    </view>
    
    <view class="product-info" v-if="productInfo">
      <image class="product-image" :src="productInfo.coverImage || defaultCover" mode="aspectFill"></image>
      <view class="product-details">
        <text class="product-title">{{ productInfo.title }}</text>
        <text class="product-desc">{{ productInfo.description }}</text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button 
        v-if="orderType === 'consultation'" 
        class="action-btn primary-btn" 
        @click="goToBasicInfo"
      >
        填写基本信息
      </button>
      <button 
        v-else-if="orderType === 'course'" 
        class="action-btn primary-btn" 
        @click="goToCourse"
      >
        开始学习
      </button>
      <button 
        v-else-if="orderType === 'meditation'" 
        class="action-btn primary-btn" 
        @click="goToMeditation"
      >
        开始冥想
      </button>
      <button 
        v-else-if="orderType === 'assessment'" 
        class="action-btn primary-btn" 
        @click="goToAssessment"
      >
        开始测评
      </button>
      
      <button class="action-btn secondary-btn" @click="goToOrderList">
        查看订单
      </button>
      
      <button class="action-btn plain-btn" @click="goToHome">
        返回首页
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 响应式数据
const orderNo = ref('')
const orderId = ref('')
const amount = ref('0.00')
const paymentMethod = ref('wechat')
const paymentTime = ref('')
const orderType = ref('')
const productInfo = ref(null)

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-cover.png'

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const methodMap = {
    'wechat': '微信支付',
    'alipay': '支付宝',
    'balance': '余额支付'
  }
  return methodMap[method] || method
}

// 格式化当前时间
const formatCurrentTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 跳转到基本信息页面
const goToBasicInfo = () => {
  uni.navigateTo({
    url: `/pages/consultation/basic-info/index?orderId=${orderId.value}`
  })
}

// 跳转到课程页面
const goToCourse = () => {
  if (productInfo.value && productInfo.value.id) {
    uni.navigateTo({
      url: `/pages/course/detail/index?id=${productInfo.value.id}`
    })
  } else {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }
}

// 跳转到冥想页面
const goToMeditation = () => {
  if (productInfo.value && productInfo.value.id) {
    uni.navigateTo({
      url: `/pages/meditation/player/index?id=${productInfo.value.id}`
    })
  } else {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }
}

// 跳转到测评页面
const goToAssessment = () => {
  if (productInfo.value && productInfo.value.id) {
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${productInfo.value.id}`
    })
  } else {
    uni.switchTab({
      url: '/pages/index/index'
    })
  }
}

// 跳转到订单列表
const goToOrderList = () => {
  if (orderType.value === 'consultation') {
    uni.navigateTo({
      url: '/pages/consultation/my-consultations/index'
    })
  } else {
    uni.switchTab({
      url: '/pages/my/index'
    })
  }
}

// 返回首页
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

// 生命周期
onLoad((options) => {
  // 解析参数
  orderNo.value = options.orderNo || ''
  orderId.value = options.orderId || ''
  amount.value = options.amount || '0.00'
  paymentMethod.value = options.method || 'wechat'
  orderType.value = options.type || ''
  paymentTime.value = formatCurrentTime()
  
  // 解析商品信息
  if (options.productInfo) {
    try {
      productInfo.value = JSON.parse(decodeURIComponent(options.productInfo))
    } catch (e) {
      console.error('解析商品信息失败:', e)
    }
  }
})
</script>

<style lang="scss" scoped>
.payment-success {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 60rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  margin-bottom: 32rpx;
  background-color: rgba(76, 175, 80, 0.1);
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 48rpx;
}

.order-info {
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .order-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      color: #666;
      font-size: 28rpx;
    }
    
    .value {
      color: #333;
      font-size: 28rpx;
      font-weight: 500;
      
      &.price {
        color: #ff6b35;
        font-weight: 600;
      }
    }
  }
}

.product-info {
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  
  .product-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
  }
  
  .product-details {
    flex: 1;
    
    .product-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
      display: block;
    }
    
    .product-desc {
      font-size: 26rpx;
      color: #666;
      display: block;
    }
  }
}

.action-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  
  .action-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.primary-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      border: none;
      box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
    }
    
    &.secondary-btn {
      background-color: #fff;
      color: #667eea;
      border: 2rpx solid #667eea;
    }
    
    &.plain-btn {
      background-color: transparent;
      color: #666;
      border: none;
    }
  }
}
</style>
