<template>
	<view class="classification-page">
		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-bar" @click="goToSearch">
				<uni-icons type="search" size="16" color="#999"></uni-icons>
				<text class="search-placeholder">搜索咨询师、课程、测评...</text>
			</view>
		</view>

		<!-- 一级分类标签 -->
		<view class="category-tabs">
			<view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: currentTab === index }]"
				@click="switchTab(index)">
				{{ tab.name }}
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 左侧二级分类导航 -->
			<scroll-view class="left-nav" scroll-y v-if="currentCategories.length > 0">
				<view v-for="(category, index) in currentCategories" :key="category.categoryId"
					:class="['nav-item', { active: currentCategoryIndex === index }]" @click="scrollToCategory(index)">
					<text class="nav-text">{{ category.categoryName }}</text>
				</view>
			</scroll-view>

			<!-- 右侧内容列表 -->
			<scroll-view class="right-content" scroll-y :scroll-into-view="scrollIntoView" @scroll="onRightScroll"
				:refresher-enabled="true" :refresher-triggered="refreshing" @refresherrefresh="onRefresh"
				:class="{ 'is-assessment': currentTab.value == 3 }" style="width: calc(100% - 200px)">

				<!-- 顶部标题和排序区域 -->
				<view class="content-header">
					<view class="header-left">
						<text class="category-name">{{ getCurrentCategoryName() }}</text>
						<text class="item-count">{{ getCurrentItemCount() }}</text>
					</view>
					<view class="header-right" v-if="currentTabType === 'consultant'">
						<view class="sort-item" @click="toggleSort('level')" :class="{ active: sortBy === 'level' }">
							<text>等级</text>
							<image :src="sortOrder === 'asc' ? '/static/icon/向上.png' : '/static/icon/向下.png'" class="sort-icon">
							</image>
						</view>
						<view class="sort-item" @click="toggleSort('price')" :class="{ active: sortBy === 'price' }">
							<text>价格</text>
							<image v-if="sortBy === 'price'"
								:src="sortOrder === 'asc' ? '/static/icon/向上.png' : '/static/icon/向下.png'" class="sort-icon"></image>
						</view>
					</view>
					<view class="header-right" v-else>
						<view class="sort-item" @click="toggleSort('price')" :class="{ active: sortBy === 'price' }">
							<text>价格</text>
							<image :src="sortOrder === 'asc' ? '/static/icon/向上.png' : '/static/icon/向下.png'" class="sort-icon">
							</image>
						</view>
					</view>
				</view>

				<!-- 二级分类内容 -->
				<view v-for="(category, categoryIndex) in currentCategories" :key="category.categoryId"
					:id="`category-${categoryIndex}`" class="category-section">
					<!-- 该分类下的内容列表 -->
					<view class="content-list">
						<view v-if="getSortedCategoryData(category.categoryId).length === 0" class="empty-state">
							<image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png"
								mode="aspectFit"></image>
							<text>暂无数据</text>
						</view>

						<view v-else class="list-container">
							<UniversalListItem v-for="item in getSortedCategoryData(category.categoryId)" :key="item.id" :item="item"
								:type="currentTabType" :dictData="localDict" :imageSize="128" @click="handleItemClick" />
						</view>
					</view>
				</view>

				<!-- 咨询师列表（如果当前是咨询师标签且没有二级分类且有数据） -->
				<view v-if="currentTabType === 'consultant' && currentCategories.length === 0 && counselorList.length > 0"
					:id="'category-counselor'" class="category-section">
					<!-- 筛选标签 -->
					<view class="filter-tags" v-if="commonSearch.length > 0">
						<view v-for="item in commonSearch" :key="item.value"
							:class="['filter-tag', { active: isFilterActive(item.value) }]" @click="handleCommonSearch(item)">
							{{ item.label }}
						</view>
					</view>

					<view class="content-list">
						<view class="list-container">
							<UniversalListItem v-for="item in getSortedCounselorList()" :key="item.id" :item="item" type="consultant"
								:dictData="localDict" :imageSize="128"
								@click="(clickedItem) => handleCounselorDetail(clickedItem, 'counselor')" />
						</view>
					</view>
				</view>

				<!-- 其他类型的全部列表（课程、测评、冥想） -->
				<view v-if="currentTabType !== 'consultant' && currentCategories.length === 0" :id="'category-all'"
					class="category-section">
					<view class="content-list">
						<view v-if="getSortedAllData().length === 0" class="empty-state">
							<image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png"
								mode="aspectFit"></image>
							<text>暂无数据</text>
						</view>

						<view v-else class="list-container">
							<UniversalListItem v-for="item in getSortedAllData()" :key="item.id" :item="item" :type="currentTabType"
								:dictData="localDict" :imageSize="128" @click="handleItemClick" />
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<cc-myTabbar :tabBarShow="1"></cc-myTabbar>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getCategoryTree } from '@/api/category.js'
import { getCourseList } from '@/api/course.js'
import { getMeditationList, getMeditationsByCategory } from '@/api/meditation.js'
import { listAssessment, getAssessmentsByCategory } from '@/api/evaluation.js'
import {
	listAvailableConsultants,
	getConsultantDetail
} from "@/api/consultant.js"
import { useDict } from "@/utils/index.js"
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 响应式数据
const tabs = ref([])
const currentTab = ref(0)
const fullCategoryData = ref([])
const currentCategoryIndex = ref(0)
const scrollIntoView = ref('')
const refreshing = ref(false)

// 数据列表
const counselorList = ref([])
const courseList = ref([])
const assessmentList = ref([])
const meditationList = ref([])

// 分类数据存储 - 按分类ID存储数据
const categoryDataMap = ref({})

// 字典数据
const localDict = ref({})

// 筛选相关
const commonSearch = ref([])
const selectedFilters = ref([])

// 排序相关
const sortBy = ref('') // 'level', 'price'
const sortOrder = ref('desc') // 'asc', 'desc'

// 计算属性
const currentTabType = computed(() => {
	return tabs.value[currentTab.value]?.type || 'consultant'
})

const currentCategories = computed(() => {
	const currentTabData = tabs.value[currentTab.value]
	return currentTabData?.children || []
})

// 方法
const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/search/search'
	})
}

// 加载主分类标签
const loadMainCategories = async () => {
	try {
		const res = await getCategoryTree()
		if (res.code === 200 && res.data && res.data.categories) {
			// 存储完整的分类数据
			fullCategoryData.value = res.data.categories

			// 将分类数据转换为标签格式
			tabs.value = res.data.categories.map(category => {
				let type = 'consultant'
				switch (category.categoryName) {
					case '咨询师':
						type = 'consultant'
						break
					case '课程':
						type = 'course'
						break
					case '冥想':
						type = 'meditation'
						break
					case '测评':
						type = 'assessment'
						break
				}

				return {
					name: category.categoryName,
					type: type,
					categoryId: category.categoryId,
					children: category.children || []
				}
			})
		} else {
			// 降级处理
			tabs.value = [
				{ name: '咨询师', type: 'consultant', children: [] },
				{ name: '课程', type: 'course', children: [] },
				{ name: '测评', type: 'assessment', children: [] },
				{ name: '冥想', type: 'meditation', children: [] }
			]
		}

		// 加载默认标签的数据
		if (tabs.value.length > 0) {
			switchTab(0)
		}
	} catch (error) {
		console.error('加载主分类标签失败:', error)
		// 使用默认分类
		tabs.value = [
			{ name: '咨询师', type: 'consultant', children: [] },
			{ name: '课程', type: 'course', children: [] },
			{ name: '测评', type: 'assessment', children: [] },
			{ name: '冥想', type: 'meditation', children: [] }
		]

		if (tabs.value.length > 0) {
			switchTab(0)
		}
	}
}

// 切换一级分类标签
const switchTab = (index) => {
	currentTab.value = index
	console.log(currentTab.value);

	currentCategoryIndex.value = 0
	scrollIntoView.value = ''

	// 重置排序状态
	sortBy.value = ''
	sortOrder.value = 'desc'

	// 清空分类数据
	categoryDataMap.value = {}

	// 加载对应的数据
	loadTabData()
}

// 加载标签对应的数据
const loadTabData = async () => {
	const type = currentTabType.value
	const categories = currentCategories.value

	// 如果没有二级分类，加载全部数据
	if (categories.length === 0) {
		await loadAllData(type)
		return
	}

	// 为每个二级分类加载数据
	for (const category of categories) {
		await loadCategoryData(category.categoryId, type)
	}
}

// 加载全部数据（没有二级分类时）
const loadAllData = async (type) => {
	try {
		let res = null
		switch (type) {
			case 'consultant':
				// 直接获取全部咨询师列表
				res = await listAvailableConsultants()
				// 咨询师数据需要特殊处理，存储到counselorList中
				if (res && res.code === 200) {
					const consultants = res.data || res.consultants || []
					counselorList.value = consultants.map(item => ({
						...item,
						avatar: item.imageUrl || item.avatar || item.headImg,
						name: item.name || item.consultantName || item.realName,
						id: item.id || item.consultantId,
						// 确保价格字段存在
						price: item.price || item.consultationFee || 0,
						// 确保等级字段存在
						counselorLevel: item.counselorLevel || item.level || '1'
					}))
				}
				break
			case 'course':
				res = await getCourseList()
				break
			case 'meditation':
				res = await getMeditationList()
				break
			case 'assessment':
				res = await listAssessment({
					pageNum: 1,
					pageSize: 20
				})
				break
		}

		if (res && res.code === 200 && type !== 'consultant') {
			// 将数据存储到第一个虚拟分类中（咨询师除外）
			categoryDataMap.value['all'] = res.data || []
		}
	} catch (error) {
		console.error('加载全部数据失败:', error)
		// 如果是咨询师加载失败，设置空数组
		if (type === 'consultant') {
			counselorList.value = []
		}
	}
}

// 加载指定分类的数据
const loadCategoryData = async (categoryId, type) => {
	try {
		let res = null
		switch (type) {
			case 'meditation':
				res = await getMeditationsByCategory(categoryId)
				break
			case 'assessment':
				res = await getAssessmentsByCategory(categoryId, {
					pageNum: 1,
					pageSize: 20
				})
				break
			case 'consultant':
				// 咨询师数据加载到counselorList中
				res = await listAvailableConsultants()
				if (res && res.code === 200) {
					const consultants = res.data || res.consultants || []
					counselorList.value = consultants.map(item => ({
						...item,
						avatar: item.imageUrl || item.avatar || item.headImg,
						name: item.name || item.consultantName || item.realName,
						id: item.id || item.consultantId,
						price: item.price || item.consultationFee || 0,
						counselorLevel: item.counselorLevel || item.level || '1'
					}))
				}
				break
			case 'course':
				res = await getCourseList()
				break
		}

		if (res && res.code === 200 && type !== 'consultant') {
			categoryDataMap.value[categoryId] = res.data || []
		}
	} catch (error) {
		console.error(`加载分类${categoryId}数据失败:`, error)
		if (type === 'consultant') {
			counselorList.value = []
		} else {
			categoryDataMap.value[categoryId] = []
		}
	}
}

// 获取指定分类的数据
const getCategoryData = (categoryId) => {
	return categoryDataMap.value[categoryId] || categoryDataMap.value['all'] || []
}

// 获取当前分类名称
const getCurrentCategoryName = () => {
	// 统一显示"全部"
	return '全部'
}

// 获取当前项目数量
const getCurrentItemCount = () => {
	if (currentTabType.value === 'consultant') {
		if (currentCategories.value.length === 0) {
			return counselorList.value.length
		} else {
			// 计算所有二级分类的总数量
			let total = 0
			currentCategories.value.forEach(category => {
				total += getCategoryData(category.categoryId).length
			})
			return total
		}
	} else {
		if (currentCategories.value.length === 0) {
			return categoryDataMap.value['all']?.length || 0
		} else {
			let total = 0
			currentCategories.value.forEach(category => {
				total += getCategoryData(category.categoryId).length
			})
			return total
		}
	}
}

// 排序切换
const toggleSort = (field) => {
	if (sortBy.value === field) {
		// 如果点击的是当前排序字段，切换排序方向
		sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
	} else {
		// 如果点击的是新的排序字段，设置为降序
		sortBy.value = field
		sortOrder.value = 'desc'
	}
}

// 咨询师等级排序映射 - 根据字典数据的 dictSort 字段排序
const getLevelSortValue = (level) => {
	// 根据您提供的字典数据，dictSort 值越大等级越高
	const levelMap = {
		'0': 0, // 咨询助理
		'1': 1, // 初级咨询师
		'2': 2, // 中级咨询师
		'3': 3, // 成熟咨询师
		'4': 4, // 高级咨询师
		'5': 5, // 资深咨询师
		'6': 6  // 咨询督导
	}

	const levelStr = String(level || '1')
	return levelMap[levelStr] || 1 // 默认为初级咨询师
}

// 排序数据的通用方法
const sortData = (data, field, order) => {
	if (!data || data.length === 0) return []

	return [...data].sort((a, b) => {
		let aValue, bValue

		if (field === 'price') {
			aValue = parseFloat(a.price || a.consultationFee || 0)
			bValue = parseFloat(b.price || b.consultationFee || 0)
		} else if (field === 'level') {
			// 使用字典数据的排序值进行等级排序
			aValue = getLevelSortValue(a.counselorLevel || a.level)
			bValue = getLevelSortValue(b.counselorLevel || b.level)
		} else {
			return 0
		}

		if (order === 'asc') {
			return aValue - bValue
		} else {
			return bValue - aValue
		}
	})
}

// 获取排序后的咨询师列表
const getSortedCounselorList = () => {
	// 默认不排序，只有用户点击排序按钮后才排序
	if (!sortBy.value) return counselorList.value
	return sortData(counselorList.value, sortBy.value, sortOrder.value)
}

// 获取排序后的分类数据
const getSortedCategoryData = (categoryId) => {
	const data = getCategoryData(categoryId)
	// 默认不排序，只有用户点击排序按钮后才排序
	if (!sortBy.value) return data
	return sortData(data, sortBy.value, sortOrder.value)
}

// 获取排序后的全部数据（非咨询师类型）
const getSortedAllData = () => {
	const data = categoryDataMap.value['all'] || []
	// 默认不排序，只有用户点击排序按钮后才排序
	if (!sortBy.value) return data
	return sortData(data, sortBy.value, sortOrder.value)
}

// 滚动到指定分类
const scrollToCategory = (index) => {
	currentCategoryIndex.value = index
	scrollIntoView.value = `category-${index}`
}

// 右侧滚动事件处理
const onRightScroll = () => {
	// 这里可以根据滚动位置动态更新左侧选中的分类
	// 由于uni-app的scroll事件信息有限，这里简化处理
}

// 处理项目点击
const handleItemClick = (item) => {
	const type = currentTabType.value

	switch (type) {
		case 'consultant':
			uni.navigateTo({
				url: `/pages/classification/counselor-detail/index?id=${item.id}`
			})
			break
		case 'course':
			uni.navigateTo({
				url: `/pages/course/detail/index?id=${item.id}`
			})
			break
		case 'meditation':
			uni.navigateTo({
				url: `/pages/meditation/detail/index?id=${item.id}`
			})
			break
		case 'assessment':
			uni.navigateTo({
				url: `/pages/evaluation/detail/index?id=${item.id}`
			})
			break
	}
}

// 处理咨询师详情跳转
const handleCounselorDetail = async (item, type) => {
	if (type === "counselor") {
		try {
			const res = await getConsultantDetail(item.id);
			if (res.code === 200) {
				uni.navigateTo({
					url: `/pages/classification/counselor-detail/index?id=${item.id}`
				});
			}
		} catch (error) {
			console.error('Failed to get counselor detail:', error);
			uni.showToast({
				title: '获取咨询师详情失败',
				icon: 'none'
			});
		}
	} else {
		uni.navigateTo({
			url: `/pages/classification/product-detail/index?productId=${item.productId}`
		});
	}
}

// 筛选相关方法
const isFilterActive = (value) => {
	return selectedFilters.value.includes(value)
}

const handleCommonSearch = (item) => {
	const index = selectedFilters.value.indexOf(item.value)
	if (index === -1) {
		selectedFilters.value.push(item.value)
	} else {
		selectedFilters.value.splice(index, 1)
	}
	// 这里可以添加筛选逻辑
}

// 下拉刷新
const onRefresh = async () => {
	refreshing.value = true

	// 清空当前数据
	categoryDataMap.value = {}

	// 重新加载数据
	await loadTabData()

	refreshing.value = false
}

// 加载字典数据
const loadDictData = async () => {
	try {
		const res = await useDict('psy_consultant_level')
		localDict.value = {
			psy_consultant_level: res.psy_consultant_level || []
		}
	} catch (error) {
		console.error('加载字典数据失败:', error)
	}
}

// 生命周期
onLoad(() => {
	loadMainCategories()
	loadDictData()
})


</script>

<style lang="scss" scoped>
.classification-page {
	height: calc(100vh - 160rpx);
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.search-section {
	padding: 24rpx 32rpx;
	background-color: #fff;
	padding-top: 40rpx;

	.search-bar {
		display: flex;
		align-items: center;
		padding: 20rpx 24rpx;
		background-color: #f8f8f8;
		border-radius: 40rpx;
		gap: 16rpx;
		max-width: calc(100vw - 300rpx); // 为右侧胶囊留出空间

		.search-placeholder {
			font-size: 28rpx;
			color: #999;
		}
	}
}

.category-tabs {
	display: flex;
	background-color: #fff;
	padding: 24rpx 32rpx;

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 16rpx 0;
		font-size: 28rpx;
		color: #666;
		position: relative;

		&.active {
			color: #20a3f3;
			font-weight: 600;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background-color: #20a3f3;
				border-radius: 2rpx;
			}
		}
	}
}

.main-content {
	flex: 1;
	display: flex;
	height: calc(100vh - 426rpx); // 减去搜索框和标签的高度
	margin-top: 16rpx;
}

.left-nav {
	width: 196rpx;
	background-color: #f5f5f5;
	border-right: 1rpx solid #eee;

	.nav-item {
		width: 180rpx;
		padding: 16rpx 12rpx;
		border-bottom: 1rpx solid #eee;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 98rpx;
		box-sizing: border-box;

		&.active {
			background-color: #fff;
			border-radius: 2rpx 8rpx 8rpx 2rpx;

			.nav-text {
				color: #000;
				font-weight: 500;
			}
		}

		.nav-text {
			font-size: 26rpx;
			color: #8A8788;
			text-align: center;
			line-height: 1.3;
			width: 180rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}

.right-content {
	flex: 1;
	background-color: #fff;
	height: calc(100vh - 370rpx);
}

.content-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	background-color: #fff;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	position: sticky;
	top: 0;
	z-index: 10;

	.header-left {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.category-name {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}

		.item-count {
			font-size: 28rpx;
			color: #666;
		}
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 32rpx;
		height: 40rpx;

		.sort-item {
			display: flex;
			align-items: center;
			gap: 8rpx;
			padding: 12rpx 16rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			color: #8A8788;
			background-color: #F5F5F5;

			text {
				font-size: 24rpx;
			}

			.sort-icon {
				width: 24rpx;
				height: 24rpx;
			}
		}
	}
}

.is-assessment {
	width: calc(100% - 200rpx);
}

.category-section {
	margin-bottom: 32rpx;

	.content-list {
		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 120rpx 32rpx;
			text-align: center;

			image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 32rpx;
			}

			text {
				font-size: 28rpx;
				color: #999;
			}
		}

		.list-container {
			padding: 24rpx;
		}
	}
}

.filter-tags {
	padding: 24rpx 32rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	background-color: #fff;

	.filter-tag {
		padding: 12rpx 24rpx;
		background-color: #f0f0f0;
		color: #666;
		font-size: 26rpx;
		border-radius: 24rpx;
		border: 1rpx solid transparent;

		&.active {
			background-color: #fff4dc;
			color: #20a3f3;
			border-color: #20a3f3;
		}
	}
}
</style>