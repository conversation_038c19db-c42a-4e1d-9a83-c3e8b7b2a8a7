<template>
  <view class="profile-container">
    <!-- 个人信息卡片 -->
    <view class="profile-card">
      <view class="profile-header">
        <view class="avatar-section" @click="changeAvatar">
          <image class="avatar" :src="profileInfo.avatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
          <view class="avatar-edit">
            <image src="/static/icon/camera.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="info-section">
          <text class="name">{{ profileInfo.name || '咨询师' }}</text>
          <text class="title">{{ profileInfo.title || '心理咨询师' }}</text>
          <text class="phone">{{ formatPhone(profileInfo.phone) }}</text>
        </view>
        <view class="settings-btn" @click="goToSettings">
          <image src="/static/icon/settings.png" mode="aspectFit"></image>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stat-item" @click="goToOrders">
          <text class="stat-number">{{ statsInfo.totalOrders || 0 }}</text>
          <text class="stat-label">总订单</text>
        </view>
        <view class="stat-item" @click="goToOrders('pending')">
          <text class="stat-number">{{ statsInfo.pendingOrders || 0 }}</text>
          <text class="stat-label">待处理</text>
        </view>
        <view class="stat-item" @click="goToHistory">
          <text class="stat-number">{{ statsInfo.totalClients || 0 }}</text>
          <text class="stat-label">服务人数</text>
        </view>
        <view class="stat-item" @click="goToWallet">
          <text class="stat-number">{{ statsInfo.totalIncome || 0 }}</text>
          <text class="stat-label">总收入</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="goToWallet">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/钱包.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">我的钱包</text>
          <view class="menu-extra">
            <text class="balance">￥{{ walletInfo.balance || 0 }}</text>
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>

        <view class="menu-item" @click="goToHistory">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/咨询.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">咨询历史</text>
          <view class="menu-extra">
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>

        <view class="menu-item" @click="goToSchedule">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/日程.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">我的日程</text>
          <view class="menu-extra">
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" @click="goToReviews">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/评价.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">客户评价</text>
          <view class="menu-extra">
            <text class="rating">{{ profileInfo.rating || 5.0 }}分</text>
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>

        <view class="menu-item" @click="goToStatistics">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/统计.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">数据统计</text>
          <view class="menu-extra">
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>

        <view class="menu-item" @click="goToHelp">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/帮助.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">帮助中心</text>
          <view class="menu-extra">
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" @click="goToAbout">
          <view class="menu-icon">
            <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/关于.png" mode="aspectFit"></image>
          </view>
          <text class="menu-title">关于我们</text>
          <view class="menu-extra">
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <cc-myTabbar :tabBarShow="4"></cc-myTabbar>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getConsultantProfile,
  getConsultantWallet,
  getConsultantStatistics,
  uploadConsultantAvatar
} from '@/api/consultant-app.js'
// import { useUserStore } from '@/stores/user'
// const userStore = useUserStore()

// 响应式数据
const profileInfo = ref({})
const walletInfo = ref({})
const statsInfo = ref({})

// 生命周期
onMounted(() => {
  loadProfileData()
})

onShow(() => {
  loadProfileData()
})

// 加载个人资料数据
const loadProfileData = async () => {
  try {
    // 并行加载多个接口数据
    const [profileRes, walletRes, statsRes] = await Promise.all([
      getConsultantProfile(),
      getConsultantWallet(),
      getConsultantStatistics()
    ])

    if (profileRes.code === 200) {
      profileInfo.value = profileRes.data
    }

    if (walletRes.code === 200) {
      walletInfo.value = walletRes.data
    }

    if (statsRes.code === 200) {
      statsInfo.value = statsRes.data
    }
  } catch (error) {
    console.error('加载个人资料失败:', error)
  }
}

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 更换头像
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0]

      // 显示加载提示
      uni.showLoading({
        title: '上传中...'
      })

      try {
        // 上传头像
        const uploadRes = await uploadConsultantAvatar({
          file: tempFilePath
        })

        if (uploadRes.code === 200) {
          profileInfo.value.avatar = uploadRes.data.avatarUrl
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('上传头像失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    }
  })
}

// 跳转到设置页面
const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/settings/index'
  })
}

// 跳转到订单页面
const goToOrders = (status = 'all') => {
  uni.navigateTo({
    url: `/pages/consultation-order/index?status=${status}`
  })
}

// 跳转到钱包页面
const goToWallet = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/wallet/index'
  })
}

// 跳转到咨询历史
const goToHistory = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/history/index'
  })
}

// 跳转到日程页面
const goToSchedule = () => {
  uni.switchTab({
    url: '/pages/schedule/index'
  })
}

// 跳转到评价页面
const goToReviews = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/reviews/index'
  })
}

// 跳转到统计页面
const goToStatistics = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/statistics/index'
  })
}

// 跳转到帮助中心
const goToHelp = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/help/index'
  })
}

// 跳转到关于我们
const goToAbout = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/about/index'
  })
}
</script>

<style scoped lang="scss">
.profile-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.profile-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 30rpx;
  margin-bottom: 20rpx;

  .profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;

    .avatar-section {
      position: relative;
      margin-right: 30rpx;

      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
      }

      .avatar-edit {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 40rpx;
        height: 40rpx;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 20rpx;
          height: 20rpx;
        }
      }
    }

    .info-section {
      flex: 1;

      .name {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #fff;
        margin-bottom: 8rpx;
      }

      .title {
        display: block;
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 8rpx;
      }

      .phone {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .settings-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 30rpx;
        height: 30rpx;
        filter: brightness(0) invert(1);
      }
    }
  }

  .stats-section {
    display: flex;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 30rpx 0;

    .stat-item {
      flex: 1;
      text-align: center;

      .stat-number {
        display: block;
        font-size: 40rpx;
        font-weight: bold;
        color: #fff;
        margin-bottom: 8rpx;
      }

      .stat-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.menu-section {
  padding: 0 20rpx;

  .menu-group {
    background-color: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .menu-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .menu-title {
        flex: 1;
        font-size: 30rpx;
        color: #333;
      }

      .menu-extra {
        display: flex;
        align-items: center;

        .balance,
        .rating {
          font-size: 26rpx;
          color: #1890ff;
          margin-right: 10rpx;
        }

        image {
          width: 24rpx;
          height: 24rpx;
        }
      }

      &:active {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>