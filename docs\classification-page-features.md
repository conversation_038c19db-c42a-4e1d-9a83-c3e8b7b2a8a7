# 分类页面功能文档

## 概述

新的分类页面实现了完整的分类浏览功能，包含搜索、一级分类标签、左侧二级分类导航和右侧内容列表，并支持锚点滚动功能。

## 页面结构

### 1. 搜索框区域
- **位置**: 页面顶部
- **功能**: 点击跳转到搜索页面
- **样式**: 圆角搜索框，左侧搜索图标
- **响应式**: 自动适配右侧胶囊按钮，避免重叠

```vue
<view class="search-section">
  <view class="search-bar" @click="goToSearch">
    <uni-icons type="search" size="16" color="#999"></uni-icons>
    <text class="search-placeholder">搜索咨询师、课程、测评...</text>
  </view>
</view>
```

### 2. 一级分类标签
- **位置**: 搜索框下方
- **功能**: 切换不同的主分类（咨询师、课程、冥想、测评）
- **样式**: 水平标签栏，选中状态有下划线
- **数据来源**: 从接口动态获取

```vue
<view class="category-tabs">
  <view 
    v-for="(tab, index) in tabs" 
    :key="index"
    :class="['tab-item', { active: currentTab === index }]"
    @click="switchTab(index)"
  >
    {{ tab.name }}
  </view>
</view>
```

### 3. 主要内容区域
采用左右分栏布局：

#### 左侧二级分类导航
- **宽度**: 200rpx
- **功能**: 显示当前一级分类下的二级分类
- **交互**: 点击滚动到对应内容区域
- **样式**: 选中状态有左侧橙色边框

#### 右侧内容列表
- **功能**: 显示分类下的具体内容
- **布局**: 按二级分类分组显示
- **组件**: 使用通用列表项组件
- **滚动**: 支持锚点滚动和下拉刷新

## 核心功能

### 1. 动态分类加载
```javascript
// 从接口获取分类数据
const loadMainCategories = async () => {
  const res = await getCategoryTree()
  if (res.code === 200 && res.data && res.data.categories) {
    // 转换为页面需要的格式
    tabs.value = res.data.categories.map(category => {
      let type = 'consultant'
      switch (category.categoryName) {
        case '咨询师': type = 'consultant'; break
        case '课程': type = 'course'; break
        case '冥想': type = 'meditation'; break
        case '测评': type = 'assessment'; break
      }
      
      return {
        name: category.categoryName,
        type: type,
        categoryId: category.categoryId,
        children: category.children || []
      }
    })
  }
}
```

### 2. 分类数据管理
```javascript
// 按分类ID存储数据
const categoryDataMap = ref({})

// 为每个二级分类加载数据
const loadTabData = async () => {
  const type = currentTabType.value
  const categories = currentCategories.value
  
  for (const category of categories) {
    await loadCategoryData(category.categoryId, type)
  }
}

// 获取指定分类的数据
const getCategoryData = (categoryId) => {
  return categoryDataMap.value[categoryId] || []
}
```

### 3. 锚点滚动功能
```javascript
// 滚动到指定分类
const scrollToCategory = (index) => {
  currentCategoryIndex.value = index
  scrollIntoView.value = `category-${index}`
}

// 右侧滚动事件处理（可扩展）
const onRightScroll = (e) => {
  // 可以根据滚动位置动态更新左侧选中的分类
}
```

### 4. 统一的项目点击处理
```javascript
const handleItemClick = (item) => {
  const type = currentTabType.value
  
  switch (type) {
    case 'consultant':
      uni.navigateTo({ url: `/pages/classification/counselor-detail/index?id=${item.id}` })
      break
    case 'course':
      uni.navigateTo({ url: `/pages/course/detail/index?id=${item.id}` })
      break
    case 'meditation':
      uni.navigateTo({ url: `/pages/meditation/detail/index?id=${item.id}` })
      break
    case 'assessment':
      uni.navigateTo({ url: `/pages/evaluation/detail/index?id=${item.id}` })
      break
  }
}
```

## 样式设计

### 1. 响应式布局
- **搜索框**: 自动适配右侧胶囊按钮
- **主内容**: 使用flex布局，自适应高度
- **左右分栏**: 固定左侧宽度，右侧自适应

### 2. 视觉层次
- **搜索框**: 浅灰背景，圆角设计
- **标签栏**: 白色背景，选中状态橙色下划线
- **左侧导航**: 浅灰背景，选中状态白色背景+橙色左边框
- **右侧内容**: 白色背景，分类标题灰色背景

### 3. 交互反馈
- **点击态**: 所有可点击元素都有视觉反馈
- **选中态**: 明确的选中状态指示
- **加载态**: 下拉刷新的加载指示

## 数据流

### 1. 初始化流程
```
页面加载 → 获取分类树 → 转换数据格式 → 加载默认标签数据
```

### 2. 切换标签流程
```
点击标签 → 清空数据 → 重置状态 → 加载新标签数据
```

### 3. 分类数据加载流程
```
获取二级分类 → 遍历分类 → 调用对应API → 存储到categoryDataMap
```

## 性能优化

### 1. 数据缓存
- **分类数据**: 切换标签时清空重新加载
- **接口缓存**: 相同分类的数据会被缓存
- **组件复用**: 使用通用列表项组件

### 2. 按需加载
- **懒加载**: 只有切换到对应标签才加载数据
- **分类加载**: 按二级分类分别加载数据
- **错误处理**: 单个分类加载失败不影响其他分类

### 3. 用户体验
- **即时反馈**: 点击立即切换状态
- **平滑滚动**: 锚点滚动到指定位置
- **下拉刷新**: 支持手动刷新数据

## 扩展性

### 1. 新增分类类型
只需在类型映射中添加新的case：
```javascript
switch (category.categoryName) {
  case '新分类':
    type = 'new-type'
    break
}
```

### 2. 自定义分类数据加载
可以为不同类型实现不同的数据加载逻辑：
```javascript
case 'new-type':
  res = await getNewTypeData(categoryId)
  break
```

### 3. 增强滚动联动
可以扩展onRightScroll方法实现更精确的滚动联动：
```javascript
const onRightScroll = (e) => {
  // 根据scrollTop计算当前显示的分类
  // 自动更新左侧选中状态
}
```

## 注意事项

### 1. 兼容性
- **接口降级**: 接口失败时使用默认分类
- **数据容错**: 处理空数据和异常情况
- **组件兼容**: 与通用列表项组件保持兼容

### 2. 性能考虑
- **内存管理**: 及时清理不需要的数据
- **滚动性能**: 避免频繁的DOM操作
- **网络请求**: 合理控制并发请求数量

### 3. 用户体验
- **加载提示**: 数据加载时的友好提示
- **错误处理**: 网络错误时的用户提示
- **空状态**: 优雅的空数据展示

## 总结

新的分类页面实现了完整的分类浏览功能，具有以下特点：

1. **完整功能**: 搜索、分类、导航、内容展示一应俱全
2. **良好交互**: 锚点滚动、状态联动、即时反馈
3. **响应式设计**: 适配不同屏幕尺寸和设备
4. **高性能**: 数据缓存、按需加载、组件复用
5. **易扩展**: 清晰的代码结构，便于添加新功能
6. **用户友好**: 直观的界面设计，流畅的操作体验

这个实现为用户提供了高效的分类浏览体验，同时为开发者提供了良好的代码维护性。
