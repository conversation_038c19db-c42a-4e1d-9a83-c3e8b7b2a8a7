import request from '@/utils/request'

// 获取格式化的系统时间槽数据
export function getFormattedTimeSlots(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/formatted',
    method: 'get',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
}

// 获取指定日期的系统时间槽
export function getSlotsByDate(date, centerId = 1) {
  return request({
    url: `/system/systemTimeSlot/date/${date}`,
    method: 'get',
    params: {
      centerId
    }
  })
}

// 获取有可用咨询师的时间槽
export function getAvailableSlots(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/available',
    method: 'get',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
} 