// 课程模拟数据
export const mockCourses = [
  {
    id: 1,
    title: "心理健康基础课程",
    subtitle: "了解心理健康的基本知识",
    description: "本课程将带您深入了解心理健康的基本概念、常见问题及应对方法，帮助您建立健康的心理状态。",
    coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/course1.jpg",
    price: 99.00,
    originalPrice: 199.00,
    duration: 180,
    chapterCount: 8,
    studentCount: 1256,
    rating: 4.8,
    status: 1,
    category: "mental_health",
    learningGoals: [
      "掌握心理健康的基本概念",
      "了解常见心理问题的识别方法",
      "学会基本的心理调节技巧",
      "建立积极的心理状态"
    ],
    targetAudience: "适合所有关注心理健康的人群",
    purchased: false
  },
  {
    id: 2,
    title: "情感关系管理",
    subtitle: "建立健康的人际关系",
    description: "学习如何建立和维护健康的情感关系，包括恋爱关系、友谊关系和家庭关系。",
    coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/course2.jpg",
    price: 129.00,
    originalPrice: 229.00,
    duration: 240,
    chapterCount: 10,
    studentCount: 892,
    rating: 4.6,
    status: 1,
    category: "relationship",
    learningGoals: [
      "理解健康关系的特征",
      "学会有效沟通技巧",
      "掌握冲突解决方法",
      "建立长久稳定的关系"
    ],
    targetAudience: "适合希望改善人际关系的人群",
    purchased: false
  },
  {
    id: 3,
    title: "职场压力管理",
    subtitle: "应对工作中的压力和挑战",
    description: "帮助职场人士识别和管理工作压力，提高工作效率和生活质量。",
    coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/course3.jpg",
    price: 149.00,
    originalPrice: 249.00,
    duration: 200,
    chapterCount: 12,
    studentCount: 1543,
    rating: 4.9,
    status: 1,
    category: "career",
    learningGoals: [
      "识别工作压力的来源",
      "学会压力管理技巧",
      "提高工作效率",
      "保持工作生活平衡"
    ],
    targetAudience: "适合职场人士和管理者",
    purchased: true
  }
]

// 课程章节模拟数据
export const mockChapters = {
  1: [
    {
      id: 101,
      courseId: 1,
      title: "心理健康概述",
      duration: 25,
      isTrial: 1,
      completed: false,
      chapterContent: "<p>心理健康是指个体在心理、情感和社会适应方面的良好状态...</p>",
      videoUrl: "https://example.com/video1.mp4",
      coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/chapter1.jpg"
    },
    {
      id: 102,
      courseId: 1,
      title: "常见心理问题识别",
      duration: 30,
      isTrial: 0,
      completed: false,
      chapterContent: "<p>本章节将介绍常见的心理问题及其识别方法...</p>",
      videoUrl: "https://example.com/video2.mp4",
      coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/chapter2.jpg"
    }
  ],
  2: [
    {
      id: 201,
      courseId: 2,
      title: "关系的基础",
      duration: 28,
      isTrial: 1,
      completed: false,
      chapterContent: "<p>健康关系的基础是相互尊重和理解...</p>",
      videoUrl: "https://example.com/video3.mp4",
      coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/chapter3.jpg"
    }
  ],
  3: [
    {
      id: 301,
      courseId: 3,
      title: "压力的来源",
      duration: 22,
      isTrial: 1,
      completed: true,
      chapterContent: "<p>工作压力主要来源于工作负荷、人际关系等...</p>",
      videoUrl: "https://example.com/video4.mp4",
      coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/chapter4.jpg"
    },
    {
      id: 302,
      courseId: 3,
      title: "压力管理技巧",
      duration: 35,
      isTrial: 0,
      completed: false,
      chapterContent: "<p>有效的压力管理技巧包括时间管理、放松训练等...</p>",
      videoUrl: "https://example.com/video5.mp4",
      coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/chapter5.jpg"
    }
  ]
}

// 课程评价模拟数据
export const mockReviews = {
  1: [
    {
      id: 1,
      courseId: 1,
      userId: 1,
      userName: "张同学",
      userAvatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/avatar1.jpg",
      rating: 5,
      content: "课程内容非常实用，老师讲解清晰，对我的心理健康有很大帮助。",
      createTime: "2024-01-15 10:30:00"
    },
    {
      id: 2,
      courseId: 1,
      userId: 2,
      userName: "李同学",
      userAvatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/avatar2.jpg",
      rating: 4,
      content: "课程质量很高，学到了很多心理健康知识，推荐给大家。",
      createTime: "2024-01-10 14:20:00"
    }
  ],
  2: [
    {
      id: 3,
      courseId: 2,
      userId: 3,
      userName: "王同学",
      userAvatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/avatar3.jpg",
      rating: 5,
      content: "这门课程帮助我改善了人际关系，非常感谢老师的指导。",
      createTime: "2024-01-12 16:45:00"
    }
  ],
  3: [
    {
      id: 4,
      courseId: 3,
      userId: 4,
      userName: "赵同学",
      userAvatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/avatar4.jpg",
      rating: 5,
      content: "职场压力管理课程非常实用，学会了很多减压技巧。",
      createTime: "2024-01-08 09:15:00"
    }
  ]
}

// 用户学习进度模拟数据
export const mockProgress = {
  1: [
    {
      id: 1,
      userId: 1,
      courseId: 1,
      chapterId: 101,
      progressPercent: 100,
      watchTime: 1500,
      totalTime: 1500,
      completed: true
    }
  ],
  3: [
    {
      id: 2,
      userId: 1,
      courseId: 3,
      chapterId: 301,
      progressPercent: 100,
      watchTime: 1320,
      totalTime: 1320,
      completed: true
    },
    {
      id: 3,
      userId: 1,
      courseId: 3,
      chapterId: 302,
      progressPercent: 60,
      watchTime: 1260,
      totalTime: 2100,
      completed: false
    }
  ]
}
