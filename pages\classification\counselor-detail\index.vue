<template>
	<view class="content">
		<view class="avatar">
			<image class="avatar-img" mode="scaleToFill"
				:src="counselorDetail.imageUrl || 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E8%80%81%E5%B8%88%E8%AF%A6%E6%83%85%E9%A1%B5%E5%9B%BE%E7%89%87.png'">
			</image>
		</view>
		<view class="bg-img">
			<image @click="handleGuarantee" mode="scaleToFill"
				:src="counselorDetail.bgImg || 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E4%BF%9D%E9%9A%9C.png'">
			</image>
		</view>
		<view class="counselor-detail">
			<view class="counselor-info">
				<view class="name">
					{{ counselorDetail.name }}
					<!-- <image mode="scaleToFill"
							src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E8%AE%A4%E8%AF%81%E8%B5%84%E6%A0%BC.png">
						</image> -->
					<!-- <view class="qualification">{{ getPsy_consultant_level(counselorDetail.counselorLevel) }}</view> -->
					<view v-if="consultantLevelText" class="qualifications">{{ consultantLevelText }}</view>
				</view>
			</view>
			<view class="consultation-method">
				<view class="">
					<text v-for="(item, index) in counselorDetail.certificates" :key="item.id">{{ item.name }}
						<text v-if="index < counselorDetail.certificates.length - 1"> | </text>
					</text>
				</view>
				<view class="price-box">
					<view v-if="getActualPrice && parseFloat(getActualPrice) > 0" class="price-main">
						<text class="currency-symbol">¥</text>
						<text class="price-integer">{{ getPriceInteger }}</text>
						<text v-if="getPriceDecimal" class="price-dot">.</text>
						<text v-if="getPriceDecimal" class="price-decimal">{{ getPriceDecimal }}</text>
						<text class="price-slash">/</text>
						<text class="price-unit">{{ getPriceUnit }}</text>
					</view>
					<view v-if="getOriginalPrice && getOriginalPrice > getActualPrice" class="original-price">¥{{
						getOriginalPrice }}</view>
				</view>
				<!-- <view class="">{{ counselorDetail.province }}{{ counselorDetail.city }}{{ counselorDetail.district }}</view> -->
			</view>
			<view class="radius-box">
				<view class="consulting-experience">
					<view class="scroll">
						<view class="scroll-view_H">
							<view class="time-box" v-for="item in consultingExperience" :key="item.id">
								<view class="time-bottom">
									<text class="time-text">{{ item.time }}</text>
									<text class="time-unit">{{ item.unit }}</text>
								</view>
								<view class="time-top">
									{{ item.timeTitle }}
								</view>
							</view>
						</view>
					</view>
					<view class="time-title-row">
						<view class="time-title">可约时间：09:00-20:00</view>
						<view class="view-more" @click="showFullSchedule">
							查看全部
							<uni-icons type="right" size="13"></uni-icons>
						</view>
					</view>
				</view>
			</view>

			<!-- Tab Navigation -->
			<view class="tab-navigation">
				<view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: activeTab === index }]"
					@click="scrollToSection(index)">
					<text>{{ tab.name }}</text>
					<image v-if="activeTab === index" class="tab-icon" src="../../../static/icon/形状 6-active.png"></image>
				</view>
			</view>

			<!-- Tab Content - 移除v-show，改为锚点导航 -->
			<!-- 个人简介 Section -->
			<view id="section-0" class="section-content">
				<view class="radius-box">
					<view class="title">
						<view class="max-title">个人简介</view>
						<!-- <view class="min-title">
							个人头衔：
							<view class="info-content" v-for="item in counselorDetail.certificates" :key="item.id">{{ item.name }}
							</view>
						</view> -->
						<view class="min-title">
							<!-- 自我介绍： -->
							<view id="info-content" class="info-content" :class="{ hide: !isInfo }">
								{{ counselorDetail.personalIntro }}
								<view class="author">—— {{ counselorDetail.name }}</view>
							</view>
							<view v-if="!showButton" @click="isInfo = !isInfo" class="show-info">
								<view v-if="isInfo" class="box-transparent"></view>
								{{ isInfo ? "展开全部" : "点击收起" }}
								<uni-icons v-if="isInfo" color="#01a6fe" type="down" size="18"></uni-icons>
								<uni-icons v-else color="#01a6fe" type="up" size="18"></uni-icons>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 擅长流派 Section -->
			<view id="section-1" class="section-content">
				<view class="radius-box">
					<view class="title">
						<view class="max-title">咨询方式</view>
						<view class="min-title">
							<view class="genre">擅长流派</view>
							<view class="style-container">
								<view v-for="style in counselorDetail.consultStyles" :key="style.id" class="style-tag"
									@click="handleStyleClick(style)">
									{{ style.dictLabel }}
									<uni-icons type="right" size="13"></uni-icons>
								</view>
							</view>
						</view>
						<view class="min-title">
							<view class="genre">擅长群体</view>
							<view class="individual-counseling" @click="openInfoPopup('个体咨询(成人)')">
								个体咨询(成人)
								<uni-icons type="right" size="13"></uni-icons>
							</view>
						</view>
						<view class="min-title" v-if="counselorDetail.specialMethods?.length > 0">
							<view style="margin-bottom: 20rpx; margin-top: 30rpx;">特色咨询方式</view>
							<view class="style-container">
								<view v-for="style in counselorDetail.specialMethods" :key="style.id" class="style-tag">
									{{ style.dictLabel }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 专业背景 Section -->
			<view id="section-2" class="section-content">
				<view class="radius-box">
					<view class="title">
						<view class="max-title">专业背景</view>
						<view class="min-title">
							<view class="section-title">教育经历</view>
							<uni-steps :options="educationSteps" active-color="#007AFF" :active="educationSteps?.length - 1"
								direction="column" />
						</view>

						<view class="min-title">
							<view class="section-title">培训经历</view>
							<uni-steps v-if="trainingSteps.length > 0" :options="trainingSteps" direction="column" :active="-1" />
						</view>
					</view>
				</view>
			</view>

			<!-- 所属门店 Section -->
			<view id="section-3" class="section-content">
				<view class="radius-box">
					<view class="title">
						<view class="max-title" style="font-size: 28rpx;">所属门店</view>
						<view class="consult-store">
							<view class="store-info-container">
								<view class="store-logo" @click="toMap">
									<image class="logo-img" src="../../../static/icon/店铺logo.png" mode="aspectFill"></image>
								</view>
								<view class="store-details">
									<view class="store-name" @click="toMap">{{ storeInfo.name }}{{ storeInfo.branchName }}</view>
									<view class="store-phones">
										<view class="store-phone" v-for="(contact, index) in storeInfo.contacts" :key="index"
											@click="makePhoneCall(contact.phone)">
											{{ contact.phone }}
										</view>
										<view class="store-phone" v-if="!storeInfo.contacts || storeInfo.contacts.length === 0"
											@click="makePhoneCall(storeInfo.phone)">
											{{ storeInfo.phone }}
										</view>
									</view>
									<view class="store-address" @click="toMap">{{ storeInfo.address }}</view>
								</view>
								<view class="arrow-icon" @click="toStoreEnvironment">
									<image mode="widthFix" src="../../../static/icon/圆角矩形 1.png"></image>
								</view>
							</view>
							<!-- <view class="image-box">
								<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay"
									:interval="interval" :duration="duration">
									<swiper-item>
										<image mode="widthFix"
											src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE1.jpg">
										</image>
									</swiper-item>
									<swiper-item>
										<image mode="widthFix"
											src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE2.jpg">
										</image>
									</swiper-item>
								</swiper>
							</view> -->
						</view>
					</view>
				</view>
			</view>

			<!-- 咨询评价 Section -->
			<view id="section-4" class="section-content">
				<view class="radius-box">
					<view class="title">
						<view class="max-title review-title">
							<view class="title-text">咨询评价</view>
							<view class="more-btn" @click="goToReviewsPage">
								<text>更多</text>
								<image mode="widthFix" src="../../../static/icon/圆角矩形 1.png"></image>
							</view>
						</view>
						<view class="min-title">
							<view class="review-summary" v-if="reviewStatistics.totalCount > 0">
								<view class="rating-overview">
									<view class="avg-rating">
										<text class="rating-number">{{ reviewStatistics.avgRating || '0.0' }}</text>
										<uni-rate color="#E6E6E6" active-color='#A04571'
											:value="parseFloat(reviewStatistics.avgRating || 0)" size="14" readonly />
									</view>
									<text class="total-count">共{{ reviewStatistics.totalCount }}条评价</text>
								</view>
								<view class="recent-reviews">
									<ReviewItem v-for="review in recentReviews.slice(0, 2)" :key="review.id" :review="review"
										:content-max-length="50" :showCard="false" />
								</view>
							</view>
							<view class="info-content" v-else>
								暂无评价数据
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 可约时间部分 - 始终显示 -->
			<view class="radius-box">
				<view class="title" @click="handleMakeAppointment">
					<view class="max-title">
						<view class="title-box">
							<view>
								可约时间
								<text class="beijing-time">(北京时间)</text>
							</view>
						</view>
						<view class="describe">如暂时无法确定具体咨询时间，可在预约服务成功后，再与咨询师协商确定具体咨询时间</view>
					</view>
					<view class="info-time">
						<view class="detail-time" v-for="item in dataInfoSimplify" :key="item.date">
							<view class="detail-time-top" :class="{ 'is-today': item.isToday }">
								<view class="top-date">
									{{ item.shortDate }}
								</view>
								<view class="top-week">
									{{ item.weekDay }}
								</view>
							</view>
							<view class="detail-time-bottom">
								<view class="hour">
									{{ item.hour }}
								</view>
								<view class="reducible">可约</view>
							</view>
							<!-- <view class="full" v-else>已满</view> -->
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<UniversalGoodsNav page-type="counselor" :detail-data="counselorDetail"
				:favorited="counselorDetail.favoriteId ? true : false" :favorite-id="counselorDetail.favoriteId"
				@favorite="handleFavorite" @contact-service="handleContactService" @share="handleShare"
				@main-action="handleMainAction" />
			<!-- <view v-if="!isCollection" class="collection" @click="handleCollection">
				<image mode="widthFix" :src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%94%B6%E8%97%8F.png`" />
				<text>收藏</text>
			</view>
			<view v-else class="collection active" @click="handleCollection">
				<image mode="widthFix" :src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%B7%B2%E6%94%B6%E8%97%8F.png`" />
				<text>已收藏</text>
			</view>
			<view class="private-message">
				<button open-type="getUserInfo" @getuserinfo="onGetUserInfo">立即私信</button>
			</view>
			<view class="consult">
				<button open-type="getUserInfo" @click="handleConsultNow">立即咨询</button>
			</view> -->
		</view>
		<uni-popup background-color="#fff" class="bottom-popup" ref="popup" type="bottom" border-radius="30rpx 30rpx 0 0">
			<view class="title">服务保障</view>
			<view v-for="item in guaranteeList" :key="item.id" class="guarantee-list">
				<view class="icon-label">
					<view class="icon">
						<image class="avatar-img" mode="scaleToFill" :src="item.icon"></image>
					</view>
					<view class="text">
						{{ item.title }}
					</view>
				</view>
				<view class="guarantee-content">
					{{ item.info }}
				</view>
			</view>
		</uni-popup>

		<!-- 咨询风格信息弹窗 -->
		<InfoModal ref="stylePopup" :title="infoPopupTitle" :content="formatStyleContent(currentStyle.remark)" />

		<!-- 定义说明信息弹窗 -->
		<InfoModal ref="infoPopup" :title="infoPopupTitle" :content="getDefinitionContent()" />


		<!-- 预约咨询弹框 -->
		<uni-popup ref="appointmentPopup" type="bottom" :mask-click="false">
			<view class="appointment-popup">
				<view class="popup-header">
					<text class="popup-title">预约咨询</text>
					<uni-icons type="close" size="20" @click="closeAppointmentPopup" />
				</view>

				<scroll-view class="popup-content" scroll-y>
					<!-- 咨询方式 -->
					<view class="form-section">
						<view class="section-title">咨询方式</view>
						<view class="inline-radio-group">
							<view v-for="item in consultationTypes" :key="item.value"
								:class="['inline-radio-item', 'inline-radio-item-bg', { active: appointmentForm.consultationType === item.value }]"
								@click="selectConsultationType(item.value)">
								<text class="inline-radio-text">{{ item.label }}</text>
							</view>
						</view>
					</view>

					<!-- 可约时间（使用增强版时间表，包含日期选择） -->
					<view class="form-section">
						<view class="section-title">可约时间</view>
						<EnhancedTimeTable ref="appointmentTimeTableRef" :showDateSelector="true" :dayRange="6" :showMessage="false"
							:showActionButtons="false" :counselorId="counselorId" @timeChange="handleAppointmentTimeChange"
							@intervalChange="handleAppointmentIntervalChange" @confirm="handleAppointmentConfirm"
							@dateChange="handleAppointmentDateChange" />

						<!-- 添加"不在当前时间表内下单后联系客服预约时间"选项 -->
						<view class="contact-service-option">
							<view :class="['radio-item', { active: appointmentForm.contactService }]" @click="selectContactService">
								<text class="radio-text">不在当前时间表内，下单后联系客服预约时间</text>
								<uni-icons :type="appointmentForm.contactService ? 'checkbox-filled' : 'circle'"
									:color="appointmentForm.contactService ? '#20a3f3' : '#ccc'" size="18" />
							</view>
						</view>
					</view>



					<!-- 支付方式 -->
					<view class="form-section">
						<view class="section-title">支付方式</view>
						<view class="inline-radio-group">
							<view v-for="item in paymentMethods" :key="item.value"
								:class="['inline-radio-item', { active: appointmentForm.paymentMethod === item.value }]"
								@click="selectPaymentMethod(item.value)">
								<view class="payment-radio-icon" :class="{ active: appointmentForm.paymentMethod === item.value }">
								</view>
								<text class="inline-radio-text">{{ item.label }}</text>
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="popup-footer">
					<view class="footer-buttons">
						<view class="duration-price-info">
							<view v-if="totalAmount > 0" class="price-main">
								<text class="currency-symbol">¥</text>
								<text class="price-integer">{{ totalAmount }}</text>
							</view>
							<text class="price-text" v-else style="color: #999;">-</text>
							<text class="duration-text">{{ calculatedDurationText }}</text>
						</view>
						<button class="confirm-btn" @click="confirmAppointment">立即支付</button>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 咨询师排期弹框 -->
		<uni-popup ref="schedulePopup" type="bottom" :mask-click="true">
			<view class="schedule-popup">
				<view class="popup-header">
					<view class="popup-title">咨询师排期</view>
					<view class="popup-close" @click="closeSchedulePopup">
						<uni-icons type="close" size="20" color="#666"></uni-icons>
					</view>
				</view>

				<view class="schedule-content">
					<view class="schedule-info" v-if="scheduleInfo">
						<view class="info-item">
							<text class="label">工作时间：</text>
							<text class="value">{{ scheduleInfo.workingHours || '周一至周日 9:00-21:00' }}</text>
						</view>
						<view class="info-item">
							<text class="label">预约提前：</text>
							<text class="value">{{ scheduleInfo.advanceBooking || '至少提前2小时预约' }}</text>
						</view>
						<view class="info-item">
							<text class="label">咨询时长：</text>
							<text class="value">{{ scheduleInfo.sessionDuration || '50分钟/次' }}</text>
						</view>
					</view>

					<view class="schedule-calendar">
						<view class="calendar-title">本周排期</view>
						<view class="week-schedule" v-if="weeklySchedule.length > 0">
							<view class="day-schedule" v-for="day in weeklySchedule" :key="day.date">
								<view class="day-header">
									<text class="day-name">{{ day.dayName }}</text>
									<text class="day-date">{{ day.shortDate }}</text>
								</view>
								<view class="time-slots">
									<view v-if="day.slots.length === 0" class="no-slots">休息</view>
									<view v-else class="slot-list">
										<text v-for="slot in day.slots" :key="slot.id" :class="['time-slot', slot.status]">
											{{ slot.time }}
										</text>
									</view>
								</view>
							</view>
						</view>
						<view v-else class="loading-schedule">
							<text>加载排期中...</text>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, nextTick, computed } from "vue";
import { onLoad, onPageScroll } from "@dcloudio/uni-app";
// getCounselorTime 导入已移除，现在由EnhancedTimeTable组件处理
import { addFavorite, removeFavorite, checkFavorite, FAVORITE_TYPES } from "@/api/favorite.js";
import { useUserStore } from "@/stores/user";
import { createConversation } from "@/api/message.js";
import { getConsultantDetail, getConsultantReviews, getReviewStatistics } from "@/api/classification.js";
import { getStore } from "@/api/store.js";
import { useDict } from "@/utils/index.js";
import EnhancedTimeTable from "@/components/EnhancedTimeTable/EnhancedTimeTable.vue";
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue';
import ReviewItem from '@/components/ReviewItem/ReviewItem.vue';
import InfoModal from '@/components/InfoModal/InfoModal.vue';
import { createConsultantOrder } from "@/api/payment.js";
import { requestWxPayment } from "@/utils/payment.js";
import { getCounselorFormattedTimeSlots } from "@/api/common.js";
import { getConsultantSchedule } from "@/api/consultant.js";

const userStore = useUserStore();

// Tab navigation data
const tabs = ref([
	{ name: '个人简介' },
	{ name: '擅长流派' },
	{ name: '专业背景' },
	{ name: '所属门店' },
	{ name: '咨询评价' }
]);
const activeTab = ref(0);

// Tab navigation methods
const scrollToSection = (index) => {
	// 设置用户点击标记，防止滚动时自动切换tab
	isUserClickingTab.value = true;
	activeTab.value = index;

	// 使用uni.pageScrollTo进行页面滚动
	uni.createSelectorQuery()
		.select(`#section-${index}`)
		.boundingClientRect((rect) => {
			if (rect) {
				// 获取页面滚动位置
				uni.createSelectorQuery()
					.selectViewport()
					.scrollOffset((scrollRes) => {
						// 计算目标滚动位置：当前滚动位置 + 元素相对视口位置 - tab高度 - 间距
						const targetScrollTop = scrollRes.scrollTop + rect.top - 38; // 80rpx为tab高度和间距
						uni.pageScrollTo({
							scrollTop: Math.max(0, targetScrollTop), // 确保不小于0
							duration: 300,
							success: () => {
								// 滚动完成后重置点击标记
								setTimeout(() => {
									isUserClickingTab.value = false;
								}, 100);
							},
							fail: () => {
								// 滚动失败也要重置标记
								setTimeout(() => {
									isUserClickingTab.value = false;
								}, 100);
							}
						});
					})
					.exec();
			}
		})
		.exec();
};

// 监听页面滚动，更新活跃tab（简化版本）
const handlePageScroll = () => {
	// 只有在用户没有主动点击tab时才自动更新活跃tab
	if (isUserClickingTab.value) return;

	// 获取所有section的位置信息，更新活跃tab
	uni.createSelectorQuery()
		.selectAll('.section-content')
		.boundingClientRect((rects) => {
			if (rects && rects.length > 0) {
				const tabOffset = 80; // tab高度和间距的偏移量

				// 找到当前滚动位置对应的section
				let newActiveTab = 0;
				for (let i = 0; i < rects.length; i++) {
					const rect = rects[i];
					// 如果section的顶部在视口上方不超过偏移量，则认为是当前激活的section
					if (rect.top <= tabOffset) {
						newActiveTab = i;
					} else {
						break;
					}
				}

				// 更新活跃tab
				if (activeTab.value !== newActiveTab) {
					activeTab.value = newActiveTab;
				}
			}
		})
		.exec();
};

// 添加用户点击tab的标记
const isUserClickingTab = ref(false);

// 注册页面滚动监听
onPageScroll(handlePageScroll);

// 咨询师详情数据
const counselorDetail = ref({});

// 教育经历步骤数据
const educationSteps = ref([]);
// 培训经历步骤数据
const trainingSteps = ref([]);

// 字典数据
const psy_consultant_level = ref([]);

// 评价相关数据
const reviewStatistics = ref({
	avgRating: 0,
	totalCount: 0,
	distribution: {}
});
const recentReviews = ref([]);

// 门店信息
const storeInfo = ref({
	name: '',
	branchName: '',
	phone: '',
	address: '',
	longitude: 0,
	latitude: 0,
	mapName: '',
	mapAddress: ''
});

// 从本地存储获取字典数据
const getStoredDict = () => {
	try {
		const storedData = uni.getStorageSync('APP_DICT_DATA');
		if (storedData) {
			const { data, timestamp } = JSON.parse(storedData);
			// 检查是否在24小时有效期内
			if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
				return data;
			}
		}
	} catch (e) {
		console.error('Error reading from storage:', e);
	}
	return null;
};

// 获取咨询师等级字典
const getPsyConsultantLevel = async () => {
	try {
		// 先尝试从缓存获取
		const storedDict = getStoredDict();
		if (storedDict?.psy_consultant_level) {
			psy_consultant_level.value = storedDict.psy_consultant_level;
			return;
		}

		// 缓存不存在或已过期，重新请求
		const { psy_consultant_level: dictData } = await useDict("psy_consultant_level");
		psy_consultant_level.value = dictData || [];
	} catch (error) {
		console.error('获取咨询师等级字典失败:', error);
		psy_consultant_level.value = [];
	}
};

// 处理教育经历数据为steps格式
const formatEducationSteps = (educations) => {
	if (!educations) return [];
	return educations.map(edu => ({
		title: `${edu.school} ${edu.major}`,
		desc: `${edu.startYear} - ${edu.endYear} | ${edu.degree}`
	}));
};

// 处理培训经历数据为steps格式
const formatTrainingSteps = (trainings) => {
	if (!trainings) return [];

	return trainings.map(training => {
		// 构建时间段显示
		let dateRange = '';
		if (training.startDate || training.endDate) {
			const start = training.startDate ? formatTrainingDate(training.startDate) : '未知';
			const end = training.endDate ? formatTrainingDate(training.endDate) : '至今';
			dateRange = `${start} - ${end}`;
		}

		// 构建详细信息数组
		const details = [];
		if (dateRange) details.push(dateRange);
		if (training.hours) details.push(`${training.hours}学时`);
		if (training.organizer) details.push(training.organizer);

		return {
			title: training.name || '',
			desc: details?.length > 0 ? details.join(' | ') : ''
		};
	}).filter(item => item.title); // 过滤掉没有名称的培训记录
};

// 培训经历日期格式化 - 显示年月日
const formatTrainingDate = (dateString) => {
	if (!dateString) return '';
	try {
		let date;

		// 处理 ISO 8601 格式（如：2018-03-31T00:00:00.000+08:00）
		if (dateString.includes('T')) {
			// 直接使用 Date 构造函数，它能正确解析 ISO 8601 格式
			date = new Date(dateString);
		}
		// 处理其他格式
		else {
			let formattedDateStr = dateString;

			// 如果是 "yyyy-MM-dd HH:mm:ss" 格式，转换为 "yyyy/MM/dd HH:mm:ss"
			if (dateString.includes(' ') && dateString.includes('-')) {
				formattedDateStr = dateString.replace(/-/g, '/');
			}
			// 如果只是 "yyyy-MM-dd" 格式，转换为 "yyyy/MM/dd"
			else if (dateString.includes('-') && !dateString.includes(' ')) {
				formattedDateStr = dateString.replace(/-/g, '/');
			}

			date = new Date(formattedDateStr);
		}

		if (isNaN(date.getTime())) {
			console.warn('Invalid date format:', dateString);
			return dateString; // 返回原始字符串作为备用
		}

		return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
	} catch (error) {
		console.error('日期格式化错误:', error);
		return dateString; // 返回原始字符串作为备用
	}
};

// 日期格式化 - iOS 兼容版本（用于其他地方）
const formatDate = (dateString) => {
	if (!dateString) return '';
	try {
		// 将 "2025-07-27 12:16:10" 格式转换为 iOS 兼容的格式
		let formattedDateStr = dateString

		// 如果是 "yyyy-MM-dd HH:mm:ss" 格式，转换为 "yyyy/MM/dd HH:mm:ss"
		if (dateString.includes(' ') && dateString.includes('-')) {
			formattedDateStr = dateString.replace(/-/g, '/')
		}
		// 如果只是 "yyyy-MM-dd" 格式，转换为 "yyyy/MM/dd"
		else if (dateString.includes('-') && !dateString.includes(' ')) {
			formattedDateStr = dateString.replace(/-/g, '/')
		}

		const date = new Date(formattedDateStr);
		if (isNaN(date.getTime())) {
			console.warn('Invalid date format:', dateString)
			return dateString; // 返回原始字符串作为备用
		}
		return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}`;
	} catch (error) {
		console.error('日期格式化错误:', error);
		return dateString; // 返回原始字符串作为备用
	}
};

onLoad(async (options) => {
	try {
		// 获取字典数据
		await getPsyConsultantLevel();
		await loadConsultationTypes();
		counselorId.value = options.id;
		// 先检查收藏状态
		const checkRes = await checkFavorite({
			targetType: FAVORITE_TYPES.CONSULTANT,
			targetId: options.id
		});
		if (checkRes.data) {
			nextTick(() => {
				bottomOptions.value[0].icon = "star-filled";
				counselorDetail.value.favoriteId = checkRes.data.favoriteId;
			});
		}

		const res = await getConsultantDetail(options.id);
		if (res.code === 200) {
			counselorDetail.value = res.data;
			// 处理咨询经验数据
			consultingExperience.value = [
				{
					id: 1,
					timeTitle: "从业时长",
					time: new Date().getFullYear() - parseInt(counselorDetail.value.startYear),
					unit: "年",
				},
				{
					id: 2,
					timeTitle: "咨询经验",
					time: counselorDetail.value.serviceHours || "5500",
					unit: "小时",
				},
				{
					id: 3,
					timeTitle: "服务人次",
					time: counselorDetail.value.serviceCount || "220",
					unit: "人次",
				}
			];

			// 处理擅长领域数据
			if (counselorDetail.value.expertises?.length > 0) {
				areasExpertise.value = counselorDetail.value.expertises.map(expertise => {
					// 处理子项
					const childrenItems = expertise.children?.map(child => ({
						id: child.id,
						name: child.typeName,
						remark: child.remark
					})) || [];

					return {
						id: expertise.id,
						name: expertise.typeName,
						icon: expertise.icon || getDefaultIcon(expertise.typeName),
						children: childrenItems
					};
				});
			}

			// 处理教育经历数据
			educationSteps.value = formatEducationSteps(counselorDetail.value.educations);
			// 处理培训经历数据
			trainingSteps.value = formatTrainingSteps(counselorDetail.value.trainings);
			// 注意：时间数据现在由EnhancedTimeTable组件自动获取

			// 加载评价数据
			await loadReviewData();

			// 加载门店信息
			await getStoreInfo();

		}
	} catch (error) {
		console.error('获取咨询师详情失败:', error);
		uni.showToast({
			title: '获取咨询师详情失败',
			icon: 'none'
		});
	}
});

// 响应式数据
const guaranteeList = ref([
	{
		id: 1,
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%A5%E9%80%89%E4%BE%9B%E5%BA%94%E9%93%BE-01.png",
		title: "严选优质咨询师",
		info: "咨询师经过系统成熟的理论学习和技术培训，拥有丰富临床经验，资料真实可靠",
	},
	{
		id: 2,
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E8%81%94%E7%9B%9F%E9%93%BE_%E9%9A%90%E7%A7%81%E4%BF%9D%E6%8A%A4%E5%A4%87%E4%BB%BD.png",
		title: "全程隐私保护",
		info: "充分保障隐私和信息安全",
	},
	{
		id: 3,
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%92%A8%E8%AF%A2%E4%BC%A6%E7%90%86.png",
		title: "咨询伦理保障",
		info: "严守《中国心理学会临床与咨询心理学工作伦理守则》，持续审查和监督",
	},
	{
		id: 4,
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%85%8D%E8%B4%B9%E5%8F%96%E6%B6%88.png",
		title: "免费修改/取消",
		info: "咨询前一天中午12点前，可免费取消预约或修改时间，每个订单最多可修改3次",
	},
]);

const consultingExperience = ref([
	{
		id: 1,
		timeTitle: "从业时长",
		time: "10",
		unit: "年",
	},
	{
		id: 2,
		timeTitle: "咨询经验",
		time: "5500",
		unit: "+小时",
	},
	{
		id: 3,
		timeTitle: "1对1督导",
		time: "220",
		unit: "+小时",
	},
	{
		id: 4,
		timeTitle: "团体督导",
		time: "380",
		unit: "+小时",
	},
	{
		id: 5,
		timeTitle: "个人体验",
		time: "160",
		unit: "+小时",
	},
]);

const areasExpertise = ref([]);

// 注意：dateInfo和timeInfo现在由EnhancedTimeTable组件内部管理

// 预约弹框相关数据
const appointmentPopup = ref(null);

// 排期弹框相关数据
const schedulePopup = ref(null);
const scheduleInfo = ref(null);
const weeklySchedule = ref([]);
const appointmentForm = ref({
	consultationType: '', // 咨询方式
	appointmentDate: '', // 预约日期
	paymentMethod: 'other', // 支付方式，默认其他支付
	selectedTimes: [], // 选择的时间段
	duration: 60, // 咨询时长，默认60分钟
	contactService: false // 是否选择联系客服预约时间
});

// 咨询方式选项（从字典获取）
const consultationTypes = ref([]);

// 支付方式选项
const paymentMethods = ref([
	{ label: '微信支付', value: 'wechat' },
	{ label: '其他支付', value: 'other' }
]);

// 咨询时长现在是动态计算的，不再需要手动选择选项

// 计算咨询时长的计算属性（基于选择的时间区间）
const calculatedDuration = computed(() => {
	// 如果选择了联系客服，返回默认1小时
	if (appointmentForm.value.contactService) {
		return 1;
	}

	// 从时间表组件获取时间区间信息
	if (appointmentTimeTableRef.value) {
		const intervals = appointmentTimeTableRef.value.getTimeIntervals();
		if (intervals && intervals?.length > 0) {
			// 计算所有区间的总时长（duration单位为分钟，转换为小时）
			return intervals.reduce((total, interval) => total + (interval.duration || 0), 0) / 60;
		}
	}

	// 如果没有选择时间，返回0
	return 0;
});

// 咨询时长显示文本
const calculatedDurationText = computed(() => {
	if (appointmentForm.value.contactService) {
		return '联系客服预约';
	}

	const hours = calculatedDuration.value;
	if (hours === 0) {
		return '请选择时长';
	}
	return hours === 1 ? '时长: 1h' : `${hours}h`;
});

// 计算价格的计算属性
const totalAmount = computed(() => {
	if (!counselorDetail.value.price) {
		return 0;
	}

	// 使用计算出的咨询时长
	const selectedHours = calculatedDuration.value;

	// 计算总价 = 基础价格 × 小时数
	return counselorDetail.value.price * selectedHours;
});

// 时间选择相关（复用预约页面的逻辑）
const old = ref({ scrollTop: 0 });
const isInfo = ref(true);
const showButton = ref(false);
const measuring = ref(false);
const indicatorDots = ref(true);
const autoplay = ref(true);
const interval = ref(2000);
const duration = ref(500);
const infoPopupTitle = ref("");
const isActive = ref(true);
const isCollection = ref(false);

const popup = ref();
const infoPopup = ref();
const dataInfoSimplify = ref([]);
const bottomOptions = ref([
	{
		icon: "star",
		text: "收藏",
		infoBackgroundColor: "#007aff",
		infoColor: "#f5f5f5",
		isStar: false,
	},
	{
		icon: "chat",
		text: "客服",
	},
	// {
	// 	icon: "cart",
	// 	text: "购物车",
	// 	info: 2,
	// },
]);
const counselorId = ref(null);

// 增强版时间表组件引用
const appointmentTimeTableRef = ref(null);

const customButtonGroup = ref([
	// {
	// 	text: "立即私信",
	// 	backgroundColor: "linear-gradient(90deg, #1E83FF, #0053B8)",
	// 	color: "#fff",
	// },
	{
		text: "预约咨询",
		backgroundColor: "linear-gradient(90deg, #1E83FF, #EF1224)",
		color: "#fff",
	},
]);

const stylePopup = ref();
const currentStyle = ref({});

// getTimeList函数已移除，时间数据现在由EnhancedTimeTable组件自动获取

// 加载咨询方式字典
const loadConsultationTypes = async () => {
	try {
		const res = await useDict('sys_consult_type');
		if (res && res.sys_consult_type) {
			consultationTypes.value = res.sys_consult_type.map(item => ({
				label: item.label,
				value: item.value
			}));
		}
	} catch (error) {
		console.error('加载咨询方式字典失败:', error);
		// 使用默认值
		consultationTypes.value = [
			{ label: '语音视频', value: 'online' },
			{ label: '到店面谈', value: 'offline' }
		];
	}
};
// 方法
const handleGuarantee = async () => {
	await nextTick();
	popup.value.open();
};
const openInfoPopup = (val) => {
	infoPopupTitle.value = val;
	nextTick(() => {
		console.log(infoPopup.value);
		infoPopup.value.open();
	});
};
const scroll = (e) => (old.value.scrollTop = e.detail.scrollTop);

// 初始化检测逻辑
const initDetection = async () => {
	measuring.value = true;
	isInfo.value = true;
	await nextTick();
	const foldedHeight = await getContentHeight();
	isInfo.value = false;
	await nextTick();
	const realHeight = await getContentHeight();
	isInfo.value = foldedHeight < realHeight;
	showButton.value = foldedHeight === realHeight;
	measuring.value = false;
	await nextTick();
};

const getContentHeight = () => {
	return new Promise((resolve) => {
		nextTick(() => {
			const query = uni.createSelectorQuery();
			query
				.select("#info-content")
				.boundingClientRect((res) => {
					resolve(res?.height || 0);
				})
				.exec();
		});
	});
};

const handleMakeAppointment = () => {
	// 咨询时长现在是根据选择的时间区间自动计算的，不需要设置默认值
	nextTick(() => {
		appointmentPopup.value.open();
	});
};

// 关闭预约弹框
const closeAppointmentPopup = () => {
	appointmentPopup.value.close();
	// 重置表单
	appointmentForm.value = {
		consultationType: '',
		appointmentDate: '',
		paymentMethod: 'wechat',
		selectedTimes: [], // 移除duration字段，因为现在是自动计算的
		contactService: false // 重置联系客服选项
	};
	selectTime.value = [];
	// 清空时间表选择
	if (appointmentTimeTableRef.value) {
		appointmentTimeTableRef.value.clearSelection();
	}
};

// 选择咨询方式
const selectConsultationType = (type) => {
	appointmentForm.value.consultationType = type;
};

// 选择支付方式
const selectPaymentMethod = (method) => {
	appointmentForm.value.paymentMethod = method;
};

// 选择联系客服预约时间
const selectContactService = () => {
	appointmentForm.value.contactService = !appointmentForm.value.contactService;
	// 如果选择了联系客服，清空时间表选择
	if (appointmentForm.value.contactService && appointmentTimeTableRef.value) {
		appointmentTimeTableRef.value.clearSelection();
	}
};

// 注意：咨询时长现在是根据选择的时间区间自动计算的，不再需要手动选择



// 预约弹框中的时间表事件处理
const handleAppointmentTimeChange = (selectedTimes) => {
	appointmentForm.value.selectedTimes = selectedTimes;
	// 如果选择了时间，取消联系客服选项
	if (selectedTimes && selectedTimes.length > 0) {
		appointmentForm.value.contactService = false;
	}
	console.log('预约弹框时间选择变化:', selectedTimes);
	console.log('计算的咨询时长:', calculatedDuration.value, '小时');
};

const handleAppointmentIntervalChange = (intervals) => {
	console.log('预约弹框时间区间变化:', intervals);
	appointmentForm.value.timeIntervals = intervals;
	// 时间区间变化时，咨询时长会自动重新计算
	if (intervals && intervals?.length > 0) {
		const totalHours = intervals.reduce((total, interval) => total + (interval.duration || 60), 0) / 60;
		console.log('根据时间区间计算的总时长:', totalHours, '小时');
	}
};

const handleAppointmentConfirm = (data) => {
	console.log('预约时间确认:', data);
	appointmentForm.value.selectedTimes = data.selectTime;
	appointmentForm.value.timeIntervals = data.timeIntervals;
	// 咨询时长会通过计算属性自动更新
	console.log('计算的咨询时长:', calculatedDuration.value, '小时');
};

const handleAppointmentDateChange = (dateItem, index) => {
	console.log('预约日期选择变化:', dateItem, index);
};



// 处理时间段选择（复用预约页面的逻辑）
const handleHour = (hour) => {
	// 检查时间槽是否已禁用
	if (hour.timeStatus === '已约满' || hour.timeStatus === '已过期') {
		uni.showToast({
			title: "该时段不可选",
			icon: "none",
			duration: 2000
		});
		return;
	}

	const fullTime = hour.fullDate + ' ' + hour.time;

	// 切换选择状态
	const index = selectTime.value.indexOf(fullTime);
	if (index === -1) {
		selectTime.value.push(fullTime);
	} else {
		selectTime.value.splice(index, 1);
	}
};

// 确认预约
const confirmAppointment = async () => {
	// 验证表单
	if (!appointmentForm.value.consultationType) {
		uni.showToast({
			title: '请选择咨询方式',
			icon: 'none'
		});
		return;
	}

	// 获取选择的时间（优先从预约弹框的时间表获取）
	let selectedTimes = [];
	if (appointmentTimeTableRef.value) {
		selectedTimes = appointmentTimeTableRef.value.getSelectedTimes();
	}

	// 如果没有选择联系客服，则必须选择时间
	if (!appointmentForm.value.contactService && selectedTimes?.length === 0) {
		uni.showToast({
			title: '请选择预约时间或选择联系客服预约',
			icon: 'none'
		});
		return;
	}

	// 如果选择了联系客服，则不需要具体时间
	if (appointmentForm.value.contactService) {
		selectedTimes = ['contact_service']; // 使用特殊标识
	}

	// 验证计算出的时长（联系客服时跳过此验证）
	const selectedHours = calculatedDuration.value;
	if (!appointmentForm.value.contactService && selectedHours <= 0) {
		uni.showToast({
			title: '请选择有效的咨询时间',
			icon: 'none'
		});
		return;
	}

	try {
		uni.showLoading({
			title: '创建订单中...'
		});

		// 构建订单数据
		const selectedHours = calculatedDuration.value; // 使用动态计算的咨询时长
		const orderData = {
			consultantId: counselorId.value,
			serviceId: 1, // 默认服务ID，可根据实际情况调整
			appointmentTime: selectedTimes[0], // 取第一个选择的时间
			duration: selectedHours * 60, // 转换为分钟
			paymentAmount: totalAmount.value,
			originalPrice: totalAmount.value,
			consultationType: appointmentForm.value.consultationType,
			// 添加选择的所有时间段信息
			selectedTimeSlots: selectedTimes,
			calculatedDuration: selectedHours // 记录计算出的时长
		};

		// 创建咨询订单
		const createResult = await createConsultantOrder(orderData);

		if (createResult.code !== 200) {
			throw new Error(createResult.msg || '创建订单失败');
		}

		const orderNo = createResult.data.orderNo;

		uni.hideLoading();
		uni.showLoading({
			title: '发起支付中...'
		});

		// 发起支付
		const payResult = await requestWxPayment(orderNo);

		uni.hideLoading();

		if (payResult.success) {
			// 支付成功
			uni.showToast({
				title: '支付成功',
				icon: 'success'
			});

			// 关闭预约弹框
			closeAppointmentPopup();

			// 可以跳转到订单详情或我的咨询页面
			setTimeout(() => {
				uni.navigateTo({
					url: `/pages/consultation/order-detail/index?orderNo=${orderNo}`
				});
			}, 1500);
		}

	} catch (error) {
		uni.hideLoading();
		console.error('预约支付失败:', error);

		let errorMessage = '预约失败，请重试';
		if (error.canceled) {
			errorMessage = '用户取消支付';
		} else if (error.message) {
			errorMessage = error.message;
		}

		uni.showToast({
			title: errorMessage,
			icon: 'none'
		});
	}
};

// handleSelectTime函数已移除，日期选择现在由EnhancedTimeTable组件内部处理





const handleCollection = () => (isCollection.value = !isCollection.value);
const handleConsultNow = () => {
	uni.navigateTo({ url: "/pages/order/order-info/order-info" });
};
const onGetUserInfo = () => { };
const toMap = () => {
	if (!storeInfo.value) return;
	uni.openLocation({
		longitude: Number(storeInfo.value.longitude),
		latitude: Number(storeInfo.value.latitude),
		name: storeInfo.value.mapName || `${storeInfo.value.name}${storeInfo.value.branchName}`,
		address: storeInfo.value.mapAddress || storeInfo.value.address,
		fail: (err) => {
			console.log("导航失败:", err);
			uni.showToast({
				title: '导航失败',
				icon: 'none'
			});
		},
	});
};

// 跳转到门店环境页面
const toStoreEnvironment = () => {
	uni.navigateTo({
		url: '/pages/store/store-environment/index'
	});
};

// 显示完整排期
const showFullSchedule = async () => {
	try {
		uni.showLoading({
			title: '加载排期中...'
		});

		// 获取咨询师排期信息
		await loadScheduleInfo();

		// 获取本周排期数据
		await loadWeeklySchedule();

		// 打开排期弹框
		schedulePopup.value.open();

		uni.hideLoading();
	} catch (error) {
		console.error('加载排期失败:', error);
		uni.hideLoading();
		uni.showToast({
			title: '加载排期失败',
			icon: 'none'
		});
	}
};

// 加载排期信息
const loadScheduleInfo = async () => {
	try {
		// 这里可以调用获取咨询师排期设置的API
		// const res = await getConsultantSchedule({ consultantId: counselorId.value });

		// 暂时使用模拟数据
		scheduleInfo.value = {
			workingHours: '周一至周日 9:00-21:00',
			advanceBooking: '至少提前2小时预约',
			sessionDuration: '50分钟/次'
		};
	} catch (error) {
		console.error('加载排期信息失败:', error);
	}
};

// 加载本周排期数据
const loadWeeklySchedule = async () => {
	try {
		const startDate = new Date();
		const endDate = new Date();
		endDate.setDate(startDate.getDate() + 6); // 获取7天的数据

		// 格式化日期为 YYYY-MM-DD
		const formatDate = (date) => {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		};

		// 获取咨询师时间表数据
		const res = await getCounselorFormattedTimeSlots(
			counselorId.value,
			formatDate(startDate),
			formatDate(endDate)
		);

		if (res.code === 200 && res.data) {
			// 处理时间表数据，按日期分组
			weeklySchedule.value = processWeeklyScheduleData(res.data);
		} else {
			weeklySchedule.value = [];
		}
	} catch (error) {
		console.error('加载本周排期失败:', error);
		weeklySchedule.value = [];
	}
};

// 处理本周排期数据
const processWeeklyScheduleData = (timeData) => {
	const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
	const schedule = [];

	// 生成7天的日期
	for (let i = 0; i < 7; i++) {
		const date = new Date();
		date.setDate(date.getDate() + i);

		const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
		const dayName = weekDays[date.getDay()];
		const shortDate = `${date.getMonth() + 1}/${date.getDate()}`;

		// 查找该日期的时间段
		const daySlots = [];
		if (timeData && timeData.length > 0) {
			timeData.forEach(period => {
				if (period.slots) {
					period.slots.forEach(slot => {
						if (slot.fullDate === dateStr) {
							daySlots.push({
								id: slot.id,
								time: slot.time,
								status: slot.timeStatus === '已约满' ? 'booked' :
									slot.isDisabled ? 'disabled' : 'available'
							});
						}
					});
				}
			});
		}

		schedule.push({
			date: dateStr,
			dayName,
			shortDate,
			slots: daySlots
		});
	}

	return schedule;
};

// 关闭排期弹框
const closeSchedulePopup = () => {
	schedulePopup.value.close();
};

// 拨打电话 - 模仿首页实现
const makePhoneCall = (phoneNumber) => {
	if (!phoneNumber) return;
	uni.makePhoneCall({
		phoneNumber: phoneNumber,
		success: () => {
			console.log("拨打电话成功！");
		},
		fail: (err) => {
			console.log("拨打电话失败！", err);
			uni.showToast({
				title: '拨打电话失败',
				icon: 'none'
			});
		}
	});
};

// 加载评价数据
const loadReviewData = async () => {
	try {
		// 加载评价统计
		const statsRes = await getReviewStatistics(counselorId.value);
		if (statsRes.code === 200) {
			reviewStatistics.value = statsRes.data || {};
		} else {
			// 如果API调用失败，使用测试数据
			reviewStatistics.value = {
				avgRating: 4.8,
				totalCount: 156,
				distribution: {
					'5': 120,
					'4': 25,
					'3': 8,
					'2': 2,
					'1': 1
				}
			};
		}

		// 加载最新评价
		const reviewsRes = await getConsultantReviews(counselorId.value);
		if (reviewsRes.code === 200) {
			recentReviews.value = (reviewsRes.data || []).slice(0, 3);
		} else {
			// 如果API调用失败，使用测试数据
			recentReviews.value = [
				{
					id: 1,
					username: '张女士',
					rating: 5,
					content: '这是我们第二次咨询，随着交谈的深入，我们开始触碰到一些根本的问题，我相信老师对我们有了一个全面客观的认识，张老师看得比较清晰，期待她能帮我老婆走出去，我到更美好的自己。',
					reviewTime: '2025/07/08',
					consultType: '情感焦点'
				},
				{
					id: 2,
					username: '李先生',
					rating: 5,
					content: '老师很专业，能够准确理解我的问题，给出了很好的建议和指导。',
					reviewTime: '2025/07/07',
					consultType: '情感焦点'
				}
			];
		}
	} catch (error) {
		console.error('加载评价数据失败:', error);
		// 使用测试数据
		reviewStatistics.value = {
			avgRating: 4.8,
			totalCount: 156,
			distribution: {
				'5': 120,
				'4': 25,
				'3': 8,
				'2': 2,
				'1': 1
			}
		};
		recentReviews.value = [
			{
				id: 1,
				username: '张女士',
				rating: 5,
				content: '这是我们第二次咨询，随着交谈的深入，我们开始触碰到一些根本的问题，我相信老师对我们有了一个全面客观的认识，张老师看得比较清晰，期待她能帮我老婆走出去，我到更美好的自己。',
				reviewTime: '2025/07/08',
				consultType: '情感焦点'
			},
			{
				id: 2,
				username: '李先生',
				rating: 5,
				content: '老师很专业，能够准确理解我的问题，给出了很好的建议和指导。',
				reviewTime: '2025/07/07',
				consultType: '情感焦点'
			}
		];
	}
};

// 跳转到评价页面
const goToReviewsPage = () => {
	uni.navigateTo({
		url: `/pages/classification/counselor-reviews/index?consultantId=${counselorId.value}`
	});
};

// 获取门店信息
const getStoreInfo = async () => {
	try {
		// 从咨询师详情中获取门店ID，如果没有则使用默认门店ID 1
		const storeId = counselorDetail.value?.storeId || 1;
		const res = await getStore(storeId);
		if (res.code === 200) {
			storeInfo.value = {
				...res.data.store,
				contacts: res.data.contacts,
				businessHours: res.data.businessHours,
				businessDays: res.data.businessDays
			};
		} else {
			// 如果获取失败，使用默认门店信息
			storeInfo.value = {
				name: '熙桓心理',
				branchName: '丽泽天街店',
				phone: '134508664532',
				address: '北京丰台区平安幸福中心A座3617（地铁14号线C口）',
				longitude: 116.322074,
				latitude: 39.866596,
				mapName: '熙桓心理丽泽天街店',
				mapAddress: '北京市丰台区丽泽路平安幸福A座36层3617（地铁14号线东管头站C口）'
			};
		}
	} catch (error) {
		console.error('获取门店信息失败:', error);
		// 使用默认门店信息
		storeInfo.value = {
			name: '熙桓心理',
			branchName: '丽泽天街店',
			phone: '134508664532',
			address: '北京丰台区平安幸福中心A座3617（地铁14号线C口）',
			longitude: 116.322074,
			latitude: 39.866596,
			mapName: '熙桓心理丽泽天街店',
			mapAddress: '北京市丰台区丽泽路平安幸福A座36层3617（地铁14号线东管头站C口）'
		};
	}
};







const onClick = async (e) => {
	if (e.index == 0) {
		// 点击收藏按钮
		try {
			if (e.content.icon == "star-filled") {
				e.content.icon = "star";
				await removeFavorite(counselorDetail.value.favoriteId);
				counselorDetail.value.favoriteId = null; // 清除收藏ID
				uni.showToast({
					title: '取消收藏成功',
					icon: 'none'
				});
			} else if (e.content.icon == "star") {
				e.content.icon = "star-filled";
				const { data } = await addFavorite({
					targetType: FAVORITE_TYPES.CONSULTANT,
					targetId: counselorId.value,
					targetTitle: counselorDetail.value.name,
					targetImage: counselorDetail.value.imageUrl
				});
				if (data?.favoriteId) {
					counselorDetail.value.favoriteId = data.favoriteId; // 保存返回的收藏ID
				} else {
					console.error('收藏成功但未返回ID');
				}
				uni.showToast({
					title: '收藏成功',
					icon: 'none'
				});
			}
			e.content.isStar = !e.content.isStar;
		} catch (error) {
			console.error('收藏操作失败:', error);
			uni.showToast({
				title: '操作失败,请重试',
				icon: 'none'
			});
		}
	} else if (e.index == 1) {
		// 点击客服按钮
		// 固定客服ID为137
		const customerServiceId = "137";
		// 尝试创建会话（如果已存在会返回现有会话）
		try {
			const { data } = await createConversation(customerServiceId);
			const conversationId = data?.conversationId || customerServiceId;
			uni.navigateTo({
				url: `/pages/my/my-message/chat/chat?conversationId=${conversationId}&userId=${userStore.userId}&consultantId=${customerServiceId}&nickname=熙桓心理客服`
			});
		} catch (error) {
			console.error("创建会话失败", error);
			uni.showToast({
				title: "连接客服失败",
				icon: "none"
			});
		}
	}
};

const buttonClick = async (e) => {
	if (e.index == 0) {
		// 打开预约咨询弹框
		handleMakeAppointment();
	}
};

// 处理收藏事件
const handleFavorite = (favoriteData) => {
	counselorDetail.value.favorited = favoriteData.favorited
	counselorDetail.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
	console.log('联系客服')
}

// 处理分享事件
const handleShare = (shareConfig) => {
	console.log('分享配置:', shareConfig)
	uni.showToast({
		title: '转发成功',
		icon: 'success'
	})
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
	// 打开预约咨询弹框
	handleMakeAppointment()
}

// 获取默认图标
const getDefaultIcon = (typeName) => {
	const iconMap = {
		'情绪管理': 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%83%85%E7%BB%AA%E7%AE%A1%E7%90%86.png',
		'人际关系': 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%BA%BA%E9%99%85%E5%85%B3%E7%B3%BB.png',
		'个人成长': 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA%E6%88%90%E9%95%BF.png',
		// '婚恋情感': '../../../static/icon/婚恋情感.png',
		// '亲子教育': '../../../static/icon/亲子教育.png',
		// '职场发展': '../../../static/icon/职场发展.png'
	};
	return iconMap[typeName] || '../../../static/icon/其他.png';
};

const handleStyleClick = (style) => {
	infoPopupTitle.value = style.dictLabel;
	currentStyle.value = style;
	nextTick(() => {
		stylePopup.value.open();
	});
};

const parseStyleRemark = (remark) => {
	if (!remark) return [];

	const lines = remark.split('\n');
	const resultArray = [];

	lines.forEach(line => {
		const colonIndex = line.indexOf(':');
		if (colonIndex !== -1) {
			const title = line.substring(0, colonIndex);
			const content = line.substring(colonIndex + 1);

			resultArray.push({
				title: title,
				content: content
			});
		}
	});

	return resultArray;
};

// 格式化咨询风格内容为InfoModal组件所需的格式
const formatStyleContent = (remark) => {
	if (!remark) return [];

	const sections = parseStyleRemark(remark);
	return sections.map(section => ({
		title: section.title,
		content: section.content
	}));
};

// 获取定义说明内容
const getDefinitionContent = () => {
	return [{
		title: '定义说明',
		content: '工作对象为满18周岁及以上具有完全民事能力的成年人。个体通过与咨询师在专业且符合伦理的设置框架下，建立相互信任的咨询关系，利用心理咨询的科学理论和方法，帮助来访者在个体层面上增加对自我的认识，解决问题的能力以及进行个人的成长获得幸福感等。'
	}];
};

// 获取咨询师等级
const getPsy_consultant_level = (value) => {
	// 如果 value 为空或未定义，返回空字符串
	if (value === undefined || value === null || value === '') {
		return '';
	}

	// 如果字典数据还没有加载完成，返回空字符串
	if (!psy_consultant_level.value || !Array.isArray(psy_consultant_level.value)) {
		return '';
	}

	// 查找对应的字典项
	const found = psy_consultant_level.value.find((item) => item.dictValue == value);
	if (found?.dictLabel) {
		return found.dictLabel;
	}

	// 降级处理：使用默认映射
	const levelMap = {
		'0': '咨询助理',
		'1': '初级咨询师',
		'2': '中级咨询师',
		'3': '成熟咨询师',
		'4': '高级咨询师',
		'5': '资深咨询师',
		'6': '咨询督导'
	};

	// 转换为字符串进行匹配
	const valueStr = String(value);
	return levelMap[valueStr] || valueStr || '';
};

// 响应式的咨询师等级显示
const consultantLevelText = computed(() => {
	return getPsy_consultant_level(counselorDetail.value.personalTitle);
});

const getActualPrice = computed(() => {
	return counselorDetail.value.price || counselorDetail.value.consultationFee || counselorDetail.value.price;
});

const getOriginalPrice = computed(() => {
	return counselorDetail.value.originalPrice;
});

const getPriceUnit = computed(() => {
	return counselorDetail.value.priceUnit || '节';
});

const getPriceInteger = computed(() => {
	if (!getActualPrice.value) return '';
	return Math.floor(parseFloat(getActualPrice.value)).toString();
});

const getPriceDecimal = computed(() => {
	if (!getActualPrice.value) return '';
	const priceNum = parseFloat(getActualPrice.value);
	const decimal = priceNum - Math.floor(priceNum);
	if (decimal === 0) return '';
	// 保留两位小数，去掉末尾的0
	return decimal.toFixed(2).substring(2).replace(/0+$/, '');
});


</script>

<style scoped lang="scss">
.content {
	background-color: #f8f8fa;
	position: relative;
	padding-bottom: 100rpx;

	.avatar {
		width: 100%;
		height: 400rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.bg-img {
		width: 100%;
		height: 86rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.counselor-detail {
		padding: 32rpx;

		.radius-box {
			background-color: #fff;
			margin-bottom: 24rpx;
			padding: 32rpx;

			.title {
				font-size: 28rpx;
				font-weight: 700;
				position: relative;

				.max-title {
					margin-bottom: 24rpx;

					.beijing-time,
					.view-more,
					.describe {
						font-size: 24rpx;
						font-weight: 400;
						color: #6e737b;
					}

					.describe {
						margin-top: 20rpx;
					}

					.title-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
					}
				}

				.info-time {
					display: flex;
					justify-content: space-between;

					.detail-time {
						flex: 1;
						margin-right: 10rpx;

						&:last-child {
							margin-right: 0;
						}

						.detail-time-top {
							width: 100%;
							height: 160rpx;
							margin-bottom: 20rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							font-size: 28rpx;
							font-weight: 400;
							color: #8d8d8d;
						}

						.is-today {
							color: #52b5f9;
						}

						.detail-time-bottom,
						.full {
							width: 100%;
							height: 120rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							font-size: 28rpx;
							font-weight: 700;
							color: #52b5f9;
							background-color: #e9f7ff;
							border-radius: 20rpx;
						}

						.full {
							color: #d8d5d5;
							background-color: #f3f4f4;
						}
					}
				}

				.consult-store {
					width: 100%;
					background-color: #fff;
					border-radius: 20rpx;

					.store-info-container {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.store-logo {
							margin-right: 15rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							.logo-img {
								width: 100rpx;
								height: 100rpx;
							}
						}

						.store-details {
							flex: 1;
							display: flex;
							flex-direction: column;

							.store-name {
								font-size: 26rpx;
								font-weight: 700;
								color: #01020C;
								margin-bottom: 15rpx;
								cursor: pointer;
							}

							.store-phones {
								margin-bottom: 15rpx;

								.store-phone {
									font-size: 22rpx;
									color: #007AFF;
									margin-bottom: 8rpx;
									cursor: pointer;
									text-decoration: underline;
									display: inline-block;
									margin-right: 16rpx;

									&:last-child {
										margin-right: 0;
									}

									&:active {
										opacity: 0.6;
									}
								}
							}

							.store-address {
								font-size: 22rpx;
								color: #999;
								line-height: 1.4;
								cursor: pointer;
							}
						}

						.arrow-icon {
							width: 24rpx;
							height: 24rpx;

							image {
								width: 100%;
								height: 100%;
							}
						}
					}
				}

				.image-box {
					margin-top: 20rpx;
				}
			}

			.min-title {
				font-weight: 700;
				font-size: 24rpx;

				.genre {
					margin-bottom: 24rpx;
				}

				.info-content {
					margin-top: 20rpx;
					font-size: 24rpx;
					color: #8A8788;
					border-bottom: 2rpx solid #f7f8f9;
					line-height: 1.7;
					/* 设置最大高度2行，超过隐藏 */
					max-height: 7em;
					overflow: hidden;
					transition: max-height 0.3s;
					/* 添加过渡动画 */
				}

				.areas-expertise {
					width: 100%;
					display: flex;
					align-items: center;
					margin-top: 20rpx;

					.areas-expertise-icon {
						width: 100rpx;

						image {
							width: 100rpx;
						}
					}

					.areas-expertise-text {
						margin-left: 20rpx;
					}

					.text-top {
						font-size: 28rpx;
						margin-bottom: 20rpx;
					}

					.text-bottom {
						font-size: 26rpx;
						font-weight: 400;
					}
				}

				.education-experience {
					font-weight: 400;
					margin-top: 20rpx;

					:deep uni-text.uni-steps__column-title {
						color: #1f2229 !important;
						margin-bottom: 16rpx;
					}

					:deep uni-text.uni-steps__column-desc {
						margin-bottom: 16rpx;
					}
				}

				.individual-counseling,
				.specialty-genres {
					padding: 16rpx;
					background-color: #F7F7F7;
					border-radius: 8rpx;
					font-weight: 400;
					display: inline-block;
				}

				.author {
					text-align: end;
					font-size: 28rpx;
					font-weight: 400;
					color: #474b52;
				}

				.hide {
					max-height: none;
					transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.show-info {
					position: relative;
					width: 100%;
					text-align: center;
					color: #01a6fe;
					background-color: #fff;

					.box-transparent {
						width: 100%;
						height: 40rpx;
						position: absolute;
						top: -40rpx;
						left: 0;
						background: linear-gradient(to top, rgba(255, 255, 255), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
					}
				}
			}
		}

		.counselor-info {
			display: flex;
			justify-content: space-between;
			margin-bottom: 23rpx;

			.name {
				font-size: 40rpx;
				font-weight: 700;
				display: flex;
				align-items: center;
			}

			image {
				margin-left: 20rpx;
				width: 34rpx;
				height: 34rpx;
			}

			.qualification {
				padding: 0 5px 0 0;
				background-color: #fff4dc;
				color: #c66d29;
				font-size: 24rpx;
				display: flex;
				align-items: center;
				border-radius: 20rpx;
				font-weight: 700;
				transform: translateX(-5rpx);
			}
		}

		.qualifications,
		.consultation-method {
			font-size: 28rpx;
			color: #31343a;
			padding-top: 10rpx;

			.price-box {
				display: flex;
				flex-direction: column;
				align-items: flex-end;

				.price-main {
					display: flex;
					align-items: baseline;
					color: #A04571;

					.currency-symbol {
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					.price-integer {
						font-weight: bold;
						font-size: 36rpx;
					}

					.price-dot {
						font-size: 44rpx;
						font-weight: bold;
						margin: 0 1rpx;
					}

					.price-decimal {
						font-size: 28rpx;
						font-weight: bold;
					}

					.price-slash {
						font-size: 24rpx;
						margin: 0 2rpx;
					}

					.price-unit {
						font-size: 24rpx;
					}
				}

				.original-price {
					font-size: 24rpx;
					color: #ACA8AA;
					text-decoration: line-through;
					margin-top: 4rpx;
				}
			}
		}

		.qualifications {
			font-size: 20rpx;
			color: #A04571;
			padding: 10rpx;
			margin-left: 15rpx;
			border-radius: 4rpx;
			background-color: #F6F1F4;
		}

		.consultation-method {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-top: 0;
			margin-bottom: 40rpx;
		}

		.consulting-experience {
			width: 100%;
			font-size: 28rpx;

			.scroll {
				.scroll-view_H {
					width: 100%;
					display: flex;
					justify-content: space-between;
					border-bottom: 2rpx solid #E7E7E7;
					padding-bottom: 32rpx;
				}

				.time-box {
					position: relative;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					.time-top {
						color: #ACA8AA;
						font-size: 24rpx;
					}

					.time-bottom {
						font-size: 36rpx;
						font-weight: 700;
						display: flex;
						align-items: center;

						.time-text {
							font-size: 30rpx;
						}

						.time-unit {
							font-size: 24rpx;
							color: #8A8788;
						}
					}
				}
			}

			.time-title-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 32rpx;

				.time-title {
					font-size: 24rpx;
					color: #A04571;
				}

				.view-more {
					display: flex;
					align-items: center;
					font-size: 22rpx;
					color: #666;
					gap: 6rpx;
					cursor: pointer;

					&:hover {
						color: #333;
					}
				}
			}
		}
	}

	.personal-resume {
		padding: 20rpx;
		background-color: #fff;
	}

	.bottom-popup {
		.title {
			font-size: 40rpx;
			font-weight: 700;
			padding: 40rpx;
		}

		.guarantee-list {
			margin: 30rpx;
			background-color: #f0fbfd;
			padding: 30rpx;
			margin-top: 0;
			margin-bottom: 10rpx;
			border-radius: 30rpx;

			.icon-label {
				display: flex;

				.text {
					// white-space: nowrap;
					font-size: 28rpx;
					font-weight: 700;

					.guarantee-content {
						color: #a3a7b0;
						font-size: 26rpx;
						font-weight: 400;
					}
				}

				.icon {
					width: 40rpx;
					height: 40rpx;
					margin-right: 10rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}

			.info-popup-text {
				position: relative;

				&:before {
					content: "";
					position: absolute;
					top: 0;
					left: -30rpx;
					width: 5px;
					height: 40rpx;
					background-color: #01a6fe;
					border-top-right-radius: 10rpx;
					border-bottom-right-radius: 10rpx;
				}
			}
		}
	}

	.make-appointment-popup {
		.make-appointment-box {
			.make-appointment-top {
				position: relative;
				background: linear-gradient(to bottom, #e3f3ff, #fff);
				border-radius: 30rpx 30rpx 0 0;

				&:before {
					content: "";
					width: 60rpx;
					height: 10rpx;
					background-color: #cbcbcb;
					position: absolute;
					border-radius: 5rpx;
					top: 18rpx;
					left: 50%;
					transform: translateX(-50%);
				}

				.title-box {
					color: #434445;
					padding: 40rpx;

					.title {
						margin-bottom: 10rpx;
					}

					text,
					.msg {
						font-size: 26rpx;
					}
				}
			}

			.scroll {

				.not-select {
					width: 100%;
					text-align: center;
					margin-bottom: 20rpx;

					.select-radio {
						transform: scale(0.7);
					}

					// .select-radio {
					// 	width: 24rpx !important;
					// 	height: 24rpx !important;
					// }
				}

				.scroll-bottom {
					width: 100%;
					height: 80rpx;
					background-color: #fff;
					margin-bottom: 20rpx;

					.scroll-bottom-btn {
						width: 80%;
						height: 100%;
						background-color: #52b5f9;
						border-radius: 100rpx;
						line-height: 80rpx;
						font-size: 30rpx;
					}
				}

				.time-box {
					width: 200rpx;
					height: 120rpx;
					position: relative;
					display: inline-block;

					&:after {
						content: "";
						position: absolute;
						right: 30rpx;
						height: 40rpx;
						width: 2rpx;
						background-color: #eceeef;
						z-index: 1;
						top: 50%;
						transform: translateY(-50%);
					}

					&:nth-last-child(0) {
						&:after {
							content: "";
						}
					}

					.time-top {
						color: #999da6;
					}

					.time-bottom {
						font-size: 36rpx;
						font-weight: 700;

						text {
							font-size: 24rpx;
							// margin-left: -5px;
						}
					}
				}
			}
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: calc(100% - 40rpx);
		height: 120rpx;
		background-color: #fff;
		z-index: 9;
		margin-top: 100rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;

		.collection {
			width: 80rpx;
			// height: 60rpx;
			margin-right: 20rpx;
			font-size: 24rpx;
			text-align: center;

			image {
				width: 100%;
				height: 100%;
				margin-bottom: -5rpx;
			}
		}

		.footer-goods-nav {
			width: 100%;

			:deep .uni-tab__cart-button-right-text {
				font-size: 30rpx !important;
			}
		}

		.active {
			color: #06adfc;
		}

		.private-message {
			width: calc(50% - 60rpx);
			margin-right: 20rpx;

			button {
				background-color: #eaf7ff;
				border: none !important;
				color: #52b5f9;
				font-weight: 700;
				border-radius: 70rpx;
			}

			uni-button:after {
				border: none !important;
			}
		}

		.consult {
			width: calc(50% - 60rpx);

			button {
				background-color: #52b5f9;
				border: none !important;
				color: #fff;
				font-weight: 700;
				border-radius: 70rpx;
			}
		}
	}

	.expertise-container {
		margin-top: 20rpx;

		.expertise-item {
			background: #f8f9fa;
			border-radius: 16rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;

			.expertise-header {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;

				.expertise-icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 16rpx;

					image {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}
				}

				.expertise-title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
				}
			}

			.expertise-children {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;

				.expertise-tag {
					background: #fff;
					padding: 8rpx 20rpx;
					border-radius: 30rpx;
					font-size: 26rpx;
					color: #666;
					border: 1px solid #e0e0e0;

					&:active {
						background: #f0f0f0;
					}
				}
			}
		}
	}

	.section-title {
		font-size: 24rpx;
		font-weight: 700;
		margin-bottom: 20rpx;
	}

	.style-container {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		margin-bottom: 30rpx;

		.style-tag {
			background: #F7F7F7;
			color: #8A8788;
			padding: 16rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			display: flex;
			align-items: center;
		}
	}

	.certificate-preview {
		margin-top: 16rpx;

		image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 8rpx;
			border: 1px solid #eee;
		}
	}

	// 评价标题样式
	.review-title {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title-text {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}

		.more-btn {
			display: flex;
			align-items: center;
			gap: 8rpx;
			font-size: 26rpx;
			color: #666;

			text {
				color: #666;
			}

			image {
				width: 16rpx;
				height: 16rpx;
			}
		}
	}

	// 评价相关样式
	.review-summary {
		.rating-overview {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.avg-rating {
				display: flex;
				align-items: center;
				gap: 16rpx;

				.rating-number {
					font-size: 36rpx;
					font-weight: bold;
					color: #A04571;
				}
			}

			.total-count {
				font-size: 24rpx;
				color: #666;
			}
		}

		.recent-reviews {
			margin-bottom: 24rpx;

			.review-item {
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f0f0f0;

				&:last-child {
					border-bottom: none;
				}

				.review-header {
					display: flex;
					align-items: center;
					margin-bottom: 12rpx;

					.rating-label {
						font-size: 22rpx;
						font-weight: 400;
						color: #ACA8AA;
					}

					.logo-img {
						width: 50rpx;
						height: 50rpx;
					}

					.username {
						font-size: 22rpx;
						color: #01020C;
					}

					.rating-container {
						display: flex;
						align-items: center;
					}
				}

				.review-content {
					font-size: 22rpx;
					color: #8A8788;
					line-height: 1.5;
				}

				.review-footer {
					display: flex;
					justify-content: space-between;
					font-size: 22rpx;
					margin-top: 30rpx;

					.consult-type {
						color: #ACA8AA;
						font-weight: 400;
					}
				}

				.review-date {
					font-size: 22rpx;
					color: #999;
				}
			}
		}

		.view-all {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8rpx;
			font-size: 26rpx;
			color: #01a6fe;
			padding: 16rpx 0;
		}
	}
}

/* 预约弹框样式 */
.appointment-popup {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.popup-content {
	flex: 1;
	padding: 0 32rpx;
	max-height: 60vh;
	width: calc(100% - 64rpx);
}

.form-section {
	margin: 32rpx 0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
}

.radio-group {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.radio-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background-color: #f8f8f8;
	border-radius: 12rpx;
	border: 2rpx solid transparent;

	&.active {
		background-color: #e8f6ff;
		border-color: #20a3f3;
	}
}

// 内联单选组样式
.inline-radio-group {
	display: flex;
	gap: 32rpx;
}

.inline-radio-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	border-radius: 8rpx;
	cursor: pointer;

	.inline-radio-text {
		font-size: 28rpx;
		color: #666;
		transition: all 0.3s ease;
	}
}

.inline-radio-item-bg {
	padding: 16rpx 24rpx;
	background-color: #F7F7F7;
	color: #8A8788;

	&.active {
		background-color: #F6F1F4;
		color: #A04571;

		.inline-radio-text {
			color: #A04571;
		}
	}
}

// 支付方式单选圆点样式
.payment-radio-icon {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #ddd;
	border-radius: 50%;
	position: relative;
	transition: all 0.3s ease;

	&.active {
		border-color: #b85a9b;

		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 16rpx;
			height: 16rpx;
			background-color: #b85a9b;
			border-radius: 50%;
		}
	}
}

.radio-text {
	font-size: 28rpx;
	color: #333;
}

.contact-service-option {
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.date-scroll {
	white-space: nowrap;

	.date-item {
		display: inline-block;
		text-align: center;
		padding: 16rpx 24rpx;
		margin-right: 16rpx;
		background-color: #f8f8f8;
		border-radius: 12rpx;
		border: 2rpx solid transparent;
		min-width: 120rpx;

		&.active {
			background-color: #e8f6ff;
			border-color: #20a3f3;
		}

		.date-week {
			font-size: 24rpx;
			color: #666;
			margin-bottom: 8rpx;
		}

		.date-number {
			font-size: 28rpx;
			color: #333;
			font-weight: 600;
		}
	}
}





.popup-footer {
	height: 150rpx;
	padding: 12rpx 32rpx;

	.footer-buttons {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.cancel-btn {
			flex: 1;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			background-color: #f8f8f8;
			color: #666;
			border-radius: 12rpx;
			font-size: 32rpx;
		}

		.duration-price-info {
			flex: 1;
			border-radius: 12rpx;

			.duration-text {
				display: block;
				font-size: 24rpx;
				color: #8A8788;
				margin-bottom: 4rpx;
			}

			.price-text {
				display: block;
				font-size: 28rpx;
				font-weight: 600;
				color: #e72f2f;
			}

			.price-main {
				display: flex;
				align-items: baseline;
				color: #A04571;

				.currency-symbol {
					font-size: 24rpx;
					margin-right: 2rpx;
				}

				.price-integer {
					font-weight: bold;
					font-size: 36rpx;
				}

				.price-dot {
					font-size: 44rpx;
					font-weight: bold;
					margin: 0 1rpx;
				}

				.price-decimal {
					font-size: 28rpx;
					font-weight: bold;
				}

				.price-slash {
					font-size: 24rpx;
					margin: 0 2rpx;
				}

				.price-unit {
					font-size: 24rpx;
				}
			}
		}

		.confirm-btn {
			flex: 1;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			background: radial-gradient(#AD3D72 0%, #9E3467 100%);
			color: #fff;
			border-radius: 12rpx;
			font-size: 32rpx;
		}
	}
}

.tab-navigation {
	display: flex;
	justify-content: space-between;
	margin-bottom: 24rpx;
	position: sticky;
	top: 0;
	z-index: 1;
	background-color: #f8f8fa;

	.tab-item {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		font-weight: 500;
		color: #8A8788;
		position: relative;
		transition: all 0.3s ease;
		cursor: pointer;

		&.active {
			color: #A04571;
			background-color: #f8f8fa;
			font-weight: 600;
		}

		.tab-icon {
			width: 28rpx;
			height: 12rpx;
			margin-top: 4rpx;
		}
	}
}

.section-content {
	margin-bottom: 20rpx;
}

// 排期弹框样式
.schedule-popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 0;
	max-height: 80vh;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 32rpx 24rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.popup-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}

		.popup-close {
			padding: 8rpx;
			cursor: pointer;
		}
	}

	.schedule-content {
		padding: 32rpx;
		max-height: 60vh;
		overflow-y: auto;

		.schedule-info {
			margin-bottom: 32rpx;

			.info-item {
				display: flex;
				margin-bottom: 16rpx;
				font-size: 28rpx;

				.label {
					color: #666;
					width: 160rpx;
					flex-shrink: 0;
				}

				.value {
					color: #333;
					flex: 1;
				}
			}
		}

		.schedule-calendar {
			.calendar-title {
				font-size: 30rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 24rpx;
			}

			.week-schedule {
				.day-schedule {
					margin-bottom: 24rpx;
					padding: 24rpx;
					background: #f8f9fa;
					border-radius: 12rpx;

					.day-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 16rpx;

						.day-name {
							font-size: 28rpx;
							font-weight: 600;
							color: #333;
						}

						.day-date {
							font-size: 24rpx;
							color: #666;
						}
					}

					.time-slots {
						.no-slots {
							text-align: center;
							color: #999;
							font-size: 26rpx;
							padding: 16rpx 0;
						}

						.slot-list {
							display: flex;
							flex-wrap: wrap;
							gap: 12rpx;

							.time-slot {
								padding: 8rpx 16rpx;
								border-radius: 8rpx;
								font-size: 24rpx;
								border: 1rpx solid #ddd;

								&.available {
									background: #e8f6ff;
									color: #20a3f3;
									border-color: #20a3f3;
								}

								&.booked {
									background: #f5f5f5;
									color: #999;
									border-color: #ddd;
								}

								&.disabled {
									background: #f0f0f0;
									color: #ccc;
									border-color: #e0e0e0;
								}
							}
						}
					}
				}
			}

			.loading-schedule {
				text-align: center;
				padding: 40rpx 0;
				color: #666;
				font-size: 28rpx;
			}
		}
	}
}
</style>
