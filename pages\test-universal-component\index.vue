<template>
	<view class="test-page">
		<view class="section">
			<view class="section-title">咨询师示例</view>
			<UniversalListItem
				:item="consultantData"
				type="consultant"
				:dictData="dictData"
				@click="handleClick"
			/>
		</view>

		<view class="section">
			<view class="section-title">课程示例</view>
			<UniversalListItem
				:item="courseData"
				type="course"
				:dictData="dictData"
				@click="handleClick"
			/>
		</view>

		<view class="section">
			<view class="section-title">冥想示例</view>
			<UniversalListItem
				:item="meditationData"
				type="meditation"
				:dictData="dictData"
				@click="handleClick"
			/>
		</view>

		<view class="section">
			<view class="section-title">测评示例</view>
			<UniversalListItem
				:item="assessmentData"
				type="assessment"
				:dictData="dictData"
				@click="handleClick"
			/>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 字典数据
const dictData = ref({
	psy_consultant_level: [
		{ value: '1', label: '初级咨询师' },
		{ value: '2', label: '中级咨询师' },
		{ value: '3', label: '高级咨询师' },
		{ value: '4', label: '专家咨询师' }
	]
})

// 咨询师测试数据
const consultantData = ref({
	id: 1,
	name: "张医生",
	avatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png",
	personalTitle: "3",
	province: "北京市",
	city: "朝阳区",
	district: "望京",
	personalIntro: "专业心理咨询师，擅长认知行为疗法和家庭治疗",
	consultStyles: [
		{ dictLabel: "认知行为疗法" },
		{ dictLabel: "家庭治疗" },
		{ dictLabel: "情绪管理" }
	],
	startYear: "2015",
	serviceCount: 1000,
	totalCases: 500,
	price: 299,
	isFree: false
})

// 课程测试数据
const courseData = ref({
	id: 2,
	title: "心理学入门课程",
	coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png",
	description: "系统学习心理学基础知识，了解人类行为和心理过程",
	tags: ["心理学", "入门", "基础"],
	studentCount: 1200,
	lessonCount: 20,
	duration: 45,
	price: 199,
	isFree: false
})

// 冥想测试数据
const meditationData = ref({
	id: 3,
	title: "正念冥想",
	coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png",
	description: "通过正念冥想练习，帮助您放松身心，减轻压力",
	tags: ["正念", "放松", "减压"],
	playCount: 5000,
	duration: 15,
	favoriteCount: 800,
	price: 9.9,
	isFree: true
})

// 测评测试数据
const assessmentData = ref({
	id: 4,
	title: "心理健康测评",
	coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png",
	description: "全面评估您的心理健康状况，提供专业建议",
	tags: ["心理健康", "自测", "评估"],
	completionCount: 3000,
	questionCount: 30,
	duration: 10,
	price: 29.9,
	isFree: false
})

// 点击处理
const handleClick = (item) => {
	console.log('点击了项目:', item)
	uni.showToast({
		title: `点击了: ${item.name || item.title}`,
		icon: 'none'
	})
}
</script>

<style lang="scss" scoped>
.test-page {
	padding: 32rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.section {
	margin-bottom: 40rpx;
	
	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 24rpx;
		padding: 0 20rpx;
	}
}
</style>
