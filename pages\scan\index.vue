<template>
  <view class="scan-container">
    <!-- 扫码区域 -->
    <view class="scan-area">
      <view class="scan-header">
        <text class="scan-title">扫一扫核销订单</text>
        <text class="scan-desc">请将二维码放入框内进行扫描</text>
      </view>

      <!-- 扫码框 -->
      <view class="scan-frame">
        <view class="scan-box">
          <view class="corner top-left"></view>
          <view class="corner top-right"></view>
          <view class="corner bottom-left"></view>
          <view class="corner bottom-right"></view>
          <view class="scan-line" :class="{ scanning: isScanning }"></view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="scan-actions">
        <button class="scan-btn" @click="startScan" :disabled="isScanning">
          {{ isScanning ? '扫描中...' : '开始扫描' }}
        </button>
      </view>
    </view>

    <!-- 手动输入区域 -->
    <view class="manual-input-section">
      <view class="input-row">
        <input
          class="order-input"
          type="text"
          placeholder="请输入订单号进行核销"
          v-model="manualOrderNo"
          maxlength="20"
        />
        <button class="verify-btn" @click="verifyManualOrder" :disabled="!manualOrderNo.trim()">
          核销
        </button>
      </view>
    </view>

    <!-- 最近核销记录 -->
    <view class="recent-records">
      <view class="records-header">
        <text class="records-title">最近核销记录</text>
        <text class="view-all" @click="goToRecords">查看全部</text>
      </view>
      <view class="records-list">
        <view
          class="record-item"
          v-for="record in recentRecords"
          :key="record.id"
          @click="viewRecordDetail(record)"
        >
          <view class="record-info">
            <text class="order-no">订单号：{{ record.orderNo }}</text>
            <text class="client-name">{{ record.clientName }}</text>
            <text class="verify-time">{{ formatDateTime(record.verifyTime) }}</text>
          </view>
          <view class="record-status">
            <text class="status-text">已核销</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-records" v-if="recentRecords.length === 0">
        <image src="/static/icon/empty-scan.png" mode="aspectFit"></image>
        <text>暂无核销记录</text>
      </view>
    </view>



    <!-- 核销结果弹窗 -->
    <view class="verify-result-modal" v-if="showVerifyResult" @click="closeVerifyResult">
      <view class="result-content" @click.stop>
        <view class="result-icon">
          <image :src="verifyResult.success ? '/static/icon/success.png' : '/static/icon/error.png'" mode="aspectFit"></image>
        </view>
        <text class="result-title">{{ verifyResult.success ? '核销成功' : '核销失败' }}</text>
        <text class="result-message">{{ verifyResult.message }}</text>
        <view class="order-detail" v-if="verifyResult.success && verifyResult.orderInfo">
          <view class="detail-item">
            <text class="label">订单号：</text>
            <text class="value">{{ verifyResult.orderInfo.orderNo }}</text>
          </view>
          <view class="detail-item">
            <text class="label">来访者：</text>
            <text class="value">{{ verifyResult.orderInfo.clientName }}</text>
          </view>
          <view class="detail-item">
            <text class="label">服务项目：</text>
            <text class="value">{{ verifyResult.orderInfo.serviceName }}</text>
          </view>
          <view class="detail-item">
            <text class="label">核销时间：</text>
            <text class="value">{{ formatDateTime(verifyResult.orderInfo.verifyTime) }}</text>
          </view>
        </view>
        <button class="result-btn" @click="closeVerifyResult">确定</button>
      </view>
    </view>

    <cc-myTabbar :tabBarShow="2"></cc-myTabbar>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { verifyOrder, getRecentVerifyRecords } from '@/api/consultant-app.js'
import { parseCompatibleDate } from '@/utils/index'

// 响应式数据
const isScanning = ref(false)
const showVerifyResult = ref(false)
const manualOrderNo = ref('')
const recentRecords = ref([])

// 核销结果
const verifyResult = ref({
  success: false,
  message: '',
  orderInfo: null
})

// 生命周期
onMounted(() => {
  loadRecentRecords()
})

onShow(() => {
  loadRecentRecords()
})

// 加载最近核销记录
const loadRecentRecords = async () => {
  try {
    const res = await getRecentVerifyRecords({ limit: 5 })
    if (res.code === 200) {
      recentRecords.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载核销记录失败:', error)
  }
}

// 开始扫描
const startScan = () => {
  isScanning.value = true

  uni.scanCode({
    success: (res) => {
      console.log('扫码结果:', res)
      handleScanResult(res.result)
    },
    fail: (err) => {
      console.error('扫码失败:', err)
      uni.showToast({
        title: '扫码失败，请重试',
        icon: 'none'
      })
    },
    complete: () => {
      isScanning.value = false
    }
  })
}

// 处理扫码结果
const handleScanResult = async (scanData) => {
  try {
    // 解析扫码数据，提取订单号
    let orderNo = ''

    if (scanData.startsWith('ORDER:')) {
      // 格式：ORDER:订单号
      orderNo = scanData.replace('ORDER:', '')
    } else if (scanData.includes('orderNo=')) {
      // URL格式：包含orderNo参数
      const match = scanData.match(/orderNo=([^&]+)/)
      orderNo = match ? match[1] : ''
    } else {
      // 直接是订单号
      orderNo = scanData
    }

    if (!orderNo) {
      showVerifyError('无效的二维码格式')
      return
    }

    await verifyOrderByNo(orderNo)
  } catch (error) {
    console.error('处理扫码结果失败:', error)
    showVerifyError('处理扫码结果失败')
  }
}

// 核销订单
const verifyOrderByNo = async (orderNo) => {
  try {
    uni.showLoading({
      title: '核销中...'
    })

    const res = await verifyOrder({ orderNo })

    if (res.code === 200) {
      verifyResult.value = {
        success: true,
        message: '订单核销成功',
        orderInfo: res.data
      }

      // 刷新最近记录
      loadRecentRecords()
    } else {
      verifyResult.value = {
        success: false,
        message: res.message || '核销失败',
        orderInfo: null
      }
    }

    showVerifyResult.value = true
  } catch (error) {
    console.error('核销订单失败:', error)
    showVerifyError('网络错误，请重试')
  } finally {
    uni.hideLoading()
  }
}

// 显示核销错误
const showVerifyError = (message) => {
  verifyResult.value = {
    success: false,
    message,
    orderInfo: null
  }
  showVerifyResult.value = true
}

// 手动核销订单
const verifyManualOrder = async () => {
  if (!manualOrderNo.value.trim()) {
    uni.showToast({
      title: '请输入订单号',
      icon: 'none'
    })
    return
  }

  const orderNo = manualOrderNo.value.trim()
  manualOrderNo.value = '' // 清空输入框
  await verifyOrderByNo(orderNo)
}

// 关闭核销结果弹窗
const closeVerifyResult = () => {
  showVerifyResult.value = false
  verifyResult.value = {
    success: false,
    message: '',
    orderInfo: null
  }
}

// 查看记录详情
const viewRecordDetail = (record) => {
  uni.navigateTo({
    url: `/pages/scan/record-detail/index?recordId=${record.id}`
  })
}

// 跳转到全部记录
const goToRecords = () => {
  uni.navigateTo({
    url: '/pages/scan/records/index'
  })
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = parseCompatibleDate(datetime)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${month}/${day} ${hour}:${minute}`
}
</script>

<style scoped lang="scss">
.scan-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.scan-area {
  background-color: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;

  .scan-header {
    text-align: center;
    margin-bottom: 60rpx;

    .scan-title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
    }

    .scan-desc {
      font-size: 26rpx;
      color: #666;
    }
  }

  .scan-frame {
    display: flex;
    justify-content: center;
    margin-bottom: 60rpx;

    .scan-box {
      position: relative;
      width: 400rpx;
      height: 400rpx;
      border: 2rpx solid #ddd;
      border-radius: 20rpx;
      overflow: hidden;

      .corner {
        position: absolute;
        width: 60rpx;
        height: 60rpx;
        border: 6rpx solid #1890ff;

        &.top-left {
          top: 0;
          left: 0;
          border-right: none;
          border-bottom: none;
          border-radius: 20rpx 0 0 0;
        }

        &.top-right {
          top: 0;
          right: 0;
          border-left: none;
          border-bottom: none;
          border-radius: 0 20rpx 0 0;
        }

        &.bottom-left {
          bottom: 0;
          left: 0;
          border-right: none;
          border-top: none;
          border-radius: 0 0 0 20rpx;
        }

        &.bottom-right {
          bottom: 0;
          right: 0;
          border-left: none;
          border-top: none;
          border-radius: 0 0 20rpx 0;
        }
      }

      .scan-line {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, transparent, #1890ff, transparent);

        &.scanning {
          animation: scanning 2s linear infinite;
        }
      }
    }
  }

  .scan-actions {
    .scan-btn {
      width: 100%;
      padding: 28rpx 0;
      background-color: #1890ff;
      color: #fff;
      border: none;
      border-radius: 12rpx;
      font-size: 30rpx;

      &:disabled {
        background-color: #d9d9d9;
        color: #999;
      }
    }
  }
}

.manual-input-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .input-row {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .order-input {
      flex: 1;
      padding: 24rpx 20rpx;
      border: 1rpx solid #d9d9d9;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #333;

      &:focus {
        border-color: #1890ff;
      }
    }

    .verify-btn {
      padding: 24rpx 40rpx;
      background-color: #52c41a;
      color: #fff;
      border: none;
      border-radius: 12rpx;
      font-size: 28rpx;
      font-weight: bold;

      &:disabled {
        background-color: #d9d9d9;
        color: #999;
      }
    }
  }
}

.recent-records {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 0 20rpx;

  .records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .records-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .view-all {
      font-size: 26rpx;
      color: #1890ff;
    }
  }

  .records-list {
    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .record-info {
        flex: 1;

        .order-no {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .client-name {
          display: block;
          font-size: 26rpx;
          color: #666;
          margin-bottom: 4rpx;
        }

        .verify-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .record-status {
        .status-text {
          font-size: 24rpx;
          color: #52c41a;
          background-color: #f6ffed;
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
        }
      }
    }
  }

  .empty-records {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0;

    image {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 20rpx;
    }

    text {
      color: #999;
      font-size: 26rpx;
    }
  }
}

.verify-result-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.result-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  text-align: center;
  padding: 40rpx 30rpx;

  .result-icon {
    margin-bottom: 30rpx;

    image {
      width: 120rpx;
      height: 120rpx;
    }
  }

  .result-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }

  .result-message {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 30rpx;
  }

  .order-detail {
    text-align: left;
    background-color: #f8f8f8;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;

    .detail-item {
      display: flex;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 26rpx;
        color: #666;
        width: 160rpx;
        flex-shrink: 0;
      }

      .value {
        font-size: 26rpx;
        color: #333;
        flex: 1;
      }
    }
  }

  .result-btn {
    width: 100%;
    padding: 24rpx 0;
    background-color: #1890ff;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 28rpx;
  }
}

@keyframes scanning {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(396rpx);
  }
}
</style>