import request from "../utils/request";

export function getTime() {
	return request({
		url: "/wechat/appointment/public?days=7",
		method: 'get',
	})
}

export function getCounselorTime(counselorId) {
	return request({
		url: `/wechat/appointment/counselor/${counselorId}?days=7`,
		method: "get",
	});
}

// 获取咨询师的格式化时间槽数据（新接口，与系统时间表格式一致）
export function getCounselorFormattedTimeSlots(counselorId, startDate, endDate) {
	return request({
		url: `/system/timeSlot/formatted/${counselorId}`,
		method: "get",
		params: {
			startDate,
			endDate
		}
	});
}
