<template>
	<view class="test-page">
		<view class="header">
			<text class="title">分类数据测试页面</text>
		</view>
		
		<view class="section">
			<view class="section-title">原始分类数据</view>
			<view class="data-display">
				<text>{{ JSON.stringify(rawData, null, 2) }}</text>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">处理后的标签数据</view>
			<view class="tabs-display">
				<view 
					v-for="(tab, index) in processedTabs" 
					:key="index"
					class="tab-item"
				>
					<text class="tab-name">{{ tab.name }}</text>
					<text class="tab-type">类型: {{ tab.type }}</text>
					<text class="tab-id">ID: {{ tab.categoryId }}</text>
					<view v-if="tab.children && tab.children.length > 0" class="children">
						<text class="children-title">子分类:</text>
						<view 
							v-for="child in tab.children" 
							:key="child.categoryId"
							class="child-item"
						>
							<text>{{ child.categoryName }} (ID: {{ child.categoryId }})</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">操作按钮</view>
			<button @click="loadData" class="load-btn">重新加载数据</button>
			<button @click="clearData" class="clear-btn">清空数据</button>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { getCategoryTree } from '@/api/category.js'

const rawData = ref(null)
const processedTabs = ref([])

const loadData = async () => {
	try {
		const res = await getCategoryTree()
		console.log('分类接口返回数据:', res)
		
		if (res.code === 200 && res.data && res.data.categories) {
			rawData.value = res.data.categories
			
			// 处理数据
			processedTabs.value = res.data.categories.map(category => {
				let type = 'consultant' // 默认类型
				switch (category.categoryName) {
					case '咨询师':
						type = 'consultant'
						break
					case '课程':
						type = 'course'
						break
					case '冥想':
						type = 'meditation'
						break
					case '测评':
						type = 'assessment'
						break
				}
				
				return {
					name: category.categoryName,
					type: type,
					categoryId: category.categoryId,
					children: category.children || []
				}
			})
		} else {
			console.error('接口返回数据格式错误:', res)
		}
	} catch (error) {
		console.error('加载分类数据失败:', error)
	}
}

const clearData = () => {
	rawData.value = null
	processedTabs.value = []
}

// 页面加载时自动获取数据
loadData()
</script>

<style lang="scss" scoped>
.test-page {
	padding: 32rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

.section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	
	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 24rpx;
		border-bottom: 2rpx solid #eee;
		padding-bottom: 16rpx;
	}
}

.data-display {
	background-color: #f5f5f5;
	padding: 24rpx;
	border-radius: 12rpx;
	
	text {
		font-size: 24rpx;
		color: #666;
		font-family: monospace;
		white-space: pre-wrap;
		word-break: break-all;
	}
}

.tabs-display {
	.tab-item {
		background-color: #f8f8f8;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		border-left: 6rpx solid #ff6b35;
		
		.tab-name {
			display: block;
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		.tab-type, .tab-id {
			display: block;
			font-size: 24rpx;
			color: #666;
			margin-bottom: 4rpx;
		}
		
		.children {
			margin-top: 16rpx;
			padding-top: 16rpx;
			border-top: 1rpx solid #eee;
			
			.children-title {
				display: block;
				font-size: 26rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 12rpx;
			}
			
			.child-item {
				background-color: #fff;
				padding: 12rpx 16rpx;
				border-radius: 8rpx;
				margin-bottom: 8rpx;
				
				text {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
}

.load-btn, .clear-btn {
	width: 100%;
	padding: 24rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	margin-bottom: 16rpx;
	border: none;
}

.load-btn {
	background-color: #ff6b35;
	color: #fff;
}

.clear-btn {
	background-color: #666;
	color: #fff;
}
</style>
