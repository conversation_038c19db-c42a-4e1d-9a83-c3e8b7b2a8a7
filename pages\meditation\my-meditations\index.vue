<template>
  <view class="my-meditations-page">
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <view class="stat-number">{{ statistics.totalTimes || 0 }}</view>
        <view class="stat-label">冥想次数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ formatDuration(statistics.totalDuration || 0) }}</view>
        <view class="stat-label">总时长</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ statistics.completedTimes || 0 }}</view>
        <view class="stat-label">完成次数</view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="['tab-item', { active: currentFilter === index }]"
        @click="switchFilter(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 冥想列表 -->
    <scroll-view 
      class="meditation-list" 
      scroll-y 
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="filteredMeditationList.length === 0" class="empty-state">
        <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-meditation.png" mode="aspectFit"></image>
        <text>暂无冥想记录</text>
        <button class="browse-btn" @click="goToBrowse">去体验冥想</button>
      </view>

      <view v-else class="meditation-items">
        <view 
          v-for="meditation in filteredMeditationList" 
          :key="meditation.id"
          class="meditation-item"
          @click="goToMeditationDetail(meditation.id)"
        >
          <view class="meditation-cover">
            <image :src="meditation.coverImage || defaultCover" mode="aspectFill"></image>
            <view class="meditation-duration">{{ meditation.duration }}分钟</view>
          </view>
          
          <view class="meditation-info">
            <view class="meditation-title">{{ meditation.title }}</view>
            <view class="meditation-subtitle">{{ meditation.subtitle }}</view>
            <view class="meditation-stats">
              <text>播放{{ meditation.playCount || 0 }}次</text>
              <text v-if="meditation.lastPlayTime">上次播放: {{ formatDate(meditation.lastPlayTime) }}</text>
            </view>
          </view>
          
          <view class="meditation-actions">
            <button 
              class="action-btn play-btn" 
              @click.stop="playMeditation(meditation)"
            >
              播放
            </button>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 最近播放记录 -->
    <view class="recent-section" v-if="currentFilter === 1">
      <view class="section-title">最近播放</view>
      <view class="recent-list">
        <view 
          v-for="record in recentRecords" 
          :key="record.id"
          class="recent-item"
          @click="playMeditation(record.meditation)"
        >
          <image :src="record.meditation.coverImage || defaultCover" mode="aspectFill" class="recent-cover"></image>
          <view class="recent-info">
            <view class="recent-title">{{ record.meditation.title }}</view>
            <view class="recent-time">{{ formatDate(record.createTime) }}</view>
          </view>
          <view class="recent-duration">{{ record.duration }}分钟</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { 
  getPurchasedMeditations, 
  getUserMeditationRecords, 
  getUserMeditationStatistics 
} from '@/api/meditation'
import { useUserStore } from '@/stores/user'

// 响应式数据
const meditationList = ref([])
const recentRecords = ref([])
const statistics = ref({})
const refreshing = ref(false)
const filterTabs = ref([
  { name: '已购买', value: 'purchased' },
  { name: '播放记录', value: 'records' },
  { name: '收藏', value: 'favorites' }
])
const currentFilter = ref(0)

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'

const userStore = useUserStore()

// 计算属性
const filteredMeditationList = computed(() => {
  const filter = filterTabs.value[currentFilter.value].value
  
  if (filter === 'purchased') {
    return meditationList.value
  } else if (filter === 'records') {
    // 从播放记录中提取冥想列表
    const meditationMap = new Map()
    recentRecords.value.forEach(record => {
      if (!meditationMap.has(record.meditationId)) {
        meditationMap.set(record.meditationId, {
          ...record.meditation,
          playCount: 1,
          lastPlayTime: record.createTime
        })
      } else {
        const existing = meditationMap.get(record.meditationId)
        existing.playCount++
        if (new Date(record.createTime) > new Date(existing.lastPlayTime)) {
          existing.lastPlayTime = record.createTime
        }
      }
    })
    return Array.from(meditationMap.values())
  } else if (filter === 'favorites') {
    // TODO: 实现收藏功能
    return []
  }
  
  return meditationList.value
})

// 方法
const switchFilter = (index) => {
  currentFilter.value = index
  
  if (index === 1 && recentRecords.value.length === 0) {
    loadRecentRecords()
  }
}

const loadPurchasedMeditations = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    const res = await getPurchasedMeditations()
    if (res.code === 200) {
      meditationList.value = res.data || []
    } else {
      uni.showToast({
        title: res.msg || '获取冥想列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取已购冥想失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const loadRecentRecords = async () => {
  if (!userStore.isLoggedIn) return

  try {
    const res = await getUserMeditationRecords()
    if (res.code === 200) {
      recentRecords.value = res.data || []
    }
  } catch (error) {
    console.error('获取播放记录失败:', error)
  }
}

const loadStatistics = async () => {
  if (!userStore.isLoggedIn) return

  try {
    const res = await getUserMeditationStatistics()
    if (res.code === 200) {
      statistics.value = res.data || {}
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const onRefresh = async () => {
  refreshing.value = true
  
  await Promise.all([
    loadPurchasedMeditations(),
    loadRecentRecords(),
    loadStatistics()
  ])
  
  refreshing.value = false
}

const goToMeditationDetail = (meditationId) => {
  uni.navigateTo({
    url: `/pages/meditation/detail/index?id=${meditationId}`
  })
}

const playMeditation = (meditation) => {
  uni.navigateTo({
    url: `/pages/meditation/player/index?id=${meditation.id}`
  })
}

const goToBrowse = () => {
  uni.navigateTo({
    url: '/pages/meditation/index'
  })
}

const formatDuration = (seconds) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分钟`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now - date
  
  if (diff < 24 * 60 * 60 * 1000) {
    return '今天'
  } else if (diff < 2 * 24 * 60 * 60 * 1000) {
    return '昨天'
  } else {
    return `${date.getMonth() + 1}-${date.getDate()}`
  }
}

// 生命周期
onLoad(() => {
  Promise.all([
    loadPurchasedMeditations(),
    loadStatistics()
  ])
})
</script>

<style lang="scss" scoped>
.my-meditations-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.stats-section {
  display: flex;
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;

  .stat-item {
    flex: 1;
    text-align: center;

    .stat-number {
      font-size: 48rpx;
      font-weight: 600;
      color: #ff6b35;
      margin-bottom: 8rpx;
    }

    .stat-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: #ff6b35;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #ff6b35;
        border-radius: 2rpx;
      }
    }
  }
}

.meditation-list {
  flex: 1;
  padding: 0 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 32rpx;
  text-align: center;

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }

  .browse-btn {
    padding: 20rpx 48rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

.meditation-items {
  .meditation-item {
    display: flex;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;

    .meditation-cover {
      position: relative;
      width: 160rpx;
      height: 120rpx;
      border-radius: 12rpx;
      overflow: hidden;
      margin-right: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }

      .meditation-duration {
        position: absolute;
        bottom: 8rpx;
        right: 8rpx;
        padding: 4rpx 8rpx;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-size: 20rpx;
        border-radius: 8rpx;
      }
    }

    .meditation-info {
      flex: 1;

      .meditation-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .meditation-subtitle {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 16rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .meditation-stats {
        display: flex;
        flex-direction: column;
        gap: 8rpx;
        font-size: 24rpx;
        color: #999;
      }
    }

    .meditation-actions {
      display: flex;
      align-items: center;

      .action-btn {
        padding: 16rpx 32rpx;
        border: none;
        border-radius: 40rpx;
        font-size: 26rpx;

        &.play-btn {
          background-color: #ff6b35;
          color: #fff;
        }
      }
    }
  }
}

.recent-section {
  background-color: #fff;
  margin: 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .recent-list {
    .recent-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .recent-cover {
        width: 80rpx;
        height: 80rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .recent-info {
        flex: 1;

        .recent-title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .recent-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .recent-duration {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}
</style>
