<template>
  <view class="meditation-player">
    <!-- 背景图 -->
    <view class="background-container">
      <image :src="meditationDetail.coverImage || defaultCover" mode="aspectFill" class="background-image"></image>
      <view class="background-overlay"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 冥想信息 -->
      <view class="meditation-info">
        <view class="meditation-title">{{ meditationDetail.title }}</view>
        <view class="meditation-subtitle">{{ meditationDetail.subtitle }}</view>
      </view>

      <!-- 播放控制区域 -->
      <view class="player-container">
        <!-- 进度显示 -->
        <view class="progress-section">
          <view class="time-display">
            <text class="current-time">{{ formatTime(currentTime) }}</text>
            <text class="total-time">{{ formatTime(duration) }}</text>
          </view>
          <view class="progress-bar" @click="onProgressClick">
            <view class="progress-track">
              <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
              <view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
            </view>
          </view>
        </view>

        <!-- 播放控制按钮 -->
        <view class="control-buttons">
          <button class="control-btn" @click="seekBackward">
            <uni-icons type="back" size="24" color="#fff"></uni-icons>
            <text>-15s</text>
          </button>
          
          <button class="play-btn" @click="togglePlay">
            <uni-icons 
              :type="isPlaying ? 'pause-filled' : 'play-filled'" 
              size="32" 
              color="#fff"
            ></uni-icons>
          </button>
          
          <button class="control-btn" @click="seekForward">
            <uni-icons type="forward" size="24" color="#fff"></uni-icons>
            <text>+15s</text>
          </button>
        </view>

        <!-- 功能按钮 -->
        <view class="function-buttons">
          <!-- 倍速功能暂时隐藏 -->
          <!-- <button class="function-btn" @click="toggleSpeed">
            <uni-icons type="settings" size="20" color="#fff"></uni-icons>
            <text>{{ playbackRate }}x</text>
          </button> -->
          
          <button class="function-btn" @click="toggleTimer">
            <uni-icons type="time" size="20" color="#fff"></uni-icons>
            <text>定时</text>
          </button>
          
          <button class="function-btn" @click="toggleFavorite">
            <uni-icons 
              :type="isFavorited ? 'heart-filled' : 'heart'" 
              size="20" 
              color="#fff"
            ></uni-icons>
            <text>收藏</text>
          </button>
        </view>
      </view>

      <!-- 引导文字 -->
      <view class="guidance-text" v-if="meditationDetail.guidanceText">
        <scroll-view class="guidance-scroll" scroll-y>
          <text>{{ meditationDetail.guidanceText }}</text>
        </scroll-view>
      </view>
    </view>

    <!-- 音频播放器已改为使用 wx.createInnerAudioContext -->

    <!-- 定时器弹窗 -->
    <uni-popup ref="timerPopup" type="bottom">
      <view class="timer-content">
        <view class="timer-title">设置定时关闭</view>
        <view class="timer-options">
          <view
            v-for="option in timerOptions"
            :key="option.value"
            :class="['timer-option', { active: selectedTimer === option.value }]"
            @click="selectTimer(option.value)"
          >
            {{ option.label }}
          </view>
        </view>
        <view class="timer-actions">
          <button class="timer-btn cancel" @click="closeTimer">取消</button>
          <button class="timer-btn confirm" @click="confirmTimer">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 倍速选择弹窗暂时隐藏 -->
    <!-- <uni-popup ref="speedPopup" type="bottom">
      <view class="speed-content">
        <view class="speed-title">选择播放速度</view>
        <view class="speed-options">
          <view
            v-for="speed in speedOptions"
            :key="speed.value"
            :class="['speed-option', { active: selectedSpeed === speed.value }]"
            @click="selectSpeed(speed.value)"
          >
            {{ speed.label }}
          </view>
        </view>
        <view class="speed-actions">
          <button class="speed-btn cancel" @click="closeSpeed">取消</button>
          <button class="speed-btn confirm" @click="confirmSpeed">确定</button>
        </view>
      </view>
    </uni-popup> -->
  </view>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getMeditationDetail, playMeditation } from '@/api/meditation'
import { useUserStore } from '@/stores/user'
import { formatTime } from '@/utils/audioPlayer'

// 响应式数据
const meditationDetail = ref({})
const meditationId = ref(null)
const audioPlayer = ref(null)
const timerPopup = ref(null)
// const speedPopup = ref(null) // 倍速功能暂时隐藏

// 播放状态
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
// const playbackRate = ref(1.0) // 倍速功能暂时隐藏
const isFavorited = ref(false)

// 定时器
const selectedTimer = ref(0)
const timerOptions = ref([
  { label: '不设置', value: 0 },
  { label: '15分钟', value: 15 },
  { label: '30分钟', value: 30 },
  { label: '45分钟', value: 45 },
  { label: '60分钟', value: 60 }
])
const timerInterval = ref(null)
const remainingTime = ref(0)

// 倍速设置（暂时隐藏）
// const selectedSpeed = ref(1.0)
// const speedOptions = ref([
//   { label: '0.5x', value: 0.5 },
//   { label: '0.75x', value: 0.75 },
//   { label: '1.0x (正常)', value: 1.0 },
//   { label: '1.25x', value: 1.25 },
//   { label: '1.5x', value: 1.5 },
//   { label: '2.0x', value: 2.0 }
// ])

// 同步selectedSpeed和playbackRate
// const syncSelectedSpeed = () => {
//   selectedSpeed.value = playbackRate.value
// }

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'

const userStore = useUserStore()

// 计算属性
const progressPercent = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 方法
const loadMeditationDetail = async () => {
  try {
    const res = await getMeditationDetail(meditationId.value)
    if (res.code === 200) {
      meditationDetail.value = res.data
      // 加载完详情后初始化音频播放器
      if (meditationDetail.value.audioUrl) {
        initAudioPlayer()
      }
    } else {
      uni.showToast({
        title: res.msg || '获取冥想详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取冥想详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const togglePlay = () => {
  if (isPlaying.value) {
    pauseAudio()
  } else {
    playAudio()
  }
}

// 初始化音频播放器
const initAudioPlayer = () => {
  if (audioPlayer.value) {
    try {
      if (audioPlayer.value.destroy) {
        audioPlayer.value.destroy()
      } else if (audioPlayer.value.stop) {
        audioPlayer.value.stop()
      }
    } catch (e) {
      console.log('清理旧播放器失败:', e)
    }
  }

  console.log('初始化音频播放器，URL:', meditationDetail.value.audioUrl)

  // 直接使用背景音频管理器
  audioPlayer.value = uni.getBackgroundAudioManager()

  // 设置音频信息
  audioPlayer.value.title = meditationDetail.value.title || '冥想音频'
  audioPlayer.value.singer = '喜欢心理'
  audioPlayer.value.coverImgUrl = meditationDetail.value.coverImage || ''
  audioPlayer.value.src = meditationDetail.value.audioUrl

  // 绑定背景音频事件
  audioPlayer.value.onPlay(() => {
    console.log('音频开始播放')
    isPlaying.value = true
  })

  audioPlayer.value.onPause(() => {
    console.log('音频暂停')
    isPlaying.value = false
  })

  audioPlayer.value.onTimeUpdate(() => {
    currentTime.value = audioPlayer.value.currentTime || 0
    duration.value = audioPlayer.value.duration || 0
  })

  audioPlayer.value.onEnded(async () => {
    console.log('音频播放结束')
    isPlaying.value = false

    // 记录完成播放
    if (userStore.isLoggedIn) {
      try {
        await playMeditation(meditationId.value, {
          duration: duration.value,
          completed: true,
          action: 'complete'
        })
      } catch (error) {
        console.error('记录播放完成失败:', error)
      }
    }

    uni.showToast({
      title: '冥想完成',
      icon: 'success'
    })
  })

  audioPlayer.value.onError((error) => {
    console.error('音频播放错误:', error)
    uni.showToast({
      title: '音频播放失败',
      icon: 'none'
    })
  })

  // 检查倍速支持
  if ('playbackRate' in audioPlayer.value) {
    console.log('背景音频支持倍速功能')
  } else {
    console.log('背景音频不支持倍速功能')
  }
}

const playAudio = async () => {
  try {
    console.log('开始播放音频')

    // 记录播放开始
    if (userStore.isLoggedIn) {
      await playMeditation(meditationId.value, {
        startTime: currentTime.value,
        action: 'play'
      })
    }

    // 播放音频
    if (audioPlayer.value) {
      audioPlayer.value.play()
    }
  } catch (error) {
    console.error('播放失败:', error)
    uni.showToast({
      title: '播放失败',
      icon: 'none'
    })
  }
}

const pauseAudio = () => {
  console.log('暂停音频')
  if (audioPlayer.value) {
    audioPlayer.value.pause()
  }
}

const seekBackward = () => {
  if (audioPlayer.value) {
    const newTime = Math.max(0, currentTime.value - 5)
    console.log('快退5秒:', currentTime.value, '->', newTime)

    try {
      // 停止当前播放，重新开始播放
      const wasPlaying = isPlaying.value
      const currentSrc = audioPlayer.value.src
      const currentTitle = audioPlayer.value.title
      const currentSinger = audioPlayer.value.singer
      const currentCover = audioPlayer.value.coverImgUrl

      // 停止播放
      audioPlayer.value.stop()

      // 重新设置音频信息和开始时间
      setTimeout(() => {
        audioPlayer.value.title = currentTitle
        audioPlayer.value.singer = currentSinger
        audioPlayer.value.coverImgUrl = currentCover
        audioPlayer.value.startTime = newTime
        audioPlayer.value.src = currentSrc

        // 如果之前在播放，继续播放
        if (wasPlaying) {
          setTimeout(() => {
            audioPlayer.value.play()
          }, 100)
        }

        currentTime.value = newTime // 立即更新UI
      }, 100)

    } catch (error) {
      console.error('快退失败:', error)
      uni.showToast({
        title: '快退失败',
        icon: 'none'
      })
    }
  }
}

const seekForward = () => {
  if (audioPlayer.value) {
    const newTime = Math.min(duration.value, currentTime.value + 5)
    console.log('快进5秒:', currentTime.value, '->', newTime)

    try {
      // 停止当前播放，重新开始播放
      const wasPlaying = isPlaying.value
      const currentSrc = audioPlayer.value.src
      const currentTitle = audioPlayer.value.title
      const currentSinger = audioPlayer.value.singer
      const currentCover = audioPlayer.value.coverImgUrl

      // 停止播放
      audioPlayer.value.stop()

      // 重新设置音频信息和开始时间
      setTimeout(() => {
        audioPlayer.value.title = currentTitle
        audioPlayer.value.singer = currentSinger
        audioPlayer.value.coverImgUrl = currentCover
        audioPlayer.value.startTime = newTime
        audioPlayer.value.src = currentSrc

        // 如果之前在播放，继续播放
        if (wasPlaying) {
          setTimeout(() => {
            audioPlayer.value.play()
          }, 100)
        }

        currentTime.value = newTime // 立即更新UI
      }, 100)

    } catch (error) {
      console.error('快进失败:', error)
      uni.showToast({
        title: '快进失败',
        icon: 'none'
      })
    }
  }
}

const onProgressClick = (e) => {
  if (audioPlayer.value && duration.value > 0) {
    try {
      const rect = e.currentTarget.getBoundingClientRect()
      const clickX = e.detail.x - rect.left
      const percent = clickX / rect.width
      const seekTime = duration.value * percent

      console.log('进度条点击跳转到:', seekTime)

      // 使用和快进/回退相同的跳转方法
      const wasPlaying = isPlaying.value
      const currentSrc = audioPlayer.value.src
      const currentTitle = audioPlayer.value.title
      const currentSinger = audioPlayer.value.singer
      const currentCover = audioPlayer.value.coverImgUrl

      // 停止播放
      audioPlayer.value.stop()

      // 重新设置音频信息和开始时间
      setTimeout(() => {
        audioPlayer.value.title = currentTitle
        audioPlayer.value.singer = currentSinger
        audioPlayer.value.coverImgUrl = currentCover
        audioPlayer.value.startTime = seekTime
        audioPlayer.value.src = currentSrc

        // 如果之前在播放，继续播放
        if (wasPlaying) {
          setTimeout(() => {
            audioPlayer.value.play()
          }, 100)
        }

        currentTime.value = seekTime // 立即更新UI
      }, 100)

    } catch (error) {
      console.error('进度条跳转失败:', error)
      uni.showToast({
        title: '跳转失败',
        icon: 'none'
      })
    }
  }
}

// 倍速相关方法（暂时隐藏）
// const toggleSpeed = () => {
//   // 同步当前播放速度到选择器
//   syncSelectedSpeed()
//   speedPopup.value?.open()
// }

// const selectSpeed = (speed) => {
//   selectedSpeed.value = speed
//   playbackRate.value = speed
//
//   console.log('设置倍速:', speed)
//
//   // 立即应用倍速设置
//   if (audioPlayer.value) {
//     try {
//       if ('playbackRate' in audioPlayer.value) {
//         audioPlayer.value.playbackRate = speed
//         console.log('倍速设置为:', speed)
//
//         // 验证设置结果
//         setTimeout(() => {
//           const actualRate = audioPlayer.value.playbackRate
//           console.log('实际倍速:', actualRate)
//           if (Math.abs(actualRate - speed) < 0.01) {
//             uni.showToast({
//               title: `播放速度: ${speed}x`,
//               icon: 'success'
//             })
//           } else {
//             uni.showToast({
//               title: '倍速设置失败',
//               icon: 'none'
//             })
//           }
//         }, 100)
//       } else {
//         console.log('不支持倍速功能')
//         uni.showToast({
//           title: '当前设备不支持倍速播放',
//           icon: 'none'
//         })
//       }
//     } catch (error) {
//       console.error('设置倍速失败:', error)
//       uni.showToast({
//         title: '倍速设置失败',
//         icon: 'none'
//       })
//     }
//   }
// }

// const confirmSpeed = () => {
//   // 关闭弹窗即可，倍速已经在选择时应用了
//   closeSpeed()
// }

// const closeSpeed = () => {
//   speedPopup.value?.close()
// }

const toggleTimer = () => {
  timerPopup.value?.open()
}

const selectTimer = (value) => {
  selectedTimer.value = value
}

const confirmTimer = () => {
  if (selectedTimer.value > 0) {
    remainingTime.value = selectedTimer.value * 60 // 转换为秒
    startTimer()
    uni.showToast({
      title: `已设置${selectedTimer.value}分钟后关闭`,
      icon: 'success'
    })
  } else {
    stopTimer()
  }
  closeTimer()
}

const closeTimer = () => {
  timerPopup.value?.close()
}

const startTimer = () => {
  stopTimer()
  timerInterval.value = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      pauseAudio()
      stopTimer()
      uni.showToast({
        title: '定时结束，已暂停播放',
        icon: 'none'
      })
    }
  }, 1000)
}

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  uni.showToast({
    title: isFavorited.value ? '已收藏' : '已取消收藏',
    icon: 'success'
  })
}



// 销毁音频播放器
const destroyAudioPlayer = () => {
  if (audioPlayer.value) {
    try {
      audioPlayer.value.stop()
      console.log('音频播放器已停止')
    } catch (error) {
      console.log('停止音频播放器失败:', error)
    }
    audioPlayer.value = null
  }
}

// 生命周期
onLoad((options) => {
  meditationId.value = options.id
  if (meditationId.value) {
    loadMeditationDetail()
  }
})

onUnmounted(() => {
  stopTimer()
  destroyAudioPlayer()
})
</script>

<style lang="scss" scoped>
.meditation-player {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.background-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;

  .background-image {
    width: 100%;
    height: 100%;
  }

  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.7));
  }
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
  color: #fff;
}

.meditation-info {
  text-align: center;
  margin-bottom: 80rpx;

  .meditation-title {
    font-size: 48rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .meditation-subtitle {
    font-size: 28rpx;
    opacity: 0.8;
  }
}

.player-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.progress-section {
  width: 100%;
  margin-bottom: 80rpx;

  .time-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    font-size: 26rpx;
    opacity: 0.8;
  }

  .progress-bar {
    width: 100%;
    height: 60rpx;
    display: flex;
    align-items: center;

    .progress-track {
      position: relative;
      width: 100%;
      height: 6rpx;
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 3rpx;

      .progress-fill {
        height: 100%;
        background-color: #fff;
        border-radius: 3rpx;
        transition: width 0.1s ease;
      }

      .progress-thumb {
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 20rpx;
        height: 20rpx;
        background-color: #fff;
        border-radius: 50%;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
      }
    }
  }
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 60rpx;
  margin-bottom: 60rpx;

  .control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    background: none;
    border: none;
    color: #fff;
    font-size: 24rpx;
    opacity: 0.8;
  }

  .play-btn {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
  }
}

.function-buttons {
  display: flex;
  gap: 80rpx;

  .function-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    background: none;
    border: none;
    color: #fff;
    font-size: 24rpx;
    opacity: 0.8;
  }
}

.guidance-text {
  margin-top: 60rpx;

  .guidance-scroll {
    max-height: 300rpx;
    padding: 30rpx;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    backdrop-filter: blur(10rpx);

    text {
      font-size: 28rpx;
      line-height: 1.6;
      color: #fff;
      opacity: 0.9;
    }
  }
}

.timer-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;

  .timer-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .timer-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 40rpx;

    .timer-option {
      flex: 1;
      min-width: 120rpx;
      padding: 20rpx;
      text-align: center;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #666;

      &.active {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }

  .timer-actions {
    display: flex;
    gap: 20rpx;

    .timer-btn {
      flex: 1;
      padding: 24rpx;
      border-radius: 12rpx;
      font-size: 30rpx;
      border: none;

      &.cancel {
        background-color: #f8f8f8;
        color: #666;
      }

      &.confirm {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }
}

/* 倍速相关样式暂时隐藏
.speed-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;

  .speed-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .speed-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 40rpx;

    .speed-option {
      flex: 1;
      min-width: 120rpx;
      padding: 20rpx;
      text-align: center;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #666;

      &.active {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }

  .speed-actions {
    display: flex;
    gap: 20rpx;

    .speed-btn {
      flex: 1;
      padding: 24rpx;
      border-radius: 12rpx;
      font-size: 30rpx;
      border: none;

      &.cancel {
        background-color: #f8f8f8;
        color: #666;
      }

      &.confirm {
        background-color: #ff6b35;
        color: #fff;
      }
    }
  }
}
*/
</style>
