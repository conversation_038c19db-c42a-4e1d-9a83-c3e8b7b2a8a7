<template>
  <view class="test-page">
    <view class="header">
      <text class="title">搜索结果Tab分类测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试搜索结果的Tab分类功能：</text>
          <text>1. 搜索结果应包含四种类型：咨询师、课程、冥想、测评</text>
          <text>2. 每种类型应有独立的Tab标签</text>
          <text>3. 点击Tab可以切换不同类型的搜索结果</text>
          <text>4. 热门搜索应包含所有四种类型</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">搜索类型说明</text>
        <view class="type-list">
          <view class="type-item" v-for="type in searchTypes" :key="type.value">
            <view class="type-icon">
              <uni-icons :type="type.icon" size="20" :color="type.color"></uni-icons>
            </view>
            <view class="type-info">
              <text class="type-name">{{ type.name }}</text>
              <text class="type-desc">{{ type.description }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试搜索</text>
        <view class="test-buttons">
          <button class="test-btn" @click="testAllSearch">全局搜索"心理"</button>
          <button class="test-btn" @click="testConsultantSearch">搜索咨询师"张老师"</button>
          <button class="test-btn" @click="testCourseSearch">搜索课程"情绪管理"</button>
          <button class="test-btn" @click="testMeditationSearch">搜索冥想"放松"</button>
          <button class="test-btn" @click="testAssessmentSearch">搜索测评"性格"</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">热门搜索预览</text>
        <view class="hot-preview">
          <view class="hot-category" v-for="category in hotCategories" :key="category.type">
            <view class="category-header">
              <uni-icons :type="category.icon" size="16" :color="category.color"></uni-icons>
              <text class="category-name">{{ category.name }}</text>
            </view>
            <view class="hot-items">
              <view class="hot-tag" v-for="(item, index) in category.items" :key="index" @click="searchHotItem(item)">
                <text class="hot-rank">{{ index + 1 }}</text>
                <text class="hot-keyword">{{ item }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">功能特性</text>
        <view class="features">
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>支持四种内容类型的搜索</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>Tab分类显示搜索结果</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>热门搜索按类型分组</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>搜索建议智能提示</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>搜索历史记录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 搜索类型配置
const searchTypes = ref([
  {
    value: 'consultant',
    name: '咨询师',
    description: '专业心理咨询师，提供一对一心理咨询服务',
    icon: 'person',
    color: '#667eea'
  },
  {
    value: 'course',
    name: '课程',
    description: '系统性心理学课程，提升心理健康知识',
    icon: 'videocam',
    color: '#764ba2'
  },
  {
    value: 'meditation',
    name: '冥想',
    description: '专业冥想音频，帮助放松身心',
    icon: 'sound',
    color: '#4CAF50'
  },
  {
    value: 'assessment',
    name: '测评',
    description: '专业心理测评，了解自己的心理状态',
    icon: 'compose',
    color: '#ff6b35'
  }
])

// 热门搜索分类
const hotCategories = ref([
  {
    type: 'all',
    name: '全部',
    icon: 'search',
    color: '#333',
    items: ['心理咨询师', '失眠', '焦虑症', '情感问题']
  },
  {
    type: 'consultant',
    name: '咨询师',
    icon: 'person',
    color: '#667eea',
    items: ['婚姻咨询师', '青少年心理咨询师', '女咨询师', '抑郁症咨询师']
  },
  {
    type: 'course',
    name: '课程',
    icon: 'videocam',
    color: '#764ba2',
    items: ['心理学课程', '情绪管理', '压力缓解', '人际关系']
  },
  {
    type: 'meditation',
    name: '冥想',
    icon: 'sound',
    color: '#4CAF50',
    items: ['放松冥想', '睡眠冥想', '焦虑缓解', '专注力训练']
  },
  {
    type: 'assessment',
    name: '测评',
    icon: 'compose',
    color: '#ff6b35',
    items: ['心理健康测评', '性格测试', '情绪测评', '压力测评']
  }
])

// 测试方法
const testAllSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?keyword=心理'
  })
}

const testConsultantSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?keyword=张老师&type=consultant'
  })
}

const testCourseSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?keyword=情绪管理&type=course'
  })
}

const testMeditationSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?keyword=放松&type=meditation'
  })
}

const testAssessmentSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?keyword=性格&type=assessment'
  })
}

const searchHotItem = (keyword) => {
  uni.navigateTo({
    url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.type-list {
  .type-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .type-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background: rgba(102, 126, 234, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .type-info {
      flex: 1;
      
      .type-name {
        display: block;
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .type-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  }
}

.hot-preview {
  .hot-category {
    margin-bottom: 32rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .category-header {
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin-bottom: 16rpx;
      
      .category-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }
    
    .hot-items {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      
      .hot-tag {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 8rpx 16rpx;
        background: #f8f9fa;
        border-radius: 20rpx;
        border: 1rpx solid #e9ecef;
        
        .hot-rank {
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20rpx;
          font-weight: 600;
        }
        
        .hot-keyword {
          font-size: 24rpx;
          color: #333;
        }
      }
    }
  }
}

.features {
  .feature-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}
</style>
