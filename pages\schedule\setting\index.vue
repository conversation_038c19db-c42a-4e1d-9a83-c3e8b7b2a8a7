<template>
  <view class="schedule-setting-container">
    <!-- 模板选择 -->
    <view class="template-section">
      <view class="section-header">
        <text class="section-title">排班模板</text>
        <button class="add-template-btn" @click="showCreateTemplate = true">新建模板</button>
      </view>

      <view class="template-list">
        <view class="template-item" v-for="template in templates" :key="template.id"
          :class="{ active: selectedTemplate === template.id }" @click="selectTemplate(template)">
          <view class="template-info">
            <text class="template-name">{{ template.name }}</text>
            <text class="template-desc">{{ template.remark || '暂无描述' }}</text>
            <view class="template-meta">
              <text class="work-days">工作日：{{ getWorkDaysText(template.templateItems) }}</text>
              <text class="work-time">{{ getWorkTimeRange(template.templateItems) }}</text>
            </view>
          </view>

          <view class="template-actions">
            <view class="default-badge" v-if="template.isDefault === 1">默认</view>
            <button class="action-btn" @click.stop="editTemplate(template)">编辑</button>
            <button class="action-btn delete-btn" @click.stop="deleteTemplate(template)"
              v-if="template.isDefault !== 1">删除</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 模板详情 -->
    <view class="template-detail-section" v-if="selectedTemplate">
      <view class="section-header">
        <text class="section-title">模板详情</text>
      </view>

      <view class="detail-list">
        <view class="detail-item" v-for="(dayItems, dayOfWeek) in groupedTemplateItems" :key="dayOfWeek">
          <view class="day-info">
            <text class="day-name">{{ weekdays[dayOfWeek - 1] }}</text>
          </view>
          <view class="time-info">
            <view class="time-slot" v-for="(item, index) in dayItems" :key="index">
              <text class="time-range">{{ item.startTime.substring(0, 5) }} - {{ item.endTime.substring(0, 5) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 容错设置 -->
    <view class="tolerance-section">
      <view class="section-header">
        <text class="section-title">容错设置</text>
      </view>

      <view class="tolerance-options">
        <view class="option-item">
          <view class="option-header">
            <text class="option-label">启用到店时间过滤</text>
            <switch :checked="toleranceSettings.enableArrivalFilter" @change="onFilterToggle" />
          </view>
          <text class="option-desc">启用后，客户只能预约您能及时到店的时间段</text>
        </view>

        <view class="option-item" v-if="toleranceSettings.enableArrivalFilter">
          <text class="option-label">到店所需时间</text>
          <view class="time-input-group">
            <input class="time-input" type="number" v-model="toleranceSettings.arrivalTimeHours" step="0.5" min="0"
              max="24" @input="onArrivalTimeChange" />
            <text class="time-unit">小时</text>
          </view>
          <text class="option-desc">设置您从当前位置到咨询中心所需的时间</text>
        </view>

        <view class="preview-section" v-if="toleranceSettings.enableArrivalFilter">
          <view class="preview-header">
            <text class="preview-title">预览效果</text>
          </view>
          <view class="preview-content">
            <view class="preview-item">
              <text class="preview-label">当前时间：</text>
              <text class="preview-value">{{ currentTime }}</text>
            </view>
            <view class="preview-item">
              <text class="preview-label">最早可预约线下咨询：</text>
              <text class="preview-value">{{ earliestOfflineTime }}</text>
            </view>
            <view class="preview-warning">
              <text>{{ toleranceSettings.arrivalTimeHours }}小时内的时间段将被过滤</text>
            </view>
          </view>
        </view>

        <view class="tolerance-actions">
          <button class="save-tolerance-btn" @click="saveToleranceSettings">保存容错设置</button>
          <button class="reset-tolerance-btn" @click="resetToleranceSettings">重置默认</button>
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="apply-section" v-if="selectedTemplate">
      <view class="section-header">
        <text class="section-title">应用设置</text>
      </view>

      <view class="apply-options">
        <view class="option-item">
          <text class="option-label">应用到日期范围</text>
          <view class="date-range">
            <picker mode="date" :value="applyStartDate" @change="onStartDateChange">
              <view class="date-picker">{{ applyStartDate || '开始日期' }}</view>
            </picker>
            <text class="separator">至</text>
            <picker mode="date" :value="applyEndDate" @change="onEndDateChange">
              <view class="date-picker">{{ applyEndDate || '结束日期' }}</view>
            </picker>
          </view>
        </view>

        <view class="option-item">
          <text class="option-label">设为默认模板</text>
          <switch :checked="setAsDefault" @change="onDefaultChange" />
        </view>
      </view>

      <button class="apply-btn" @click="applyTemplate" :disabled="!applyStartDate || !applyEndDate">
        应用模板
      </button>
    </view>

    <!-- 创建模板弹窗 -->
    <view class="create-template-modal" v-if="showCreateTemplate" @click="closeCreateTemplate">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ editingTemplate ? '编辑模板' : '新建模板' }}</text>
          <text class="modal-close" @click="closeCreateTemplate">×</text>
        </view>

        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">模板名称</text>
            <input class="form-input" v-model="templateForm.name" placeholder="请输入模板名称" maxlength="20" />
          </view>

          <view class="form-item">
            <text class="form-label">描述</text>
            <textarea class="form-textarea" v-model="templateForm.remark" placeholder="请输入模板描述"
              maxlength="100"></textarea>
          </view>

          <view class="form-item">
            <text class="form-label">每日工作时间设置</text>



            <view class="daily-schedule">
              <view class="day-schedule" v-for="(day, index) in weekdays" :key="index" @click.stop>
                <view class="day-header">
                  <view class="day-info">
                    <text class="day-name">{{ day }}</text>
                    <switch :checked="templateForm.dailySchedules[index].isWorking"
                      @change="(e) => toggleDayWorking(index, e.detail.value)" @click.stop />
                  </view>
                </view>

                <view class="day-content" v-if="templateForm.dailySchedules[index].isWorking">
                  <view class="time-range" @click.stop>
                    <picker mode="time" :value="templateForm.dailySchedules[index].startTime"
                      @change="(e) => updateDayTime(index, 'startTime', e.detail.value)">
                      <view class="time-picker">{{ templateForm.dailySchedules[index].startTime || '开始时间' }}</view>
                    </picker>
                    <text class="separator">至</text>
                    <picker mode="time" :value="templateForm.dailySchedules[index].endTime"
                      @change="(e) => updateDayTime(index, 'endTime', e.detail.value)">
                      <view class="time-picker">{{ templateForm.dailySchedules[index].endTime || '结束时间' }}</view>
                    </picker>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn-cancel" @click="closeCreateTemplate">取消</button>
          <button class="btn-confirm" @click="saveTemplate" :disabled="!isTemplateFormValid">保存</button>
        </view>
      </view>
    </view>


  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  getTemplatesByCounselorId,
  createTemplate,
  updateTemplate,
  deleteTemplate as deleteTemplateApi,
  setDefaultTemplate,
  applyTemplateToSchedule,
  getConsultantToleranceSettings,
  updateConsultantArrivalTime,
  updateConsultantFilterEnabled,
  resetConsultantToleranceSettings
} from '@/api/consultant-app.js'

// 响应式数据
const templates = ref([])
const selectedTemplate = ref(null)
const selectedTemplateItems = ref([])
const showCreateTemplate = ref(false)
const editingTemplate = ref(null)

// 应用设置
const applyStartDate = ref('')
const applyEndDate = ref('')
const setAsDefault = ref(false)

// 容错设置
const toleranceSettings = ref({
  enableArrivalFilter: true,
  arrivalTimeHours: 2.0
})

const currentTime = ref('')
const earliestOfflineTime = ref('')

// 表单数据
const templateForm = ref({
  name: '',
  remark: '',
  dailySchedules: [
    // 周一到周日的排班设置
    { isWorking: true, startTime: '09:00', endTime: '18:00' },
    { isWorking: true, startTime: '09:00', endTime: '18:00' },
    { isWorking: true, startTime: '09:00', endTime: '18:00' },
    { isWorking: true, startTime: '09:00', endTime: '18:00' },
    { isWorking: true, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' }
  ]
})

// 星期数组
const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 计算属性
const isTemplateFormValid = computed(() => {
  const hasName = templateForm.value.name.trim()
  const hasWorkingDay = templateForm.value.dailySchedules.some(day => day.isWorking)
  const hasValidTimes = templateForm.value.dailySchedules.every(day => {
    if (!day.isWorking) return true
    return day.startTime && day.endTime && day.startTime < day.endTime
  })

  return hasName && hasWorkingDay && hasValidTimes
})

// 按天分组的模板项
const groupedTemplateItems = computed(() => {
  const groups = {}
  if (selectedTemplateItems.value && selectedTemplateItems.value.length > 0) {
    selectedTemplateItems.value.forEach(item => {
      const dayOfWeek = item.dayOfWeek
      if (!groups[dayOfWeek]) {
        groups[dayOfWeek] = []
      }
      groups[dayOfWeek].push(item)
    })
  }
  return groups
})

// 生命周期
onMounted(() => {
  loadTemplates()
  loadToleranceSettings()
  updateCurrentTime()

  // 每分钟更新一次当前时间
  setInterval(updateCurrentTime, 60000)
})

// 加载模板列表
const loadTemplates = async () => {
  try {
    // 这里需要获取当前咨询师ID
    const counselorId = 1 // 实际应该从用户信息中获取
    const res = await getTemplatesByCounselorId(counselorId)
    if (res.code === 200) {
      templates.value = res.data || []

      // 自动选择默认模板
      const defaultTemplate = templates.value.find(t => t.isDefault)
      if (defaultTemplate) {
        selectTemplate(defaultTemplate)
      }
    }
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

// 选择模板
const selectTemplate = (template) => {
  selectedTemplate.value = template.id
  selectedTemplateItems.value = template.templateItems || []
}

// 获取工作日文本
const getWorkDaysText = (templateItems) => {
  if (!templateItems || templateItems.length === 0) return '无'
  const workDays = [...new Set(templateItems.map(item => item.dayOfWeek))].sort()
  return workDays.map(day => weekdays[day - 1]).join('、')
}

// 获取工作时间范围
const getWorkTimeRange = (templateItems) => {
  if (!templateItems || templateItems.length === 0) return '无'

  // 按天分组显示时间段
  const dayGroups = {}
  templateItems.forEach(item => {
    const dayOfWeek = item.dayOfWeek
    if (!dayGroups[dayOfWeek]) {
      dayGroups[dayOfWeek] = []
    }
    dayGroups[dayOfWeek].push({
      startTime: item.startTime.substring(0, 5),
      endTime: item.endTime.substring(0, 5)
    })
  })

  // 如果所有天的时间段都相同，显示统一时间
  const allTimeSlots = Object.values(dayGroups).flat()
  const uniqueTimeSlots = allTimeSlots.filter((slot, index, arr) =>
    arr.findIndex(s => s.startTime === slot.startTime && s.endTime === slot.endTime) === index
  )

  if (uniqueTimeSlots.length === 1) {
    return `${uniqueTimeSlots[0].startTime} - ${uniqueTimeSlots[0].endTime}`
  } else {
    // 显示时间范围
    const startTimes = allTimeSlots.map(slot => slot.startTime).sort()
    const endTimes = allTimeSlots.map(slot => slot.endTime).sort()
    return `${startTimes[0]} - ${endTimes[endTimes.length - 1]} (多时段)`
  }
}

// 编辑模板
const editTemplate = (template) => {
  editingTemplate.value = template

  // 初始化每日排班数据
  const dailySchedules = [
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' },
    { isWorking: false, startTime: '09:00', endTime: '18:00' }
  ]

  // 从templateItems中恢复每日排班设置
  if (template.templateItems && template.templateItems.length > 0) {
    template.templateItems.forEach(item => {
      const dayIndex = item.dayOfWeek - 1 // 转换为0-6的索引
      if (dayIndex >= 0 && dayIndex < 7) {
        dailySchedules[dayIndex].isWorking = true
        dailySchedules[dayIndex].startTime = item.startTime.substring(0, 5)
        dailySchedules[dayIndex].endTime = item.endTime.substring(0, 5)
      }
    })
  }

  templateForm.value = {
    name: template.name,
    remark: template.remark || '',
    dailySchedules: dailySchedules
  }
  showCreateTemplate.value = true
}

// 删除模板
const deleteTemplate = async (template) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除模板"${template.templateName}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteTemplateApi([template.id])
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          loadTemplates()
        } catch (error) {
          console.error('删除模板失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 切换某天的工作状态
const toggleDayWorking = (dayIndex, isWorking) => {
  templateForm.value.dailySchedules[dayIndex].isWorking = isWorking
}

// 更新某天的时间
const updateDayTime = (dayIndex, field, value) => {
  templateForm.value.dailySchedules[dayIndex][field] = value
}

// 保存模板
const saveTemplate = async () => {
  try {
    // 构建模板数据，包含每日排班的templateItems
    const templateItems = []

    templateForm.value.dailySchedules.forEach((daySchedule, dayIndex) => {
      if (daySchedule.isWorking) {
        templateItems.push({
          dayOfWeek: dayIndex + 1, // 1-7对应周一到周日
          startTime: daySchedule.startTime + ':00',
          endTime: daySchedule.endTime + ':00',
          centerId: 1 // 默认中心ID
        })
      }
    })

    const data = {
      name: templateForm.value.name,
      remark: templateForm.value.remark,
      counselorId: 1, // 实际应该从用户信息中获取
      templateItems: templateItems
    }

    let res
    if (editingTemplate.value) {
      res = await updateTemplate({ ...data, id: editingTemplate.value.id })
    } else {
      res = await createTemplate(data)
    }

    if (res.code === 200) {
      uni.showToast({
        title: editingTemplate.value ? '更新成功' : '创建成功',
        icon: 'success'
      })
      closeCreateTemplate()
      loadTemplates()
    }
  } catch (error) {
    console.error('保存模板失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 关闭创建模板弹窗
const closeCreateTemplate = () => {
  showCreateTemplate.value = false
  editingTemplate.value = null
  templateForm.value = {
    name: '',
    remark: '',
    dailySchedules: [
      // 重置为默认的每日排班设置
      { isWorking: true, startTime: '09:00', endTime: '18:00' },
      { isWorking: true, startTime: '09:00', endTime: '18:00' },
      { isWorking: true, startTime: '09:00', endTime: '18:00' },
      { isWorking: true, startTime: '09:00', endTime: '18:00' },
      { isWorking: true, startTime: '09:00', endTime: '18:00' },
      { isWorking: false, startTime: '09:00', endTime: '18:00' },
      { isWorking: false, startTime: '09:00', endTime: '18:00' }
    ]
  }
}



const onStartDateChange = (e) => {
  applyStartDate.value = e.detail.value
}

const onEndDateChange = (e) => {
  applyEndDate.value = e.detail.value
}

const onDefaultChange = (e) => {
  setAsDefault.value = e.detail.value
}

// 容错设置相关方法
const loadToleranceSettings = async () => {
  try {
    const consultantId = 1 // 实际应该从用户信息中获取
    const centerId = 1 // 实际应该从当前中心获取

    const res = await getConsultantToleranceSettings(consultantId, centerId)
    if (res.code === 200) {
      toleranceSettings.value = {
        enableArrivalFilter: res.data.config?.enableArrivalFilter || true,
        arrivalTimeHours: res.data.config?.arrivalTimeHours || 2.0
      }
      updateEarliestTime()
    }
  } catch (error) {
    console.error('加载容错设置失败:', error)
  }
}

const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
  updateEarliestTime()
}

const updateEarliestTime = () => {
  if (toleranceSettings.value.enableArrivalFilter) {
    const now = new Date()
    const earliest = new Date(now.getTime() + toleranceSettings.value.arrivalTimeHours * 60 * 60 * 1000)
    earliestOfflineTime.value = earliest.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    earliestOfflineTime.value = '无限制'
  }
}

const onFilterToggle = (e) => {
  toleranceSettings.value.enableArrivalFilter = e.detail.value
  updateEarliestTime()
}

const onArrivalTimeChange = () => {
  updateEarliestTime()
}

const saveToleranceSettings = async () => {
  try {
    const consultantId = 1 // 实际应该从用户信息中获取
    const centerId = 1 // 实际应该从当前中心获取

    // 保存到店时间
    await updateConsultantArrivalTime(consultantId, centerId, toleranceSettings.value.arrivalTimeHours)

    // 保存过滤开关
    await updateConsultantFilterEnabled(consultantId, centerId, toleranceSettings.value.enableArrivalFilter)

    uni.showToast({
      title: '容错设置保存成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('保存容错设置失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

const resetToleranceSettings = async () => {
  uni.showModal({
    title: '确认重置',
    content: '确定要重置为默认容错设置吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const consultantId = 1 // 实际应该从用户信息中获取
          const centerId = 1 // 实际应该从当前中心获取

          await resetConsultantToleranceSettings(consultantId, centerId)

          // 重新加载设置
          await loadToleranceSettings()

          uni.showToast({
            title: '已重置为默认设置',
            icon: 'success'
          })
        } catch (error) {
          console.error('重置容错设置失败:', error)
          uni.showToast({
            title: '重置失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 应用模板
const applyTemplate = async () => {
  try {
    const data = {
      templateId: selectedTemplate.value,
      startDate: applyStartDate.value,
      endDate: applyEndDate.value,
      setAsDefault: setAsDefault.value
    }

    const res = await applyTemplateToSchedule(data)
    if (res.code === 200) {
      uni.showToast({
        title: '应用成功',
        icon: 'success'
      })

      // 如果设为默认，更新模板状态
      if (setAsDefault.value) {
        loadTemplates()
      }
    }
  } catch (error) {
    console.error('应用模板失败:', error)
    uni.showToast({
      title: '应用失败',
      icon: 'none'
    })
  }
}
</script>

<style scoped lang="scss">
.schedule-setting-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.template-section,
.template-detail-section,
.tolerance-section,
.apply-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .add-template-btn {
      padding: 12rpx 24rpx;
      background-color: #1890ff;
      color: #fff;
      border: none;
      border-radius: 20rpx;
      font-size: 24rpx;
    }
  }
}

.template-list {
  .template-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      background-color: #f0f7ff;
      border-left: 4rpx solid #1890ff;
    }

    .template-info {
      flex: 1;

      .template-name {
        display: block;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .template-desc {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 12rpx;
      }

      .template-meta {

        .work-days,
        .work-time {
          display: block;
          font-size: 22rpx;
          color: #999;
          margin-bottom: 4rpx;
        }
      }
    }

    .template-actions {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .default-badge {
        padding: 6rpx 12rpx;
        background-color: #52c41a;
        color: #fff;
        border-radius: 12rpx;
        font-size: 20rpx;
      }

      .action-btn {
        padding: 12rpx 20rpx;
        border: 1rpx solid #d9d9d9;
        border-radius: 16rpx;
        font-size: 22rpx;
        background-color: #fff;
        color: #666;

        &.delete-btn {
          color: #ff4d4f;
          border-color: #ff4d4f;
        }
      }
    }
  }
}

.detail-list {
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .day-info {
      .day-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .time-info {
      .time-slot {
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .time-range {
          font-size: 24rpx;
          color: #1890ff;
          font-weight: 500;
          display: inline-block;
          padding: 4rpx 8rpx;
          background-color: #e6f7ff;
          border-radius: 4rpx;
          margin-bottom: 4rpx;
        }
      }
    }
  }
}

.tolerance-options,
.apply-options {
  padding: 30rpx;

  .option-item {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .option-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;
    }

    .option-label {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .option-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.5;
    }

    .time-input-group {
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin: 16rpx 0;

      .time-input {
        width: 120rpx;
        padding: 16rpx 20rpx;
        border: 1rpx solid #d9d9d9;
        border-radius: 8rpx;
        text-align: center;
        font-size: 28rpx;
        color: #333;

        &:focus {
          border-color: #1890ff;
        }
      }

      .time-unit {
        font-size: 26rpx;
        color: #666;
      }
    }

    .date-range,
    .time-range {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .date-picker,
      .time-picker {
        flex: 1;
        padding: 20rpx;
        border: 1rpx solid #d9d9d9;
        border-radius: 8rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333;
      }

      .separator {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.preview-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 20rpx;

  .preview-header {
    margin-bottom: 16rpx;

    .preview-title {
      font-size: 26rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .preview-content {
    .preview-item {
      display: flex;
      margin-bottom: 12rpx;

      .preview-label {
        font-size: 24rpx;
        color: #666;
        width: 160rpx;
      }

      .preview-value {
        font-size: 24rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .preview-warning {
      margin-top: 16rpx;
      padding: 12rpx 16rpx;
      background-color: #fff7e6;
      border-left: 4rpx solid #fa8c16;
      border-radius: 4rpx;

      text {
        font-size: 22rpx;
        color: #fa8c16;
      }
    }
  }
}

.tolerance-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;

  .save-tolerance-btn,
  .reset-tolerance-btn {
    flex: 1;
    padding: 24rpx 0;
    border-radius: 12rpx;
    font-size: 28rpx;
    border: none;
  }

  .save-tolerance-btn {
    background-color: #52c41a;
    color: #fff;
  }

  .reset-tolerance-btn {
    background-color: #f5f5f5;
    color: #666;
  }
}

.apply-btn {
  width: calc(100% - 60rpx);
  margin: 0 30rpx 30rpx;
  padding: 28rpx 0;
  background-color: #1890ff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;

  &:disabled {
    background-color: #d9d9d9;
    color: #999;
  }
}

.create-template-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .modal-content {
    width: 640rpx;
    background-color: #fff;
    border-radius: 20rpx;
    max-height: 80vh;
    overflow-y: auto;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .modal-close {
        font-size: 40rpx;
        color: #999;
      }
    }

    .modal-body {
      padding: 30rpx;

      .form-item {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 16rpx;
        }

        .form-input,
        .form-textarea {
          width: 100%;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          font-size: 28rpx;
          color: #333;
          box-sizing: border-box;

          .form-input {
            height: 60rpx;
          }

          &:focus {
            border-color: #1890ff;
          }
        }

        .form-textarea {
          min-height: 120rpx;
          resize: none;
        }

        .weekdays-selector {
          display: flex;
          gap: 12rpx;
          flex-wrap: wrap;

          .weekday-item {
            padding: 16rpx 20rpx;
            border: 1rpx solid #d9d9d9;
            border-radius: 20rpx;
            font-size: 24rpx;
            color: #666;
            text-align: center;

            &.active {
              background-color: #1890ff;
              color: #fff;
              border-color: #1890ff;
            }
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 20rpx;
      padding: 30rpx;
      border-top: 1rpx solid #f0f0f0;

      .btn-cancel,
      .btn-confirm {
        flex: 1;
        padding: 24rpx 0;
        border-radius: 12rpx;
        font-size: 28rpx;
        border: none;
      }

      .btn-cancel {
        background-color: #f5f5f5;
        color: #666;
      }

      .btn-confirm {
        background-color: #1890ff;
        color: #fff;

        &:disabled {
          background-color: #d9d9d9;
          color: #999;
        }
      }
    }
  }
}

// 每日排班设置样式
.daily-schedule {
  .day-schedule {
    margin-bottom: 20rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;

    .day-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;

      .day-name {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .day-content {
      .time-range {
        display: flex;
        align-items: center;
        gap: 15rpx;

        .time-picker {
          flex: 1;
          padding: 12rpx 20rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 6rpx;
          font-size: 26rpx;
          text-align: center;
          background-color: #fff;
        }

        .separator {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}
</style>
