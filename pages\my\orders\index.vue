<template>
  <view class="orders-page">
    <!-- 顶部Tab -->
    <view class="tab-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', { active: currentTab === index }]"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 订单列表 -->
    <scroll-view 
      scroll-y 
      class="order-list"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <view v-if="orderList.length > 0">
        <view 
          v-for="order in orderList" 
          :key="order.id"
          class="order-item"
          @click="goToOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-info">
              <text class="order-no">订单号：{{ order.orderNo }}</text>
              <text class="order-time">{{ formatTime(order.createTime) }}</text>
            </view>
            <view class="order-status" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </view>
          </view>
          
          <!-- 订单内容 -->
          <view class="order-content">
            <image 
              class="product-image" 
              :src="order.productImage || defaultCover" 
              mode="aspectFill"
            ></image>
            <view class="product-info">
              <text class="product-title">{{ order.productTitle }}</text>
              <text class="product-desc">{{ order.productDesc }}</text>
              <view class="price-info">
                <text class="price">¥{{ order.amount }}</text>
                <text class="type-tag">{{ getOrderTypeText(order.type) }}</text>
              </view>
            </view>
          </view>
          
          <!-- 订单操作 -->
          <view class="order-actions" v-if="getOrderActions(order).length > 0">
            <button 
              v-for="action in getOrderActions(order)"
              :key="action.key"
              :class="['action-btn', action.type]"
              @click.stop="handleAction(action.key, order)"
            >
              {{ action.text }}
            </button>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-else class="empty-state">
        <image class="empty-image" src="/static/images/empty-orders.png" mode="aspectFit"></image>
        <text class="empty-text">暂无订单</text>
        <button class="go-shopping-btn" @click="goShopping">去购买</button>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="hasMore && orderList.length > 0" class="load-more">
        <uni-load-more :status="loadMoreStatus"></uni-load-more>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'

// 响应式数据
const currentTab = ref(0)
const refreshing = ref(false)
const hasMore = ref(true)
const loadMoreStatus = ref('more')
const orderList = ref([])

// Tab配置
const tabs = [
  { name: '全部', type: 'all' },
  { name: '待支付', type: 'pending' },
  { name: '已支付', type: 'paid' },
  { name: '已完成', type: 'completed' },
  { name: '已取消', type: 'cancelled' }
]

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-cover.png'

// 分页参数
const pageParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 切换Tab
const switchTab = (index) => {
  currentTab.value = index
  resetAndLoadOrders()
}

// 重置并加载订单
const resetAndLoadOrders = () => {
  pageParams.pageNum = 1
  hasMore.value = true
  orderList.value = []
  loadOrders()
}

// 加载订单列表
const loadOrders = async (loadMore = false) => {
  try {
    if (!loadMore) {
      loadMoreStatus.value = 'loading'
    }
    
    const params = {
      ...pageParams,
      status: tabs[currentTab.value].type === 'all' ? '' : tabs[currentTab.value].type
    }
    
    // 这里应该调用实际的API
    const res = await mockGetOrders(params)
    
    if (res.code === 200) {
      const newOrders = res.data || []
      
      if (loadMore) {
        orderList.value = [...orderList.value, ...newOrders]
      } else {
        orderList.value = newOrders
      }
      
      // 判断是否还有更多数据
      hasMore.value = newOrders.length >= pageParams.pageSize
      loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    }
  } catch (error) {
    console.error('加载订单失败:', error)
    loadMoreStatus.value = 'more'
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 模拟API调用
const mockGetOrders = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockOrders = [
        {
          id: 1,
          orderNo: 'CO2024071501',
          type: 'consultation',
          status: 'pending',
          amount: '299.00',
          productTitle: '张医生 - 个人心理咨询',
          productDesc: '2024-07-15 14:00 (50分钟)',
          productImage: '',
          createTime: '2024-07-15 10:30:00'
        },
        {
          id: 2,
          orderNo: 'ME2024071502',
          type: 'meditation',
          status: 'paid',
          amount: '19.90',
          productTitle: '焦虑缓解冥想',
          productDesc: '快速缓解焦虑情绪，恢复内心平静',
          productImage: '',
          createTime: '2024-07-15 09:15:00'
        }
      ]
      
      resolve({
        code: 200,
        data: mockOrders.filter(order => 
          params.status === '' || order.status === params.status
        )
      })
    }, 500)
  })
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  pageParams.pageNum = 1
  await loadOrders()
  refreshing.value = false
}

// 上拉加载更多
const onLoadMore = () => {
  if (hasMore.value && loadMoreStatus.value !== 'loading') {
    pageParams.pageNum++
    loadOrders(true)
  }
}

// 获取订单状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'confirmed': '已确认',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取订单状态样式类
const getStatusClass = (status) => {
  return `status-${status}`
}

// 获取订单类型文本
const getOrderTypeText = (type) => {
  const typeMap = {
    'consultation': '咨询',
    'course': '课程',
    'meditation': '冥想',
    'assessment': '测评'
  }
  return typeMap[type] || type
}

// 获取订单操作按钮
const getOrderActions = (order) => {
  const actions = []
  
  switch (order.status) {
    case 'pending':
      actions.push(
        { key: 'cancel', text: '取消订单', type: 'cancel' },
        { key: 'pay', text: '立即支付', type: 'primary' }
      )
      break
    case 'paid':
      if (order.type === 'consultation') {
        actions.push({ key: 'fillInfo', text: '填写信息', type: 'primary' })
      } else {
        actions.push({ key: 'use', text: '立即使用', type: 'primary' })
      }
      break
    case 'completed':
      actions.push({ key: 'review', text: '评价', type: 'secondary' })
      break
  }
  
  return actions
}

// 处理订单操作
const handleAction = (action, order) => {
  switch (action) {
    case 'pay':
      // 跳转到支付页面
      goToOrderDetail(order)
      break
    case 'cancel':
      cancelOrder(order)
      break
    case 'fillInfo':
      uni.navigateTo({
        url: `/pages/consultation/basic-info/index?orderId=${order.id}`
      })
      break
    case 'use':
      useProduct(order)
      break
    case 'review':
      goToReview(order)
      break
  }
}

// 取消订单
const cancelOrder = (order) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        // 调用取消订单API
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        })
        resetAndLoadOrders()
      }
    }
  })
}

// 使用商品
const useProduct = (order) => {
  switch (order.type) {
    case 'course':
      uni.navigateTo({
        url: `/pages/course/detail/index?id=${order.productId}`
      })
      break
    case 'meditation':
      uni.navigateTo({
        url: `/pages/meditation/player/index?id=${order.productId}`
      })
      break
    case 'assessment':
      uni.navigateTo({
        url: `/pages/evaluation/detail/index?id=${order.productId}`
      })
      break
  }
}

// 跳转到评价页面
const goToReview = (order) => {
  const reviewPages = {
    'consultation': '/pages/consultation/review/index',
    'course': '/pages/course/review/index',
    'meditation': '/pages/meditation/review/index',
    'assessment': '/pages/evaluation/review/index'
  }
  
  const url = reviewPages[order.type]
  if (url) {
    uni.navigateTo({
      url: `${url}?orderId=${order.id}`
    })
  }
}

// 跳转到订单详情
const goToOrderDetail = (order) => {
  if (order.type === 'consultation') {
    uni.navigateTo({
      url: `/pages/consultation/order-detail/index?id=${order.id}`
    })
  } else {
    // 其他类型的订单可以跳转到对应的详情页
    uni.showToast({
      title: '订单详情开发中',
      icon: 'none'
    })
  }
}

// 去购买
const goShopping = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${month}-${day} ${hours}:${minutes}`
}

// 生命周期
onLoad((options) => {
  // 如果有指定Tab，切换到对应Tab
  if (options.tab) {
    const tabIndex = tabs.findIndex(tab => tab.type === options.tab)
    if (tabIndex !== -1) {
      currentTab.value = tabIndex
    }
  }
})

onShow(() => {
  resetAndLoadOrders()
})
</script>

<style lang="scss" scoped>
.orders-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.tab-bar {
  background-color: #fff;
  display: flex;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 32rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;
    
    &.active {
      color: #667eea;
      font-weight: 600;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2rpx;
      }
    }
  }
}

.order-list {
  height: calc(100vh - 120rpx);
  padding: 32rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .order-info {
    .order-no {
      display: block;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 8rpx;
    }
    
    .order-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .order-status {
    font-size: 26rpx;
    font-weight: 500;
    
    &.status-pending {
      color: #ff9800;
    }
    
    &.status-paid {
      color: #4CAF50;
    }
    
    &.status-completed {
      color: #2196F3;
    }
    
    &.status-cancelled {
      color: #f44336;
    }
  }
}

.order-content {
  display: flex;
  padding: 24rpx 32rpx;
  
  .product-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
  }
  
  .product-info {
    flex: 1;
    
    .product-title {
      display: block;
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .product-desc {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 16rpx;
    }
    
    .price-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .price {
        font-size: 32rpx;
        font-weight: 600;
        color: #ff6b35;
      }
      
      .type-tag {
        font-size: 22rpx;
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
    }
  }
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  
  .action-btn {
    height: 64rpx;
    padding: 0 24rpx;
    border-radius: 32rpx;
    font-size: 26rpx;
    
    &.primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      border: none;
    }
    
    &.secondary {
      background-color: #fff;
      color: #667eea;
      border: 1rpx solid #667eea;
    }
    
    &.cancel {
      background-color: #fff;
      color: #999;
      border: 1rpx solid #ddd;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 48rpx;
  }
  
  .go-shopping-btn {
    width: 200rpx;
    height: 64rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border-radius: 32rpx;
    font-size: 26rpx;
    border: none;
  }
}

.load-more {
  padding: 32rpx 0;
}
</style>
