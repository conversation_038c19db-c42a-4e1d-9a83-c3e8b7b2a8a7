# 左侧导航文字优化文档

## 问题描述

测评分类中的长文本（如"心理健康测评"）在200rpx宽度的左侧导航中会出现挤压和不美观的换行问题。

## 优化方案

### 1. 减少内边距
```scss
.nav-item {
  padding: 16rpx 8rpx;  // 从 20rpx 16rpx 减少到 16rpx 8rpx
}
```

### 2. 优化字体大小
```scss
.nav-text {
  font-size: 20rpx;     // 从 22rpx 减少到 20rpx
}
```

### 3. 使用多行文本截断
```scss
.nav-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;        // 最多显示2行
  line-clamp: 2;                // 标准属性
  overflow: hidden;
  max-height: 48rpx;            // 限制最大高度
}
```

### 4. 优化行高和间距
```scss
.nav-text {
  line-height: 1.2;             // 紧凑的行高
  padding: 0 2rpx;              // 最小内边距
  word-break: break-all;        // 允许单词内换行
}
```

## 技术细节

### 文本处理策略
1. **字体大小**: 20rpx - 在保持可读性的前提下尽可能小
2. **行高**: 1.2 - 紧凑但不拥挤
3. **最大行数**: 2行 - 避免过度换行
4. **文本截断**: 超出部分用省略号显示
5. **换行策略**: break-all - 允许在任意位置换行

### 布局优化
```scss
.nav-item {
  min-height: 80rpx;            // 保证最小高度
  display: flex;                // flex布局
  align-items: center;          // 垂直居中
  justify-content: center;      // 水平居中
}
```

### 兼容性处理
```scss
// Webkit内核浏览器
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;

// 标准属性
line-clamp: 2;
```

## 测试用例

### 短文本测试
- ✅ "咨询师" - 单行显示，居中对齐
- ✅ "课程" - 单行显示，居中对齐
- ✅ "冥想" - 单行显示，居中对齐

### 中等长度文本测试
- ✅ "心理测评" - 单行显示，正常
- ✅ "情感咨询" - 单行显示，正常

### 长文本测试
- ✅ "心理健康测评" - 两行显示，不挤压
- ✅ "专业心理咨询" - 两行显示，美观
- ✅ "深度心理分析" - 两行显示，居中

### 超长文本测试
- ✅ "综合心理健康评估测试" - 两行显示，超出部分省略

## 视觉效果

### 优化前
```
┌─────────────┐
│心理健康测│
│    评      │  ← 不美观的换行
└─────────────┘
```

### 优化后
```
┌─────────────┐
│  心理健康   │
│    测评     │  ← 美观的居中换行
└─────────────┘
```

## 响应式考虑

### 不同屏幕尺寸
- **小屏幕**: 20rpx字体确保可读性
- **大屏幕**: 文字不会过小，保持美观
- **横屏**: 布局保持稳定

### 不同文本长度
- **1-2字**: 单行居中显示
- **3-4字**: 单行显示，可能换行
- **5-6字**: 两行显示，美观换行
- **7+字**: 两行显示，超出省略

## 性能优化

### CSS优化
```scss
.nav-text {
  // 使用硬件加速
  transform: translateZ(0);
  
  // 优化渲染
  will-change: auto;
  
  // 避免重排
  contain: layout style;
}
```

### 渲染优化
- 使用CSS多行截断而非JavaScript
- 避免频繁的DOM操作
- 利用浏览器原生的文本处理能力

## 总结

通过以下优化措施：

1. **减少内边距**: 为文字留出更多空间
2. **优化字体大小**: 在可读性和空间之间找到平衡
3. **多行文本处理**: 使用CSS原生的多行截断
4. **精确的高度控制**: 避免布局抖动
5. **兼容性处理**: 确保在不同浏览器中正常显示

现在左侧导航可以优雅地处理各种长度的文本，既保持了美观性，又确保了可读性。

### 最终效果
- ✅ 短文本：单行居中显示
- ✅ 中等文本：合理换行
- ✅ 长文本：两行显示，超出省略
- ✅ 整体布局：美观、一致、稳定
