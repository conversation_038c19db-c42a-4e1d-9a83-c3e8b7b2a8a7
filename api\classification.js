import request from '../utils/request'

// 查询产品列表
export function listProduct(query) {
	return request({
		url: '/psy/product/list',
		method: 'get',
		params: query
	})
}

// 查询产品详情
export function getProduct(productId) {
	return request({
		url: `/psy/product/${productId}`,
		method: 'get'
	})
}

// 获取分类树形结构
export function getCategoryTree() {
	return request({
		url: '/psy/category/treeWithProducts',
		method: 'get'
	})
}

// 获取子分类列表
export function getCategoryChildren(parentId) {
	return request({
		url: `/psy/category/children/${parentId}`,
		method: 'get'
	})
}

// 获取分类详情
export function getCategoryDetail(categoryId) {
	return request({
		url: `/psy/category/${categoryId}`,
		method: 'get'
	})
}

// 获取可用的咨询师列表
export function listAvailableConsultants(query) {
	// 如果有时间参数，使用时间筛选接口
	if (query && query.date && query.startTime && query.endTime) {
		return request({
			url: '/miniapp/timeSlot/counselors',
			method: 'get',
			params: query
		});
	}

	// 如果没有时间参数，返回所有咨询师
	return request({
		url: '/system/consultant/list',
		method: 'get',
		params: query
	});
}

// 搜索咨询师
export function searchConsultants(params) {
	return request({
		url: '/system/consultant/search',
		method: 'get',
		params: params
	})
}

// 获取咨询师详情
export function getConsultantDetail(id) {
	return request({
		url: `/system/consultant/detail/${id}`,
		method: 'get'
	})
}

// 获取咨询师评价列表
export function getConsultantReviews(consultantId) {
	return request({
		url: `/miniapp/user/consultant/review/consultant/${consultantId}`,
		method: 'get'
	})
}

// 获取咨询师评价统计
export function getReviewStatistics(consultantId) {
	return request({
		url: `/miniapp/user/consultant/review/statistics/${consultantId}`,
		method: 'get'
	})
}

// 提交咨询评价
export function submitReview(reviewData) {
	return request({
		url: '/miniapp/user/consultant/review/submit',
		method: 'post',
		data: reviewData
	})
}

// 获取我的评价列表
export function getMyReviews() {
	return request({
		url: '/miniapp/user/consultant/review/myReviews',
		method: 'get'
	})
}

// 检查是否可以评价
export function checkCanRate(recordId) {
	return request({
		url: `/miniapp/user/consultant/review/canRate/${recordId}`,
		method: 'get'
	})
}