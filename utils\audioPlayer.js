/**
 * 音频播放器工具类
 * 基于 wx.createInnerAudioContext 实现
 */

class AudioPlayer {
  constructor() {
    this.audioContext = null
    this.isPlaying = false
    this.currentTime = 0
    this.duration = 0
    this.src = ''
    this.playbackRate = 1.0
    this.backgroundAudioManager = null
    this.useBackgroundAudio = false

    // 事件回调
    this.onPlayCallback = null
    this.onPauseCallback = null
    this.onTimeUpdateCallback = null
    this.onEndedCallback = null
    this.onErrorCallback = null
    this.onCanplayCallback = null
    this.onPlaybackRateChangeCallback = null
  }

  /**
   * 初始化音频播放器
   * @param {string} src - 音频源地址
   * @param {Object} options - 配置选项
   */
  init(src, options = {}) {
    this.destroy()

    this.src = src
    this.playbackRate = options.playbackRate || 1.0
    this.useBackgroundAudio = options.useBackgroundAudio || false

    if (this.useBackgroundAudio && uni.getBackgroundAudioManager) {
      // 使用背景音频管理器（支持倍速）
      this.backgroundAudioManager = uni.getBackgroundAudioManager()
      this.initBackgroundAudio(src, options)
    } else {
      // 使用内部音频上下文
      this.audioContext = uni.createInnerAudioContext()
      this.initInnerAudio(src, options)
    }

    return this
  }

  /**
   * 初始化内部音频上下文
   */
  initInnerAudio(src, options) {
    // 设置基本属性
    this.audioContext.src = src
    this.audioContext.autoplay = options.autoplay || false
    this.audioContext.loop = options.loop || false
    this.audioContext.volume = options.volume || 1.0

    // 绑定事件
    this.bindInnerAudioEvents()
  }

  /**
   * 初始化背景音频管理器
   */
  initBackgroundAudio(src, options) {
    const bgAudio = this.backgroundAudioManager

    // 设置基本属性
    bgAudio.src = src
    bgAudio.title = options.title || '音频播放'
    bgAudio.epname = options.epname || ''
    bgAudio.singer = options.singer || ''
    bgAudio.coverImgUrl = options.coverImgUrl || ''

    // 设置倍速（如果支持）
    if (bgAudio.playbackRate !== undefined) {
      bgAudio.playbackRate = this.playbackRate
    }

    // 绑定事件
    this.bindBackgroundAudioEvents()
  }

  /**
   * 绑定内部音频事件
   */
  bindInnerAudioEvents() {
    if (!this.audioContext) return

    this.audioContext.onPlay(() => {
      this.isPlaying = true
      this.onPlayCallback && this.onPlayCallback()
    })

    this.audioContext.onPause(() => {
      this.isPlaying = false
      this.onPauseCallback && this.onPauseCallback()
    })

    this.audioContext.onTimeUpdate(() => {
      this.currentTime = this.audioContext.currentTime
      this.duration = this.audioContext.duration || 0
      this.onTimeUpdateCallback && this.onTimeUpdateCallback({
        currentTime: this.currentTime,
        duration: this.duration
      })
    })

    this.audioContext.onEnded(() => {
      this.isPlaying = false
      this.onEndedCallback && this.onEndedCallback()
    })

    this.audioContext.onError((error) => {
      console.error('音频播放错误:', error)
      this.onErrorCallback && this.onErrorCallback(error)
    })

    this.audioContext.onCanplay(() => {
      this.duration = this.audioContext.duration || 0
      this.onCanplayCallback && this.onCanplayCallback()
    })
  }

  /**
   * 绑定背景音频事件
   */
  bindBackgroundAudioEvents() {
    if (!this.backgroundAudioManager) return

    const bgAudio = this.backgroundAudioManager

    bgAudio.onPlay(() => {
      this.isPlaying = true
      this.onPlayCallback && this.onPlayCallback()
    })

    bgAudio.onPause(() => {
      this.isPlaying = false
      this.onPauseCallback && this.onPauseCallback()
    })

    bgAudio.onTimeUpdate(() => {
      this.currentTime = bgAudio.currentTime
      this.duration = bgAudio.duration || 0
      this.onTimeUpdateCallback && this.onTimeUpdateCallback({
        currentTime: this.currentTime,
        duration: this.duration
      })
    })

    bgAudio.onEnded(() => {
      this.isPlaying = false
      this.onEndedCallback && this.onEndedCallback()
    })

    bgAudio.onError((error) => {
      console.error('背景音频播放错误:', error)
      this.onErrorCallback && this.onErrorCallback(error)
    })

    bgAudio.onCanplay(() => {
      this.duration = bgAudio.duration || 0
      this.onCanplayCallback && this.onCanplayCallback()
    })
  }

  /**
   * 播放音频
   */
  play() {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.play()
    } else if (this.audioContext) {
      this.audioContext.play()
    }
    return this
  }

  /**
   * 暂停音频
   */
  pause() {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.pause()
    } else if (this.audioContext) {
      this.audioContext.pause()
    }
    return this
  }

  /**
   * 停止音频
   */
  stop() {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.stop()
    } else if (this.audioContext) {
      this.audioContext.stop()
    }
    return this
  }

  /**
   * 跳转到指定时间
   * @param {number} time - 时间（秒）
   */
  seek(time) {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.startTime = time
    } else if (this.audioContext) {
      this.audioContext.seek(time)
    }
    return this
  }

  /**
   * 设置音量
   * @param {number} volume - 音量 (0-1)
   */
  setVolume(volume) {
    const vol = Math.max(0, Math.min(1, volume))
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      // 背景音频管理器不支持音量设置
      console.warn('背景音频管理器不支持音量设置')
    } else if (this.audioContext) {
      this.audioContext.volume = vol
    }
    return this
  }

  /**
   * 设置播放速度
   * @param {number} rate - 播放速度 (0.5-2.0)
   */
  setPlaybackRate(rate) {
    const validRate = Math.max(0.5, Math.min(2.0, rate))
    this.playbackRate = validRate

    console.log('设置播放速度:', validRate, '当前模式:', this.useBackgroundAudio ? '背景音频' : '内部音频')

    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      // 背景音频管理器支持倍速
      try {
        // 检查是否支持 playbackRate
        if ('playbackRate' in this.backgroundAudioManager) {
          console.log('设置背景音频倍速:', validRate)
          this.backgroundAudioManager.playbackRate = validRate

          // 验证设置是否成功
          setTimeout(() => {
            const actualRate = this.backgroundAudioManager.playbackRate
            console.log('实际设置的倍速:', actualRate)
            if (actualRate === validRate) {
              this.onPlaybackRateChangeCallback && this.onPlaybackRateChangeCallback(validRate)
              uni.showToast({
                title: `播放速度: ${validRate}x`,
                icon: 'success'
              })
            } else {
              console.warn('倍速设置失败，实际值:', actualRate)
              uni.showToast({
                title: '当前平台不支持倍速播放',
                icon: 'none'
              })
            }
          }, 100)
        } else {
          console.warn('背景音频管理器不支持 playbackRate 属性')
          uni.showToast({
            title: '当前平台不支持倍速播放',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('设置倍速失败:', error)
        uni.showToast({
          title: '倍速设置失败',
          icon: 'none'
        })
      }
    } else if (this.audioContext) {
      // 内部音频上下文不支持倍速
      console.warn('InnerAudioContext 不支持倍速播放')
      uni.showToast({
        title: '请切换到背景音频模式以支持倍速',
        icon: 'none'
      })
    } else {
      console.error('音频播放器未初始化')
      uni.showToast({
        title: '音频播放器未初始化',
        icon: 'none'
      })
    }

    return this
  }

  /**
   * 获取当前播放速度
   */
  getPlaybackRate() {
    return this.playbackRate
  }

  /**
   * 切换播放速度
   * @param {Array} speeds - 可选速度数组
   */
  togglePlaybackRate(speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]) {
    const currentIndex = speeds.indexOf(this.playbackRate)
    const nextIndex = (currentIndex + 1) % speeds.length
    const nextRate = speeds[nextIndex]

    this.setPlaybackRate(nextRate)
    return nextRate
  }

  /**
   * 获取当前播放状态
   */
  getState() {
    return {
      isPlaying: this.isPlaying,
      currentTime: this.currentTime,
      duration: this.duration,
      src: this.src,
      playbackRate: this.playbackRate,
      useBackgroundAudio: this.useBackgroundAudio
    }
  }

  /**
   * 快进
   * @param {number} seconds - 快进秒数
   */
  forward(seconds = 15) {
    const newTime = Math.min(this.duration, this.currentTime + seconds)
    this.seek(newTime)
    return this
  }

  /**
   * 快退
   * @param {number} seconds - 快退秒数
   */
  backward(seconds = 15) {
    const newTime = Math.max(0, this.currentTime - seconds)
    this.seek(newTime)
    return this
  }

  /**
   * 切换播放/暂停状态
   */
  toggle() {
    if (this.isPlaying) {
      this.pause()
    } else {
      this.play()
    }
    return this
  }

  /**
   * 设置事件回调
   */
  onPlay(callback) {
    this.onPlayCallback = callback
    return this
  }

  onPause(callback) {
    this.onPauseCallback = callback
    return this
  }

  onTimeUpdate(callback) {
    this.onTimeUpdateCallback = callback
    return this
  }

  onEnded(callback) {
    this.onEndedCallback = callback
    return this
  }

  onError(callback) {
    this.onErrorCallback = callback
    return this
  }

  onCanplay(callback) {
    this.onCanplayCallback = callback
    return this
  }

  onPlaybackRateChange(callback) {
    this.onPlaybackRateChangeCallback = callback
    return this
  }

  /**
   * 销毁音频播放器
   */
  destroy() {
    if (this.audioContext) {
      this.audioContext.destroy()
      this.audioContext = null
    }

    if (this.backgroundAudioManager) {
      // 注意：背景音频管理器是全局单例，不能销毁，只能停止
      try {
        this.backgroundAudioManager.stop()
      } catch (e) {
        // 忽略停止时的错误
      }
      this.backgroundAudioManager = null
    }

    // 重置状态
    this.isPlaying = false
    this.currentTime = 0
    this.duration = 0
    this.src = ''
    this.playbackRate = 1.0
    this.useBackgroundAudio = false

    // 清除回调
    this.onPlayCallback = null
    this.onPauseCallback = null
    this.onTimeUpdateCallback = null
    this.onEndedCallback = null
    this.onErrorCallback = null
    this.onCanplayCallback = null
    this.onPlaybackRateChangeCallback = null

    return this
  }
}

/**
 * 创建音频播放器实例
 * @param {string} src - 音频源地址
 * @param {Object} options - 配置选项
 * @returns {AudioPlayer} 音频播放器实例
 */
export function createAudioPlayer(src, options = {}) {
  return new AudioPlayer().init(src, options)
}

/**
 * 格式化时间
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串 (mm:ss)
 */
export function formatTime(seconds) {
  if (isNaN(seconds) || seconds < 0) return '00:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 格式化时长
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时长字符串
 */
export function formatDuration(seconds) {
  if (isNaN(seconds) || seconds < 0) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  } else if (mins > 0) {
    return `${mins}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

export default AudioPlayer
