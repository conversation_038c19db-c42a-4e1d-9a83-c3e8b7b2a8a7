<template>
  <view 
    class="favorite-button" 
    :class="{ 'favorited': isFavorited, 'loading': loading }"
    @click="handleClick"
  >
    <uni-icons 
      :type="isFavorited ? 'star-filled' : 'star'" 
      :size="iconSize" 
      :color="iconColor"
    />
    <text v-if="showText" class="favorite-text">
      {{ isFavorited ? '已收藏' : '收藏' }}
    </text>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { 
  addFavorite, 
  removeFavorite, 
  checkFavorite, 
  buildFavoriteData 
} from '@/api/favorite.js'

// Props
const props = defineProps({
  // 页面类型：consultant, course, meditation, assessment
  pageType: {
    type: String,
    required: true,
    validator: (value) => ['consultant', 'course', 'meditation', 'assessment'].includes(value)
  },
  // 目标ID
  targetId: {
    type: [Number, String],
    required: true
  },
  // 目标信息（用于收藏时保存）
  targetInfo: {
    type: Object,
    default: () => ({})
  },
  // 初始收藏状态
  favorited: {
    type: Boolean,
    default: false
  },
  // 收藏ID（如果已收藏）
  favoriteId: {
    type: [Number, String],
    default: null
  },
  // 是否显示文字
  showText: {
    type: Boolean,
    default: false
  },
  // 图标大小
  iconSize: {
    type: [Number, String],
    default: 20
  },
  // 自动检查收藏状态
  autoCheck: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['change', 'success', 'error'])

// 响应式数据
const isFavorited = ref(props.favorited)
const currentFavoriteId = ref(props.favoriteId)
const loading = ref(false)

// 计算属性
const iconColor = computed(() => {
  if (loading.value) return '#ccc'
  return isFavorited.value ? '#ff6b35' : '#666'
})

// 监听props变化
watch(() => props.favorited, (newVal) => {
  isFavorited.value = newVal
})

watch(() => props.favoriteId, (newVal) => {
  currentFavoriteId.value = newVal
})

// 检查收藏状态
const checkFavoriteStatus = async () => {
  if (!props.autoCheck) return
  
  try {
    const targetType = getTargetTypeByPage(props.pageType)
    const res = await checkFavorite({
      targetType,
      targetId: props.targetId
    })
    
    if (res.code === 200) {
      isFavorited.value = res.data
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
  }
}

// 获取目标类型
const getTargetTypeByPage = (pageType) => {
  const typeMap = {
    'consultant': 1,
    'course': 2,
    'meditation': 3,
    'assessment': 4
  }
  return typeMap[pageType] || 1
}

// 处理点击事件
const handleClick = async () => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    if (isFavorited.value) {
      // 取消收藏
      await handleRemoveFavorite()
    } else {
      // 添加收藏
      await handleAddFavorite()
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    emit('error', error)
    
    uni.showToast({
      title: error.message || '操作失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 添加收藏
const handleAddFavorite = async () => {
  const favoriteData = buildFavoriteData(
    props.pageType, 
    props.targetId, 
    props.targetInfo
  )
  
  const res = await addFavorite(favoriteData)
  
  if (res.code === 200) {
    isFavorited.value = true
    currentFavoriteId.value = res.data?.favoriteId
    
    emit('change', {
      favorited: true,
      favoriteId: currentFavoriteId.value
    })
    
    emit('success', {
      action: 'add',
      favoriteId: currentFavoriteId.value
    })
    
    uni.showToast({
      title: '收藏成功',
      icon: 'success'
    })
  } else {
    throw new Error(res.msg || '收藏失败')
  }
}

// 取消收藏
const handleRemoveFavorite = async () => {
  if (!currentFavoriteId.value) {
    throw new Error('收藏ID不存在')
  }
  
  const res = await removeFavorite(currentFavoriteId.value)
  
  if (res.code === 200) {
    isFavorited.value = false
    const oldFavoriteId = currentFavoriteId.value
    currentFavoriteId.value = null
    
    emit('change', {
      favorited: false,
      favoriteId: null
    })
    
    emit('success', {
      action: 'remove',
      favoriteId: oldFavoriteId
    })
    
    uni.showToast({
      title: '取消收藏成功',
      icon: 'success'
    })
  } else {
    throw new Error(res.msg || '取消收藏失败')
  }
}

// 暴露方法
defineExpose({
  checkFavoriteStatus,
  refresh: checkFavoriteStatus
})

// 生命周期
import { onMounted } from 'vue'

onMounted(() => {
  if (props.autoCheck && !props.favorited) {
    checkFavoriteStatus()
  }
})
</script>

<style lang="scss" scoped>
.favorite-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
  }
  
  &.loading {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &.favorited {
    .favorite-text {
      color: #ff6b35;
    }
  }
  
  .favorite-text {
    font-size: 24rpx;
    color: #666;
    transition: color 0.3s ease;
  }
}
</style>
