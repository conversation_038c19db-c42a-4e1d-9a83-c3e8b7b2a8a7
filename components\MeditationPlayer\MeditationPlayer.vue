<template>
	<view class="meditation-player-mini">
		<!-- 播放控制 -->
		<view class="player-controls">
			<view class="meditation-info">
				<image :src="coverImage" mode="aspectFill" class="cover-image" />
				<view class="info-text">
					<text class="title">{{ title }}</text>
					<text class="subtitle">{{ subtitle }}</text>
				</view>
			</view>
			
			<view class="control-section">
				<!-- 进度条 -->
				<view class="progress-container">
					<text class="time-text">{{ formatTime(currentTime) }}</text>
					<view class="progress-bar" @click="onProgressClick">
						<view class="progress-track">
							<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
						</view>
					</view>
					<text class="time-text">{{ formatTime(duration) }}</text>
				</view>
				
				<!-- 控制按钮 -->
				<view class="control-buttons">
					<button class="control-btn" @click="seekBackward">
						<uni-icons type="back" size="16" color="#666"></uni-icons>
					</button>
					
					<button class="play-btn" @click="togglePlay">
						<uni-icons 
							:type="isPlaying ? 'pause-filled' : 'play-filled'" 
							size="20" 
							:color="isPlaying ? '#ff6b35' : '#666'"
						></uni-icons>
					</button>
					
					<button class="control-btn" @click="seekForward">
						<uni-icons type="forward" size="16" color="#666"></uni-icons>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { createAudioPlayer, formatTime } from '@/utils/audioPlayer'

// Props
const props = defineProps({
	// 音频源地址
	audioUrl: {
		type: String,
		required: true
	},
	// 标题
	title: {
		type: String,
		default: '冥想音频'
	},
	// 副标题
	subtitle: {
		type: String,
		default: ''
	},
	// 封面图
	coverImage: {
		type: String,
		default: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
	},
	// 自动播放
	autoplay: {
		type: Boolean,
		default: false
	}
})

// Emits
const emit = defineEmits(['play', 'pause', 'ended', 'timeUpdate', 'error'])

// 响应式数据
const audioPlayer = ref(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)

// 计算属性
const progressPercent = computed(() => {
	if (duration.value === 0) return 0
	return (currentTime.value / duration.value) * 100
})

// 初始化音频播放器
const initPlayer = () => {
	if (audioPlayer.value) {
		audioPlayer.value.destroy()
	}
	
	audioPlayer.value = createAudioPlayer(props.audioUrl, {
		autoplay: props.autoplay
	})
	
	// 绑定事件
	audioPlayer.value
		.onPlay(() => {
			isPlaying.value = true
			emit('play')
		})
		.onPause(() => {
			isPlaying.value = false
			emit('pause')
		})
		.onTimeUpdate((data) => {
			currentTime.value = data.currentTime
			duration.value = data.duration
			emit('timeUpdate', data)
		})
		.onEnded(() => {
			isPlaying.value = false
			emit('ended')
		})
		.onError((error) => {
			emit('error', error)
		})
}

// 方法
const togglePlay = () => {
	if (audioPlayer.value) {
		audioPlayer.value.toggle()
	}
}

const seekBackward = () => {
	if (audioPlayer.value) {
		audioPlayer.value.backward(15)
	}
}

const seekForward = () => {
	if (audioPlayer.value) {
		audioPlayer.value.forward(15)
	}
}

const onProgressClick = (e) => {
	if (audioPlayer.value && duration.value > 0) {
		const rect = e.currentTarget.getBoundingClientRect()
		const clickX = e.detail.x - rect.left
		const percent = clickX / rect.width
		const seekTime = duration.value * percent
		audioPlayer.value.seek(seekTime)
	}
}

// 监听音频源变化
watch(() => props.audioUrl, (newUrl) => {
	if (newUrl) {
		initPlayer()
	}
}, { immediate: true })

// 暴露方法给父组件
defineExpose({
	play: () => audioPlayer.value?.play(),
	pause: () => audioPlayer.value?.pause(),
	stop: () => audioPlayer.value?.stop(),
	seek: (time) => audioPlayer.value?.seek(time),
	getState: () => audioPlayer.value?.getState(),
	destroy: () => audioPlayer.value?.destroy()
})

// 生命周期
onUnmounted(() => {
	if (audioPlayer.value) {
		audioPlayer.value.destroy()
	}
})
</script>

<style lang="scss" scoped>
.meditation-player-mini {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.player-controls {
	.meditation-info {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		
		.cover-image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 12rpx;
			margin-right: 20rpx;
		}
		
		.info-text {
			flex: 1;
			
			.title {
				display: block;
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.subtitle {
				display: block;
				font-size: 24rpx;
				color: #666;
			}
		}
	}
	
	.control-section {
		.progress-container {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			
			.time-text {
				font-size: 24rpx;
				color: #666;
				min-width: 80rpx;
				text-align: center;
			}
			
			.progress-bar {
				flex: 1;
				margin: 0 20rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				
				.progress-track {
					width: 100%;
					height: 4rpx;
					background-color: #f0f0f0;
					border-radius: 2rpx;
					position: relative;
					
					.progress-fill {
						height: 100%;
						background-color: #ff6b35;
						border-radius: 2rpx;
						transition: width 0.1s ease;
					}
				}
			}
		}
		
		.control-buttons {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 40rpx;
			
			.control-btn {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background-color: #f8f8f8;
				border: none;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.play-btn {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				background-color: #f8f8f8;
				border: none;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
</style>
