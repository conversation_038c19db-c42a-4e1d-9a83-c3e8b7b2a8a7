import request from "../utils/request";

// 获取会话列表
export function getConversationList(isConsultant = false) {
	const url = isConsultant ? '/system/message/consultant/conversations' : '/system/message/conversations';
	return request({
		url,
		method: 'GET'
	})
}

// 创建会话
export function createConversation(consultantId) {
	// 方法1：直接添加到URL中
	return request({
		url: `/system/message/conversation/${consultantId}`,
		method: 'POST'
	})
	
	// 如果上面的方法不行，可以尝试以下方法：
	
	// 方法2：使用GET方法
	// return request({
	//   url: '/system/message/conversation',
	//   method: 'GET',
	//   params: {
	//     consultantId
	//   }
	// })
	
	// 方法3：使用FormData发送表单数据
	// const formData = new FormData();
	// formData.append('consultantId', consultantId);
	// return request({
	//   url: '/system/message/conversation',
	//   method: 'POST',
	//   data: formData,
	//   headers: {
	//     'Content-Type': 'application/x-www-form-urlencoded'
	//   }
	// })
}

// 获取消息列表
export function getMessageList(conversationId, pageNum = 1, pageSize = 20) {
	return request({
		url: `/system/message/list/${conversationId}`,
		method: 'GET',
		params: {
			pageNum,
			pageSize
		}
	})
}

// 发送消息
export function sendMessage(message) {
	return request({
		url: '/system/message/send',
		method: 'POST',
		data: message
	})
}

// 撤回消息
export function withdrawMessage(messageId) {
	return request({
		url: `/system/message/withdraw/${messageId}`,
		method: 'PUT'
	})
}

// 标记消息为已读
export function markMessageAsRead(messageId) {
	return request({
		url: `/system/message/read/${messageId}`,
		method: 'PUT'
	})
}

// 标记会话所有消息为已读
export function markAllMessagesAsRead(conversationId, isUser) {
	return request({
		url: `/system/message/read/all/${conversationId}`,
		method: 'PUT',
		params: {
			isUser
		}
	})
}

// 获取未读消息数量
export function getUnreadCount() {
	return request({
		url: '/system/message/unread/count',
		method: 'GET'
	})
}

// 上传图片
export function uploadImage(file) {
	return new Promise((resolve, reject) => {
		uni.uploadFile({
			url: '/common/upload',
			filePath: file,
			name: 'file',
			success: (res) => {
				if (res.statusCode === 200) {
					const data = JSON.parse(res.data);
					resolve(data);
				} else {
					reject(new Error('上传失败'));
				}
			},
			fail: (err) => {
				reject(err);
			}
		});
	});
}

// 上传文件
export function uploadFile(file) {
	return new Promise((resolve, reject) => {
		uni.uploadFile({
			url: '/common/upload/file',
			filePath: file,
			name: 'file',
			success: (res) => {
				if (res.statusCode === 200) {
					const data = JSON.parse(res.data);
					resolve(data);
				} else {
					reject(new Error('上传失败'));
				}
			},
			fail: (err) => {
				reject(err);
			}
		});
	});
}

// 历史消息相关API（备用）
// 获取历史消息
export function getMessageHistory(receiverId, pageNum = 1, pageSize = 20) {
	return request({
		url: '/message/history',
		method: 'GET',
		params: {
			receiverId,
			pageNum,
			pageSize
		}
	})
}

// 发送图片消息
export function sendImageMessage(file, receiverId) {
	const formData = new FormData();
	formData.append('file', file);
	formData.append('receiverId', receiverId);
	return request({
		url: '/message/sendImage',
		method: 'POST',
		data: formData,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

// 发送文件消息
export function sendFileMessage(file, receiverId) {
	const formData = new FormData();
	formData.append('file', file);
	formData.append('receiverId', receiverId);
	return request({
		url: '/message/sendFile',
		method: 'POST',
		data: formData,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}