<template>
  <view class="schedule-container">
    <!-- 日期选择器 -->
    <view class="date-picker-section">
      <view class="date-header">
        <text class="current-month">{{ currentMonth }}</text>
        <view class="date-actions">
          <button class="today-btn" @click="goToToday">今天</button>
          <!-- <button class="flexible-btn" @click="goToFlexibleSchedule">灵活排班</button> -->
          <button class="setting-btn" @click="goToScheduleSetting">排期设置</button>
        </view>
      </view>

      <!-- 日期滚动选择 -->
      <scroll-view class="date-scroll" scroll-x show-scrollbar-x="false">
        <view class="date-list">
          <view class="date-item" v-for="(date, index) in dateList" :key="index"
            :class="{ active: selectedDate === date.dateStr }" @click="selectDate(date.dateStr)">
            <text class="week-day">{{ date.weekDay }}</text>
            <text class="day-number">{{ date.day }}</text>
            <view class="date-indicator" v-if="date.hasAppointment"></view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 预约情况列表 -->
    <view class="appointments-section">
      <view class="section-header">
        <text class="section-title">{{ formatSelectedDate(selectedDate) }}</text>
        <text class="appointments-count">{{ totalAppointments }}个预约</text>
      </view>

      <scroll-view class="appointments-list" scroll-y>
        <view class="appointment-item" v-for="appointment in appointments" :key="appointment.id"
          @click="viewAppointmentDetail(appointment)">
          <view class="appointment-time">
            <text class="time-text">{{ appointment.startTime }} - {{ appointment.endTime }}</text>
            <view class="status-badge" :class="appointment.status">
              <text>{{ getStatusText(appointment.status) }}</text>
            </view>
          </view>

          <view class="appointment-info">
            <view class="client-info">
              <image class="client-avatar" :src="appointment.clientAvatar || '/static/icon/default-avatar.png'"
                mode="aspectFill"></image>
              <view class="client-details">
                <text class="client-name">{{ appointment.clientName }}</text>
                <text class="service-type">{{ appointment.serviceType }}</text>
              </view>
            </view>

            <view class="appointment-meta">
              <text class="order-no">订单号：{{ appointment.orderNo }}</text>
              <text class="booking-time">预约时间：{{ formatDateTime(appointment.bookingTime) }}</text>
            </view>
          </view>

          <view class="appointment-actions">
            <image src="/static/icon/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="appointments.length === 0">
          <image src="/static/icon/empty-appointment.png" mode="aspectFit"></image>
          <text>当天暂无预约</text>
          <button class="add-schedule-btn" @click="goToScheduleSetting">设置排期</button>
        </view>
      </scroll-view>
    </view>



    <!-- 时间设置弹窗 -->
    <uni-popup ref="timeSettingPopup" type="bottom">
      <view class="time-setting-modal">
        <view class="modal-header">
          <text class="modal-title">设置可用时间</text>
          <text class="modal-close" @click="closeTimeSettingModal">×</text>
        </view>
        <view class="modal-content">
          <view class="setting-item">
            <text class="setting-label">工作日</text>
            <view class="weekday-selector">
              <view class="weekday-item" v-for="(day, index) in weekdays" :key="index"
                :class="{ active: selectedWeekdays.includes(index) }" @click="toggleWeekday(index)">
                {{ day }}
              </view>
            </view>
          </view>
          <view class="setting-item">
            <text class="setting-label">工作时间</text>
            <view class="time-range">
              <picker mode="time" :value="workStartTime" @change="onStartTimeChange">
                <view class="time-picker">{{ workStartTime }}</view>
              </picker>
              <text class="time-separator">至</text>
              <picker mode="time" :value="workEndTime" @change="onEndTimeChange">
                <view class="time-picker">{{ workEndTime }}</view>
              </picker>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeTimeSettingModal">取消</button>
          <button class="btn-confirm" @click="saveTimeSetting">保存</button>
        </view>
      </view>
    </uni-popup>

    <cc-myTabbar :tabBarShow="0"></cc-myTabbar>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import TimeTable from '@/components/TimeTable/TimeTable.vue'
import {
  getConsultantSchedule,
  setConsultantAvailableTime,
  getConsultantStatistics
} from '@/api/consultant-app.js'
import { parseCompatibleDate } from '@/utils/index'

// 响应式数据
const selectedDate = ref('')
const dateList = ref([])
const appointments = ref([])
const currentMonth = ref('')
const totalAppointments = ref(0)

// 统计数据
const todayAppointments = ref(0)
const weekAppointments = ref(0)
const monthAppointments = ref(0)

// 时间设置
const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
const selectedWeekdays = ref([0, 1, 2, 3, 4]) // 默认工作日
const workStartTime = ref('09:00')
const workEndTime = ref('18:00')

// 生命周期
onMounted(() => {
  initializePage()
})

onShow(() => {
  initializePage()
})

// 初始化页面
const initializePage = () => {
  const today = new Date()
  selectedDate.value = formatDate(today)
  currentMonth.value = `${today.getFullYear()}年${today.getMonth() + 1}月`

  generateDateList()
  loadAppointments()
}

// 生成日期列表
const generateDateList = () => {
  const dates = []
  const today = new Date()

  // 生成前后各7天的日期
  for (let i = -7; i <= 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    dates.push({
      dateStr: formatDate(date),
      day: date.getDate(),
      weekDay: getWeekDay(date.getDay()),
      hasAppointment: false // 这里可以根据实际数据设置
    })
  }

  dateList.value = dates
}

// 加载预约数据
const loadAppointments = async () => {
  try {
    const params = {
      date: selectedDate.value
    }

    const res = await getConsultantSchedule(params)
    if (res.code === 200) {
      appointments.value = res.data.appointments || []
      totalAppointments.value = appointments.value.length
    }
  } catch (error) {
    console.error('加载预约数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await getConsultantStatistics()
    if (res.code === 200) {
      todayAppointments.value = res.data.todayCount || 0
      weekAppointments.value = res.data.weekCount || 0
      monthAppointments.value = res.data.monthCount || 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 处理日程数据
const processScheduleData = (data) => {
  // 处理日期信息
  dateInfo.value = data.dates || []

  // 处理时间信息
  timeInfo.value = data.timeSlots || []

  // 处理今日预约
  todayList.value = data.todayAppointments || []
}

// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取星期几
const getWeekDay = (dayIndex) => {
  const weekDays = ['日', '一', '二', '三', '四', '五', '六']
  return weekDays[dayIndex]
}

// 选择日期
const selectDate = (dateStr) => {
  selectedDate.value = dateStr
  loadAppointments()
}

// 回到今天
const goToToday = () => {
  const today = new Date()
  selectedDate.value = formatDate(today)
  loadAppointments()
}

// 格式化选中日期显示
const formatSelectedDate = (dateStr) => {
  if (!dateStr) return ''
  const date = parseCompatibleDate(dateStr)
  const today = new Date()

  if (formatDate(date) === formatDate(today)) {
    return '今天'
  }

  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekDay = getWeekDay(date.getDay())

  return `${month}月${day}日 周${weekDay}`
}

// 跳转到灵活排班页面
// const goToFlexibleSchedule = () => {
//   uni.navigateTo({
//     url: '/pages/schedule/flexible/index'
//   })
// }

// 跳转到排期设置页面
const goToScheduleSetting = () => {
  uni.navigateTo({
    url: '/pages/schedule/setting/index'
  })
}

// 查看预约详情
const viewAppointmentDetail = (appointment) => {
  uni.navigateTo({
    url: `/pages/consultation-order/detail/index?orderId=${appointment.orderId}`
  })
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = parseCompatibleDate(datetime)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${month}/${day} ${hour}:${minute}`
}

// 处理时间选择
const handleTimeSelect = (slot) => {
  console.log('选择时间段:', slot)

  if (editMode.value) {
    // 编辑模式：切换时间段可用状态
    toggleTimeSlotAvailability(slot)
  } else {
    // 查看模式：查看预约详情
    if (slot.appointmentId) {
      goToOrderDetail(slot.appointmentId)
    }
  }
}

// 切换时间段可用状态
const toggleTimeSlotAvailability = async (slot) => {
  try {
    const newStatus = slot.available ? 'unavailable' : 'available'

    const res = await setConsultantAvailableTime({
      date: slot.date,
      timeSlot: slot.timeSlot,
      status: newStatus
    })

    if (res.code === 200) {
      // 更新本地状态
      slot.available = !slot.available
      uni.showToast({
        title: newStatus === 'available' ? '已设为可用' : '已设为不可用',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('更新时间段状态失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 切换编辑模式
const toggleEditMode = () => {
  editMode.value = !editMode.value

  if (editMode.value) {
    uni.showToast({
      title: '进入编辑模式，点击时间段可切换可用状态',
      icon: 'none',
      duration: 3000
    })
  } else {
    uni.showToast({
      title: '退出编辑模式',
      icon: 'success'
    })
  }
}

// 处理日期选择
const handleDateSelect = (date) => {
  console.log('选择日期:', date)
  // 可以在这里处理日期切换
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    'pending': 'status-pending',
    'ongoing': 'status-ongoing',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待开始',
    'ongoing': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

// 开始咨询
const startConsultation = (item) => {
  uni.showModal({
    title: '确认开始咨询',
    content: `确定要开始与${item.clientName}的咨询吗？`,
    success: (res) => {
      if (res.confirm) {
        // 跳转到咨询页面或更新状态
        goToOrderDetail(item.orderId)
      }
    }
  })
}

// 跳转到订单详情
const goToOrderDetail = (orderId) => {
  uni.navigateTo({
    url: `/pages/consultation-order/order-detail/index?orderId=${orderId}`
  })
}

// 切换工作日
const toggleWeekday = (index) => {
  const idx = selectedWeekdays.value.indexOf(index)
  if (idx > -1) {
    selectedWeekdays.value.splice(idx, 1)
  } else {
    selectedWeekdays.value.push(index)
  }
}

// 开始时间变化
const onStartTimeChange = (e) => {
  workStartTime.value = e.detail.value
}

// 结束时间变化
const onEndTimeChange = (e) => {
  workEndTime.value = e.detail.value
}

// 关闭时间设置弹窗
const closeTimeSettingModal = () => {
  showTimeSettingModal.value = false
}

// 保存时间设置
const saveTimeSetting = async () => {
  try {
    const data = {
      weekdays: selectedWeekdays.value,
      startTime: workStartTime.value,
      endTime: workEndTime.value
    }

    const res = await setConsultantAvailableTime(data)
    if (res.code === 200) {
      uni.showToast({
        title: '设置成功',
        icon: 'success'
      })
      closeTimeSettingModal()
      loadScheduleData() // 重新加载数据
    }
  } catch (error) {
    console.error('保存时间设置失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}
</script>

<style scoped lang="scss">
.schedule-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.date-picker-section {
  background-color: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;

  .date-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx 20rpx;

    .current-month {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .date-actions {
      display: flex;
      gap: 16rpx;

      .today-btn,
      .flexible-btn,
      .setting-btn {
        padding: 12rpx 24rpx;
        font-size: 24rpx;
        border-radius: 20rpx;
        border: none;
      }

      .today-btn {
        background-color: #f0f0f0;
        color: #666;
      }

      .flexible-btn {
        background-color: #52c41a;
        color: #fff;
      }

      .setting-btn {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }

  .date-scroll {
    white-space: nowrap;

    .date-list {
      display: flex;
      padding: 0 20rpx;

      .date-item {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 24rpx;
        margin-right: 16rpx;
        border-radius: 16rpx;
        position: relative;

        .week-day {
          font-size: 22rpx;
          color: #999;
          margin-bottom: 8rpx;
        }

        .day-number {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .date-indicator {
          position: absolute;
          bottom: 8rpx;
          width: 8rpx;
          height: 8rpx;
          background-color: #1890ff;
          border-radius: 50%;
        }

        &.active {
          background-color: #1890ff;

          .week-day,
          .day-number {
            color: #fff;
          }

          .date-indicator {
            background-color: #fff;
          }
        }
      }
    }
  }
}

.appointments-section {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 0 20rpx 20rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .appointments-count {
      font-size: 24rpx;
      color: #666;
    }
  }

  .appointments-list {
    height: calc(100vh - 300rpx);

    .appointment-item {
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .appointment-time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .time-text {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
        }

        .status-badge {
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
          font-size: 22rpx;

          &.pending {
            background-color: #fff7e6;
            color: #fa8c16;
          }

          &.ongoing {
            background-color: #f6ffed;
            color: #52c41a;
          }

          &.completed {
            background-color: #f0f0f0;
            color: #666;
          }

          &.cancelled {
            background-color: #fff2f0;
            color: #ff4d4f;
          }
        }
      }

      .appointment-info {
        .client-info {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;

          .client-avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            margin-right: 20rpx;
          }

          .client-details {
            flex: 1;

            .client-name {
              display: block;
              font-size: 28rpx;
              color: #333;
              margin-bottom: 8rpx;
            }

            .service-type {
              font-size: 24rpx;
              color: #666;
            }
          }
        }

        .appointment-meta {

          .order-no,
          .booking-time {
            display: block;
            font-size: 22rpx;
            color: #999;
            margin-bottom: 4rpx;
          }
        }
      }

      .appointment-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }

      &:active {
        background-color: #f5f5f5;
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 30rpx;
        opacity: 0.6;
      }

      text {
        font-size: 28rpx;
        color: #999;
        margin-bottom: 30rpx;
      }

      .add-schedule-btn {
        padding: 20rpx 40rpx;
        background-color: #1890ff;
        color: #fff;
        border: none;
        border-radius: 20rpx;
        font-size: 26rpx;
      }
    }
  }
}

.today-appointments {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .appointment-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .time-info {
      flex: 1;

      .time {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
      }

      .status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;

        &.status-pending {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.status-ongoing {
          background-color: #f6ffed;
          color: #52c41a;
        }

        &.status-completed {
          background-color: #f0f0f0;
          color: #666;
        }

        &.status-cancelled {
          background-color: #fff2f0;
          color: #ff4d4f;
        }
      }
    }

    .client-info {
      flex: 1;
      text-align: center;

      .name {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
      }

      .type {
        font-size: 24rpx;
        color: #666;
      }
    }

    .action-btn {
      .btn-primary {
        background-color: #1890ff;
        color: #fff;
        border: none;
        border-radius: 20rpx;
        padding: 12rpx 24rpx;
        font-size: 24rpx;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  text {
    color: #999;
    font-size: 28rpx;
  }
}

.floating-actions {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .floating-btn {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

    image {
      width: 40rpx;
      height: 40rpx;
    }

    &.edit-btn {
      background-color: #52c41a;
    }

    &.setting-btn {
      background-color: #1890ff;
    }
  }
}

.time-setting-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .modal-close {
      font-size: 40rpx;
      color: #999;
    }
  }

  .modal-content {
    padding: 30rpx;

    .setting-item {
      margin-bottom: 40rpx;

      .setting-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
      }

      .weekday-selector {
        display: flex;
        gap: 10rpx;

        .weekday-item {
          flex: 1;
          text-align: center;
          padding: 16rpx 0;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          font-size: 24rpx;
          color: #666;

          &.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
          }
        }
      }

      .time-range {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .time-picker {
          flex: 1;
          padding: 20rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          text-align: center;
          font-size: 28rpx;
        }

        .time-separator {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .btn-cancel,
    .btn-confirm {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }

    .btn-cancel {
      background-color: #f5f5f5;
      color: #666;
    }

    .btn-confirm {
      background-color: #1890ff;
      color: #fff;
    }
  }
}
</style>