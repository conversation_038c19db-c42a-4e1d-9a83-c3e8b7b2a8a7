<template>
	<view class="speed-test">
		<view class="header">
			<text class="title">倍速功能测试</text>
		</view>
		
		<view class="player">
			<view class="info">
				<text>当前倍速: {{ currentSpeed }}x</text>
				<text>播放状态: {{ isPlaying ? '播放中' : '已暂停' }}</text>
				<text>音频模式: {{ useBackgroundAudio ? '背景音频' : '内部音频' }}</text>
			</view>
			
			<view class="controls">
				<button @click="togglePlay" class="btn">
					{{ isPlaying ? '暂停' : '播放' }}
				</button>
				
				<button @click="switchMode" class="btn">
					切换模式
				</button>
			</view>
			
			<view class="speed-controls">
				<button 
					v-for="speed in speeds" 
					:key="speed"
					@click="setSpeed(speed)"
					:class="['speed-btn', { active: currentSpeed === speed }]"
				>
					{{ speed }}x
				</button>
			</view>
			
			<view class="debug-info">
				<text class="debug-title">调试信息:</text>
				<text v-for="(log, index) in debugLogs" :key="index" class="debug-log">
					{{ log }}
				</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'

// 测试音频URL
const testAudioUrl = 'https://music.163.com/song/media/outer/url?id=25906124.mp3'

// 响应式数据
const isPlaying = ref(false)
const currentSpeed = ref(1.0)
const useBackgroundAudio = ref(true)
const debugLogs = ref([])
const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]

// 音频管理器
let audioManager = null

// 添加调试日志
const addLog = (message) => {
	const timestamp = new Date().toLocaleTimeString()
	debugLogs.value.unshift(`[${timestamp}] ${message}`)
	if (debugLogs.value.length > 10) {
		debugLogs.value.pop()
	}
	console.log(message)
}

// 初始化音频
const initAudio = () => {
	try {
		if (audioManager) {
			if (audioManager.destroy) {
				audioManager.destroy()
			} else if (audioManager.stop) {
				audioManager.stop()
			}
		}
		
		if (useBackgroundAudio.value) {
			addLog('初始化背景音频管理器')
			audioManager = uni.getBackgroundAudioManager()
			
			// 设置音频信息
			audioManager.title = '倍速测试音频'
			audioManager.singer = '测试'
			audioManager.src = testAudioUrl
			
			// 绑定事件
			audioManager.onPlay(() => {
				isPlaying.value = true
				addLog('背景音频开始播放')
			})
			
			audioManager.onPause(() => {
				isPlaying.value = false
				addLog('背景音频暂停')
			})
			
			audioManager.onError((error) => {
				addLog('背景音频错误: ' + JSON.stringify(error))
			})
			
			// 检查倍速支持
			if ('playbackRate' in audioManager) {
				addLog('背景音频支持倍速功能')
			} else {
				addLog('背景音频不支持倍速功能')
			}
			
		} else {
			addLog('初始化内部音频上下文')
			audioManager = uni.createInnerAudioContext()
			audioManager.src = testAudioUrl
			
			// 绑定事件
			audioManager.onPlay(() => {
				isPlaying.value = true
				addLog('内部音频开始播放')
			})
			
			audioManager.onPause(() => {
				isPlaying.value = false
				addLog('内部音频暂停')
			})
			
			audioManager.onError((error) => {
				addLog('内部音频错误: ' + JSON.stringify(error))
			})
			
			addLog('内部音频不支持倍速功能')
		}
		
	} catch (error) {
		addLog('初始化音频失败: ' + error.message)
	}
}

// 切换播放
const togglePlay = () => {
	try {
		if (!audioManager) {
			addLog('音频管理器未初始化')
			return
		}
		
		if (isPlaying.value) {
			audioManager.pause()
			addLog('暂停播放')
		} else {
			audioManager.play()
			addLog('开始播放')
		}
	} catch (error) {
		addLog('播放控制失败: ' + error.message)
	}
}

// 设置倍速
const setSpeed = (speed) => {
	try {
		currentSpeed.value = speed
		addLog(`设置倍速: ${speed}x`)
		
		if (!audioManager) {
			addLog('音频管理器未初始化')
			return
		}
		
		if (useBackgroundAudio.value) {
			if ('playbackRate' in audioManager) {
				audioManager.playbackRate = speed
				addLog(`背景音频倍速设置为: ${speed}x`)
				
				// 验证设置结果
				setTimeout(() => {
					const actualRate = audioManager.playbackRate
					addLog(`实际倍速: ${actualRate}x`)
					if (Math.abs(actualRate - speed) > 0.01) {
						addLog('倍速设置失败！')
					} else {
						addLog('倍速设置成功！')
					}
				}, 100)
			} else {
				addLog('背景音频不支持倍速')
			}
		} else {
			addLog('内部音频不支持倍速')
		}
		
	} catch (error) {
		addLog('设置倍速失败: ' + error.message)
	}
}

// 切换模式
const switchMode = () => {
	useBackgroundAudio.value = !useBackgroundAudio.value
	addLog(`切换到${useBackgroundAudio.value ? '背景音频' : '内部音频'}模式`)
	initAudio()
}

// 初始化
initAudio()

// 清理
onUnmounted(() => {
	if (audioManager) {
		try {
			if (audioManager.destroy) {
				audioManager.destroy()
			} else if (audioManager.stop) {
				audioManager.stop()
			}
		} catch (e) {
			// 忽略清理错误
		}
	}
})
</script>

<style lang="scss" scoped>
.speed-test {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

.player {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	
	.info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
		margin-bottom: 40rpx;
		
		text {
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.controls {
		display: flex;
		gap: 20rpx;
		margin-bottom: 40rpx;
		
		.btn {
			flex: 1;
			padding: 24rpx;
			background-color: #007aff;
			color: #fff;
			border: none;
			border-radius: 12rpx;
			font-size: 28rpx;
		}
	}
	
	.speed-controls {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		margin-bottom: 40rpx;
		
		.speed-btn {
			flex: 1;
			min-width: 100rpx;
			padding: 20rpx;
			background-color: #f8f8f8;
			color: #666;
			border: none;
			border-radius: 8rpx;
			font-size: 26rpx;
			
			&.active {
				background-color: #ff6b35;
				color: #fff;
			}
		}
	}
	
	.debug-info {
		.debug-title {
			display: block;
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.debug-log {
			display: block;
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
			margin-bottom: 8rpx;
			padding: 8rpx;
			background-color: #f8f8f8;
			border-radius: 4rpx;
		}
	}
}
</style>
