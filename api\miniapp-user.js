/**
 * 小程序用户端接口
 * 基于新的后端接口架构
 */
import request from '@/utils/request.js'

// ==================== 极速匹配功能 ====================
/**
 * 获取匹配问题列表
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 搜索关键词
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 */
export function getMatchQuestionList(params = {}) {
  return request({
    url: '/miniapp/user/match/question/list',
    method: 'GET',
    data: params
  })
}

/**
 * 获取问题详情
 * @param {number} questionId - 问题ID
 */
export function getMatchQuestionDetail(questionId) {
  return request({
    url: `/miniapp/user/match/question/${questionId}`,
    method: 'GET'
  })
}

/**
 * 根据选项筛选咨询师（核心匹配功能）
 * @param {Object} params - 匹配参数
 * @param {Array} params.optionIds - 选项ID数组
 * @param {Object} params.filters - 其他筛选条件
 */
export function matchConsultants(params) {
  return request({
    url: '/miniapp/user/match/question/match',
    method: 'POST',
    data: params
  })
}

/**
 * 快速匹配咨询师
 * @param {Object} params - 快速匹配参数
 */
export function quickMatchConsultants(params) {
  return request({
    url: '/miniapp/user/match/question/quickMatch',
    method: 'POST',
    data: params
  })
}

/**
 * 获取选项关联的咨询师
 * @param {number} optionId - 选项ID
 */
export function getOptionConsultants(optionId) {
  return request({
    url: `/miniapp/user/match/question/option/${optionId}/consultants`,
    method: 'GET'
  })
}

/**
 * 搜索匹配问题
 * @param {Object} params - 搜索参数
 */
export function searchMatchQuestions(params) {
  return request({
    url: '/miniapp/user/match/question/search',
    method: 'GET',
    data: params
  })
}

/**
 * 获取推荐问题
 */
export function getRecommendQuestions() {
  return request({
    url: '/miniapp/user/match/question/recommend',
    method: 'GET'
  })
}

/**
 * 获取热门问题
 */
export function getHotQuestions() {
  return request({
    url: '/miniapp/user/match/question/hot',
    method: 'GET'
  })
}

// ==================== 导航菜单功能 ====================
/**
 * 获取小程序导航菜单（自动权限判断）
 */
export function getTabbarMenus() {
  return request({
    url: '/miniapp/user/tabbar/list',
    method: 'GET'
  })
}

/**
 * 根据权限获取菜单
 * @param {Array} permissions - 权限数组
 */
export function getMenusByPermissions(permissions) {
  return request({
    url: '/miniapp/user/tabbar/listByPermissions',
    method: 'GET',
    data: { permissions }
  })
}

/**
 * 获取用户可访问菜单
 */
export function getUserMenus() {
  return request({
    url: '/miniapp/user/tabbar/userMenus',
    method: 'GET'
  })
}

/**
 * 获取首页菜单
 */
export function getHomeMenus() {
  return request({
    url: '/miniapp/user/tabbar/home',
    method: 'GET'
  })
}

/**
 * 检查菜单权限
 * @param {Object} params - 权限检查参数
 */
export function checkMenuPermission(params) {
  return request({
    url: '/miniapp/user/tabbar/checkPermission',
    method: 'POST',
    data: params
  })
}

// ==================== 咨询订单功能 ====================
/**
 * 创建咨询订单
 * @param {Object} orderData - 订单数据
 */
export function createConsultantOrder(orderData) {
  return request({
    url: '/miniapp/user/consultant/order/create',
    method: 'POST',
    data: orderData
  })
}

/**
 * 获取我的订单列表
 * @param {Object} params - 查询参数
 */
export function getMyOrders(params = {}) {
  return request({
    url: '/miniapp/user/consultant/order/myOrders',
    method: 'GET',
    data: params
  })
}

/**
 * 获取订单详情
 * @param {number} orderId - 订单ID
 */
export function getOrderDetail(orderId) {
  return request({
    url: `/miniapp/user/consultant/order/${orderId}`,
    method: 'GET'
  })
}

/**
 * 取消订单
 * @param {number} orderId - 订单ID
 */
export function cancelOrder(orderId) {
  return request({
    url: `/miniapp/user/consultant/order/${orderId}/cancel`,
    method: 'POST'
  })
}

/**
 * 检查时间段可用性
 * @param {Object} params - 检查参数
 */
export function checkTimeAvailable(params) {
  return request({
    url: '/miniapp/user/consultant/order/checkAvailable',
    method: 'GET',
    data: params
  })
}

/**
 * 申请退款
 * @param {number} orderId - 订单ID
 */
export function refundOrder(orderId) {
  return request({
    url: `/miniapp/user/consultant/order/${orderId}/refund`,
    method: 'POST'
  })
}

// ==================== 咨询师评价功能 ====================
/**
 * 提交咨询评价
 * @param {Object} reviewData - 评价数据
 */
export function submitConsultantReview(reviewData) {
  return request({
    url: '/miniapp/user/consultant/review/submit',
    method: 'POST',
    data: reviewData
  })
}

/**
 * 获取我的评价列表
 * @param {Object} params - 查询参数
 */
export function getMyReviews(params = {}) {
  return request({
    url: '/miniapp/user/consultant/review/myReviews',
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询师评价列表
 * @param {number} consultantId - 咨询师ID
 * @param {Object} params - 查询参数
 */
export function getConsultantReviews(consultantId, params = {}) {
  return request({
    url: `/miniapp/user/consultant/review/consultant/${consultantId}`,
    method: 'GET',
    data: params
  })
}

/**
 * 获取评价详情
 * @param {number} reviewId - 评价ID
 */
export function getReviewDetail(reviewId) {
  return request({
    url: `/miniapp/user/consultant/review/${reviewId}`,
    method: 'GET'
  })
}

/**
 * 检查是否可评价
 * @param {number} recordId - 咨询记录ID
 */
export function canRateRecord(recordId) {
  return request({
    url: `/miniapp/user/consultant/review/canRate/${recordId}`,
    method: 'GET'
  })
}

/**
 * 获取咨询师评价统计
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantReviewStatistics(consultantId) {
  return request({
    url: `/miniapp/user/consultant/review/statistics/${consultantId}`,
    method: 'GET'
  })
}
