<!DOCTYPE html>
<html>
<head>
    <title>字典数据测试</title>
</head>
<body>
    <h1>字典数据加载测试</h1>
    <div id="result"></div>
    
    <script>
        // 模拟 useDict 函数的修复版本
        async function testUseDict(...args) {
            let res = {}
            try {
                // 使用 Promise.all 来并行处理所有字典请求
                const promises = args.map(async (dictType) => {
                    try {
                        // 模拟 API 调用
                        const mockData = {
                            psy_consultant_level: [
                                { dictValue: '0', dictLabel: '咨询助理' },
                                { dictValue: '1', dictLabel: '初级咨询师' },
                                { dictValue: '2', dictLabel: '中级咨询师' },
                                { dictValue: '3', dictLabel: '成熟咨询师' },
                                { dictValue: '4', dictLabel: '高级咨询师' },
                                { dictValue: '5', dictLabel: '资深咨询师' },
                                { dictValue: '6', dictLabel: '咨询督导' }
                            ]
                        };
                        
                        // 模拟网络延迟
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        return {
                            dictType,
                            data: mockData[dictType] || []
                        };
                    } catch (error) {
                        console.error(`获取字典 ${dictType} 失败:`, error);
                        return {
                            dictType,
                            data: []
                        };
                    }
                });

                const results = await Promise.all(promises);
                
                // 将结果组装成对象
                results.forEach(({ dictType, data }) => {
                    res[dictType] = data;
                });

                return res;
            } catch (error) {
                console.error('useDict 执行失败:', error);
                // 返回空对象，避免组件报错
                args.forEach(dictType => {
                    res[dictType] = [];
                });
                return res;
            }
        }
        
        // 测试函数
        async function runTest() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在加载字典数据...';
            
            try {
                const dictData = await testUseDict('psy_consultant_level');
                console.log('字典数据:', dictData);
                
                resultDiv.innerHTML = `
                    <h2>加载成功！</h2>
                    <pre>${JSON.stringify(dictData, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('测试失败:', error);
                resultDiv.innerHTML = `<h2>加载失败：${error.message}</h2>`;
            }
        }
        
        // 页面加载完成后运行测试
        window.onload = runTest;
    </script>
</body>
</html>
