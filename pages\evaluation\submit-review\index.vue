<template>
  <view class="submit-review-page">
    <!-- 测评信息 -->
    <view class="scale-info">
      <text class="scale-name">{{scaleName}}</text>
      <text class="scale-desc">请为这个测评打分并写下您的评价</text>
    </view>

    <!-- 评分区域 -->
    <view class="rating-section">
      <view class="section-title">
        <text>整体评分</text>
        <text class="required">*</text>
      </view>
      <view class="rating-stars">
        <text 
          class="star" 
          v-for="i in 5" 
          :key="i"
          :class="{'filled': i <= rating, 'hover': i <= hoverRating}"
          @click="setRating(i)"
          @touchstart="setHoverRating(i)"
          @touchend="clearHoverRating"
        >★</text>
      </view>
      <view class="rating-text">
        <text>{{getRatingText(rating)}}</text>
      </view>
    </view>

    <!-- 评价内容 -->
    <view class="content-section">
      <view class="section-title">
        <text>评价内容</text>
        <text class="optional">（选填）</text>
      </view>
      <textarea 
        class="review-textarea"
        v-model="content"
        placeholder="请写下您对这个测评的评价和建议..."
        :maxlength="500"
        auto-height
      ></textarea>
      <view class="char-count">
        <text>{{content.length}}/500</text>
      </view>
    </view>

    <!-- 匿名选项 -->
    <view class="anonymous-section">
      <view class="anonymous-option" @click="toggleAnonymous">
        <view class="checkbox" :class="{'checked': isAnonymous}">
          <text v-if="isAnonymous">✓</text>
        </view>
        <text class="anonymous-text">匿名评价</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn" 
        :disabled="!canSubmit || submitting"
        @click="submitReview1"
      >
        <text v-if="submitting">提交中...</text>
        <text v-else>提交评价</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { submitAssessmentReview } from '@/api/evaluation'

// 响应式数据
const scaleId = ref('')
const scaleName = ref('')
const recordId = ref('')
const rating = ref(0)
const hoverRating = ref(0)
const content = ref('')
const isAnonymous = ref(false)
const submitting = ref(false)

// 计算属性
const canSubmit = computed(() => {
  return rating.value > 0
})

onLoad((options) => {
  if (options.scaleId) {
    scaleId.value = options.scaleId
  }
  if (options.scaleName) {
    scaleName.value = decodeURIComponent(options.scaleName)
  }
  if (options.recordId) {
    recordId.value = options.recordId
  }
  
  // 设置页面标题
  uni.setNavigationBarTitle({
    title: '评价测评'
  })
})

// 设置评分
const setRating = (score) => {
  rating.value = score
}

// 设置悬停评分
const setHoverRating = (score) => {
  hoverRating.value = score
}

// 清除悬停评分
const clearHoverRating = () => {
  hoverRating.value = 0
}

// 获取评分文本
const getRatingText = (score) => {
  const texts = {
    1: '很不满意',
    2: '不满意', 
    3: '一般',
    4: '满意',
    5: '非常满意'
  }
  return texts[score] || '请选择评分'
}

// 切换匿名状态
const toggleAnonymous = () => {
  isAnonymous.value = !isAnonymous.value
}

// 提交评价
const submitReview1 = async () => {
  if (!canSubmit.value || submitting.value) return
  
  submitting.value = true
  
  try {
    const reviewData = {
      scaleId: parseInt(scaleId.value),
      recordId: recordId.value ? parseInt(recordId.value) : undefined,
      rating: rating.value,
      content: content.value.trim(),
      isAnonymous: isAnonymous.value ? 1 : 0
    }
    
    const res = await submitAssessmentReview(reviewData)
    if (res.code === 200) {
      uni.showToast({
        title: '评价提交成功',
        icon: 'success'
      })
      
      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.msg || '提交失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.submit-review-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
}

.scale-info {
  background: #fff;
  border-radius: 12rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
  
  .scale-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 12rpx;
  }
  
  .scale-desc {
    font-size: 26rpx;
    color: #666;
  }
}

.rating-section,
.content-section,
.anonymous-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
  
  .required {
    color: #ff4757;
    margin-left: 4rpx;
  }
  
  .optional {
    color: #999;
    font-size: 24rpx;
    margin-left: 8rpx;
  }
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
  
  .star {
    font-size: 60rpx;
    color: #ddd;
    transition: all 0.2s;
    
    &.filled,
    &.hover {
      color: #ff6b35;
      transform: scale(1.1);
    }
  }
}

.rating-text {
  text-align: center;
  
  text {
    font-size: 28rpx;
    color: #666;
  }
}

.review-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  background: #fafafa;
}

.char-count {
  text-align: right;
  margin-top: 10rpx;
  
  text {
    font-size: 24rpx;
    color: #999;
  }
}

.anonymous-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #ddd;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    
    &.checked {
      background: #409eff;
      border-color: #409eff;
      color: #fff;
    }
    
    text {
      font-size: 24rpx;
      font-weight: bold;
    }
  }
  
  .anonymous-text {
    font-size: 28rpx;
    color: #333;
  }
}

.submit-section {
  margin-top: 60rpx;
  
  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: #409eff;
    color: #fff;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      background: #c0c4cc;
      color: #fff;
    }
  }
}
</style>
