<template>
	<view class="content">
		<!-- 分类标签页 -->
		<view class="category-tabs">
			<view v-for="(tab, index) in tabs" :key="tab.type" :class="['tab-item', { active: currentTab === index }]"
				@click="switchTab(index)">
				{{ tab.name }}
			</view>
		</view>

		<!-- 咨询师收藏列表 -->
		<view v-if="currentTab === 0" class="list-content">
			<view class="counselor" v-for="item in counselorList" :key="item.counselorId"
				@click="handleCounselorDetail(item)">
				<view class="counselor-left">
					<image class="avatar" mode="heightFix" :src="item.image" @error="error"></image>
				</view>
				<view class="counselor-right">
					<view class="name-box">
						<view class="name-grade">
							<view class="name">{{ item.name }}</view>
							<view class="grade">{{ getPsy_consultant_level(item.counselorLevel) }}</view>
						</view>
						<view class="address">{{ formatAddress(item) }}</view>
					</view>
					<scroll-view scroll-x="true" class="professional-box">
						<text v-for="(style, index) in item.consultStyles" :key="index" class="professional-tag">
							{{ style.dictLabel }}
						</text>
					</scroll-view>
					<view class="info">{{ item.personalIntro || '暂无简介' }}</view>
					<view class="bottom-row">
						<view class="working-info">
							从业{{ new Date().getFullYear() - parseInt(item.startYear) }}年
							<text v-if="item.serviceCount">, 服务{{ item.serviceCount }}人次</text>
						</view>
						<view class="price">
							<text class="currency-symbol">¥</text>
							<text class="price-value">{{ item.price }}</text>
							<text class="unit">/节</text>
						</view>
					</view>
				</view>
			</view>
			<view v-if="counselorList.length === 0" class="empty-tip">暂无收藏的咨询师</view>
		</view>

		<!-- 课程收藏列表 -->
		<view v-if="currentTab === 1" class="list-content">
			<view class="product" v-for="item in courseList" :key="item.productId" @click="handleCourseDetail(item)">
				<view class="product-left">
					<image class="product-img" mode="aspectFill" :src="item.image" @error="error"></image>
				</view>
				<view class="product-right">
					<view class="product-name">{{ item.name }}</view>
					<view class="service-direction" v-if="item.serviceDirection">{{ formatServiceDirection(item.serviceDirection)
					}}</view>
					<view class="price-info">
						<view class="price-box">
							<text class="currency-symbol">¥</text>
							<text class="price-value">{{ item.price }}</text>
						</view>
						<view class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</view>
					</view>
				</view>
			</view>
			<view v-if="courseList.length === 0" class="empty-tip">暂无收藏的课程</view>
		</view>

		<!-- 冥想收藏列表 -->
		<view v-if="currentTab === 2" class="list-content">
			<view class="product" v-for="item in meditationList" :key="item.meditationId"
				@click="handleMeditationDetail(item)">
				<view class="product-left">
					<image class="product-img" mode="aspectFill" :src="item.image" @error="error"></image>
				</view>
				<view class="product-right">
					<view class="product-name">{{ item.name }}</view>
					<view class="meditation-info">
						<text class="duration" v-if="item.duration">{{ formatDuration(item.duration) }}</text>
						<text class="category" v-if="item.categoryName">{{ item.categoryName }}</text>
					</view>
					<view class="price-info">
						<view class="price-box">
							<text class="currency-symbol">¥</text>
							<text class="price-value">{{ item.price }}</text>
						</view>
						<view class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</view>
					</view>
				</view>
			</view>
			<view v-if="meditationList.length === 0" class="empty-tip">暂无收藏的冥想</view>
		</view>

		<!-- 测评收藏列表 -->
		<view v-if="currentTab === 3" class="list-content">
			<view class="product" v-for="item in assessmentList" :key="item.assessmentId"
				@click="handleAssessmentDetail(item)">
				<view class="product-left">
					<image class="product-img" mode="aspectFill" :src="item.image" @error="error"></image>
				</view>
				<view class="product-right">
					<view class="product-name">{{ item.name }}</view>
					<view class="assessment-info">
						<text class="question-count" v-if="item.questionCount">{{ item.questionCount }}题</text>
						<text class="category" v-if="item.categoryName">{{ item.categoryName }}</text>
					</view>
					<view class="price-info">
						<view class="price-box">
							<text class="currency-symbol">¥</text>
							<text class="price-value">{{ item.price }}</text>
						</view>
						<view class="original-price" v-if="item.originalPrice">¥{{ item.originalPrice }}</view>
					</view>
				</view>
			</view>
			<view v-if="assessmentList.length === 0" class="empty-tip">暂无收藏的测评</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getFavoriteList, getFavoriteStats, FAVORITE_TYPES } from "@/api/favorite.js";
import { useDict } from "@/utils/index.js";
import { useUserStore } from "@/stores/user.js";

const userStore = useUserStore();

// 标签页配置
const tabs = ref([
	{ name: '咨询师', type: 'consultant' },
	{ name: '课程', type: 'course' },
	{ name: '冥想', type: 'meditation' },
	{ name: '测评', type: 'assessment' }
]);

const currentTab = ref(0);
const counselorList = ref([]);
const courseList = ref([]);
const meditationList = ref([]);
const assessmentList = ref([]);
const psy_consultant_level = ref([]);
const serviceType = ref([]);

// 获取收藏列表
const loadFavoriteList = async () => {
	try {
		const currentTabType = tabs.value[currentTab.value].type;
		let targetType;

		switch (currentTabType) {
			case 'consultant':
				targetType = FAVORITE_TYPES.CONSULTANT;
				break;
			case 'course':
				targetType = FAVORITE_TYPES.COURSE;
				break;
			case 'meditation':
				targetType = FAVORITE_TYPES.MEDITATION;
				break;
			case 'assessment':
				targetType = FAVORITE_TYPES.ASSESSMENT;
				break;
		}

		const res = await getFavoriteList({ targetType });

		if (res.code === 200) {
			const favoriteData = res.data || [];

			switch (currentTabType) {
				case 'consultant':
					// 咨询师收藏
					counselorList.value = favoriteData.map(item => ({
						counselorId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						counselorLevel: item.targetInfo?.level,
						consultStyles: item.targetInfo?.consultStyles || [],
						...item.targetInfo
					}));
					break;
				case 'course':
					// 课程收藏
					courseList.value = favoriteData.map(item => ({
						productId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						price: item.targetInfo?.price || 0,
						originalPrice: item.targetInfo?.originalPrice,
						serviceDirection: item.targetInfo?.serviceDirection,
						...item.targetInfo
					}));
					break;
				case 'meditation':
					// 冥想收藏
					meditationList.value = favoriteData.map(item => ({
						meditationId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						price: item.targetInfo?.price || 0,
						originalPrice: item.targetInfo?.originalPrice,
						duration: item.targetInfo?.duration,
						categoryName: item.targetInfo?.categoryName,
						...item.targetInfo
					}));
					break;
				case 'assessment':
					// 测评收藏
					assessmentList.value = favoriteData.map(item => ({
						assessmentId: item.targetId,
						favoriteId: item.favoriteId,
						name: item.actualTitle || item.targetTitle,
						image: item.actualImage || item.targetImage,
						price: item.targetInfo?.price || 0,
						originalPrice: item.targetInfo?.originalPrice,
						questionCount: item.targetInfo?.questionCount,
						categoryName: item.targetInfo?.categoryName,
						...item.targetInfo
					}));
					break;
			}
		}
	} catch (error) {
		console.error('获取收藏列表失败:', error);
		uni.showToast({
			title: '获取收藏列表失败',
			icon: 'none'
		});
	}
};

// 切换标签
const switchTab = (index) => {
	if (currentTab.value !== index) {
		currentTab.value = index;
		loadFavoriteList(); // 切换标签时重新获取列表
	}
};

// 跳转到咨询师详情
const handleCounselorDetail = (item) => {
	uni.navigateTo({
		url: `/pages/classification/counselor-detail/index?id=${item.counselorId}`
	});
};

// 跳转到课程详情
const handleCourseDetail = (item) => {
	uni.navigateTo({
		url: `/pages/course/detail/index?courseId=${item.productId}`
	});
};

// 跳转到冥想详情
const handleMeditationDetail = (item) => {
	uni.navigateTo({
		url: `/pages/meditation/detail/index?meditationId=${item.meditationId}`
	});
};

// 跳转到测评详情
const handleAssessmentDetail = (item) => {
	uni.navigateTo({
		url: `/pages/evaluation/detail/index?assessmentId=${item.assessmentId}`
	});
};

// 获取咨询师等级字典
const getDict = async () => {
	try {
		const { psy_consultant_level: dictData } = await useDict("psy_consultant_level");
		psy_consultant_level.value = dictData || [];
	} catch (error) {
		console.error('获取咨询师等级字典失败:', error);
	}
};

const getPsy_consultant_level = (value) => {
	return psy_consultant_level.value.find((item) => item.dictValue == value)?.dictLabel || "";
};

// 格式化地址
const formatAddress = (item) => {
	if (!item) return '暂无地址';
	return [item.province, item.city, item.district, item.address].filter(Boolean).join('') || '暂无地址';
};

// 格式化服务方向
const formatServiceDirection = (serviceDirection) => {
	if (!serviceDirection || !serviceType.value.length) return "";
	let serviceDirectionList = serviceDirection.split(",");
	let serviceDirectionListStr = "";
	serviceDirectionList.forEach((item, index) => {
		const found = serviceType.value.find(type => type.value === item);
		if (found?.label) {
			serviceDirectionListStr += found.label + (index < serviceDirectionList.length - 1 ? " | " : "");
		}
	});
	return serviceDirectionListStr.trim();
};

// 格式化时长（秒转换为分钟）
const formatDuration = (seconds) => {
	if (!seconds) return '0分钟';
	const minutes = Math.floor(seconds / 60);
	return `${minutes}分钟`;
};

// 图片加载失败处理
const error = () => {
	// 可以设置默认图片
	console.log('图片加载失败');
};

onLoad(() => {
	getDict();
	loadFavoriteList();
});
</script>

<style lang="scss" scoped>
.content {
	background-color: #f5f5f6;
	min-height: 100vh;

	// 分类标签页样式（参考首页）
	.category-tabs {
		display: flex;
		background: #fff;
		padding: 0 20rpx;

		.tab-item {
			padding: 24rpx 32rpx;
			font-size: 28rpx;
			color: #666;
			position: relative;

			&.active {
				color: #333;
				font-weight: 500;

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 48rpx;
					height: 4rpx;
					background: #20a3f3;
					border-radius: 2rpx;
				}
			}
		}
	}

	.list-content {
		padding: 20rpx;
	}

	.counselor {
		width: calc(100% - 40rpx);
		padding: 10rpx 20rpx;
		display: flex;
		margin-bottom: 20rpx;

		.counselor-left {
			width: 140rpx;
			height: 140rpx;
			margin-right: 20rpx;

			.avatar {
				width: 100%;
				height: 100%;
				border-radius: 20rpx;
			}
		}

		.counselor-right {
			width: calc(100% - 180rpx);

			.name-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;

				.name-grade {
					display: flex;
					align-items: center;

					.name {
						font-size: 36rpx;
						font-weight: 700;
						margin-right: 10rpx;
					}

					.grade {
						font-size: 24rpx;
						background-color: #fff4dc;
						padding: 2rpx 10rpx;
						color: #c16019;
						border-radius: 20rpx;
					}
				}

				.address {
					font-size: 24rpx;
					color: #666;
				}
			}

			.professional-box {
				width: 100%;
				white-space: nowrap;
				margin: 10rpx 0;

				/* 隐藏滚动条 */
				&::-webkit-scrollbar {
					display: none;
				}

				.professional-tag {
					display: inline-block;
					background: #f0f0f0;
					border-radius: 8rpx;
					padding: 4rpx 12rpx;
					font-size: 24rpx;
					margin-right: 10rpx;
					color: #666;
				}
			}

			.info {
				font-size: 24rpx;
				color: #666;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin: 10rpx 0;
			}

			.bottom-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 10rpx;

				.working-info {
					font-size: 24rpx;
					color: #666;
				}

				.price {
					color: #e72f2f;

					.currency-symbol {
						font-size: 20rpx;
					}

					.price-value {
						font-weight: bold;
						font-size: 30rpx;
					}

					.unit {
						margin-left: 5rpx;
						font-size: 20rpx;
					}
				}
			}
		}
	}

	.product {
		background: #fff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		display: flex;

		.product-left {
			width: 200rpx;
			height: 200rpx;
			margin-right: 20rpx;

			.product-img {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.product-right {
			flex: 1;

			.product-name {
				font-size: 28rpx;
				font-weight: bold;
				margin-bottom: 10rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}

			.service-direction {
				font-size: 26rpx;
				color: #666;
				margin: 10rpx 0;
			}

			.meditation-info {
				font-size: 26rpx;
				color: #666;
				margin: 10rpx 0;

				.duration {
					margin-right: 20rpx;
				}

				.category {
					color: #20a3f3;
				}
			}

			.assessment-info {
				font-size: 26rpx;
				color: #666;
				margin: 10rpx 0;

				.question-count {
					margin-right: 20rpx;
				}

				.category {
					color: #20a3f3;
				}
			}

			.price-info {
				display: flex;
				align-items: baseline;
				margin-top: 10rpx;

				.price-box {
					color: #e72f2f;

					.currency-symbol {
						font-size: 24rpx;
					}

					.price-value {
						font-size: 36rpx;
						font-weight: bold;
					}
				}

				.original-price {
					margin-left: 10rpx;
					font-size: 24rpx;
					color: #999;
					text-decoration: line-through;
				}
			}
		}
	}

	.empty-tip {
		text-align: center;
		color: #999;
		padding: 40rpx 0;
	}
}
</style>
