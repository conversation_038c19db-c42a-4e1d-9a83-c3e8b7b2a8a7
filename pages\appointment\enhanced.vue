<template>
	<view class="content">
		<view class="consultant">
			<view class="address" v-if="storeInfo">
				<view class="left">
					<view class="top">{{storeInfo.name}} {{storeInfo.branchName}}</view>
					<view class="bottom">{{storeInfo.address}}</view>
				</view>
				<view class="right" @click="toMap">
					<view class="top">
						<image mode="scaleToFill" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%96%B9%E4%BD%8D.png"></image>
					</view>
					<view class="bottom" v-if="distance">距您{{distance}}km</view>
				</view>
			</view>
			
			<view class="make-appointment-box">
				<view class="scroll">
					<!-- 使用增强版时间表组件 -->
					<EnhancedTimeTable
						ref="timeTableRef"
						:showDateSelector="true"
						:dayRange="6"
						:showMessage="true"
						:showActionButtons="true"
						message="请选择咨询时间，每次咨询时长为1小时,不在当前时间表内请下单后与客服预约时间"
						@timeChange="handleTimeChange"
						@intervalChange="handleIntervalChange"
						@confirm="handleConfirm"
						@dateChange="handleDateChange"
					/>
				</view>
			</view>
		</view>
		<cc-myTabbar :tabBarShow="2"></cc-myTabbar>
	</view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { onShow, onReady } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user.js";
import { getStore } from "@/api/store.js";
import EnhancedTimeTable from "@/components/EnhancedTimeTable/EnhancedTimeTable.vue";

onReady(() => {
	uni.hideTabBar();
});

onShow(() => {
	getStoreInfo();
});

const timeTableRef = ref(null);
const storeInfo = ref(null);
const distance = ref(null);
const user = useUserStore();

// 计算两点之间的距离
const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // 地球半径，单位km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const d = R * c;
    return d.toFixed(2); // 保留两位小数
};

// 获取用户位置
const getUserLocation = () => {
    return new Promise((resolve, reject) => {
        uni.getLocation({
            type: 'gcj02',
            success: (res) => {
                resolve(res);
            },
            fail: (err) => {
                console.error('获取位置失败：', err);
                reject(err);
            }
        });
    });
};

// 获取门店信息
const getStoreInfo = async () => {
	try {
		const res = await getStore(1); // 获取ID为1的门店信息
		if (res.code === 200) {
			storeInfo.value = res.data.store;
			// 获取用户位置并计算距离
			try {
				const location = await getUserLocation();
				distance.value = calculateDistance(
					location.latitude,
					location.longitude,
					storeInfo.value.latitude,
					storeInfo.value.longitude
				);
			} catch (locationError) {
				console.log('用户未授权位置信息或获取位置失败');
				distance.value = null;
			}
		} else {
			uni.showToast({
				title: '获取门店信息失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('获取门店信息错误：', error);
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		});
	}
};

const toMap = () => {
	if (!storeInfo.value) return;
	uni.openLocation({
		longitude: Number(storeInfo.value.longitude),
		latitude: Number(storeInfo.value.latitude),
		name: storeInfo.value.mapName,
		address: storeInfo.value.mapAddress,
		fail: (err) => console.log("导航失败:", err),
	});
};

// 时间表事件处理
const handleTimeChange = (selectedTimes) => {
	console.log('选择的时间点:', selectedTimes);
};

const handleIntervalChange = (intervals) => {
	console.log('时间区间:', intervals);
};

const handleDateChange = (dateItem, index) => {
	console.log('选择的日期:', dateItem, index);
};

const handleConfirm = (data) => {
	console.log('确认选择:', data);
	
	// 保存选择的时间到store
	user.handleSelectTime(data.selectTime);
	// 保存格式化后的时间参数到store
	user.setSelectedTimeParams(data.timeParams);
	
	console.log('Store state after setting params:', {
		selectTime: user.$state.selectTime,
		selectedTimeParams: user.$state.selectedTimeParams,
		shouldFilterByTime: user.$state.shouldFilterByTime
	});
	
	uni.switchTab({
		url: `/pages/classification/index`
	});
};

// 暴露方法供外部调用
const clearTimeSelection = () => {
	if (timeTableRef.value) {
		timeTableRef.value.clearSelection();
	}
};

const getSelectedTimes = () => {
	if (timeTableRef.value) {
		return timeTableRef.value.getSelectedTimes();
	}
	return [];
};

const getTimeIntervals = () => {
	if (timeTableRef.value) {
		return timeTableRef.value.getTimeIntervals();
	}
	return [];
};

const refreshTimeList = () => {
	if (timeTableRef.value) {
		timeTableRef.value.refreshTimeList();
	}
};

// 暴露方法
defineExpose({
	clearTimeSelection,
	getSelectedTimes,
	getTimeIntervals,
	refreshTimeList
});
</script>

<style lang="scss" scoped>
.content {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;

	.consultant {
		width: calc(100% - 40rpx);
		padding: 20rpx;
		
		.address {
			width: calc(100% - 40rpx);
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.left {
				.top {
					font-size: 34rpx;
					font-weight: 700;
					margin-bottom: 10rpx;
				}
				.bottom {
					font-size: 24rpx;
					color: #a2a2a2;
				}
			}
			
			.right {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				position: relative;
				
				.top {
					image {
						width: 36rpx;
						height: 36rpx;
						margin-right: 10rpx;
					}
				}
				
				.bottom {
					font-size: 22rpx;
					color: #a2a2a2;
				}
				
				&:before {
					content: "";
					width: 2rpx;
					height: 100rpx;
					position: absolute;
					left: -30rpx;
					background-color: #dddddd;
				}
			}
		}
		
		.make-appointment-box {
			margin-top: 20rpx;
			margin-bottom: 140rpx;
			
			.scroll {
				// 时间表组件的容器样式
			}
		}
	}
}
</style>
