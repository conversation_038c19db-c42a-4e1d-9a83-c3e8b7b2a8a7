<template>
  <view class="test-page">
    <view class="header">
      <text class="title">视频加载状态测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试视频播放器的加载状态优化：</text>
          <text>1. 拖动进度条时不会一直显示"加载中"</text>
          <text>2. 短暂缓冲时不会闪烁加载状态</text>
          <text>3. 智能判断真正需要显示加载的时机</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试视频</text>
        <view class="test-buttons">
          <button class="test-btn" @click="testShortVideo">测试短视频</button>
          <button class="test-btn" @click="testLongVideo">测试长视频</button>
          <button class="test-btn" @click="testNetworkVideo">测试网络视频</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">优化内容</text>
        <view class="fix-list">
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>添加智能加载状态管理</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>防抖机制避免状态频繁切换</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>优化seeking/seeked事件处理</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>时间更新时自动取消加载状态</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>延迟显示加载状态避免闪烁</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试步骤</text>
        <view class="steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <text>点击测试按钮打开视频播放器</text>
          </view>
          <view class="step-item">
            <view class="step-number">2</view>
            <text>等待视频开始播放</text>
          </view>
          <view class="step-item">
            <view class="step-number">3</view>
            <text>拖动进度条到不同位置</text>
          </view>
          <view class="step-item">
            <view class="step-number">4</view>
            <text>观察加载状态是否合理显示</text>
          </view>
          <view class="step-item">
            <view class="step-number">5</view>
            <text>测试播放/暂停功能</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 测试方法
const testShortVideo = () => {
  const videoUrl = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/video/short-demo.mp4'
  const title = '短视频测试'
  
  uni.navigateTo({
    url: `/pages/course/video-player/index?videoUrl=${encodeURIComponent(videoUrl)}&title=${encodeURIComponent(title)}&chapterId=test1&courseId=test`
  })
}

const testLongVideo = () => {
  const videoUrl = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/video/%E6%88%91%E6%98%AF%E5%A6%82%E4%BD%95%E5%9C%A8B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6%E7%9A%84_%E4%B8%A8%E5%BF%83%E7%90%86%E5%AD%A6%E4%BB%8E%E5%85%A5%E9%97%A8%E5%88%B0%E5%85%A5%E5%9C%9F%20%E2%80%94%E2%80%94%20B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6.mp4'
  const title = '长视频测试 - 心理学学习'
  
  uni.navigateTo({
    url: `/pages/course/video-player/index?videoUrl=${encodeURIComponent(videoUrl)}&title=${encodeURIComponent(title)}&chapterId=test2&courseId=test`
  })
}

const testNetworkVideo = () => {
  const videoUrl = 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
  const title = '网络视频测试'
  
  uni.navigateTo({
    url: `/pages/course/video-player/index?videoUrl=${encodeURIComponent(videoUrl)}&title=${encodeURIComponent(title)}&chapterId=test3&courseId=test`
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  }
}

.fix-list {
  .fix-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.steps {
  .step-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 20rpx;
    
    .step-number {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 600;
      flex-shrink: 0;
    }
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}
</style>
