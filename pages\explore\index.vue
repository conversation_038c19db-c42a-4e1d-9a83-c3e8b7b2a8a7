<template>
  <view class="explore-container">
    <!-- 搜索栏 -->
    <view class="search-wrapper" :style="{ 
      paddingTop: `${statusBarHeight}px`,
      height: `${navBarHeight}px`
    }">
      <view 
        class="search-box" 
        :style="{
          width: `${windowWidth - menuRight - menuWidth - 30}px`,
          height: `${menuHeight}px`,
          marginTop: `${menuTop - statusBarHeight}px`
        }"
        @click="toSearch"
      >
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <view class="search-placeholder">搜索</view>
      </view>
    </view>

    <!-- 标签切换 -->
    <view class="tab-wrapper">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 分类列表 -->
    <scroll-view 
      class="category-scroll" 
      scroll-x 
      show-scrollbar="false"
      :scroll-left="scrollLeft"
    >
      <view 
        class="category-item"
        :class="{ active: currentCategory === -1 }"
        @click="switchCategory(-1)"
      >
        全部
      </view>
      <view 
        v-for="(category, index) in categories" 
        :key="category.categoryId"
        class="category-item"
        :class="{ active: currentCategory === index }"
        @click="switchCategory(index)"
      >
        {{ category.categoryName }}
        <text class="count">({{ category.count }})</text>
      </view>
    </scroll-view>

    <!-- 轮播图 -->
    <swiper 
      class="banner" 
      circular 
      :indicator-dots="true" 
      indicator-color="rgba(255, 255, 255, 0.6)"
      indicator-active-color="#ffffff"
      :autoplay="true" 
      :interval="3000" 
      :duration="500"
    >
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <image 
          :src="item.imageUrl || defaultImage" 
          mode="aspectFill"
          @click="viewDetail(item)"
        ></image>
      </swiper-item>
    </swiper>

    <!-- 内容列表 -->
    <scroll-view 
      class="content-list" 
      scroll-y
      @scrolltolower="loadMore"
    >
      <view 
        v-for="item in currentList" 
        :key="item.id"
        class="list-item"
        @click="viewDetail(item)"
      >
        <image 
          :src="item.imageUrl || defaultImage" 
          mode="aspectFill" 
          class="item-image"
        ></image>
        <view class="item-info">
          <text class="item-title">{{ item.scaleName || item.name || item.title }}</text>
          <text class="item-desc">{{ item.description || item.introduction }}</text>
          <view class="item-meta">
            <text class="question-count" v-if="currentTab === 0">{{ item.questionCount }}道题</text>
            <text class="duration" v-else>{{ item.duration }}分钟</text>
            <text class="price" v-if="item.payMode === 1">¥{{ item.price }}</text>
            <text class="free" v-else>免费</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <uni-load-more :status="loadingStatus"></uni-load-more>
    </scroll-view>

    <cc-myTabbar :tabBarShow="3"></cc-myTabbar>
  </view>
</template>

<script setup>
// 导入所需的组件和API
import { ref, onMounted, computed } from 'vue'
import { onLoad, onReady } from "@dcloudio/uni-app"
import {
  listAssessment,
  getCategories,
  getAssessmentsByCategory
} from '@/api/evaluation'
import { getExploreMeditationList, getMeditationCategories } from '@/api/explore'

// 导航栏相关数据
const statusBarHeight = ref(0)
const menuButtonInfo = ref({})
const navBarHeight = ref(0)
const menuRight = ref(0)
const menuTop = ref(0)
const menuHeight = ref(32)
const menuWidth = ref(87) // 默认胶囊按钮宽度
const windowWidth = ref(375)

// 获取系统信息并计算导航栏高度
const initNavigationBar = () => {
  // #ifdef MP-WEIXIN
  try {
    // 获取窗口信息
    const windowInfo = uni.getWindowInfo()
    windowWidth.value = windowInfo.windowWidth
    
    // 获取系统信息
    const systemInfo = uni.getAppBaseInfo()
    statusBarHeight.value = windowInfo.statusBarHeight
    
    // 获取胶囊按钮位置信息
    const menuInfo = uni.getMenuButtonBoundingClientRect()
    menuButtonInfo.value = menuInfo
    menuRight.value = windowWidth.value - menuInfo.right
    menuHeight.value = menuInfo.height
    menuTop.value = menuInfo.top
    menuWidth.value = menuInfo.width
    
    // 计算导航栏高度
    navBarHeight.value = menuInfo.height + (menuInfo.top - statusBarHeight.value) * 2
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 使用默认值
    statusBarHeight.value = 20
    navBarHeight.value = 44
    menuRight.value = 7
    menuHeight.value = 32
    menuTop.value = statusBarHeight.value + 6
    menuWidth.value = 87 // 默认胶囊按钮宽度
  }
  // #endif
  
  // #ifdef H5 || APP-PLUS
  try {
    const windowInfo = uni.getWindowInfo()
    windowWidth.value = windowInfo.windowWidth
    statusBarHeight.value = windowInfo.statusBarHeight
    
    const systemInfo = uni.getAppBaseInfo()
    const isIOS = systemInfo.platform.toLowerCase() === 'ios'
    
    navBarHeight.value = isIOS ? 44 : 48
    menuRight.value = 10
    menuHeight.value = 32
    menuTop.value = statusBarHeight.value + (isIOS ? 6 : 8)
    menuWidth.value = 87 // 默认胶囊按钮宽度
  } catch (error) {
    console.error('获取系统信息失败:', error)
    // 使用默认值
    statusBarHeight.value = 20
    navBarHeight.value = 44
    menuRight.value = 10
    menuHeight.value = 32
    menuTop.value = 26
    menuWidth.value = 87
  }
  // #endif
}

// 基础数据
const searchKeyword = ref('')
const tabs = ['测评', '冥想']
const currentTab = ref(0)
const currentCategory = ref(-1) // -1 表示全部
const scrollLeft = ref(0)
const loadingStatus = ref('more')
const page = ref(1)
const pageSize = ref(10)

// 统计数据
const totalAssessments = ref(0)
const totalCategories = ref(0)

// 默认图片
const defaultImage = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-image.png'

// 列表数据
const assessmentList = ref([])
const meditationList = ref([])
const assessmentCategories = ref([])
const meditationCategories = ref([])

// 计算属性
const bannerList = computed(() => {
  const list = currentTab.value === 0 ? assessmentList.value : meditationList.value
  return list.slice(0, 3)
})

const categories = computed(() => {
  return currentTab.value === 0 ? assessmentCategories.value : meditationCategories.value
})

const currentList = computed(() => {
  if (currentTab.value === 0) {
    if (currentCategory.value === -1) {
      // 全部测评
      return assessmentList.value
    } else {
      // 特定分类的测评
      const category = assessmentCategories.value[currentCategory.value]
      return category ? category.assessments : []
    }
  } else {
    return meditationList.value
  }
})

// 页面方法
const toSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?type=' + currentTab.value
  })
}

const switchTab = (index) => {
  currentTab.value = index
  currentCategory.value = -1
  page.value = 1
  loadData()
}

const switchCategory = (index) => {
  currentCategory.value = index
  page.value = 1
  loadData()
}

const loadMore = () => {
  if (loadingStatus.value === 'noMore') return
  page.value++
  loadData()
}

const viewDetail = (item) => {
  if (currentTab.value === 0) {
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${item.id}`
    })
  } else {
    uni.navigateTo({
      url: `/pages/meditation/index?id=${item.id}`
    })
  }
}

// API调用相关方法
const loadData = async () => {
  try {
    loadingStatus.value = 'loading'
    if (currentTab.value === 0) {
      await loadAssessments()
    } else {
      await loadMeditations()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

const loadAssessments = async () => {
  try {
    // 获取分类数据
    const res = await getCategories()
    if (res.code === 200) {
      const { categories, totalAssessments: total, totalCategories: totalCats } = res.data
      assessmentCategories.value = categories || []
      totalAssessments.value = total
      totalCategories.value = totalCats
      
      if (currentCategory.value === -1) {
        // 全部测评：合并所有分类的测评
        assessmentList.value = categories.reduce((acc, category) => {
          return acc.concat(category.assessments || [])
        }, [])
      }
    }
    
    loadingStatus.value = 'more' // 由于返回所有数据，不需要分页
  } catch (error) {
    console.error('获取测评列表失败:', error)
  }
}

const loadMeditations = async () => {
  const categoryId = categories.value[currentCategory.value]?.categoryId
  const params = {
    keyword: searchKeyword.value,
    categoryId,
    page: page.value,
    pageSize: pageSize.value
  }
  
  try {
    const res = await getExploreMeditationList(params)
    if (res.code === 200) {
      if (page.value === 1) {
        meditationList.value = res.data
      } else {
        meditationList.value = [...meditationList.value, ...res.data]
      }
      loadingStatus.value = res.data.length < pageSize.value ? 'noMore' : 'more'
    }
  } catch (error) {
    console.error('获取冥想列表失败:', error)
  }
}

const loadCategories = async () => {
  try {
    // 获取冥想分类
    const meditationRes = await getMeditationCategories()
    if (meditationRes.code === 200) {
      meditationCategories.value = meditationRes.data || []
    }
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

// 生命周期钩子
onLoad(() => {
  initNavigationBar() // 初始化导航栏
  loadData() // 加载分类和列表数据
})

onReady(() => {
  uni.hideTabBar()
})
</script>

<style lang="scss" scoped>
.explore-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  
  .search-wrapper {
    background: #fff;
    position: relative;
    
    .search-box {
      position: absolute;
      left: 30rpx;
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 36rpx;
      padding: 0 24rpx;
      box-sizing: border-box;
      
      .uni-icons {
        margin-right: 12rpx;
        flex-shrink: 0;
      }
      
      .search-placeholder {
        flex: 1;
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  .tab-wrapper {
    display: flex;
    padding: 0 30rpx;
    background: #fff;
    border-bottom: 1rpx solid #eee;
    
    .tab-item {
      position: relative;
      padding: 24rpx 40rpx;
      font-size: 32rpx;
      color: #666;
      
      &.active {
        color: #333;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 48rpx;
          height: 4rpx;
          background: #409eff;
          border-radius: 2rpx;
        }
      }
    }
  }
  
  .category-scroll {
    white-space: nowrap;
    background: #fff;
    padding: 20rpx 0;
    
    .category-item {
      display: inline-block;
      padding: 12rpx 32rpx;
      margin: 0 16rpx;
      font-size: 28rpx;
      color: #666;
      background: #f5f5f5;
      border-radius: 28rpx;
      
      &:first-child {
        margin-left: 30rpx;
      }
      
      &:last-child {
        margin-right: 30rpx;
      }
      
      &.active {
        color: #fff;
        background: #409eff;
      }

      .count {
        font-size: 24rpx;
        margin-left: 4rpx;
      }
    }
  }
  
  .banner {
    height: 300rpx;
    padding: 30rpx;
    box-sizing: border-box;
    
    image {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }
  }
  
  .content-list {
    padding: 0 30rpx;
		width: calc(100% - 60rpx);
    
    .list-item {
      display: flex;
      background: #fff;
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;
      
      .item-image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
        margin-right: 24rpx;
      }
      
      .item-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .item-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 12rpx;
        }
        
        .item-desc {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 24rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        
        .item-meta {
          margin-top: auto;
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .question-count, .duration {
            font-size: 24rpx;
            color: #409eff;
          }
          
          .price {
            font-size: 28rpx;
            color: #ff6b6b;
            font-weight: 500;
          }
          
          .free {
            font-size: 24rpx;
            color: #52c41a;
          }
        }
      }
    }
  }
}
</style>
