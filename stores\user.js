// src/stores/user.js
import {
	defineStore
} from 'pinia'
// import {
// 	refetchUser
// } from '@/api/user' // 根据实际API路径调整
import {
	getInfo,
	getCode,
	getMobileLogin
} from "@/api/my.js"
import {
	getToken,
	setToken,
	removeToken
} from '@/utils/auth'
import {
	useChatStore
} from "@/stores/chat";
import { wxPhoneLogin } from "@/api/index.js";
import { useMenuStore } from '@/stores/menu'

export const useUserStore = defineStore('user', {
	state: () => ({
		token: getToken() || '', // 登录令牌
		profile: uni.getStorageSync('profile') || null, // 用户资料
		roles: uni.getStorageSync('roles') || [], // 用户角色数组
		lastLoginTime: 0, // 最后登录时间
		selectTime: [],
		selectedTimeParams: [], // 添加新的状态
		shouldFilterByTime: false, // 是否需要按时间筛选
		smsCountdown: 0, // 剩余秒数
		smsTimer: null, // 定时器ID
	}),

	getters: {
		isLoggedIn: (state) => !!state.token, // 登录状态验证
		userName: (state) => state.profile?.emergencyContactName || '未登录',
		userId: (state) => state.profile?.userId || null,
		userAvatar: (state) => state.profile?.avatar || null,
		// 获取roleKey
		roleKey: (state) => state.roles?.[0]?.roleKey || ''
	},

	actions: {
		// 统一登录方法
		async login(phone, code, loginType = 'mobile') {
			try {
				let res;
				if (loginType === 'mobile') {
					// 手机号验证码登录
					res = await getMobileLogin({
						phone,
						code
					})
				} else if (loginType === 'wxPhone') {
					// 微信手机号一键登录
					res = await wxPhoneLogin(code)
				}

				if (res.code === 200) {
					// 存储 token 和用户信息
					this.token = res.token
					// 持久化存储
					setToken(res.token)

					console.log('登录成功，开始获取用户信息')
					await this.fetchProfile()
					console.log('用户信息获取完成，用户ID:', this.userId)

					// 获取用户角色后，更新菜单
					const menuStore = useMenuStore()
					if (this.roleKey) {
						await menuStore.fetchMenusByRole(this.roleKey)
					}
					
					uni.showToast({
						title: '登录成功',
						icon: 'success',
						duration: 1500
					})
					
					// 延迟跳转，让用户看到成功提示
					setTimeout(() => {
						this.navigateToFirstMenu(loginType)
					}, 1500)
					
					return true
				} else {
					uni.showToast({
						title: res.msg || '登录失败',
						icon: 'none'
					})
					return false
				}
			} catch (error) {
				console.error('登录错误:', error)
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				})
				this.clearUser()
				return false
			}
		},

		// 跳转到第一个菜单页面
		navigateToFirstMenu(loginType = 'mobile') {
			const menuStore = useMenuStore()
			const firstMenu = menuStore.getFirstTabBarMenu

			if (firstMenu && firstMenu.path) {
				// 如果有动态菜单，跳转到第一个菜单页面
				uni.switchTab({
					url: firstMenu.path
				})
			} else {
				// 如果没有动态菜单，使用默认跳转逻辑
				if (loginType === 'mobile') {
					uni.navigateBack(1)
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
			}
		},

		// 发送验证码方法
		async sendSmsCode(phone) {
			if (this.isCountingDown) return // 防止重复发送
			if (!/^1[3-9]\d{9}$/.test(phone)) {
				uni.showToast({
					title: "手机号格式错误",
					icon: "none"
				})
				return false
			}

			try {
				const res = await getCode({
					phone
				})
				if (res.code === 200) {
					uni.showToast({
						title: "验证码已发送",
						icon: "success"
					})
					this.startCountdown()
					return true
				} else {
					uni.showToast({
						title: res.msg || "发送失败",
						icon: "none"
					})
					return false
				}
			} catch (error) {
				console.error('验证码发送失败:', error)
				uni.showToast({
					title: "网络请求失败",
					icon: "none"
				})
				return false
			}
		},

		// 获取用户资料
		async fetchProfile() {
			try {
				const res = await getInfo()
				console.log('获取用户信息响应:', res)

				this.profile = res.user

				// 保存用户信息和角色
				uni.setStorageSync('profile', res.user)
				if (res.user.roles && res.user.roles.length > 0) {
					this.roles = res.user.roles
					uni.setStorageSync('roles', res.user.roles)

					// 获取用户角色后，更新菜单
					const menuStore = useMenuStore()
					await menuStore.fetchMenusByRole(this.roleKey)
				}

				// 确保用户ID存在后再初始化WebSocket
				if (res.user && res.user.userId) {
					console.log('初始化WebSocket，用户ID:', res.user.userId)
					const chatStore = useChatStore();
					chatStore.initWebSocket(res.user.userId);
				} else {
					console.error('用户信息中缺少userId:', res.user)
				}

				return res.data
			} catch (error) {
				console.error('获取用户信息失败:', error)
				this.clearUser()
				throw error
			}
		},

		// 退出登录
		logout() {
			this.clearUser()
			uni.reLaunch({
				url: '/pages/login/login'
			}) // 跳转到登录页
		},

		// 清空用户数据
		clearUser() {
			this.token = ''
			this.profile = null
			this.roles = []
			removeToken()
			uni.removeStorageSync('profile')
			uni.removeStorageSync('roles')
			
			// 重置菜单为默认菜单
			const menuStore = useMenuStore()
			menuStore.resetToDefault()
		},

		// 更新用户资料
		updateProfile(newProfile) {
			this.profile = {
				...this.profile,
				...newProfile
			}
		},
		handleSelectTime(selectTime) {
			this.selectTime = selectTime
		},
		setSelectedTimeParams(params) { // 添加新的action
			this.selectedTimeParams = params;
			this.shouldFilterByTime = true; // 设置标志为true
		},
		clearTimeFilter() {
			this.shouldFilterByTime = false;
		},
		// 开始倒计时
		startCountdown(seconds = 60) {
			this.smsCountdown = seconds
			this.smsTimer = setInterval(() => {
				if (--this.smsCountdown <= 0) {
					this.clearCountdown()
				}
			}, 1000)
		},

		// 清理倒计时
		clearCountdown() {
			clearInterval(this.smsTimer)
			this.smsTimer = null
			this.smsCountdown = 0
		}
	},
})