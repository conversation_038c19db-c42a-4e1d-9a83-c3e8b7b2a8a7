# 分类页面接口更新文档

## 更新概述

根据后端新的小程序用户端和咨询师端接口架构，更新了分类页面的接口调用，使用新的接口体系来获取咨询师和相关数据。

## 🔄 接口架构变更

### **原有接口架构**
```javascript
// 旧的接口调用
import { listAvailableConsultants, searchConsultants, getConsultantDetail } from "@/api/classification.js"

// 直接调用系统接口
listAvailableConsultants() → /system/consultant/list
searchConsultants() → /system/consultant/search
getConsultantDetail() → /system/consultant/detail/{id}
```

### **新的接口架构**
```javascript
// 新的接口调用
import { listAvailableConsultants, getConsultantDetail } from "@/api/consultant.js"
import { getTabbarMenus, getUserMenus } from "@/api/miniapp-user.js"

// 使用小程序用户端接口
listAvailableConsultants() → /miniapp/user/match/question/quickMatch
getConsultantDetail() → /system/consultant/detail/{id}
```

## 📁 新增接口文件

### **1. api/miniapp-user.js**
小程序用户端接口集合，包含：

#### **极速匹配功能**
- `getMatchQuestionList()` - 获取匹配问题列表
- `getMatchQuestionDetail()` - 获取问题详情
- `matchConsultants()` - 根据选项筛选咨询师
- `quickMatchConsultants()` - 快速匹配咨询师
- `getOptionConsultants()` - 获取选项关联咨询师
- `searchMatchQuestions()` - 搜索匹配问题
- `getRecommendQuestions()` - 获取推荐问题
- `getHotQuestions()` - 获取热门问题

#### **导航菜单功能**
- `getTabbarMenus()` - 获取小程序导航菜单
- `getMenusByPermissions()` - 根据权限获取菜单
- `getUserMenus()` - 获取用户可访问菜单
- `getHomeMenus()` - 获取首页菜单
- `checkMenuPermission()` - 检查菜单权限

#### **咨询订单功能**
- `createConsultantOrder()` - 创建咨询订单
- `getMyOrders()` - 获取我的订单列表
- `getOrderDetail()` - 获取订单详情
- `cancelOrder()` - 取消订单
- `checkTimeAvailable()` - 检查时间段可用性
- `refundOrder()` - 申请退款

#### **咨询师评价功能**
- `submitConsultantReview()` - 提交咨询评价
- `getMyReviews()` - 获取我的评价列表
- `getConsultantReviews()` - 获取咨询师评价列表
- `getReviewDetail()` - 获取评价详情
- `canRateRecord()` - 检查是否可评价
- `getConsultantReviewStatistics()` - 获取咨询师评价统计

### **2. api/consultant.js**
咨询师相关接口集合，包含：

#### **咨询师基础信息**
- `listAvailableConsultants()` - 获取可用咨询师列表
- `searchConsultants()` - 搜索咨询师
- `getConsultantDetail()` - 获取咨询师详情
- `getConsultantsByOption()` - 根据选项获取关联咨询师
- `getRecommendConsultants()` - 获取推荐咨询师
- `getHotConsultants()` - 获取热门咨询师

#### **咨询师评价相关**
- `getConsultantReviews()` - 获取咨询师评价列表
- `getConsultantReviewStatistics()` - 获取咨询师评价统计
- `getConsultantHighRatingReviews()` - 获取咨询师高分评价
- `getConsultantLatestReviews()` - 获取咨询师最新评价
- `getConsultantReviewTypeStatistics()` - 获取咨询师评价类型统计

#### **咨询订单相关**
- `createConsultantOrder()` - 创建咨询订单
- `checkConsultantAvailable()` - 检查咨询师时间段可用性
- `getConsultantOrderStatistics()` - 获取咨询师订单统计

#### **匹配相关**
- `matchConsultantsByOptions()` - 根据问题选项匹配咨询师
- `quickMatchConsultants()` - 快速匹配咨询师
- `getMatchStatistics()` - 获取匹配统计信息

## 🔧 分类页面接口更新

### **导入更新**
```javascript
// 更新前
import { listAvailableConsultants, searchConsultants, getConsultantDetail } from "@/api/classification.js"

// 更新后
import { 
  listAvailableConsultants, 
  getConsultantDetail
} from "@/api/consultant.js"
```

### **咨询师数据加载优化**
```javascript
// 更新前
res = await listAvailableConsultants()

// 更新后
res = await listAvailableConsultants({
  quickMatch: true,  // 使用快速匹配
  categoryId: categoryId  // 支持分类筛选
})
```

### **数据字段映射优化**
```javascript
// 更新后的数据映射
counselorList.value = consultants.map(item => ({
  ...item,
  avatar: item.imageUrl || item.avatar || item.headImg,
  name: item.name || item.consultantName || item.realName,
  id: item.id || item.consultantId,
  price: item.price || item.consultationFee || 0,
  counselorLevel: item.counselorLevel || item.level || '1'
}))
```

## 🚀 功能增强

### **1. 智能匹配**
- 使用极速匹配接口获取推荐咨询师
- 支持根据用户偏好快速匹配
- 提供更精准的咨询师推荐

### **2. 分类筛选**
- 支持根据分类ID筛选咨询师
- 可以传递分类参数进行精确匹配
- 提高咨询师推荐的相关性

### **3. 错误处理**
```javascript
try {
  res = await listAvailableConsultants(params)
  // 处理成功
} catch (error) {
  console.error('加载咨询师失败:', error)
  if (type === 'consultant') {
    counselorList.value = []  // 设置空数组
  }
}
```

### **4. 数据兼容性**
- 支持多种数据字段格式
- 自动映射不同的字段名称
- 确保数据的完整性和一致性

## 📊 接口对比

### **咨询师列表接口**
| 功能 | 旧接口 | 新接口 | 优势 |
|------|--------|--------|------|
| 获取咨询师 | `/system/consultant/list` | `/miniapp/user/match/question/quickMatch` | 智能匹配推荐 |
| 搜索咨询师 | `/system/consultant/search` | `/miniapp/user/match/question/search` | 基于问题匹配 |
| 分类筛选 | 不支持 | 支持categoryId参数 | 精确分类筛选 |
| 匹配算法 | 简单列表 | 智能匹配算法 | 提高匹配精度 |

### **数据结构对比**
```javascript
// 旧数据结构
{
  code: 200,
  data: [
    {
      id: 1,
      name: "张医生",
      imageUrl: "avatar.jpg"
    }
  ]
}

// 新数据结构
{
  code: 200,
  data: [
    {
      id: 1,
      realName: "张医生",
      headImg: "avatar.jpg",
      consultationFee: 200,
      level: "2"
    }
  ]
}
```

## 🎯 使用建议

### **1. 渐进式迁移**
- 保持旧接口作为备用
- 逐步切换到新接口
- 确保功能的连续性

### **2. 错误处理**
- 添加完善的错误处理机制
- 提供降级方案
- 确保用户体验不受影响

### **3. 数据验证**
- 验证新接口返回的数据格式
- 确保字段映射的正确性
- 处理可能的数据缺失

### **4. 性能优化**
- 利用新接口的智能匹配功能
- 减少不必要的数据请求
- 提高页面加载速度

## 📝 总结

通过更新到新的小程序用户端接口架构：

1. ✅ **功能增强**: 支持智能匹配和精确筛选
2. ✅ **性能提升**: 使用更高效的匹配算法
3. ✅ **用户体验**: 提供更精准的咨询师推荐
4. ✅ **架构统一**: 使用统一的小程序接口体系
5. ✅ **扩展性**: 为后续功能扩展奠定基础

新的接口架构为分类页面提供了更强大的功能支持和更好的用户体验！
