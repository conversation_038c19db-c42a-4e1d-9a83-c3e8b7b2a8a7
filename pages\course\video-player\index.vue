<template>
  <view class="video-player-page">
    <!-- 视频播放器 -->
    <view class="video-container">
      <t-video
        id="video-player"
        :src="videoUrl"
        :poster="posterImage"
        :title="videoTitle"
        :show_center_play_btn="true"
        :show_play_btn="true"
        :show_fullscreen_btn="true"
        :show_progress="true"
        :show_loading="true"
        :controls="true"
        :autoplay="false"
        :loop="false"
        :muted="false"
        :page_gesture="false"
        :direction="0"
        :object_fit="'contain'"
        style="width:100%;height:100%;"
        @Play="onPlay"
        @Pause="onPause"
        @Ended="onEnded"
        @Timeupdate="onTimeUpdate"
        @Fullscreenchange="onFullscreenChange"
        @Error="onError"
        @Loadstart="onLoadStart"
        @Loadeddata="onLoadedData"
        @Canplay="onCanPlay"
        @Canplaythrough="onCanPlayThrough"
        @Waiting="onWaiting"
        @Seeking="onSeeking"
        @Seeked="onSeeked"
        @Progress="onProgress"
        @videoloaded="onVideoLoaded"
        @videosuccess="onVideoSuccess"
        @videofailed="onVideoFailed"
      ></t-video>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-overlay">
        <uni-icons type="spinner-cycle" size="40" color="#fff"></uni-icons>
        <text class="loading-text">视频加载中...</text>
      </view>
      
      <!-- 错误状态 -->
      <view v-if="error" class="error-overlay">
        <uni-icons type="closeempty" size="40" color="#fff"></uni-icons>
        <text class="error-text">视频加载失败</text>
        <button class="retry-btn" @click="retryLoad">重试</button>
      </view>
    </view>
    
    <!-- 视频信息 -->
    <view class="video-info">
      <view class="video-title">{{ videoTitle }}</view>
      <view class="video-meta">
        <text class="duration">时长: {{ formatDuration(videoDuration) }}</text>
        <text class="progress">进度: {{ formatDuration(currentTime) }} / {{ formatDuration(videoDuration) }}</text>
      </view>
      <view class="video-description">
        <text>{{ videoDescription || '暂无描述' }}</text>
      </view>
    </view>
    
    <!-- 章节列表 -->
    <view v-if="chapterList.length > 0" class="chapter-list">
      <view class="list-title">相关章节</view>
      <scroll-view scroll-y class="chapter-scroll">
        <view
          v-for="(chapter, index) in chapterList"
          :key="chapter.id"
          :class="['chapter-item', { active: chapter.id === currentChapterId }]"
          @click="switchChapter(chapter)"
        >
          <view class="chapter-number">{{ chapter.chapterOrder || (index + 1) }}</view>
          <view class="chapter-info">
            <view class="chapter-title">{{ chapter.chapterTitle }}</view>
            <view class="chapter-duration">{{ formatDuration(chapter.duration) }}</view>
          </view>
          <view class="chapter-status">
            <uni-icons
              v-if="chapter.id === currentChapterId"
              type="sound"
              size="16"
              color="#007aff"
            ></uni-icons>
            <uni-icons
              v-else-if="chapter.mediaUrl"
              type="videocam"
              size="16"
              color="#999"
            ></uni-icons>
            <uni-icons
              v-else
              type="compose"
              size="16"
              color="#ccc"
            ></uni-icons>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 视频右下角控制按钮 -->
    <view class="video-controls">
      <button class="video-control-btn speed-btn" @click="adjustSpeed">
        <text class="speed-text">{{ playbackRate }}x</text>
      </button>
      <button class="video-control-btn fullscreen-btn" @click="toggleFullscreen">
        <uni-icons type="scan" size="18" color="#fff"></uni-icons>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { getCourseChapters } from '@/api/course'

// 响应式数据
const videoUrl = ref('')
const videoTitle = ref('')
const videoDescription = ref('')
const posterImage = ref('')
const currentChapterId = ref(null)
const courseId = ref(null)
const chapterList = ref([])

// 播放状态
const isPlaying = ref(false)
const loading = ref(false) // 默认不显示加载状态
const error = ref(false)
const currentTime = ref(0)
const videoDuration = ref(0)
const playbackRate = ref(1)

// 视频上下文
let videoContext = null

// 加载状态防抖定时器
let loadingTimer = null

// 智能设置加载状态
const setLoadingState = (isLoading, delay = 0) => {
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }

  if (delay > 0) {
    loadingTimer = setTimeout(() => {
      loading.value = isLoading
      loadingTimer = null
    }, delay)
  } else {
    loading.value = isLoading
  }
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds || seconds === 0) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 视频事件处理
const onPlay = () => {
  isPlaying.value = true
  console.log('视频开始播放')
}

const onPause = () => {
  isPlaying.value = false
  console.log('视频暂停')
}

const onEnded = () => {
  isPlaying.value = false
  console.log('视频播放结束')
  // 可以在这里实现自动播放下一章节
  playNextChapter()
}

const onTimeUpdate = (e) => {
  currentTime.value = e.detail.currentTime
  videoDuration.value = e.detail.duration

  // 如果视频正在播放且时间在更新，说明不需要显示加载状态
  if (isPlaying.value && e.detail.currentTime > 0) {
    setLoadingState(false)
  }
}

const onFullscreenChange = (e) => {
  console.log('全屏状态变化:', e.detail.fullScreen)
}

const onError = (e) => {
  console.error('视频播放错误:', e)
  error.value = true
  loading.value = false
  uni.showToast({
    title: '视频播放失败',
    icon: 'none'
  })
}

const onLoadStart = () => {
  setLoadingState(true)
  error.value = false
  console.log('视频开始加载')
}

const onLoadedData = () => {
  setLoadingState(false)
  error.value = false
  console.log('视频数据加载完成')
}

// 添加更多视频事件处理
const onCanPlay = () => {
  setLoadingState(false)
  error.value = false
  console.log('视频可以播放')
}

const onWaiting = () => {
  // 延迟显示加载状态，避免短暂缓冲时的闪烁
  setLoadingState(true, 300)
  console.log('视频缓冲中')
}

// 添加更多事件处理来管理加载状态
const onCanPlayThrough = () => {
  setLoadingState(false)
  error.value = false
  console.log('视频可以流畅播放')
}

const onSeeking = () => {
  // 跳转时立即显示加载状态
  setLoadingState(true)
  console.log('视频跳转中')
}

const onSeeked = () => {
  // 跳转完成后立即取消加载状态
  setLoadingState(false)
  console.log('视频跳转完成')
}

const onProgress = () => {
  // 视频缓冲进度更新时，通常意味着数据正在加载，但不一定需要显示加载状态
  console.log('视频缓冲进度更新')
}

// 小智宝视频插件特有事件处理
const onVideoLoaded = () => {
  console.log('视频已可播放')
  // 在这里获取视频上下文
  try {
    const pages = uni.getCurrentPages()
    const currentPage = pages[pages.length - 1]
    videoContext = currentPage.selectComponent('#video-player')
  } catch (error) {
    console.error('获取视频上下文失败:', error)
  }
  setLoadingState(false)
  error.value = false
}

const onVideoSuccess = () => {
  console.log('视频成功回调')
  setLoadingState(false)
  error.value = false
}

const onVideoFailed = (e) => {
  console.error('视频失败回调:', e)
  error.value = true
  loading.value = false

  let errorMessage = '视频播放失败'
  if (e.detail && e.detail.ret) {
    switch (e.detail.ret) {
      case 6:
        errorMessage = '小程序appid未注册'
        break
      case 12:
        errorMessage = '用户状态异常'
        break
      case 13:
        errorMessage = '视频链接异常'
        break
      case 9:
        errorMessage = '用户时长不够'
        break
      case 8:
        errorMessage = '审核不通过'
        break
      default:
        errorMessage = `视频播放失败(错误码:${e.detail.ret})`
    }
  }

  uni.showToast({
    title: errorMessage,
    icon: 'none'
  })
}



// 控制方法

const toggleFullscreen = () => {
  if (!videoContext) return
  videoContext.requestFullScreen()
}

const adjustSpeed = () => {
  const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2]
  const currentIndex = speeds.indexOf(playbackRate.value)
  const nextIndex = (currentIndex + 1) % speeds.length
  playbackRate.value = speeds[nextIndex]
  
  if (videoContext) {
    videoContext.playbackRate(playbackRate.value)
  }
}

const retryLoad = () => {
  error.value = false
  loading.value = true
  // 重新设置视频源
  if (videoContext) {
    videoContext.src = videoUrl.value
  }
}

// 章节切换
const switchChapter = (chapter) => {
  if (!chapter.mediaUrl) {
    uni.showToast({
      title: '该章节暂无视频',
      icon: 'none'
    })
    return
  }
  
  currentChapterId.value = chapter.id
  videoUrl.value = chapter.mediaUrl
  videoTitle.value = chapter.chapterTitle
  videoDescription.value = chapter.chapterContent || ''
  
  // 重新加载视频
  if (videoContext) {
    videoContext.src = videoUrl.value
  }
}

// 播放下一章节
const playNextChapter = () => {
  const currentIndex = chapterList.value.findIndex(chapter => chapter.id === currentChapterId.value)
  if (currentIndex >= 0 && currentIndex < chapterList.value.length - 1) {
    const nextChapter = chapterList.value[currentIndex + 1]
    if (nextChapter.mediaUrl) {
      switchChapter(nextChapter)
    }
  }
}

// 加载章节列表
const loadChapterList = async () => {
  try {
    const res = await getCourseChapters(courseId.value)
    if (res.code === 200) {
      chapterList.value = res.data.filter(chapter => chapter.mediaUrl) // 只显示有视频的章节
    }
  } catch (error) {
    console.error('获取章节列表失败:', error)
  }
}

// 生命周期
onLoad((options) => {
  videoUrl.value = decodeURIComponent(options.videoUrl || '')
  videoTitle.value = decodeURIComponent(options.title || '视频播放')
  currentChapterId.value = options.chapterId
  courseId.value = options.courseId

  // 设置导航标题
  uni.setNavigationBarTitle({
    title: videoTitle.value
  })

  // 验证视频URL
  if (!videoUrl.value) {
    error.value = true
    loading.value = false
    console.error('视频URL为空')
  } else {
    console.log('视频URL:', videoUrl.value)
    // 有视频URL时，初始状态不显示加载
    loading.value = false
    error.value = false
  }

  // 加载章节列表
  if (courseId.value) {
    loadChapterList()
  }
})

onMounted(() => {
  // 创建视频上下文 - 使用selectComponent代替createVideoContext
  // 需要在组件加载完成后获取
})

onUnload(() => {
  // 页面卸载时暂停视频和清理定时器
  if (videoContext) {
    videoContext.pause()
  }
  if (loadingTimer) {
    clearTimeout(loadingTimer)
    loadingTimer = null
  }
})
</script>

<style lang="scss" scoped>
.video-player-page {
  min-height: 100vh;
  background-color: #000;
  display: flex;
  flex-direction: column;
}

.video-container {
  position: relative;
  width: 100%;
  height: 450rpx;
  background-color: #000;



  .loading-overlay,
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.8);

    .loading-text,
    .error-text {
      color: #fff;
      font-size: 28rpx;
      margin-top: 20rpx;
    }

    .retry-btn {
      margin-top: 20rpx;
      padding: 16rpx 32rpx;
      background-color: #007aff;
      color: #fff;
      border-radius: 8rpx;
      border: none;
      font-size: 28rpx;
    }
  }
}

.video-info {
  background: linear-gradient(135deg, #fff, #f8f9fa);
  padding: 32rpx;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: -10rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 6rpx;
    background: #e0e0e0;
    border-radius: 3rpx;
  }

  .video-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    line-height: 1.4;
  }

  .video-meta {
    display: flex;
    gap: 32rpx;
    margin-bottom: 16rpx;

    text {
      font-size: 26rpx;
      color: #666;
      background: rgba(102, 126, 234, 0.1);
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-weight: 500;
    }
  }

  .video-description {
    text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }
  }
}

.chapter-list {
  flex: 1;
  background-color: #fff;
  margin-top: 16rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .list-title {
    padding: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    text-align: center;
  }

  .chapter-scroll {
    height: 400rpx;
  }

  .chapter-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f8f9fa;
      transform: translateX(8rpx);
    }

    &.active {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
      border-left: 4rpx solid #667eea;

      .chapter-title {
        color: #667eea;
        font-weight: 600;
      }

      .chapter-number {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: #fff;
      }
    }

    .chapter-number {
      width: 60rpx;
      height: 60rpx;
      border-radius: 30rpx;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: #666;
      margin-right: 24rpx;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .chapter-info {
      flex: 1;

      .chapter-title {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
        line-height: 1.4;
      }

      .chapter-duration {
        font-size: 24rpx;
        color: #999;
        background: rgba(102, 126, 234, 0.1);
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        display: inline-block;
      }
    }

    .chapter-status {
      margin-left: 16rpx;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.2);
        transform: scale(1.1);
      }
    }
  }
}

.video-controls {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 16rpx;
  z-index: 100;

  .video-control-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 36rpx;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.9);
      background: rgba(0, 0, 0, 0.8);
    }

    &.speed-btn {
      width: 80rpx;

      .speed-text {
        color: #fff;
        font-size: 22rpx;
        font-weight: 600;
      }
    }


  }
}

// 横屏适配
@media screen and (orientation: landscape) {
  .video-container {
    height: 100vh;
  }

  .video-info,
  .chapter-list,
  .control-bar {
    display: none;
  }
}
</style>
