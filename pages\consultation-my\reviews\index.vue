<template>
  <view class="reviews-container">
    <!-- 评价统计 -->
    <view class="stats-card">
      <view class="stats-header">
        <view class="rating-summary">
          <view class="rating-score">
            <text class="score-number">{{ statsInfo.averageRating || 5.0 }}</text>
            <view class="stars-container">
              <image
                v-for="i in 5"
                :key="i"
                :src="i <= Math.floor(statsInfo.averageRating || 5) ? '/static/icon/star-filled.png' : '/static/icon/star-empty.png'"
                mode="aspectFit"
                class="star-icon"
              ></image>
            </view>
            <text class="total-reviews">基于{{ statsInfo.totalReviews || 0 }}条评价</text>
          </view>
        </view>
        <view class="rating-chart">
          <view
            class="chart-item"
            v-for="item in ratingDistribution"
            :key="item.star"
          >
            <text class="star-text">{{ item.star }}星</text>
            <view class="progress-container">
              <view class="progress-track">
                <view class="progress-fill" :style="{ width: item.percentage + '%' }"></view>
              </view>
            </view>
            <text class="count-text">{{ item.count }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="{ active: currentFilter === index }"
        @click="switchFilter(index)"
      >
        <text>{{ tab.name }}</text>
        <text class="tab-count" v-if="tab.count > 0">({{ tab.count }})</text>
      </view>
    </view>

    <!-- 评价列表 -->
    <scroll-view
      class="reviews-list"
      scroll-y
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="review-card" v-for="review in reviewList" :key="review.id">
        <!-- 评价头部 -->
        <view class="review-header">
          <view class="user-section">
            <image class="user-avatar" :src="review.clientAvatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
            <view class="user-info">
              <text class="user-name">{{ review.clientName }}</text>
              <view class="review-rating">
                <image
                  v-for="i in 5"
                  :key="i"
                  :src="i <= review.rating ? '/static/icon/star-filled.png' : '/static/icon/star-empty.png'"
                  mode="aspectFit"
                  class="rating-star"
                ></image>
              </view>
            </view>
          </view>
          <text class="review-date">{{ formatDateTime(review.createTime) }}</text>
        </view>

        <!-- 评价内容 -->
        <view class="review-body">
          <text class="review-text">{{ review.content }}</text>

          <!-- 评价图片 -->
          <view class="review-gallery" v-if="review.images && review.images.length > 0">
            <image
              v-for="(img, index) in review.images"
              :key="index"
              :src="img"
              mode="aspectFill"
              class="gallery-image"
              @click="previewImage(review.images, index)"
            ></image>
          </view>
        </view>

        <!-- 服务标签 -->
        <view class="service-tag">
          <text class="service-label">{{ review.serviceName }}</text>
          <text class="service-date">{{ formatDateTime(review.serviceTime) }}</text>
        </view>

        <!-- 咨询师回复 -->
        <view class="consultant-reply" v-if="review.reply">
          <view class="reply-avatar">
            <image src="/static/icon/consultant-avatar.png" mode="aspectFill"></image>
          </view>
          <view class="reply-content">
            <view class="reply-header">
              <text class="reply-title">咨询师回复</text>
              <text class="reply-time">{{ formatDateTime(review.reply.createTime) }}</text>
            </view>
            <text class="reply-text">{{ review.reply.content }}</text>
          </view>
        </view>

        <!-- 回复按钮 -->
        <view class="reply-action" v-if="!review.reply">
          <button class="reply-button" @click="showReplyModal(review)">
            <image src="/static/icon/reply.png" mode="aspectFit"></image>
            <text>回复评价</text>
          </button>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="reviewList.length === 0 && !loading">
        <image src="/static/icon/empty-review.png" mode="aspectFit"></image>
        <text>暂无评价</text>
      </view>
    </scroll-view>

    <!-- 回复弹窗 -->
    <view class="reply-modal" v-if="showReplyForm" @click="closeReplyModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">回复评价</text>
          <text class="modal-close" @click="closeReplyModal">×</text>
        </view>
        <view class="modal-body">
          <view class="original-review">
            <text class="review-label">原评价：</text>
            <text class="review-text">{{ currentReview.content }}</text>
          </view>
          <textarea 
            class="reply-input"
            v-model="replyContent"
            placeholder="请输入回复内容"
            maxlength="200"
            :show-count="true"
          ></textarea>
        </view>
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeReplyModal">取消</button>
          <button class="btn-confirm" @click="submitReply" :disabled="!replyContent.trim()">发送回复</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getConsultantReviews, replyToReview, getReviewStatistics } from '@/api/consultant-app.js'

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const pageNum = ref(1)
const pageSize = ref(10)
const currentFilter = ref(0)

const reviewList = ref([])
const statsInfo = ref({})
const ratingDistribution = ref([])
const showReplyForm = ref(false)
const currentReview = ref({})
const replyContent = ref('')

// 筛选标签
const filterTabs = ref([
  { name: '全部', value: 'all', count: 0 },
  { name: '5星', value: '5', count: 0 },
  { name: '4星', value: '4', count: 0 },
  { name: '3星', value: '3', count: 0 },
  { name: '2星', value: '2', count: 0 },
  { name: '1星', value: '1', count: 0 },
  { name: '未回复', value: 'no_reply', count: 0 }
])

// 生命周期
onMounted(() => {
  loadReviews()
  loadStatistics()
})

onShow(() => {
  loadReviews()
})

// 加载评价列表
const loadReviews = async (isRefresh = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    if (isRefresh) {
      pageNum.value = 1
      hasMore.value = true
    }
    
    const params = {
      filter: filterTabs.value[currentFilter.value].value,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    }
    
    const res = await getConsultantReviews(params)
    if (res.code === 200) {
      const newReviews = res.data.list || []
      
      if (isRefresh) {
        reviewList.value = newReviews
      } else {
        reviewList.value.push(...newReviews)
      }
      
      hasMore.value = newReviews.length === pageSize.value
      
      // 更新筛选标签计数
      updateFilterCounts(res.data.filterCounts || {})
    }
  } catch (error) {
    console.error('加载评价失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await getReviewStatistics()
    if (res.code === 200) {
      statsInfo.value = res.data.stats || {}
      ratingDistribution.value = res.data.distribution || [
        { star: 5, count: 0, percentage: 0 },
        { star: 4, count: 0, percentage: 0 },
        { star: 3, count: 0, percentage: 0 },
        { star: 2, count: 0, percentage: 0 },
        { star: 1, count: 0, percentage: 0 }
      ]
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 更新筛选标签计数
const updateFilterCounts = (counts) => {
  filterTabs.value.forEach(tab => {
    tab.count = counts[tab.value] || 0
  })
}

// 切换筛选
const switchFilter = (index) => {
  if (currentFilter.value === index) return
  
  currentFilter.value = index
  pageNum.value = 1
  reviewList.value = []
  loadReviews(true)
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  loadReviews(true)
  loadStatistics()
}



// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 预览图片
const previewImage = (images, current) => {
  uni.previewImage({
    urls: images,
    current: current
  })
}

// 显示回复弹窗
const showReplyModal = (review) => {
  currentReview.value = review
  showReplyForm.value = true
}

// 关闭回复弹窗
const closeReplyModal = () => {
  showReplyForm.value = false
  currentReview.value = {}
  replyContent.value = ''
}

// 提交回复
const submitReply = async () => {
  if (!replyContent.value.trim()) {
    uni.showToast({
      title: '请输入回复内容',
      icon: 'none'
    })
    return
  }
  
  try {
    const res = await replyToReview(currentReview.value.id, {
      content: replyContent.value.trim()
    })
    
    if (res.code === 200) {
      uni.showToast({
        title: '回复成功',
        icon: 'success'
      })
      
      // 更新本地数据
      const reviewIndex = reviewList.value.findIndex(item => item.id === currentReview.value.id)
      if (reviewIndex > -1) {
        reviewList.value[reviewIndex].reply = {
          content: replyContent.value.trim(),
          createTime: new Date().toISOString()
        }
      }
      
      closeReplyModal()
    }
  } catch (error) {
    console.error('回复失败:', error)
    uni.showToast({
      title: '回复失败',
      icon: 'none'
    })
  }
}
</script>

<style scoped lang="scss">
.reviews-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;

  .stats-header {
    display: flex;
    align-items: center;
    gap: 40rpx;

    .rating-summary {
      flex: 1;

      .rating-score {
        text-align: center;

        .score-number {
          display: block;
          font-size: 80rpx;
          font-weight: bold;
          color: #fff;
          margin-bottom: 16rpx;
          text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        }

        .stars-container {
          display: flex;
          justify-content: center;
          gap: 8rpx;
          margin-bottom: 16rpx;

          .star-icon {
            width: 36rpx;
            height: 36rpx;
            filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
          }
        }

        .total-reviews {
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .rating-chart {
      flex: 1.5;

      .chart-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .star-text {
          width: 80rpx;
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.9);
        }

        .progress-container {
          flex: 1;
          margin: 0 20rpx;

          .progress-track {
            height: 20rpx;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 10rpx;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, #ffd700, #ffb347);
              border-radius: 10rpx;
              transition: width 0.3s ease;
            }
          }
        }

        .count-text {
          width: 60rpx;
          text-align: right;
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }
}

.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 24rpx 20rpx;
  margin-bottom: 20rpx;
  overflow-x: auto;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

  .tab-item {
    flex-shrink: 0;
    padding: 16rpx 28rpx;
    margin-right: 16rpx;
    border-radius: 24rpx;
    background-color: #f8f9fa;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:last-child {
      margin-right: 0;
    }

    text {
      font-size: 26rpx;
      color: #666;
      font-weight: 500;
    }

    .tab-count {
      margin-left: 8rpx;
      font-size: 22rpx;
    }

    &.active {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border-color: #1890ff;
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);

      text {
        color: #fff;
        font-weight: 600;
      }
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.reviews-list {
  height: calc(100vh - 300rpx);
  padding: 0 20rpx;
}

.review-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;

    .user-section {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        border: 3rpx solid #f0f0f0;
      }

      .user-info {
        .user-name {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 12rpx;
        }

        .review-rating {
          display: flex;
          gap: 6rpx;

          .rating-star {
            width: 28rpx;
            height: 28rpx;
          }
        }
      }
    }

    .review-date {
      font-size: 24rpx;
      color: #999;
      margin-top: 8rpx;
    }
  }

  .review-body {
    margin-bottom: 24rpx;

    .review-text {
      font-size: 30rpx;
      color: #333;
      line-height: 1.7;
      margin-bottom: 20rpx;
    }

    .review-gallery {
      display: flex;
      gap: 16rpx;
      flex-wrap: wrap;

      .gallery-image {
        width: 180rpx;
        height: 180rpx;
        border-radius: 16rpx;
        border: 1rpx solid #f0f0f0;
      }
    }
  }

  .service-tag {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 20rpx;
    background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    border-left: 4rpx solid #1890ff;

    .service-label {
      font-size: 28rpx;
      color: #1890ff;
      font-weight: 500;
    }

    .service-date {
      font-size: 24rpx;
      color: #666;
    }
  }

  .consultant-reply {
    display: flex;
    background-color: #fafafa;
    border-radius: 20rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    border-left: 4rpx solid #52c41a;

    .reply-avatar {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #52c41a;
      }
    }

    .reply-content {
      flex: 1;

      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;

        .reply-title {
          font-size: 26rpx;
          color: #52c41a;
          font-weight: 600;
        }

        .reply-time {
          font-size: 22rpx;
          color: #999;
        }
      }

      .reply-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }
  }

  .reply-action {
    text-align: center;

    .reply-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: #fff;
      border: none;
      border-radius: 24rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);

      image {
        width: 28rpx;
        height: 28rpx;
        filter: brightness(0) invert(1);
      }

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
      }
    }
  }
}



.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background-color: #fff;
  border-radius: 24rpx;
  margin: 0 20rpx;

  image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
  }

  text {
    color: #999;
    font-size: 30rpx;
    font-weight: 500;
  }
}

.reply-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4rpx);

  .modal-content {
    width: 640rpx;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #333;
      }

      .modal-close {
        font-size: 44rpx;
        color: #999;
        width: 44rpx;
        height: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;

        &:active {
          background-color: #f5f5f5;
        }
      }
    }

    .modal-body {
      padding: 32rpx;

      .original-review {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        border-left: 4rpx solid #1890ff;

        .review-label {
          display: block;
          font-size: 26rpx;
          color: #1890ff;
          font-weight: 600;
          margin-bottom: 12rpx;
        }

        .review-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.7;
        }
      }

      .reply-input {
        width: 100%;
        min-height: 240rpx;
        padding: 24rpx;
        border: 2rpx solid #e9ecef;
        border-radius: 16rpx;
        font-size: 30rpx;
        color: #333;
        line-height: 1.6;
        box-sizing: border-box;
        transition: border-color 0.3s ease;

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 24rpx;
      padding: 32rpx;
      border-top: 1rpx solid #f0f0f0;

      .btn-cancel,
      .btn-confirm {
        flex: 1;
        padding: 28rpx 0;
        border-radius: 16rpx;
        font-size: 30rpx;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
      }

      .btn-cancel {
        background-color: #f8f9fa;
        color: #666;

        &:active {
          background-color: #e9ecef;
        }
      }

      .btn-confirm {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
        }

        &:disabled {
          background: #d9d9d9;
          color: #999;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }
}
</style>
