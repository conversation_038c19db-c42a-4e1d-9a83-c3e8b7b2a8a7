<template>
  <view class="test-page">
    <view class="header">
      <text class="title">小智宝视频插件测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">插件状态</text>
        <view class="status-info">
          <text class="status-item">插件版本: 1.0.11</text>
          <text class="status-item">Provider: wx5474241b2a65b906</text>
          <text class="status-item">状态: {{ pluginStatus }}</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试视频</text>
        <view class="video-container">
          <t-video
            id="test-video"
            :src="testVideoUrl"
            :poster="testPoster"
            :title="testTitle"
            :show_center_play_btn="true"
            :show_play_btn="true"
            :show_fullscreen_btn="true"
            :show_progress="true"
            :controls="true"
            :autoplay="false"
            :loop="false"
            style="width:100%;height:400rpx;"
            @Play="onPlay"
            @Pause="onPause"
            @Ended="onEnded"
            @Timeupdate="onTimeUpdate"
            @Error="onError"
            @videoloaded="onVideoLoaded"
            @videosuccess="onVideoSuccess"
            @videofailed="onVideoFailed"
          ></t-video>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">事件日志</text>
        <scroll-view scroll-y class="log-container">
          <view 
            v-for="(log, index) in eventLogs" 
            :key="index" 
            class="log-item"
          >
            <text class="log-time">{{ log.time }}</text>
            <text class="log-message">{{ log.message }}</text>
          </view>
        </scroll-view>
      </view>
      
      <view class="section">
        <text class="section-title">控制按钮</text>
        <view class="control-buttons">
          <button class="control-btn" @click="playVideo">播放</button>
          <button class="control-btn" @click="pauseVideo">暂停</button>
          <button class="control-btn" @click="clearLogs">清空日志</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const pluginStatus = ref('初始化中...')
const eventLogs = ref([])
const testVideoUrl = ref('https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4')
const testPoster = ref('')
const testTitle = ref('小智宝视频插件测试')

// 视频上下文
let videoContext = null

// 添加日志
const addLog = (message) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  eventLogs.value.unshift({
    time,
    message
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 视频事件处理
const onPlay = () => {
  addLog('视频开始播放')
}

const onPause = () => {
  addLog('视频暂停')
}

const onEnded = () => {
  addLog('视频播放结束')
}

const onTimeUpdate = (e) => {
  const currentTime = e.detail.currentTime || 0
  const duration = e.detail.duration || 0
  addLog(`时间更新: ${currentTime.toFixed(1)}s / ${duration.toFixed(1)}s`)
}

const onError = (e) => {
  addLog(`视频错误: ${JSON.stringify(e.detail)}`)
}

// 小智宝插件特有事件
const onVideoLoaded = () => {
  addLog('插件回调: 视频已可播放')
  pluginStatus.value = '视频已加载'
  
  // 获取视频上下文
  try {
    const pages = uni.getCurrentPages()
    const currentPage = pages[pages.length - 1]
    videoContext = currentPage.selectComponent('#test-video')
    addLog('视频上下文获取成功')
  } catch (error) {
    addLog(`视频上下文获取失败: ${error.message}`)
  }
}

const onVideoSuccess = () => {
  addLog('插件回调: 视频成功')
  pluginStatus.value = '插件工作正常'
}

const onVideoFailed = (e) => {
  addLog(`插件回调: 视频失败 - ${JSON.stringify(e.detail)}`)
  pluginStatus.value = '插件加载失败'
  
  if (e.detail && e.detail.ret) {
    let errorMessage = ''
    switch (e.detail.ret) {
      case 6:
        errorMessage = '小程序appid未注册'
        break
      case 12:
        errorMessage = '用户状态异常'
        break
      case 13:
        errorMessage = '视频链接异常'
        break
      case 9:
        errorMessage = '用户时长不够'
        break
      case 8:
        errorMessage = '审核不通过'
        break
      default:
        errorMessage = `未知错误(错误码:${e.detail.ret})`
    }
    addLog(`错误详情: ${errorMessage}`)
  }
}

// 控制方法
const playVideo = () => {
  if (videoContext) {
    videoContext.play()
    addLog('手动播放视频')
  } else {
    addLog('视频上下文未初始化')
  }
}

const pauseVideo = () => {
  if (videoContext) {
    videoContext.pause()
    addLog('手动暂停视频')
  } else {
    addLog('视频上下文未初始化')
  }
}

const clearLogs = () => {
  eventLogs.value = []
  addLog('日志已清空')
}

// 生命周期
onMounted(() => {
  addLog('页面加载完成')
  pluginStatus.value = '等待视频加载...'
})
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }
}

.status-info {
  .status-item {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
    padding: 12rpx 16rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
  }
}

.video-container {
  background-color: #000;
  border-radius: 12rpx;
  overflow: hidden;
}

.log-container {
  height: 400rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 16rpx;
  
  .log-item {
    display: flex;
    margin-bottom: 12rpx;
    font-size: 24rpx;
    
    .log-time {
      color: #999;
      margin-right: 16rpx;
      min-width: 120rpx;
    }
    
    .log-message {
      color: #333;
      flex: 1;
    }
  }
}

.control-buttons {
  display: flex;
  gap: 16rpx;
  
  .control-btn {
    flex: 1;
    padding: 24rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 28rpx;
    font-weight: 600;
  }
}
</style>
