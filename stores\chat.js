import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/utils/request'
import { 
  getConversationList, 
  createConversation, 
  getMessageList, 
  sendMessage as apiSendMessage, 
  withdrawMessage as apiWithdrawMessage, 
  markMessageAsRead as apiMarkMessageAsRead,
  markAllMessagesAsRead as apiMarkAllMessagesAsRead,
  getUnreadCount as apiGetUnreadCount,
  uploadImage,
  uploadFile
} from '@/api/message'

export const useChatStore = defineStore('chat', () => {
  // 会话列表
  const conversationList = ref([])
  // 当前会话
  const currentConversation = ref(null)
  // 当前会话的消息列表
  const messageList = ref([])
  // 未读消息总数
  const unreadTotal = ref(0)
  // WebSocket连接状态
  const wsConnected = ref(false)
  
  // 心跳定时器
  let heartbeatTimer = null
  // 重连定时器
  let reconnectTimer = null
  // 重连尝试次数
  let reconnectAttempts = 0
  // 最大重连次数
  const MAX_RECONNECT_ATTEMPTS = 5
  // 心跳间隔（毫秒）
  const HEARTBEAT_INTERVAL = 15000 // 15秒
  // 重连间隔（毫秒）
  const RECONNECT_INTERVAL = 5000 // 5秒
  // 当前用户ID
  let currentUserId = null

  // 获取会话列表
  const getConversations = async (isConsultant = false) => {
    try {
      console.log('获取会话列表，咨询师模式:', isConsultant)
      const response = await getConversationList(isConsultant)
      console.log('会话列表API响应:', response)

      const data = response.data || response.rows || []
      console.log('处理后的会话数据:', data)

      conversationList.value = data
      return data
    } catch (error) {
      console.error('获取会话列表失败', error)
      return []
    }
  }

  // 创建新会话
  const createNewConversation = async (consultantId) => {
    try {
      const { data } = await createConversation(consultantId)
      if (data) {
        // 如果创建成功，刷新会话列表
        await getConversations()
        return data
      }
    } catch (error) {
      console.error('创建会话失败', error)
    }
    return null
  }

  // 获取会话消息
  const getMessages = async (conversationId, page = 1, pageSize = 20) => {
    try {
      const data = await getMessageList(conversationId, page, pageSize);
      if (data && data.rows) {
        // 只更新store中的消息列表，但返回完整响应
        messageList.value = data.rows || [];
        return data;
      }
      return null;
    } catch (error) {
      console.error('获取消息列表失败', error);
      return null;
    }
  }

  // 发送消息
  const sendMessage = async (message) => {
    try {
      const { data } = await apiSendMessage(message)
      if (data) {
        messageList.value.push(data)
      }
      return data
    } catch (error) {
      console.error('发送消息失败', error)
      return null
    }
  }

  // 撤回消息
  const withdrawMessage = async (messageId) => {
    try {
      const { data } = await apiWithdrawMessage(messageId)
      if (data) {
        // 更新消息状态为已撤回
        const index = messageList.value.findIndex(msg => msg.messageId === messageId)
        if (index !== -1) {
          messageList.value[index].isWithdrawn = '1'
        }
      }
      return data
    } catch (error) {
      console.error('撤回消息失败', error)
      return false
    }
  }

  // 标记消息为已读
  const markMessageAsRead = async (messageId) => {
    try {
      const { data } = await apiMarkMessageAsRead(messageId)
      return data
    } catch (error) {
      console.error('标记消息已读失败', error)
      return false
    }
  }

  // 标记会话所有消息为已读
  const markAllMessagesAsRead = async (conversationId, isUser) => {
    try {
      const { data } = await apiMarkAllMessagesAsRead(conversationId, isUser)
      // 更新会话列表中的未读数
      if (data) {
        const index = conversationList.value.findIndex(conv => conv.conversationId === conversationId)
        if (index !== -1) {
          if (isUser) {
            conversationList.value[index].userUnreadCount = 0
          } else {
            conversationList.value[index].consultantUnreadCount = 0
          }
        }
      }
      return data
    } catch (error) {
      console.error('标记全部已读失败', error)
      return false
    }
  }

  // 获取未读消息数量
  const getUnreadCount = async () => {
    try {
      const { data } = await apiGetUnreadCount()
      unreadTotal.value = data || 0
      return data
    } catch (error) {
      console.error('获取未读数量失败', error)
      return 0
    }
  }

  // 上传图片
  const uploadImageFile = async (file) => {
    try {
      const { data } = await uploadImage(file)
      return data
    } catch (error) {
      console.error('上传图片失败', error)
      return null
    }
  }

  // 上传文件
  const uploadFileData = async (file) => {
    try {
      const { data } = await uploadFile(file)
      return data
    } catch (error) {
      console.error('上传文件失败', error)
      return null
    }
  }

  // 初始化WebSocket连接
  const initWebSocket = (userId) => {
    if (!userId || userId === 'undefined' || userId === 'null') {
      console.error('初始化WebSocket失败: 用户ID无效:', userId, '类型:', typeof userId);
      return;
    }

    console.log('初始化WebSocket连接,用户ID:', userId, '类型:', typeof userId);
    
    // 保存当前用户ID，用于重连
    currentUserId = userId;

    // 获取WebSocket地址
    // 这里直接使用固定地址,方便调试
    // const wsUrl = `wss://xhxlzx.cn:8081/websocket/${userId}`;
    const wsUrl = `ws://localhost:8080/websocket/${userId}`;
    console.log('WebSocket连接地址:', wsUrl);
    
    // 关闭之前的连接
    closeWebSocket();
    
    // 创建WebSocket连接
    try {
      const socketTask = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('WebSocket连接请求已发送');
        },
        fail: (error) => {
          console.error('WebSocket连接请求失败', error);
          wsConnected.value = false;
          scheduleReconnect();
        }
      });
      
      console.log('WebSocket对象创建成功:', socketTask);
      uni.webSocketTask = socketTask;
    } catch (error) {
      console.error('创建WebSocket连接时出错:', error);
      return null;
    }
    
    // 监听连接打开
    uni.onSocketOpen((res) => {
      console.log('WebSocket连接已打开', res);
      wsConnected.value = true;
      reconnectAttempts = 0;
      startHeartbeat();
    });
    
    // 监听连接错误
    uni.onSocketError((res) => {
      console.error('WebSocket连接错误', res);
      wsConnected.value = false;
      scheduleReconnect();
    });
    
    // 监听连接关闭
    uni.onSocketClose((res) => {
      console.log('WebSocket连接已关闭', res);
      wsConnected.value = false;
      stopHeartbeat();
      scheduleReconnect();
    });
    
    // 监听消息接收
    uni.onSocketMessage((res) => {
      try {
        console.log('WebSocket原始消息:', res.data);
        const message = JSON.parse(res.data);
        console.log('WebSocket解析后消息:', message);

        // 处理心跳响应
        if (message.type === 'heartbeat' || message.type === 'heartbeat_check') {
          if (message.type === 'heartbeat_check') {
            sendHeartbeat();
          }
          return;
        }

        // 处理连接响应
        if (message.type === 'connect') {
          console.log('连接成功:', message);
          return;
        }

        // 添加详细的消息类型日志
        console.log('消息类型判断 - messageType:', message.messageType, 'type:', message.type);

        // 根据消息类型处理
        if (message.messageType === 'system') {
          // 系统消息，可能是已读通知或撤回通知
          handleSystemMessage(message);
          console.log('收到WebSocket系统消息:', message);
        } else if (message.type === 'chat' || message.messageType === '0' || message.messageType === '1' || message.messageType === '2') {
          // 普通聊天消息 - 扩展判断条件
          console.log('准备处理聊天消息:', message);
          console.log('当前会话信息:', currentConversation.value);
          console.log('当前用户ID:', currentUserId);
          handleChatMessage(message);
          console.log('处理完聊天消息:', message);
        } else {
          console.log('未知消息类型，原始消息:', message);
        }
      } catch (error) {
        console.error('处理WebSocket消息失败', error, '原始消息:', res.data);
      }
    });
  }
  
  // 启动心跳
  const startHeartbeat = () => {
    console.log('启动心跳检测')
    stopHeartbeat()
    
    // 立即发送一次心跳
    sendHeartbeat()
    
    // 设置定时发送心跳
    heartbeatTimer = setInterval(() => {
      sendHeartbeat()
    }, HEARTBEAT_INTERVAL)
  }
  
  // 停止心跳
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }
  
  // 发送心跳
  const sendHeartbeat = () => {
    if (wsConnected.value) {
      // console.log('发送心跳')
      sendWebSocketMessage('heartbeat', { timestamp: Date.now() })
    }
  }
  
  // 安排重连
  const scheduleReconnect = () => {
    // 如果已经达到最大重连次数，放弃重连
    if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      console.log('达到最大重连次数，放弃重连')
      return
    }
    
    // 如果已经有重连计划，不再重复安排
    if (reconnectTimer) {
      return
    }
    
    // 安排重连
    reconnectTimer = setTimeout(() => {
      reconnectTimer = null
      reconnectAttempts++
      
      console.log(`第 ${reconnectAttempts} 次尝试重连...`)
      if (currentUserId) {
        initWebSocket(currentUserId)
      }
    }, RECONNECT_INTERVAL)
  }
  
  // 关闭WebSocket连接
  const closeWebSocket = () => {
    stopHeartbeat()
    
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    if (uni.webSocketTask) {
      try {
        uni.closeSocket()
      } catch (error) {
        console.error('关闭WebSocket连接失败', error)
      }
    }
  }

  // 处理系统消息
  const handleSystemMessage = (message) => {
    try {
      console.log(message.content);
      
      let content;
      if(typeof message.content != 'string') {
        content = JSON.parse(message.content)
      } else {
        content = message.content
      }
      
      if (content.type === 'read_notification') {
        // 对方已读消息的通知
        console.log('对方已读消息', content.messageId)
        // 可以在这里更新UI显示消息已读状态
      } else if (content.type === 'withdraw_notification') {
        // 消息被撤回的通知
        const index = messageList.value.findIndex(msg => msg.messageId === content.messageId)
        if (index !== -1) {
          messageList.value[index].isWithdrawn = '1'
        }
      }
    } catch (error) {
      console.error('处理系统消息失败', error)
    }
  }

  // 处理聊天消息
  const handleChatMessage = (message) => {
    console.log('处理聊天消息,消息ID:', message.messageId, '会话ID:', message.conversationId);
    console.log('消息发送者ID:', message.senderId, '当前用户ID:', currentUserId);
    console.log('消息详细信息:', message);

    // 检查是否是自己通过小程序发送的消息
    // 如果消息有特殊标识表明是从小程序发送的，且发送者是当前用户，则跳过处理
    const isFromCurrentUser = String(message.senderId) === String(currentUserId);
    const isFromMiniProgram = message.source === 'miniprogram' || message.fromClient === true;

    if (isFromCurrentUser && isFromMiniProgram) {
      console.log('这是自己通过小程序发送的消息，跳过处理');
      return;
    }

    // 如果是管理员代发的消息（senderId是咨询师ID但不是从小程序发送），咨询师应该能看到
    if (isFromCurrentUser && !isFromMiniProgram) {
      console.log('这是管理员代发的消息，咨询师需要看到');
    }

    // 直接触发自定义事件,通知页面有新消息到达
    uni.$emit('chat-new-message', message);
    console.log('已触发chat-new-message事件');

    // 如果是当前会话的消息，添加到消息列表
    if (currentConversation.value && String(message.conversationId) === String(currentConversation.value.conversationId)) {
      console.log('消息属于当前会话,添加到消息列表');
      messageList.value.push(message);
    } else {
      console.log('消息不属于当前会话或当前没有活动会话');
      // 如果不是当前会话的消息，增加未读计数
      unreadTotal.value = (unreadTotal.value || 0) + 1;
      console.log('未读消息计数增加，当前未读数:', unreadTotal.value);

      // 触发未读消息更新事件，通知其他页面更新角标
      uni.$emit('unread-count-changed', unreadTotal.value);
    }
    
    // 更新会话列表中的最后消息和未读数
    const index = conversationList.value.findIndex(conv => String(conv.conversationId) === String(message.conversationId));
    if (index !== -1) {
      // 更新最后消息信息
      conversationList.value[index].lastMessage = message.content;
      conversationList.value[index].lastMessageTime = message.sendTime;
      conversationList.value[index].lastSenderId = message.senderId;

      // 更新未读数 - 只有别人发送给自己的消息才增加未读数
      if (!currentConversation.value || String(currentConversation.value.conversationId) !== String(message.conversationId)) {
        // 如果当前用户是消息的接收者，增加未读数
        if (String(message.receiverId) === String(currentUserId)) {
          // 根据当前用户的角色增加对应的未读数
          // 如果当前用户是普通用户，增加用户未读数
          // 如果当前用户是咨询师，增加咨询师未读数
          conversationList.value[index].userUnreadCount = (conversationList.value[index].userUnreadCount || 0) + 1;
        }
      }
    } else {
      // 如果是新会话，刷新会话列表
      console.log('新会话,刷新会话列表');
      getConversations();
    }
  }

  // 发送WebSocket消息
  const sendWebSocketMessage = (type, data) => {
    if (!wsConnected.value) {
      console.error('WebSocket未连接')
      return false
    }
    
    try {
      const message = {
        type,
        ...data
      }
      
      // 通过WebSocket发送消息
      uni.sendSocketMessage({
        data: JSON.stringify(message),
        success: () => {
          // console.log('WebSocket消息发送成功:', type)
          return true
        },
        fail: (error) => {
          console.error('WebSocket消息发送失败:', type, error)
          return false
        }
      })
      
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败', error)
      return false
    }
  }

  // 通过WebSocket发送聊天消息
  const sendChatMessage = (message) => {
    // 将消息添加到当前会话的消息列表
    if (message.conversationId === currentConversation?.conversationId) {
      // 如果用户在聊天页面，消息已经在聊天页面中添加
      // 防止重复添加
    }
    
    return sendWebSocketMessage('chat', message);
  }

  // 通过WebSocket发送已读通知
  const sendReadNotification = (messageId, conversationId, isUser) => {
    return sendWebSocketMessage('read', { messageId, conversationId, isUser })
  }

  // 通过WebSocket发送撤回通知
  const sendWithdrawNotification = (messageId) => {
    return sendWebSocketMessage('withdraw', { messageId })
  }

  return {
    conversationList,
    currentConversation,
    messageList,
    unreadTotal,
    wsConnected,
    getConversationList: getConversations,
    createConversation: createNewConversation,
    getMessageList: getMessages,
    sendMessage,
    withdrawMessage,
    markMessageAsRead,
    markAllMessagesAsRead,
    getUnreadCount,
    uploadImage: uploadImageFile,
    uploadFile: uploadFileData,
    initWebSocket,
    closeWebSocket,
    sendChatMessage,
    sendReadNotification,
    sendWithdrawNotification
  }
})
