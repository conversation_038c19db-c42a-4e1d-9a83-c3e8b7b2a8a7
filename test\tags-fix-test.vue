<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">标签修复测试</text>
      <text class="test-desc">测试字符串格式tags解析和价格区域免费文字隐藏</text>
    </view>

    <!-- 课程测试 - 字符串格式tags -->
    <view class="test-section">
      <text class="section-title">课程 - 字符串格式tags（免费，右侧不显示价格）</text>
      <UniversalListItem :item="courseTestData" type="course" @click="handleItemClick" />
    </view>

    <!-- 冥想测试 - 字符串格式tags -->
    <view class="test-section">
      <text class="section-title">冥想 - 字符串格式tags（免费，右侧不显示价格）</text>
      <UniversalListItem :item="meditationTestData" type="meditation" @click="handleItemClick" />
    </view>

    <!-- 测评测试 - 免费不显示标签 -->
    <view class="test-section">
      <text class="section-title">测评 - 免费（右侧不显示价格，标签显示免费）</text>
      <UniversalListItem :item="assessmentTestData" type="assessment" @click="handleItemClick" />
    </view>

    <!-- 收费课程测试 -->
    <view class="test-section">
      <text class="section-title">课程 - 收费显示标签</text>
      <UniversalListItem :item="paidCourseTestData" type="course" @click="handleItemClick" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 免费课程测试数据 - 字符串格式tags
const courseTestData = ref({
  id: 1,
  type: "course",
  title: "情绪管理基础课程",
  description: "学会基础的情绪管理技巧",
  price: "0.00",
  rating: 4.5,
  course: {
    id: 1,
    title: "情绪管理基础课程",
    price: 0,
    free: true,
    totalDuration: 120,
    tags: '["情绪管理","压力缓解","心理健康"]', // 字符串格式
    studentCount: 1500
  }
})

// 免费冥想测试数据 - 字符串格式tags
const meditationTestData = ref({
  id: 2,
  type: "meditation",
  title: "放松冥想练习",
  description: "帮助您快速放松身心",
  price: "0.00",
  rating: 4.7,
  meditation: {
    id: 2,
    title: "放松冥想练习",
    price: 0,
    free: true,
    duration: 10,
    tags: '["放松","减压","睡眠","冥想"]', // 字符串格式
    playCount: 3200
  }
})

// 免费测评测试数据
const assessmentTestData = ref({
  id: 3,
  type: "assessment",
  title: "情绪状态评估",
  description: "评估您当前的情绪状态",
  price: "0.00",
  rating: 4.3,
  assessment: {
    id: 3,
    name: "情绪状态评估",
    price: 0,
    free: true,
    questionCount: 20,
    duration: "5-8分钟",
    monthTestCount: 800
  }
})

// 收费课程测试数据
const paidCourseTestData = ref({
  id: 4,
  type: "course",
  title: "高级心理咨询技巧",
  description: "专业的心理咨询技巧培训",
  price: "199.00",
  rating: 4.8,
  course: {
    id: 4,
    title: "高级心理咨询技巧",
    price: 199,
    originalPrice: 299,
    free: false,
    totalDuration: 600,
    tags: '["专业技能","心理咨询","高级课程"]', // 字符串格式
    studentCount: 580
  }
})

// 处理点击事件
const handleItemClick = (item) => {
  console.log('点击了项目:', item)
  uni.showToast({
    title: `点击了${item.title}`,
    icon: 'none'
  })
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.test-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;

  .test-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .test-desc {
    font-size: 24rpx;
    color: #666;
  }
}

.test-section {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;

  .section-title {
    display: block;
    padding: 20rpx 30rpx;
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
    background-color: #f5f5f5;
    border-bottom: 1rpx solid #e0e0e0;
  }
}
</style>
