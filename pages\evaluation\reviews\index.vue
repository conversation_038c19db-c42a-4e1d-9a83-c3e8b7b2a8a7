<template>
  <view class="reviews-page">
    <!-- 顶部统计 -->
    <view class="reviews-header">
      <view class="scale-info">
        <text class="scale-name">{{scaleName}}</text>
        <text class="total-reviews">共{{total}}条评价</text>
      </view>
      <view class="rating-summary" v-if="averageRating">
        <text class="avg-rating">{{averageRating}}</text>
        <view class="stars">
          <text class="star" v-for="i in 5" :key="i" :class="{'filled': i <= Math.floor(averageRating)}">★</text>
        </view>
      </view>
    </view>

    <!-- 筛选选项 -->
    <view class="filter-tabs">
      <view 
        class="filter-tab" 
        v-for="(filter, index) in filterOptions" 
        :key="index"
        :class="{'active': currentFilter === filter.value}"
        @click="switchFilter(filter.value)"
      >
        <text>{{filter.label}}</text>
      </view>
    </view>

    <!-- 评价列表 -->
    <scroll-view 
      scroll-y 
      class="reviews-list"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="review-item" v-for="(review, index) in reviewsList" :key="index">
        <view class="review-header">
          <view class="user-info">
            <image class="avatar" :src="review.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <view class="user-details">
              <text class="username">{{review.nickName || '匿名用户'}}</text>
              <text class="review-time">{{formatTime(review.createTime)}}</text>
            </view>
          </view>
          <view class="rating-stars">
            <text class="star" v-for="i in 5" :key="i" :class="{'filled': i <= review.rating}">★</text>
          </view>
        </view>
        <view class="review-content">
          <text>{{review.content}}</text>
        </view>
        <!-- 如果有回复 -->
        <view class="review-reply" v-if="review.reply">
          <view class="reply-header">
            <text class="reply-label">官方回复：</text>
          </view>
          <text class="reply-content">{{review.reply}}</text>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="load-status" v-if="reviewsList.length > 0">
        <text v-if="loading">加载中...</text>
        <text v-else-if="noMore">没有更多了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && reviewsList.length === 0">
        <image src="/static/empty-reviews.png" mode="aspectFit"></image>
        <text>暂无评价</text>
      </view>
    </scroll-view>

    <!-- 底部提交评价按钮 -->
    <view class="submit-review-btn" @click="submitReview" v-if="canSubmitReview">
      <text>写评价</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getScaleReviews } from '@/api/evaluation'
import { formatTime } from '@/utils/index'

// 响应式数据
const scaleId = ref('')
const scaleName = ref('')
const reviewsList = ref([])
const total = ref(0)
const averageRating = ref(0)
const loading = ref(false)
const refreshing = ref(false)
const noMore = ref(false)
const currentFilter = ref('all')
const pageNum = ref(1)
const pageSize = ref(10)
const canSubmitReview = ref(false)

// 筛选选项
const filterOptions = ref([
  { label: '全部', value: 'all' },
  { label: '5星', value: '5' },
  { label: '4星', value: '4' },
  { label: '3星', value: '3' },
  { label: '2星', value: '2' },
  { label: '1星', value: '1' }
])

onLoad((options) => {
  if (options.scaleId) {
    scaleId.value = options.scaleId
  }
  if (options.scaleName) {
    scaleName.value = decodeURIComponent(options.scaleName)
  }
  if (options.canSubmitReview) {
    canSubmitReview.value = options.canSubmitReview === 'true'
  }
  
  loadReviews()
})

// 加载评价数据
const loadReviews = async (isRefresh = false) => {
  if (loading.value) return
  loading.value = true
  
  if (isRefresh) {
    pageNum.value = 1
    noMore.value = false
  }
  
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value
    }
    
    // 添加评分筛选
    if (currentFilter.value !== 'all') {
      params.rating = parseInt(currentFilter.value)
    }
    
    const res = await getScaleReviews(scaleId.value, params)
    if (res.code === 200) {
      const newReviews = res.data || []
      
      if (isRefresh) {
        reviewsList.value = newReviews
      } else {
        reviewsList.value.push(...newReviews)
      }
      
      total.value = res.total || 0
      averageRating.value = res.averageRating || 0
      
      // 检查是否还有更多数据
      if (newReviews.length < pageSize.value) {
        noMore.value = true
      }
    }
  } catch (error) {
    uni.showToast({
      title: '获取评价失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 切换筛选条件
const switchFilter = (filterValue) => {
  if (currentFilter.value === filterValue) return
  currentFilter.value = filterValue
  pageNum.value = 1
  reviewsList.value = []
  noMore.value = false
  loadReviews(true)
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  loadReviews(true)
}

// 加载更多
const loadMore = () => {
  if (noMore.value || loading.value) return
  pageNum.value++
  loadReviews()
}

// 提交评价
const submitReview = () => {
  uni.navigateTo({
    url: `/pages/evaluation/submit-review/index?scaleId=${scaleId.value}&scaleName=${encodeURIComponent(scaleName.value)}`
  })
}
</script>

<style lang="scss" scoped>
.reviews-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.reviews-header {
  background: #fff;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  
  .scale-info {
    flex: 1;
    
    .scale-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .total-reviews {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .rating-summary {
    display: flex;
    align-items: center;
    gap: 12rpx;
    
    .avg-rating {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff6b35;
    }
    
    .stars {
      display: flex;
      gap: 4rpx;
      
      .star {
        font-size: 24rpx;
        color: #ddd;
        
        &.filled {
          color: #ff6b35;
        }
      }
    }
  }
}

.filter-tabs {
  background: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  gap: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .filter-tab {
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    background: #f8f8f8;
    font-size: 26rpx;
    color: #666;
    transition: all 0.3s;
    
    &.active {
      background: #409eff;
      color: #fff;
    }
  }
}

.reviews-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.review-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 20rpx;
      
      .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
      }
      
      .user-details {
        .username {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          display: block;
          margin-bottom: 6rpx;
        }
        
        .review-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .rating-stars {
      display: flex;
      gap: 2rpx;
      
      .star {
        font-size: 20rpx;
        color: #ddd;
        
        &.filled {
          color: #ff6b35;
        }
      }
    }
  }
  
  .review-content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    margin-bottom: 20rpx;
  }
  
  .review-reply {
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 20rpx;
    
    .reply-header {
      margin-bottom: 10rpx;
      
      .reply-label {
        font-size: 24rpx;
        color: #409eff;
        font-weight: 500;
      }
    }
    
    .reply-content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
    }
  }
}

.load-status {
  text-align: center;
  padding: 30rpx;
  font-size: 26rpx;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}

.submit-review-btn {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  background: #409eff;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
}
</style>
