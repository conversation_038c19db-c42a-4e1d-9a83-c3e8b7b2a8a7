<template>
  <view class="test-page">
    <view class="header">
      <text class="title">支付系统测试</text>
    </view>

    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试完整的支付流程：</text>
          <text>1. 咨询订单：支付成功后跳转到基本信息表单</text>
          <text>2. 其他订单：支付成功后直接可以使用</text>
          <text>3. 支付失败处理和用户取消处理</text>
          <text>4. 订单状态更新和页面跳转</text>
        </view>
      </view>

      <view class="section">
        <text class="section-title">支付流程测试</text>
        <view class="test-buttons">
          <button class="test-btn consultation" @click="testConsultationPayment">
            测试咨询支付
          </button>
          <button class="test-btn course" @click="testCoursePayment">
            测试课程支付
          </button>
          <button class="test-btn meditation" @click="testMeditationPayment">
            测试冥想支付
          </button>
          <button class="test-btn assessment" @click="testAssessmentPayment">
            测试测评支付
          </button>
          <button class="test-btn secondary" @click="testFavoriteSystem">
            测试收藏系统
          </button>
        </view>
      </view>

      <view class="section">
        <text class="section-title">订单管理测试</text>
        <view class="test-buttons">
          <button class="test-btn secondary" @click="goToOrders">
            我的订单
          </button>
          <button class="test-btn secondary" @click="goToConsultationOrders">
            咨询订单
          </button>
          <button class="test-btn secondary" @click="testPaymentSuccess">
            支付成功页面
          </button>
          <button class="test-btn secondary" @click="testBasicInfo">
            基本信息表单
          </button>
        </view>
      </view>

      <view class="section">
        <text class="section-title">支付功能特性</text>
        <view class="features">
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>真实微信支付集成</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>支付状态实时查询</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>支付失败友好提示</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>用户取消支付处理</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>咨询订单特殊流程</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>订单状态自动更新</text>
          </view>
        </view>
      </view>

      <view class="section">
        <text class="section-title">支付流程说明</text>
        <view class="flow-steps">
          <view class="flow-step">
            <view class="step-number">1</view>
            <view class="step-content">
              <text class="step-title">创建订单</text>
              <text class="step-desc">调用后端API创建订单，获取订单号</text>
            </view>
          </view>
          <view class="flow-step">
            <view class="step-number">2</view>
            <view class="step-content">
              <text class="step-title">打开支付弹框</text>
              <text class="step-desc">显示商品信息和支付方式选择</text>
            </view>
          </view>
          <view class="flow-step">
            <view class="step-number">3</view>
            <view class="step-content">
              <text class="step-title">调用微信支付</text>
              <text class="step-desc">获取支付参数，调用微信支付API</text>
            </view>
          </view>
          <view class="flow-step">
            <view class="step-number">4</view>
            <view class="step-content">
              <text class="step-title">处理支付结果</text>
              <text class="step-desc">成功：更新状态，跳转页面；失败：显示错误</text>
            </view>
          </view>
          <view class="flow-step">
            <view class="step-number">5</view>
            <view class="step-content">
              <text class="step-title">后续流程</text>
              <text class="step-desc">咨询：填写信息；其他：直接使用</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付弹框 -->
    <PaymentModal ref="paymentModal" :order-info="orderInfo" @close="onPaymentClose" @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail" />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'
import {
  createOrder,
  requestWxPayment,
  showPaymentResult,
  buyProduct
} from '@/utils/payment.js'
import {
  ORDER_TYPES,
  getOrderTypeName,
  getOrderList,
  getOrderDetail,
  cancelOrder,
  requestRefund
} from '@/api/payment.js'

// 响应式数据
const paymentModal = ref(null)
const orderInfo = ref({})

// 测试咨询支付
const testConsultationPayment = () => {
  orderInfo.value = {
    type: ORDER_TYPES.CONSULTANT,
    product: {
      id: 123,
      title: '张医生 - 个人心理咨询',
      description: '2024-07-15 14:00 (50分钟)',
      price: 299,
      originalPrice: 399,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/consultant/default-avatar.png'
    },
    couponDiscount: 50,
    pointsDiscount: 0
  }
  paymentModal.value?.open(orderInfo.value)
}

// 测试课程支付
const testCoursePayment = () => {
  orderInfo.value = {
    type: ORDER_TYPES.COURSE,
    product: {
      id: 456,
      title: '情绪管理入门课程',
      description: '学会识别和调节自己的情绪，提升心理健康水平',
      price: 199,
      originalPrice: 299,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
    },
    couponDiscount: 50,
    pointsDiscount: 20
  }
  paymentModal.value?.open(orderInfo.value)
}

// 测试冥想支付
const testMeditationPayment = () => {
  orderInfo.value = {
    type: ORDER_TYPES.MEDITATION,
    product: {
      id: 789,
      title: '焦虑缓解冥想',
      description: '快速缓解焦虑情绪，恢复内心平静',
      price: 19.9,
      originalPrice: 29.9,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
    },
    couponDiscount: 10,
    pointsDiscount: 0
  }
  paymentModal.value?.open(orderInfo.value)
}

// 测试测评支付
const testAssessmentPayment = () => {
  orderInfo.value = {
    type: ORDER_TYPES.ASSESSMENT,
    product: {
      id: 101,
      title: '心理健康测评',
      description: '全面评估您的心理健康状况',
      price: 39,
      originalPrice: 49,
      coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
    },
    couponDiscount: 10,
    pointsDiscount: 0
  }
  paymentModal.value?.open(orderInfo.value)
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 根据订单类型跳转到不同页面
  const productInfo = encodeURIComponent(JSON.stringify(orderInfo.value.product))

  if (orderInfo.value.type === 'consultation') {
    // 咨询订单跳转到咨询登记表
    uni.navigateTo({
      url: `/pages/consultation/registration-form/index?orderId=${paymentData.orderId}&showSuccess=true`
    })
  } else {
    // 其他订单跳转到支付成功页面
    uni.navigateTo({
      url: `/pages/payment/success/index?orderNo=${paymentData.orderNo}&orderId=${paymentData.orderId}&amount=${paymentData.amount}&method=${paymentData.method}&type=${orderInfo.value.type}&productInfo=${productInfo}`
    })
  }
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 跳转到订单页面
const goToOrders = () => {
  uni.navigateTo({
    url: '/pages/my/orders/index'
  })
}

const goToConsultationOrders = () => {
  uni.navigateTo({
    url: '/pages/consultation/my-consultations/index'
  })
}

// 测试支付成功页面
const testPaymentSuccess = () => {
  const productInfo = encodeURIComponent(JSON.stringify({
    id: 1,
    title: '情绪管理入门课程',
    description: '学会识别和调节自己的情绪，提升心理健康水平',
    coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
  }))

  uni.navigateTo({
    url: `/pages/payment/success/index?orderNo=TEST${Date.now()}&orderId=test_${Date.now()}&amount=199.00&method=wechat&type=course&productInfo=${productInfo}`
  })
}

// 测试基本信息表单
const testBasicInfo = () => {
  uni.navigateTo({
    url: `/pages/consultation/basic-info/index?orderId=test_${Date.now()}`
  })
}

// 测试收藏系统
const testFavoriteSystem = () => {
  uni.navigateTo({
    url: '/pages/my/favorites/index'
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;

  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;

  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;

    &.consultation {
      background: linear-gradient(135deg, #667eea, #764ba2);
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
    }

    &.course {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
    }

    &.meditation {
      background: linear-gradient(135deg, #ff6b35, #ff8a50);
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
    }

    &.assessment {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
    }

    &.secondary {
      background: linear-gradient(135deg, #999, #777);
      box-shadow: 0 4rpx 12rpx rgba(153, 153, 153, 0.3);
    }
  }
}

.features {
  .feature-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;

    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.flow-steps {
  .flow-step {
    display: flex;
    align-items: flex-start;
    gap: 20rpx;
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .step-number {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 600;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;

      .step-title {
        display: block;
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .step-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}
</style>
