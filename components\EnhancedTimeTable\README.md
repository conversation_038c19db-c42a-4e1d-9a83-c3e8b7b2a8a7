# EnhancedTimeTable 增强版时间表组件

这是一个功能完整的时间表组件，封装了预约页面中的所有时间选择逻辑，支持时间区间选择、冲突检测、状态管理等功能。

## 功能特性

- ✅ 自动获取时间数据
- ✅ 日期选择器
- ✅ 时间区间选择（1小时为单位）
- ✅ 时间冲突检测
- ✅ 时间状态管理（可预约、已约满、已过期）
- ✅ 可视化时间区间显示
- ✅ 操作按钮（清空选择、确认选择）
- ✅ 完整的事件回调

## 使用方法

### 基本用法

```vue
<template>
  <EnhancedTimeTable
    :showDateSelector="true"
    :dayRange="6"
    :showMessage="true"
    :showActionButtons="true"
    @timeChange="handleTimeChange"
    @intervalChange="handleIntervalChange"
    @confirm="handleConfirm"
    @dateChange="handleDateChange"
  />
</template>

<script setup>
import EnhancedTimeTable from "@/components/EnhancedTimeTable/EnhancedTimeTable.vue";

const handleTimeChange = (selectedTimes) => {
  console.log('选择的时间点:', selectedTimes);
};

const handleIntervalChange = (intervals) => {
  console.log('时间区间:', intervals);
};

const handleConfirm = (data) => {
  console.log('确认选择:', data);
  // data 包含: { selectTime, timeIntervals, timeParams }
};

const handleDateChange = (dateItem, index) => {
  console.log('选择的日期:', dateItem, index);
};
</script>
```

### 高级用法

```vue
<template>
  <EnhancedTimeTable
    ref="timeTableRef"
    :showDateSelector="true"
    :dayRange="7"
    :showMessage="true"
    :showActionButtons="true"
    message="自定义提示信息"
    @timeChange="handleTimeChange"
    @intervalChange="handleIntervalChange"
    @confirm="handleConfirm"
  />
</template>

<script setup>
import { ref } from 'vue';
import EnhancedTimeTable from "@/components/EnhancedTimeTable/EnhancedTimeTable.vue";

const timeTableRef = ref(null);

// 获取选择的时间
const getSelectedTimes = () => {
  return timeTableRef.value?.getSelectedTimes() || [];
};

// 获取时间区间
const getTimeIntervals = () => {
  return timeTableRef.value?.getTimeIntervals() || [];
};

// 清空选择
const clearSelection = () => {
  timeTableRef.value?.clearSelection();
};

// 刷新时间列表
const refreshTimeList = () => {
  timeTableRef.value?.refreshTimeList();
};

// 手动确认选择
const confirmSelection = () => {
  timeTableRef.value?.confirmSelection();
};
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showDateSelector | Boolean | true | 是否显示日期选择器 |
| dayRange | Number | 6 | 获取时间数据的天数范围 |
| showMessage | Boolean | true | 是否显示提示信息 |
| message | String | '请选择咨询时间...' | 提示信息内容 |
| showActionButtons | Boolean | true | 是否显示操作按钮 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| timeChange | selectedTimes: Array | 选择的时间点发生变化 |
| intervalChange | intervals: Array | 时间区间发生变化 |
| confirm | data: Object | 确认选择时触发 |
| dateChange | dateItem: Object, index: Number | 日期选择发生变化 |

### confirm 事件数据结构

```javascript
{
  selectTime: ['2024-01-01 09:00', '2024-01-01 09:15', ...], // 选择的时间点数组
  timeIntervals: [
    {
      startTime: '2024-01-01 09:00',
      timePoints: ['2024-01-01 09:00', '2024-01-01 09:15', ...],
      duration: 60
    }
  ], // 时间区间数组
  timeParams: [
    { date: '2024-01-01', time: '09:00' },
    { date: '2024-01-01', time: '09:15' },
    ...
  ] // 格式化的时间参数
}
```

## Methods 方法

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getSelectedTimes | - | Array | 获取当前选择的时间点 |
| getTimeIntervals | - | Array | 获取当前的时间区间 |
| clearSelection | - | - | 清空所有选择 |
| refreshTimeList | - | - | 刷新时间列表数据 |
| confirmSelection | - | - | 手动触发确认选择 |

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量定制：

```scss
.enhanced-time-table {
  // 主色调
  --primary-color: #52b5f9;
  --primary-hover: #1890ff;
  
  // 禁用状态
  --disabled-bg: #f5f5f5;
  --disabled-color: #999;
  
  // 边框颜色
  --border-color: #eee;
  --active-border: #5fbaf9;
}
```

## 注意事项

1. 组件依赖 `@/api/timeSlot.js` 中的 `getFormattedTimeSlots` 方法获取时间数据
2. 时间选择以1小时为单位，每15分钟为一个时间点
3. 组件会自动处理时间冲突检测和状态更新
4. 切换日期时会自动清空已选择的时间
5. 组件内部使用 uni-app 的 Toast 显示提示信息

## 与原有 TimeTable 组件的区别

| 特性 | 原有组件 | 增强版组件 |
|------|----------|------------|
| 数据获取 | 需要外部传入 | 内部自动获取 |
| 时间选择逻辑 | 需要外部实现 | 内部完整实现 |
| 冲突检测 | 需要外部实现 | 内部自动处理 |
| 状态管理 | 需要外部管理 | 内部自动管理 |
| 操作按钮 | 无 | 内置清空和确认按钮 |
| 使用复杂度 | 高 | 低 |

## 迁移指南

如果要从原有的预约页面迁移到使用增强版组件：

1. 移除原有的时间数据获取逻辑
2. 移除原有的时间选择处理逻辑
3. 移除原有的状态管理代码
4. 使用 `EnhancedTimeTable` 组件替换原有的 `TimeTable` 组件
5. 监听 `confirm` 事件处理确认选择的逻辑

详细的迁移示例请参考 `pages/appointment/enhanced.vue` 文件。
