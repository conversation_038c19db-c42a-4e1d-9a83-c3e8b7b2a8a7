# 咨询师订单管理功能更新

## 更新概述

根据后端提供的正确的咨询师端接口，完全重构了咨询师订单管理页面，包括：
1. 更新API接口调用
2. 添加价格和日期筛选功能
3. 更新订单状态管理
4. 添加订单操作功能（确认、拒绝等）

## 后端接口更新

### 主要接口
- **订单列表**: `/miniapp/consultant/orderManage/myOrders`
- **筛选接口**: `/miniapp/consultant/orderManage/filterByPriceAndDate`
- **订单详情**: `/miniapp/consultant/orderManage/{orderId}`
- **确认订单**: `/miniapp/consultant/orderManage/{orderId}/confirm`
- **拒绝订单**: `/miniapp/consultant/orderManage/{orderId}/reject`
- **状态统计**: `/miniapp/consultant/orderManage/statusCount`

### 订单状态定义
根据后端接口，订单状态包括：
- **待支付**: 用户下单但未支付
- **已支付**: 用户已支付，等待咨询师确认（显示为"待确认"）
- **待咨询**: 咨询师已确认，等待咨询开始
- **咨询中**: 正在进行咨询
- **已完成**: 咨询已完成
- **已取消**: 订单已取消

### 筛选接口参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| minPrice | BigDecimal | 否 | 最小价格（元） |
| maxPrice | BigDecimal | 否 | 最大价格（元） |
| startDate | String | 否 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 否 | 结束日期，格式：yyyy-MM-dd |
| status | String | 否 | 订单状态 |
| pageNum | Integer | 否 | 页码，从1开始 |
| pageSize | Integer | 否 | 每页数量 |
| orderBy | String | 否 | 排序字段 |
| sortOrder | String | 否 | 排序方式，asc或desc |

## 前端修改内容

### 1. API接口文件更新 (`api/consultant-app.js`)

#### 更新的接口函数：

```javascript
// 获取咨询师的订单列表
export function getConsultantOrders(params = {}) {
  return request({
    url: '/miniapp/consultant/orderManage/myOrders',
    method: 'GET',
    data: params
  })
}

// 根据价格和日期筛选咨询师订单
export function filterConsultantOrdersByPriceAndDate(params = {}) {
  return request({
    url: '/miniapp/consultant/orderManage/filterByPriceAndDate',
    method: 'GET',
    data: params
  })
}

// 确认订单（咨询师确认接单）
export function confirmConsultantOrder(orderId) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/confirm`,
    method: 'POST'
  })
}

// 拒绝订单
export function rejectConsultantOrder(orderId, rejectReason) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/reject`,
    method: 'POST',
    data: { rejectReason }
  })
}

// 获取不同状态的订单数量
export function getOrderStatusCount() {
  return request({
    url: '/miniapp/consultant/orderManage/statusCount',
    method: 'GET'
  })
}
```

### 2. 订单管理页面更新 (`pages/consultation-order/index.vue`)

#### 新增UI组件
- **筛选按钮**: 在搜索栏右侧添加了筛选按钮
- **筛选弹窗**: 使用 `uni-popup` 组件实现底部弹出的筛选面板

#### 筛选功能包含
1. **价格范围筛选**: 最低价格和最高价格输入框
2. **日期范围筛选**: 开始日期和结束日期选择器
3. **排序方式选择**: 支持按创建时间、支付金额、预约时间、支付时间、更新时间排序
4. **排序顺序选择**: 升序或降序

#### 更新的状态标签
```javascript
// 状态标签 - 匹配后端状态定义
const statusTabs = ref([
  { name: '全部', value: '', count: 0 },
  { name: '待确认', value: '已支付', count: 0 },
  { name: '待咨询', value: '待咨询', count: 0 },
  { name: '咨询中', value: '咨询中', count: 0 },
  { name: '已完成', value: '已完成', count: 0 },
  { name: '已取消', value: '已取消', count: 0 }
])
```

#### 新增响应式数据
```javascript
// 筛选相关
const filterPopup = ref(null)
const isFiltering = ref(false)
const filterForm = ref({
  minPrice: '',
  maxPrice: '',
  startDate: '',
  endDate: '',
  orderBy: 'createTime',
  sortOrder: 'desc'
})

// 排序选项
const sortOptions = ref([
  { label: '创建时间', value: 'createTime' },
  { label: '支付金额', value: 'paymentAmount' },
  { label: '预约时间', value: 'scheduledTime' },
  { label: '支付时间', value: 'paymentTime' },
  { label: '更新时间', value: 'updateTime' }
])
```

#### 新增订单操作方法
- `confirmOrder()`: 确认订单（咨询师确认接单）
- `rejectOrder()`: 拒绝订单（带拒绝原因）
- `startConsultation()`: 开始咨询
- `loadStatusCounts()`: 加载状态标签计数

#### 新增筛选方法
- `showFilterModal()`: 显示筛选弹窗
- `closeFilterModal()`: 关闭筛选弹窗
- `onStartDateChange()`: 开始日期选择处理
- `onEndDateChange()`: 结束日期选择处理
- `selectSort()`: 排序方式选择
- `resetFilter()`: 重置筛选条件
- `applyFilter()`: 应用筛选条件

#### 修改的方法
- `loadOrders()`: 更新为支持筛选条件，自动判断使用普通接口还是筛选接口
- `getStatusText()`: 更新状态文本映射
- `getStatusClass()`: 更新状态样式映射

### 3. 样式更新

#### 搜索栏样式
- 添加了筛选按钮的样式
- 调整了搜索栏的布局为flex布局

#### 筛选弹窗样式
- 完整的弹窗样式，包括头部、内容区域、底部按钮
- 价格输入框和日期选择器的样式
- 排序选项的选择样式
- 响应式设计，适配不同屏幕尺寸

## 功能特性

### 1. 智能接口切换
- 当有筛选条件时，自动使用筛选接口
- 无筛选条件时，使用原有的普通接口
- 保持向后兼容性

### 2. 参数验证
- 价格范围验证：最小价格不能大于最大价格
- 日期范围验证：开始日期不能晚于结束日期
- 输入格式验证

### 3. 用户体验优化
- 筛选条件重置功能
- 实时的筛选状态反馈
- 流畅的弹窗动画效果
- 直观的排序选项展示

## 使用说明

1. **打开筛选**: 点击搜索栏右侧的"筛选"按钮
2. **设置条件**: 在弹窗中设置价格范围、日期范围、排序方式等
3. **应用筛选**: 点击"应用筛选"按钮执行筛选
4. **重置条件**: 点击"重置"按钮清空所有筛选条件
5. **关闭弹窗**: 点击"×"按钮或点击遮罩层关闭弹窗

## 技术要点

- 使用 Vue 3 Composition API
- 响应式数据管理
- 组件化设计
- 参数验证和错误处理
- 样式模块化

## 兼容性

- 支持微信小程序
- 支持H5
- 支持App
- 向后兼容原有功能
