# 心理测评系统API迁移测试指南

## 概述

本文档描述了心理测评系统适配最新后端API后需要测试的功能点，确保所有功能正常工作。

## 主要变化总结

### 1. API路径最终确定
- **最终路径**: `/miniapp/user/assessment/*`
- 所有测评相关接口统一使用此路径前缀

### 2. 测评流程简化
- 保持传统的测评流程：开始测评 -> 获取题目列表 -> 提交答案 -> 完成测评
- 使用sessionId进行会话管理
- 答案实时提交保存

## 需要测试的功能模块

### 1. 量表浏览功能
#### 测试页面
- `pages/index/index.vue` - 首页测评列表
- `pages/evaluation/index.vue` - 测评列表页
- `pages/classification/simple-index.vue` - 分类页面
- `pages/explore/index.vue` - 探索页面

#### 测试点
- [ ] 量表列表加载正常
- [ ] 分页功能正常
- [ ] 搜索功能正常
- [ ] 热门量表显示正常
- [ ] 推荐量表显示正常
- [ ] 免费量表筛选正常

#### 相关API
```javascript
// 最新API路径
listAssessment() -> /miniapp/user/assessment/scale/list
getAssessment(id) -> /miniapp/user/assessment/scale/{id}
searchAssessments() -> /miniapp/user/assessment/scale/search
getPopularAssessments() -> /miniapp/user/assessment/scale/hot
getRecommendedAssessments() -> /miniapp/user/assessment/scale/recommend
getFreeAssessments() -> /miniapp/user/assessment/scale/free
```

### 2. 测评详情功能
#### 测试页面
- `pages/evaluation/detail/index.vue`

#### 测试点
- [ ] 量表详情信息显示正常
- [ ] 用户评价展示正常
- [ ] 相关测评推荐正常
- [ ] 开始测评按钮功能正常
- [ ] 继续测评功能正常（如有未完成的测评）

### 3. 测评流程功能
#### 测试页面
- `pages/evaluation/answer/index.vue`

#### 测试点
- [ ] 开始新测评功能正常
- [ ] 题目逐个加载正常
- [ ] 答案提交实时保存
- [ ] 进度条显示正确
- [ ] 上一题/下一题导航正常
- [ ] 保存进度功能正常
- [ ] 完成测评跳转正常
- [ ] 页面退出时进度保存提示

#### 相关API
```javascript
// 最新的测评流程API
startAssessment() -> /miniapp/user/assessment/test/start
submitAnswer() -> /miniapp/user/assessment/test/submit
completeAssessment() -> /miniapp/user/assessment/test/complete
getTestResult() -> /miniapp/user/assessment/test/result/{sessionId}
```

### 4. 测评报告功能
#### 测试页面
- `pages/evaluation/report/index.vue`

#### 测试点
- [ ] 测评结果显示正常
- [ ] 维度得分可视化正常
- [ ] 结果等级和描述正确
- [ ] 用时计算正确
- [ ] 评价测评按钮功能正常
- [ ] 分享报告功能正常
- [ ] 重新测评功能正常

#### 相关API
```javascript
// 报告相关API
getTestResult() -> /miniapp/user/assessment/test/result/{sessionId}
getAssessmentRecordDetail() -> /miniapp/user/assessment/record/{recordId}
```

### 5. 测评记录功能
#### 测试页面
- `pages/evaluation/index.vue` - 我的测评记录

#### 测试点
- [ ] 测评记录列表显示正常
- [ ] 记录状态显示正确（进行中/已完成）
- [ ] 点击记录跳转功能正常
- [ ] 继续未完成测评功能正常

#### 相关API
```javascript
// 记录相关API
getAssessmentRecords() -> /miniapp/user/assessment/record/list
getAssessmentRecordDetail() -> /miniapp/user/assessment/record/{recordId}
getAssessmentHistory() -> /miniapp/user/assessment/record/history/{scaleId}
```

### 6. 评价功能
#### 测试页面
- `pages/evaluation/reviews/index.vue` - 评价列表
- `pages/evaluation/submit-review/index.vue` - 提交评价

#### 测试点
- [ ] 评价列表显示正常
- [ ] 评分统计正确
- [ ] 筛选功能正常
- [ ] 提交评价功能正常
- [ ] 匿名评价选项正常

#### 相关API
```javascript
// 评价相关API
getScaleReviews() -> /miniapp/user/assessment/review/list/{scaleId}
submitReview() -> /miniapp/user/assessment/review/submit
getUserReviews() -> /miniapp/user/assessment/review/user
```

### 7. 搜索功能
#### 测试页面
- `pages/search/search.vue`

#### 测试点
- [ ] 测评搜索功能正常
- [ ] 搜索结果显示正确
- [ ] 搜索历史记录正常

## 兼容性测试

### 旧版本兼容性
由于保留了兼容接口，需要测试：
- [ ] 旧版本的测评记录能正常显示
- [ ] 旧版本的报告能正常查看
- [ ] 数据迁移过程中无数据丢失

### 错误处理
- [ ] 网络错误时的提示正常
- [ ] API返回错误时的处理正常
- [ ] 会话过期时的处理正常

## 性能测试

### 响应时间
- [ ] 量表列表加载时间 < 2秒
- [ ] 题目切换响应时间 < 1秒
- [ ] 答案提交响应时间 < 1秒
- [ ] 报告生成时间 < 3秒

### 数据量测试
- [ ] 大量测评记录时的列表性能
- [ ] 长时间测评的进度保存
- [ ] 多维度得分的显示性能

## 测试环境准备

### 1. 后端API确认
确保后端已部署新的API接口：
- `/miniapp/user/psy-assessment/*` 系列接口
- 会话管理功能
- 进度保存功能

### 2. 数据准备
- 准备测试用的量表数据
- 准备不同状态的测评记录
- 准备测试用的评价数据

### 3. 测试账号
- 普通用户账号
- 有历史测评记录的账号
- 有进行中测评的账号

## 回归测试清单

在完成所有功能测试后，进行回归测试：
- [ ] 所有测评相关页面能正常访问
- [ ] 所有API调用返回正确数据
- [ ] 用户体验流程完整
- [ ] 错误处理机制正常
- [ ] 性能指标达标

## 问题记录模板

发现问题时，请按以下格式记录：

```
问题标题: [页面名称] - [功能描述] - [问题简述]
复现步骤:
1. 
2. 
3. 

预期结果:
实际结果:
错误信息:
影响程度: [高/中/低]
```

## 测试完成标准

- [ ] 所有测试点通过
- [ ] 无阻塞性问题
- [ ] 性能指标达标
- [ ] 用户体验良好
- [ ] 兼容性测试通过
