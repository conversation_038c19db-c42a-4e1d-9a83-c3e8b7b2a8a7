<template>
	<view class="submit-review-container">
		<!-- 咨询师信息 -->
		<view class="consultant-info">
			<image class="avatar" :src="consultantInfo.imageUrl || defaultAvatar" mode="aspectFill" />
			<view class="info">
				<text class="name">{{ consultantInfo.name }}</text>
				<text class="title">{{ consultantInfo.personalTitle }}</text>
			</view>
		</view>

		<!-- 评分 -->
		<view class="rating-section">
			<text class="section-title">请为本次咨询打分</text>
			<view class="rating-container">
				<uni-rate :value="reviewForm.rating" size="32" @change="handleRatingChange" />
				<text class="rating-text">{{ getRatingText(reviewForm.rating) }}</text>
			</view>
		</view>

		<!-- 评价内容 -->
		<view class="content-section">
			<text class="section-title">评价内容</text>
			<textarea class="content-input" v-model="reviewForm.content" placeholder="请分享您的咨询体验，您的评价将帮助其他用户更好地了解咨询师"
				maxlength="500" :show-count="true" />
		</view>

		<!-- 咨询类型 -->
		<view class="type-section">
			<text class="section-title">咨询类型</text>
			<view class="type-tags">
				<view class="type-tag" :class="{ active: reviewForm.consultType === type }" v-for="type in consultTypes"
					:key="type" @click="selectConsultType(type)">
					{{ type }}
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-section">
			<button class="submit-btn" :class="{ disabled: !canSubmit }" @click="submitReview" :disabled="!canSubmit">
				提交评价
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { submitReview as submitReviewApi, checkCanRate } from '@/api/classification.js'

// 页面参数
const consultantId = ref(null)
const recordId = ref(null)

// 咨询师信息
const consultantInfo = ref({})

// 评价表单
const reviewForm = ref({
	rating: 5,
	content: '',
	consultType: '情感焦点'
})

// 咨询类型选项
const consultTypes = ref(['情感焦点', '个人成长', '家庭关系', '职场压力', '其他'])

// 默认头像
const defaultAvatar = '/static/images/default-avatar.png'

// 页面加载
onLoad(async (options) => {
	consultantId.value = options.consultantId
	recordId.value = options.recordId

	if (options.consultantInfo) {
		try {
			consultantInfo.value = JSON.parse(decodeURIComponent(options.consultantInfo))
		} catch (e) {
			console.error('解析咨询师信息失败:', e)
		}
	}

	// 检查是否可以评价
	if (recordId.value) {
		await checkReviewPermission()
	}
})

// 检查评价权限
const checkReviewPermission = async () => {
	try {
		const res = await checkCanRate(recordId.value)
		if (res.code === 200) {
			if (!res.data.canRate) {
				uni.showToast({
					title: res.data.hasReviewed ? '您已评价过该咨询' : '暂无评价权限',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			}
		}
	} catch (error) {
		console.error('检查评价权限失败:', error)
	}
}

// 评分变化处理
const handleRatingChange = (value) => {
	reviewForm.value.rating = value
}

// 获取评分文本
const getRatingText = (rating) => {
	const texts = {
		1: '很不满意',
		2: '不满意',
		3: '一般',
		4: '满意',
		5: '非常满意'
	}
	return texts[rating] || '请评分'
}

// 选择咨询类型
const selectConsultType = (type) => {
	reviewForm.value.consultType = type
}

// 是否可以提交
const canSubmit = computed(() => {
	return reviewForm.value.rating > 0 &&
		reviewForm.value.content.trim().length >= 10 &&
		reviewForm.value.consultType
})

// 提交评价
const submitReview = async () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请完善评价信息',
			icon: 'none'
		})
		return
	}

	try {
		uni.showLoading({
			title: '提交中...'
		})

		const reviewData = {
			consultantId: consultantId.value,
			recordId: recordId.value,
			rating: reviewForm.value.rating,
			content: reviewForm.value.content.trim(),
			consultType: reviewForm.value.consultType
		}

		const res = await submitReviewApi(reviewData)

		uni.hideLoading()

		if (res.code === 200) {
			uni.showToast({
				title: '评价提交成功',
				icon: 'success'
			})

			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		} else {
			uni.showToast({
				title: res.msg || '提交失败',
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('提交评价失败:', error)
		uni.showToast({
			title: '提交失败，请重试',
			icon: 'none'
		})
	}
}
</script>

<style lang="scss" scoped>
.submit-review-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 40rpx;
}

.consultant-info {
	background: white;
	border-radius: 16rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	display: flex;
	align-items: center;
	gap: 24rpx;

	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		background: #f0f0f0;
	}

	.info {
		flex: 1;

		.name {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 8rpx;
		}

		.title {
			font-size: 26rpx;
			color: #666;
		}
	}
}

.rating-section,
.content-section,
.type-section {
	background: white;
	border-radius: 16rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;

	.section-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 32rpx;
	}
}

.rating-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;

	.rating-text {
		font-size: 28rpx;
		color: #ff6b35;
		font-weight: 500;
	}
}

.content-input {
	width: 100%;
	min-height: 200rpx;
	padding: 24rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 12rpx;
	font-size: 28rpx;
	line-height: 1.5;
	background: #fafafa;

	&:focus {
		border-color: #ff6b35;
		background: white;
	}
}

.type-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;

	.type-tag {
		padding: 16rpx 32rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 40rpx;
		font-size: 26rpx;
		color: #666;
		background: #fafafa;
		transition: all 0.3s ease;

		&.active {
			border-color: #ff6b35;
			background: #ff6b35;
			color: white;
		}
	}
}

.submit-section {
	margin-top: 80rpx;

	.submit-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(90deg, #ff6b35, #ff8c42);
		border: none;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;

		&.disabled {
			background: #ccc;
			color: #999;
		}
	}
}
</style>
