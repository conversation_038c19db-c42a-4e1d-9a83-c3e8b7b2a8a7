<template>
	<view class="content glacier-theme">
		<!-- 门店信息 -->

		<view class="content-top">
			<view class="store-info" @click="showStoreModal">
				<image class="store-avatar" src="../../static/icon/矢量智能对象.png" mode="aspectFill"></image>
				<text class="store-name">{{ storeInfo?.name }} {{ storeInfo?.branchName }}</text>
				<image class="store-avatar-right" src="../../static/icon/圆角矩形 1.png" mode="aspectFill"></image>
			</view>

			<!-- 搜索框 -->
			<view class="search-bar" @click="goToSearch">
				<view class="search-icon-box">
					<image src="../../static/icon/icon_sousuo.png"></image>
					<text class="search-placeholder">咨询师</text>
				</view>
				<view class="search-btn">搜索</view>
			</view>

			<!-- 轮播图 -->
			<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
				:duration="duration">
				<swiper-item v-for="(item, index) in bannerList" :key="index">
					<image class="swiper-img" :mode="item.displayMode" :src="item.imageUrl" @error="error"></image>
				</swiper-item>
			</swiper>

			<!-- 快速匹配和立即预约按钮 -->
			<view class="quick-actions">
				<view class="action-item" @click="handleToMatch">
					<view class="action-item-left">
						<text class="action-text title">快速匹配</text>
						<text class="action-text">填写信息匹配</text>
						<text class="action-text">最适合您的咨询师</text>
					</view>
					<image class="action-icon"
						src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/快速匹配.png"></image>
				</view>
				<view class="action-item" @click="handleToAppointment">
					<view class="action-item-right">
						<text class="action-text title-book-now">立即预约</text>
						<text class="action-text">自定义的时间段</text>
						<text class="action-text">筛选咨询师</text>
					</view>
					<image class="action-icon"
						src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/立即预约.png"></image>
				</view>
			</view>
		</view>

		<view class="content-bottom">
			<!-- 分类标签页 -->
			<view class="category-tabs">
				<view v-for="(tab, index) in tabs" :key="tab.categoryId" :class="['tab-item', { active: currentTab === index }]"
					@click="switchTab(index, tab.categoryId)">
					{{ tab.name }}
					<image v-if="currentTab === index" class="tab-icon" src="../../static/icon/图层 4.png"></image>
				</view>
			</view>

			<!-- 内容区域 -->
			<scroll-view scroll-y class="content-list" @scrolltolower="onLoadMore">
				<view class="tab-content">
					<view v-if="currentList.length === 0" class="empty-state">
						<image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png"
							mode="aspectFit"></image>
						<text>暂无数据</text>
					</view>

					<view v-else class="list-container">
						<UniversalListItem v-for="item in currentList" :key="item.id" :item="item" :type="currentTabType"
							@click="handleItemClick" :dictData="dictData" />
					</view>
				</view>
			</scroll-view>
		</view>


		<cc-myTabbar :tabBarShow="0"></cc-myTabbar>

		<!-- 门店信息弹框 -->
		<uni-popup ref="storePopup" type="center" :mask-click="true">
			<view class="store-modal">
				<view class="modal-header">
					<text class="modal-title">店铺信息</text>
				</view>
				<view class="modal-content">
					<view class="store-info-detail">
						<image class="store-logo" src="../../static/icon/店铺logo.png" mode="aspectFill"></image>
						<text class="store-full-name">{{ storeInfo?.name }}{{ storeInfo?.branchName }}</text>
					</view>

					<view class="contact-section">
						<view class="contact-item" v-for="(contact, index) in storeInfo?.contacts" :key="index"
							@click="makePhoneCall(contact.phone)">
							<view class="contact-text-box">
								<text class="section-title">联系电话</text>
								<text class="contact-phone">{{ contact.phone }}</text>
							</view>
							<image class="store-logo" src="../../static/icon/电话.png" mode="aspectFill"></image>
						</view>
					</view>

					<view class="address-section">
						<view class="address-item" @click="navigateToStore">
							<view class="address-text-box">
								<text class="section-title">详细地址</text>
								<text class="address-text">{{ storeInfo?.mapAddress || storeInfo?.address }}</text>
							</view>
							<image class="store-logo" src="../../static/icon/定位.png" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { onShow, onReady } from "@dcloudio/uni-app";
import { getBannerList, getlist } from "@/api/index.js";
import { getStore } from "@/api/store.js";
import { getCourseList } from "@/api/course.js";
import { getMeditationList } from "@/api/meditation.js";
import { listAssessment } from "@/api/evaluation.js";
import { getCategoryTree } from "@/api/category.js";
import { useDict, handleTree } from "@/utils/index.js";
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue';

// 响应式数据
const indicatorDots = ref(true);
const autoplay = ref(true);
const interval = ref(3000);
const duration = ref(500);
const bannerList = ref([]);
const storeInfo = ref(null);
const distance = ref(null);

// 弹框引用
const storePopup = ref(null);

// 数据列表
const counselorList = ref([]);
const courseList = ref([]);
const assessmentList = ref([]);
const meditationList = ref([]);
const dictData = ref({});

// 加载状态已移除下拉刷新

// 分类标签
const tabs = ref([]);
const currentTab = ref(0);
const isTabsInitialized = ref(false); // 标记是否已经初始化过tabs

// 计算属性
const currentTabType = computed(() => {
	return tabs.value[currentTab.value]?.type || 'consultant';
});

const currentList = computed(() => {
	const type = currentTabType.value;
	switch (type) {
		case 'consultant':
			return counselorList.value;
		case 'assessment':
			return assessmentList.value;
		case 'meditation':
			return meditationList.value;
		case 'course':
			return courseList.value;
		default:
			return [];
	}
});

// 计算两点之间的距离
const calculateDistance = (lat1, lon1, lat2, lon2) => {
	const R = 6371; // 地球半径，单位km
	const dLat = (lat2 - lat1) * Math.PI / 180;
	const dLon = (lon2 - lon1) * Math.PI / 180;
	const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
		Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
		Math.sin(dLon / 2) * Math.sin(dLon / 2);
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
	const d = R * c;
	return d.toFixed(2); // 保留两位小数
};

// 获取用户位置
const getUserLocation = () => {
	return new Promise((resolve, reject) => {
		uni.getLocation({
			type: 'gcj02',
			success: (res) => {
				resolve(res);
			},
			fail: (err) => {
				console.error('获取位置失败：', err);
				reject(err);
			}
		});
	});
};

// 获取门店信息
const getStoreInfo = async () => {
	try {
		const res = await getStore(1); // 获取ID为1的门店信息
		if (res.code === 200) {
			// 保存完整的门店数据，包括联系人信息
			storeInfo.value = {
				...res.data.store,
				contacts: res.data.contacts,
				businessHours: res.data.businessHours,
				businessDays: res.data.businessDays
			};
			// 获取用户位置并计算距离
			try {
				const location = await getUserLocation();
				distance.value = calculateDistance(
					location.latitude,
					location.longitude,
					storeInfo.value.latitude,
					storeInfo.value.longitude
				);
			} catch (locationError) {
				console.log('用户未授权位置信息或获取位置失败');
				distance.value = null;
			}
		} else {
			uni.showToast({
				title: '获取门店信息失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('获取门店信息错误：', error);
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		});
	}
};

// 显示门店信息弹框
const showStoreModal = () => {
	if (storePopup.value) {
		storePopup.value.open();
	}
};

// 打电话
const makePhoneCall = (phoneNumber) => {
	uni.makePhoneCall({
		phoneNumber: phoneNumber,
		success: () => {
			console.log("拨打电话成功！");
		},
		fail: (err) => {
			console.log("拨打电话失败！", err);
			uni.showToast({
				title: '拨打电话失败',
				icon: 'none'
			});
		}
	});
};

// 导航到门店
const navigateToStore = () => {
	if (!storeInfo.value) return;
	uni.openLocation({
		longitude: Number(storeInfo.value.longitude),
		latitude: Number(storeInfo.value.latitude),
		name: storeInfo.value.mapName || storeInfo.value.name,
		address: storeInfo.value.mapAddress || storeInfo.value.address,
		fail: (err) => {
			console.log("导航失败:", err);
			uni.showToast({
				title: '导航失败',
				icon: 'none'
			});
		},
	});
};

// 打开地图
const toMap = () => {
	if (!storeInfo.value) return;
	uni.openLocation({
		longitude: Number(storeInfo.value.longitude),
		latitude: Number(storeInfo.value.latitude),
		name: storeInfo.value.mapName,
		address: storeInfo.value.mapAddress,
		fail: (err) => console.log("导航失败:", err),
	});
};

// 获取轮播图数据
const getImageList = async () => {
	try {
		const res = await getBannerList();
		if (res.code === 200) {
			bannerList.value = res.data;
		}
	} catch (error) {
		console.error('获取轮播图错误：', error);
	}
};

// 加载分类标签
const loadCategories = async () => {
	try {
		const res = await getCategoryTree();
		if (res.code === 200 && res.data && res.data.categories) {
			// 将分类数据转换为首页需要的格式
			tabs.value = res.data.categories.map(category => {
				// 根据分类名称映射到对应的类型
				let type = 'consultant'; // 默认类型
				switch (category.categoryName) {
					case '咨询师':
						type = 'consultant';
						break;
					case '课程':
						type = 'course';
						break;
					case '冥想':
						type = 'meditation';
						break;
					case '测评':
						type = 'assessment';
						break;
				}

				return {
					name: category.categoryName,
					type: type,
					categoryId: category.categoryId
				};
			});
		} else {
			// 如果接口失败，使用默认分类
			tabs.value = [
				{ name: '咨询师', type: 'consultant' },
				{ name: '测评', type: 'assessment' },
				{ name: '冥想', type: 'meditation' },
				{ name: '课程', type: 'course' }
			];
		}

		// 只在首次初始化时加载默认标签的数据
		if (tabs.value.length > 0 && !isTabsInitialized.value) {
			switchTab(0);
			isTabsInitialized.value = true;
		}
	} catch (error) {
		console.error('加载分类标签失败:', error);
		// 使用默认分类
		tabs.value = [
			{ name: '咨询师', type: 'consultant' },
			{ name: '测评', type: 'assessment' },
			{ name: '冥想', type: 'meditation' },
			{ name: '课程', type: 'course' }
		];

		// 只在首次初始化时加载默认标签的数据
		if (tabs.value.length > 0 && !isTabsInitialized.value) {
			switchTab(0);
			isTabsInitialized.value = true;
		}
	}
};

// 切换标签
const switchTab = (index) => {
	currentTab.value = index;

	// 根据标签页加载对应数据
	const type = tabs.value[index]?.type;
	switch (type) {
		case 'consultant':
			if (counselorList.value.length === 0) {
				loadCounselorList();
			}
			break;
		case 'course':
			if (courseList.value.length === 0) {
				loadCourseList();
			}
			break;
		case 'meditation':
			if (meditationList.value.length === 0) {
				loadMeditationList();
			}
			break;
		case 'assessment':
			if (assessmentList.value.length === 0) {
				loadAssessmentList();
			}
			break;
	}
};

// 下拉刷新功能已移除

// 上拉加载更多
const onLoadMore = async () => {
	const type = currentTabType.value;
	// 只有测评支持分页加载更多
	if (type === 'assessment') {
		await loadAssessmentList(true); // 加载更多
	}
};

// 跳转到搜索页
const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/search/search'
	});
};

// 跳转到匹配页
const handleToMatch = () => {
	uni.navigateTo({
		url: '/pages/match/match'
	});
};

// 跳转到预约页
const handleToAppointment = () => {
	uni.switchTab({
		url: '/pages/appointment/index'
	});
};

// 通用项目点击处理
const handleItemClick = (item) => {
	const type = currentTabType.value;

	switch (type) {
		case 'consultant':
			uni.navigateTo({
				url: `/pages/classification/counselor-detail/index?id=${item.id}`
			});
			break;
		case 'course':
			uni.navigateTo({
				url: `/pages/course/detail/index?id=${item.id}`
			});
			break;
		case 'meditation':
			uni.navigateTo({
				url: `/pages/meditation/detail/index?id=${item.id}`
			});
			break;
		case 'assessment':
			uni.navigateTo({
				url: `/pages/evaluation/detail/index?id=${item.id}`
			});
			break;
	}
};

// 加载咨询师列表
const loadCounselorList = async () => {
	try {
		const res = await getlist();
		if (res.code === 200) {
			counselorList.value = res.data || [];
		}
	} catch (error) {
		console.error('加载咨询师列表失败:', error);
	}
};

// 加载测评列表
const loadAssessmentList = async (loadMore = false) => {
	try {
		const pageNum = loadMore ? Math.floor(assessmentList.value.length / 10) + 1 : 1;
		const res = await listAssessment({
			pageNum: pageNum,
			pageSize: 10
		});
		if (res.code === 200) {
			// 统一使用data字段，兼容旧的rows字段
			const data = res.data || res.rows || [];
			if (loadMore) {
				// 加载更多时追加数据
				assessmentList.value = [...assessmentList.value, ...data];
			} else {
				// 首次加载或刷新时替换数据
				assessmentList.value = data;
			}
		}
	} catch (error) {
		console.error('加载测评列表失败:', error);
	}
};

// 加载冥想列表
const loadMeditationList = async () => {
	try {
		const res = await getMeditationList();
		if (res.code === 200) {
			meditationList.value = res.data || [];
		}
	} catch (error) {
		console.error('加载冥想列表失败:', error);
	}
};

// 加载课程列表
const loadCourseList = async () => {
	try {
		const res = await getCourseList();
		if (res.code === 200) {
			courseList.value = res.data || [];
		}
	} catch (error) {
		console.error('加载课程列表失败:', error);
	}
};

const getTags = (tags) => {
	if (!tags) return '';
	try {
		// Parse the string into array and join with commas
		return JSON.parse(tags).join(' ');
	} catch (e) {
		console.error('解析标签失败:', e);
		return '';
	}
}

const getDict = () => {
	useDict('psy_consultant_level').then(res => {
		dictData.value['psy_consultant_level'] = res.psy_consultant_level || [];
	})
}

// 生命周期钩子
onMounted(() => {
	getImageList();
	getStoreInfo();
	getDict()
});

onShow(() => {
	getStoreInfo();
	// 加载分类标签
	loadCategories();
});

onReady(() => {
	uni.hideTabBar();
});
</script>

<style lang="scss" scoped>
.content {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;

	.content-top {
		padding: 32rpx;
	}
}

.store-info {
	display: flex;
	align-items: center;
	// width: 309rpx;
	height: 38rpx;
	margin-bottom: 38rpx;
	margin-top: 32rpx;

	.store-avatar {
		width: 30rpx;
		height: 36rpx;
	}

	.store-avatar-right {
		width: 14rpx;
		height: 24rpx;
		margin-left: 7rpx;
	}

	.store-name {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #000000;
		text-align: left;
		font-style: normal;
		text-transform: none;
		margin-left: 5rpx;
	}

}

.search-bar {
	height: 64rpx;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
	border-radius: 32rpx 32rpx 32rpx 32rpx;

	.search-icon-box {
		display: flex;
		align-items: center;
	}

	.search-placeholder {
		margin-left: 8rpx;
		font-size: 22rpx;
		color: #999999;
	}

	image {
		width: 30rpx;
		height: 30rpx;
		margin-left: 23rpx;
	}

	.search-btn {
		width: 98rpx;
		height: 54rpx;
		background-color: #A04571;
		color: #FFFEFE;
		font-size: 24rpx;
		border-radius: 27rpx 27rpx 27rpx 27rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 5rpx;
		margin-left: 0;
	}
}

.swiper {
	height: 316rpx;
	overflow: hidden;
	margin-bottom: 30rpx;

	.swiper-img {
		width: 100%;
		height: 100%;
		border-radius: 12rpx;
	}
}

.quick-actions {
	display: flex;
	gap: 26rpx;

	.action-item {
		flex: 1;
		height: 196rpx;
		background: #fff;
		border-radius: 12rpx;
		display: flex;
		align-items: center;

		.action-item-left,
		.action-item-right {
			display: flex;
			flex-direction: column;
			margin-left: 25rpx;
			margin-right: 10rpx;
		}

		.action-icon {
			width: 116rpx;
			height: 122rpx;
			margin-bottom: 12rpx;
		}

		.action-text {
			font-size: 22rpx;
			color: #ACA8AA;
		}

		.title,
		.title-book-now {
			font-size: 30rpx;
			color: #455DA0;
			font-weight: 700;
			margin-bottom: 23rpx;
		}

		.title-book-now {
			color: #A04571;
		}
	}
}

.category-tabs {
	display: flex;
	background: #fff;
	padding: 34rpx;

	// border-bottom: 1px solid #eee;
	.tab-item {
		position: relative;
		// padding: 24rpx 32rpx;
		font-size: 28rpx;
		color: #8A8788;
		position: relative;
		margin-right: 138rpx;

		&.active {
			font-size: 32rpx;
			color: #A04571;
		}
	}

	.tab-item:last-child {
		margin-right: 0;
	}

	.tab-icon {
		position: absolute;
		bottom: -16rpx;
		right: -16rpx;
		width: 26rpx;
		height: 26rpx;
	}
}

.content-list {
	padding: 0;
	width: 100%;
}

.tab-content {
	width: 100%;

	.list-container {
		padding: 0 32rpx;
		background-color: #fff;
	}
}

.empty-tip {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

.course-grid {
	grid-template-columns: repeat(2, 1fr);
	gap: 24rpx;
}

.course-item {
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	.course-cover {
		position: relative;
		height: 240rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.course-info {
		padding: 24rpx;

		.course-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 12rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.course-desc {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 20rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}

		.course-meta {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.course-price {
				display: flex;
				align-items: baseline;

				.price-symbol {
					font-size: 24rpx;
					color: #ff6b35;
				}

				.price-value {
					font-size: 36rpx;
					font-weight: 600;
					color: #ff6b35;
				}

				.original-price {
					font-size: 24rpx;
					color: #999;
					text-decoration: line-through;
					margin-left: 12rpx;
				}
			}

			.course-stats {
				.student-count {
					font-size: 24rpx;
					color: #999;
				}

				.course-duration {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 32rpx;
	text-align: center;

	image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 32rpx;
	}

	text {
		font-size: 28rpx;
		color: #999;
	}
}

.counselor-item {
	margin-bottom: 20rpx;
	background: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	display: flex;

	.counselor-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		margin-right: 20rpx;
	}

	.counselor-info {
		flex: 1;

		.counselor-header {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;

			.counselor-name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				margin-right: 12rpx;
			}

			.counselor-title {
				font-size: 24rpx;
				color: #666;
				background: #f5f5f5;
				padding: 4rpx 12rpx;
				border-radius: 4rpx;
			}
		}

		.counselor-tags {
			display: flex;
			flex-wrap: wrap;
			gap: 12rpx;
			margin-bottom: 12rpx;

			.tag {
				font-size: 24rpx;
				color: #666;
				background: #f5f5f5;
				padding: 4rpx 12rpx;
				border-radius: 4rpx;
			}
		}

		.counselor-desc {
			font-size: 26rpx;
			color: #666;
			margin-bottom: 12rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}

		.counselor-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.counselor-stats {
				font-size: 24rpx;
				color: #999;
			}

			.counselor-price {
				.price-symbol {
					font-size: 24rpx;
					color: #ff6b6b;
				}

				.price-value {
					font-size: 32rpx;
					font-weight: 500;
					color: #ff6b6b;
				}

				.price-unit {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}

// 门店信息弹框样式
.store-modal {
	width: 480rpx;
	background: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	padding: 30rpx;

	.modal-header {
		text-align: center;

		.modal-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #01020C;
		}

		margin-bottom: 38rpx;
	}

	.modal-content {
		.store-logo {
			width: 58rpx;
			height: 58rpx;
			margin-right: 13rpx;
		}

		.store-info-detail {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;


			.store-full-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}
		}

		.contact-section,
		.address-section {
			margin-bottom: 24rpx;

			.section-title {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 8rpx;
				display: block;
			}

			.contact-item,
			.address-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 16rpx 20rpx;
				background: #f8f8f8;
				border-radius: 12rpx;
				margin-bottom: 12rpx;

				.contact-text-box,
				.address-text-box {
					display: flex;
					flex-direction: column;
				}

				.address-text-box {
					width: calc(100% - 58rpx);
				}

				&:last-child {
					margin-bottom: 0;
				}

				.contact-phone,
				.address-text {
					font-size: 28rpx;
					color: #333;
					flex: 1;
				}
			}
		}
	}
}
</style>
