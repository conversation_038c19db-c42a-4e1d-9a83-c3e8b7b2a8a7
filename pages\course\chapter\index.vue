<template>
  <view class="chapter-page">
    <!-- 视频播放器 -->
    <view class="video-container" v-if="chapterDetail.videoUrl">
      <t-video
        id="chapter-video-player"
        :src="chapterDetail.videoUrl"
        :poster="chapterDetail.coverImage"
        :controls="true"
        :autoplay="false"
        :show_fullscreen_btn="true"
        :show_play_btn="true"
        :show_center_play_btn="true"
        style="width:100%;height:100%;"
        @Timeupdate="onTimeUpdate"
        @Ended="onVideoEnded"
        @Play="onVideoPlay"
        @Pause="onVideoPause"
        @videoloaded="onVideoLoaded"
        @videosuccess="onVideoSuccess"
        @videofailed="onVideoFailed"
      ></t-video>
    </view>

    <!-- 章节内容 -->
    <scroll-view class="content-area" scroll-y>
      <!-- 章节信息 -->
      <view class="chapter-info">
        <view class="chapter-title">{{ chapterDetail.title }}</view>
        <view class="chapter-meta">
          <text>时长: {{ chapterDetail.duration }}分钟</text>
          <text v-if="chapterDetail.isTrial">试听章节</text>
        </view>
      </view>

      <!-- 学习进度 -->
      <view class="progress-section" v-if="userProgress">
        <view class="progress-title">学习进度</view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: userProgress.progressPercent + '%' }"></view>
        </view>
        <view class="progress-text">{{ userProgress.progressPercent }}%</view>
      </view>

      <!-- 章节内容 -->
      <view class="chapter-content" v-if="chapterDetail.chapterContent">
        <view class="content-title">章节内容</view>
        <rich-text :nodes="chapterDetail.chapterContent"></rich-text>
      </view>

      <!-- 需要购买提示 -->
      <view class="purchase-tip" v-if="chapterDetail.needPurchase">
        <view class="tip-icon">🔒</view>
        <view class="tip-text">此章节需要购买课程后才能观看</view>
        <button class="purchase-btn" @click="goToPurchase">立即购买</button>
      </view>

      <!-- 需要登录提示 -->
      <view class="login-tip" v-if="chapterDetail.needLogin">
        <view class="tip-icon">👤</view>
        <view class="tip-text">请先登录后观看</view>
        <button class="login-btn" @click="goToLogin">立即登录</button>
      </view>

      <!-- 章节导航 -->
      <view class="chapter-nav">
        <button 
          v-if="prevChapter" 
          class="nav-btn prev-btn" 
          @click="goToChapter(prevChapter.id)"
        >
          <uni-icons type="left" size="16"></uni-icons>
          上一章节
        </button>
        <button 
          v-if="nextChapter" 
          class="nav-btn next-btn" 
          @click="goToChapter(nextChapter.id)"
        >
          下一章节
          <uni-icons type="right" size="16"></uni-icons>
        </button>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button class="complete-btn" @click="markAsCompleted" v-if="!chapterDetail.completed">
        标记为已完成
      </button>
      <view class="completed-status" v-else>
        <uni-icons type="checkmarkempty" size="20" color="#4CAF50"></uni-icons>
        <text>已完成</text>
      </view>
    </view>

    <!-- 菜单弹窗 -->
    <uni-popup ref="menuPopup" type="bottom">
      <view class="menu-content">
        <view class="menu-item" @click="toggleSpeed">
          <text>播放速度: {{ playbackRate }}x</text>
        </view>
        <view class="menu-item" @click="toggleQuality">
          <text>画质: {{ videoQuality }}</text>
        </view>
        <view class="menu-item" @click="shareChapter">
          <text>分享章节</text>
        </view>
        <view class="menu-item cancel" @click="closeMenu">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { 
  getChapterDetail, 
  updateProgress,
  getCourseChapters 
} from '@/api/course'
import { useUserStore } from '@/stores/user'

// 响应式数据
const chapterDetail = ref({})
const userProgress = ref(null)
const chapterList = ref([])
const prevChapter = ref(null)
const nextChapter = ref(null)
const chapterId = ref(null)
const courseId = ref(null)
const playbackRate = ref(1.0)
const videoQuality = ref('高清')
const currentTime = ref(0)
const duration = ref(0)
const isPlaying = ref(false)

const userStore = useUserStore()
const menuPopup = ref(null)

// 方法
const closeMenu = () => {
  menuPopup.value?.close()
}

const loadChapterDetail = async () => {
  try {
    const res = await getChapterDetail(chapterId.value)
    if (res.code === 200) {
      chapterDetail.value = res.data
      
      // 获取用户进度
      if (res.data.progress) {
        userProgress.value = res.data.progress
      }
    } else {
      uni.showToast({
        title: res.msg || '获取章节详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取章节详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const loadChapterList = async () => {
  try {
    const res = await getCourseChapters(courseId.value)
    if (res.code === 200) {
      chapterList.value = res.data || []
      
      // 找到当前章节的前后章节
      const currentIndex = chapterList.value.findIndex(ch => ch.id == chapterId.value)
      if (currentIndex > 0) {
        prevChapter.value = chapterList.value[currentIndex - 1]
      }
      if (currentIndex < chapterList.value.length - 1) {
        nextChapter.value = chapterList.value[currentIndex + 1]
      }
    }
  } catch (error) {
    console.error('获取章节列表失败:', error)
  }
}

const onTimeUpdate = (e) => {
  currentTime.value = e.detail.currentTime
  duration.value = e.detail.duration
  
  // 自动保存学习进度
  if (isPlaying.value && userStore.isLoggedIn) {
    saveProgress()
  }
}

const onVideoEnded = () => {
  isPlaying.value = false
  // 视频播放完成，自动标记为已完成
  markAsCompleted()
}

const onVideoPlay = () => {
  isPlaying.value = true
}

const onVideoPause = () => {
  isPlaying.value = false
  // 暂停时保存进度
  if (userStore.isLoggedIn) {
    saveProgress()
  }
}

// 小智宝视频插件特有事件处理
const onVideoLoaded = () => {
  console.log('视频已可播放')
}

const onVideoSuccess = () => {
  console.log('视频成功回调')
}

const onVideoFailed = (e) => {
  console.error('视频失败回调:', e)

  let errorMessage = '视频播放失败'
  if (e.detail && e.detail.ret) {
    switch (e.detail.ret) {
      case 6:
        errorMessage = '小程序appid未注册'
        break
      case 12:
        errorMessage = '用户状态异常'
        break
      case 13:
        errorMessage = '视频链接异常'
        break
      case 9:
        errorMessage = '用户时长不够'
        break
      case 8:
        errorMessage = '审核不通过'
        break
      default:
        errorMessage = `视频播放失败(错误码:${e.detail.ret})`
    }
  }

  uni.showToast({
    title: errorMessage,
    icon: 'none'
  })
}

const saveProgress = async () => {
  if (!userStore.isLoggedIn || !duration.value) return
  
  const progressPercent = Math.round((currentTime.value / duration.value) * 100)
  
  try {
    await updateProgress({
      courseId: courseId.value,
      chapterId: chapterId.value,
      progressPercent,
      watchTime: Math.round(currentTime.value),
      totalTime: Math.round(duration.value)
    })
    
    // 更新本地进度显示
    if (userProgress.value) {
      userProgress.value.progressPercent = progressPercent
    }
  } catch (error) {
    console.error('保存进度失败:', error)
  }
}

const markAsCompleted = async () => {
  if (!userStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  try {
    await updateProgress({
      courseId: courseId.value,
      chapterId: chapterId.value,
      progressPercent: 100,
      watchTime: Math.round(duration.value || chapterDetail.value.duration * 60),
      totalTime: Math.round(duration.value || chapterDetail.value.duration * 60),
      completed: true
    })
    
    chapterDetail.value.completed = true
    
    uni.showToast({
      title: '已标记为完成',
      icon: 'success'
    })
  } catch (error) {
    console.error('标记完成失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

const goToChapter = (id) => {
  uni.redirectTo({
    url: `/pages/course/chapter/index?id=${id}&courseId=${courseId.value}`
  })
}

const goToPurchase = () => {
  uni.navigateTo({
    url: `/pages/course/detail/index?id=${courseId.value}`
  })
}

const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/login'
  })
}

const toggleSpeed = () => {
  const speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
  const currentIndex = speeds.indexOf(playbackRate.value)
  playbackRate.value = speeds[(currentIndex + 1) % speeds.length]
  closeMenu()
}

const toggleQuality = () => {
  const qualities = ['标清', '高清', '超清']
  const currentIndex = qualities.indexOf(videoQuality.value)
  videoQuality.value = qualities[(currentIndex + 1) % qualities.length]
  closeMenu()
}

const shareChapter = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `pages/course/chapter/index?id=${chapterId.value}&courseId=${courseId.value}`,
    title: chapterDetail.value.title,
    summary: '推荐一个很棒的课程章节',
    imageUrl: chapterDetail.value.coverImage
  })
  closeMenu()
}

// 生命周期
onLoad((options) => {
  chapterId.value = options.id
  courseId.value = options.courseId
  
  if (chapterId.value) {
    loadChapterDetail()
  }
  
  if (courseId.value) {
    loadChapterList()
  }
})

// 页面卸载时保存进度
onUnmounted(() => {
  if (isPlaying.value && userStore.isLoggedIn) {
    saveProgress()
  }
})
</script>

<style lang="scss" scoped>
.chapter-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.video-container {
  background-color: #000;
  height: 420rpx;
}

.content-area {
  flex: 1;
  padding-bottom: 120rpx;
}

.chapter-info {
  padding: 32rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;

  .chapter-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .chapter-meta {
    display: flex;
    gap: 32rpx;
    font-size: 26rpx;
    color: #999;
  }
}

.progress-section {
  padding: 32rpx;
  background-color: #fff;
  margin-bottom: 16rpx;

  .progress-title {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 20rpx;
  }

  .progress-bar {
    height: 8rpx;
    background-color: #f0f0f0;
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 12rpx;

    .progress-fill {
      height: 100%;
      background-color: #ff6b35;
      transition: width 0.3s ease;
    }
  }

  .progress-text {
    font-size: 26rpx;
    color: #666;
    text-align: right;
  }
}

.chapter-content {
  padding: 32rpx;
  background-color: #fff;
  margin-bottom: 16rpx;

  .content-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.purchase-tip, .login-tip {
  padding: 60rpx 32rpx;
  background-color: #fff;
  text-align: center;
  margin-bottom: 16rpx;

  .tip-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
  }

  .tip-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 32rpx;
  }

  .purchase-btn, .login-btn {
    padding: 20rpx 48rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

.chapter-nav {
  display: flex;
  justify-content: space-between;
  padding: 32rpx;
  background-color: #fff;

  .nav-btn {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 20rpx 32rpx;
    background-color: #f8f8f8;
    color: #666;
    border: none;
    border-radius: 40rpx;
    font-size: 26rpx;

    &.next-btn {
      margin-left: auto;
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;

  .complete-btn {
    width: 100%;
    padding: 24rpx;
    background-color: #4CAF50;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 600;
  }

  .completed-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    padding: 24rpx;
    color: #4CAF50;
    font-size: 28rpx;
  }
}

.menu-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .menu-item {
    padding: 32rpx;
    text-align: center;
    font-size: 30rpx;
    color: #333;
    border-bottom: 1px solid #eee;

    &.cancel {
      color: #999;
      border-bottom: none;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
