<template>
	<view class="meditation-container">
		<!-- 分类筛选 -->
		<view class="category-filter">
			<scroll-view class="category-scroll" scroll-x>
				<view
					v-for="(category, index) in categories"
					:key="index"
					:class="['category-item', { active: currentCategory === index }]"
					@click="switchCategory(index)"
				>
					{{ category.name }}
				</view>
			</scroll-view>
		</view>

		<!-- 冥想列表 -->
		<view class="meditation-section">
			<view class="meditation-list">
				<view
					v-for="meditation in meditationList"
					:key="meditation.id"
					class="meditation-item"
					@click="toDetail(meditation)"
				>
					<view class="meditation-cover">
						<image :src="meditation.coverImage || defaultCover" mode="aspectFill"></image>
						<view class="meditation-duration">{{ meditation.duration }}分钟</view>
						<view v-if="meditation.isFree" class="free-tag">免费</view>
					</view>
					<view class="meditation-info">
						<view class="meditation-title">{{ meditation.title }}</view>
						<view class="meditation-subtitle">{{ meditation.subtitle }}</view>
						<view class="meditation-meta">
							<view class="meditation-stats">
								<text class="play-count">{{ meditation.playCount || 0 }}次播放</text>
								<text v-if="meditation.rating" class="rating">{{ meditation.rating }}分</text>
							</view>
							<view class="meditation-price" v-if="!meditation.isFree">
								<text class="price-symbol">¥</text>
								<text class="price-value">{{ meditation.price }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getMeditationList, getMeditationsByCategory } from '@/api/meditation'

// 响应式数据
const meditationList = ref([])
const featuredList = ref([])
const dailyList = ref([])
const currentCategory = ref(0)

// 分类数据
const categories = ref([
	{ name: '全部', value: '' },
	{ name: '专注', value: 'focus' },
	{ name: '放松', value: 'relax' },
	{ name: '睡眠', value: 'sleep' },
	{ name: '减压', value: 'stress' },
	{ name: '冥想', value: 'meditation' }
])

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'

// 方法
const switchCategory = (index) => {
	currentCategory.value = index
	loadMeditationList()
}

const loadMeditationList = async () => {
	try {
		const params = { page: 1, pageSize: 20 }

		// 添加分类筛选
		if (currentCategory.value > 0) {
			params.category = categories.value[currentCategory.value].value
		}

		const res = await getMeditationList(params)
		if (res.code === 200) {
			meditationList.value = res.data || []

			// 分离推荐和每日冥想
			featuredList.value = meditationList.value.filter(item => item.isFeatured).slice(0, 5)
			dailyList.value = meditationList.value.filter(item => item.isDaily).slice(0, 10)
		}
	} catch (error) {
		console.error('加载冥想列表失败:', error)
	}
}

const toDetail = (item) => {
	uni.navigateTo({
		url: `/pages/meditation/detail/index?id=${item.id}`
	})
}

const toCategory = (category) => {
	// 切换到对应分类
	const index = categories.value.findIndex(cat => cat.value === category.value)
	if (index > -1) {
		switchCategory(index)
	}
}

// 生命周期
onLoad(() => {
	loadMeditationList()
})
</script>

<style lang="scss" scoped>
.meditation-container {
	min-height: 100vh;
	background-color: #f8f8f8;
	padding-bottom: 40rpx;
}

.category-filter {
	background-color: #fff;
	padding: 24rpx 0;
	border-bottom: 1px solid #eee;

	.category-scroll {
		white-space: nowrap;

		.category-item {
			display: inline-block;
			padding: 12rpx 32rpx;
			margin: 0 16rpx;
			font-size: 28rpx;
			color: #666;
			border-radius: 40rpx;
			background-color: #f5f5f5;
			transition: all 0.3s;

			&.active {
				color: #fff;
				background-color: #ff6b35;
			}

			&:first-child {
				margin-left: 32rpx;
			}

			&:last-child {
				margin-right: 32rpx;
			}
		}
	}
}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin: 30rpx;
	}
	
	.meditation-section {
		padding: 32rpx;

		.meditation-list {
			.meditation-item {
				display: flex;
				background-color: #fff;
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 16rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

				.meditation-cover {
					position: relative;
					width: 160rpx;
					height: 120rpx;
					border-radius: 12rpx;
					overflow: hidden;
					margin-right: 24rpx;

					image {
						width: 100%;
						height: 100%;
					}

					.meditation-duration {
						position: absolute;
						bottom: 8rpx;
						right: 8rpx;
						padding: 4rpx 8rpx;
						background-color: rgba(0, 0, 0, 0.6);
						color: #fff;
						font-size: 20rpx;
						border-radius: 8rpx;
					}

					.free-tag {
						position: absolute;
						top: 8rpx;
						left: 8rpx;
						padding: 4rpx 8rpx;
						background-color: #4CAF50;
						color: #fff;
						font-size: 20rpx;
						border-radius: 8rpx;
					}
				}

				.meditation-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.meditation-title {
						font-size: 32rpx;
						font-weight: 600;
						color: #333;
						margin-bottom: 8rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.meditation-subtitle {
						font-size: 26rpx;
						color: #666;
						margin-bottom: 16rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.meditation-meta {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.meditation-stats {
							display: flex;
							gap: 16rpx;
							font-size: 24rpx;
							color: #999;

							.rating {
								color: #ff6b35;
							}
						}

						.meditation-price {
							display: flex;
							align-items: baseline;

							.price-symbol {
								font-size: 24rpx;
								color: #ff6b35;
							}

							.price-value {
								font-size: 32rpx;
								font-weight: 600;
								color: #ff6b35;
							}
						}
					}
				}
			}
		}
	}
</style> 