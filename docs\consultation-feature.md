# 咨询功能实现文档

## 功能概述

本项目已完成咨询系统的前端实现，包括咨询预约、订单管理、咨询记录、评价等完整流程，与后端API完全对接。

## 后端API接口

后端已实现的咨询相关接口：

### 订单管理
- `POST /miniapp/consultation/order` - 创建咨询订单
- `GET /miniapp/consultation/orders` - 获取用户订单列表
- `GET /miniapp/consultation/order/{id}` - 获取订单详情
- `POST /miniapp/consultation/order/cancel/{id}` - 取消订单
- `GET /miniapp/consultation/checkAvailable` - 检查时间段是否可预约

### 咨询记录
- `GET /miniapp/consultation/records` - 获取用户咨询记录列表
- `GET /miniapp/consultation/record/{id}` - 获取咨询记录详情
- `GET /miniapp/consultation/history/{consultantId}` - 获取与咨询师的咨询历史

### 评价系统
- `POST /miniapp/consultation/review` - 提交咨询评价
- `GET /miniapp/consultation/reviews` - 获取用户评价列表
- `GET /miniapp/consultation/consultant/reviews/{consultantId}` - 获取咨询师评价列表
- `GET /miniapp/consultation/canRate/{recordId}` - 检查是否可以评价

### 统计信息
- `GET /miniapp/consultation/statistics` - 获取用户咨询统计

## 前端功能模块

### 1. 咨询预约页面 (`pages/consultation/order/index.vue`)
- **功能**: 创建咨询预约订单
- **特性**:
  - 咨询师信息展示（头像、姓名、职称、评分、价格）
  - 咨询类型选择（心理咨询、情感咨询、职场咨询、家庭咨询）
  - 预约时间选择（日期 + 时间段）
  - 咨询时长选择（60/90/120分钟）
  - 问题描述输入
  - 费用明细展示（咨询费 + 服务费）
  - 预约须知说明
  - 集成支付弹框

### 2. 我的咨询页面 (`pages/consultation/my-consultations/index.vue`)
- **功能**: 管理用户的咨询相关信息
- **特性**:
  - 咨询统计（总咨询次数、已完成、总时长）
  - 三个标签页筛选：
    - **我的预约**: 显示订单列表，支持取消预约、立即支付
    - **咨询记录**: 显示咨询历史，支持评价咨询
    - **我的评价**: 显示已提交的评价
  - 下拉刷新功能
  - 空状态引导

### 3. 咨询订单详情页 (`pages/consultation/order-detail/index.vue`)
- **功能**: 查看订单详细信息
- **特性**:
  - 订单状态展示（待支付、已支付、已确认、已完成、已取消）
  - 咨询师信息和联系功能
  - 预约信息详情
  - 订单信息详情
  - 状态相关操作按钮（取消预约、立即支付、进入咨询室）

### 4. 咨询评价页面 (`pages/consultation/review/index.vue`)
- **功能**: 提交咨询评价
- **特性**:
  - 咨询信息回顾
  - 星级评分（1-5星）
  - 评价标签选择（专业耐心、效果显著、沟通顺畅等）
  - 评价内容输入（最少10字，最多500字）
  - 匿名评价选项
  - 推荐选项

## API接口文件 (`api/consultation.js`)

已实现的前端API接口方法：

```javascript
// 订单相关
createConsultationOrder(data)      // 创建咨询订单
getUserOrders()                    // 获取用户订单列表
getOrderDetails(id)                // 获取订单详情
cancelOrder(id, cancelReason)      // 取消订单
checkTimeAvailable(params)         // 检查时间段是否可预约

// 咨询记录
getUserRecords()                   // 获取用户咨询记录列表
getRecordDetails(id)               // 获取咨询记录详情
getConsultationHistory(consultantId) // 获取与咨询师的咨询历史

// 评价相关
submitConsultationReview(data)     // 提交咨询评价
getUserReviews()                   // 获取用户评价列表
getConsultantReviews(consultantId) // 获取咨询师评价列表
checkCanRate(recordId)             // 检查是否可以评价

// 统计信息
getUserStatistics()                // 获取用户咨询统计
```

## 页面路由配置

已在 `pages.json` 中添加的路由：

```json
{
  "path": "pages/consultation/order/index",
  "style": { "navigationBarTitleText": "预约咨询" }
},
{
  "path": "pages/consultation/my-consultations/index",
  "style": { "navigationBarTitleText": "我的咨询" }
},
{
  "path": "pages/consultation/order-detail/index",
  "style": { "navigationBarTitleText": "订单详情" }
},
{
  "path": "pages/consultation/review/index",
  "style": { "navigationBarTitleText": "咨询评价" }
}
```

## 功能入口

### 1. 咨询师详情页入口
- 修改了咨询师详情页面的底部按钮
- 将"立即咨询"改为"预约咨询"
- 点击跳转到咨询预约页面，传递咨询师信息

### 2. 我的页面入口
- 在"我的"页面添加了"我的咨询"选项
- 可查看所有咨询相关信息和操作

## 技术特点

### 1. 完整的业务流程
- **预约流程**: 选择咨询师 → 填写预约信息 → 支付 → 确认预约
- **咨询流程**: 预约确认 → 进入咨询室 → 完成咨询 → 评价
- **管理流程**: 查看订单 → 管理记录 → 查看评价

### 2. 状态管理
- 订单状态：待支付、已支付、已确认、已完成、已取消
- 咨询记录状态：已预约、进行中、已完成、已取消
- 相应的状态样式和操作按钮

### 3. 数据验证
- 预约时间冲突检查
- 表单数据完整性验证
- 评价权限验证

### 4. 用户体验
- 支付集成（使用统一支付弹框）
- 下拉刷新和错误处理
- 加载状态提示
- 空状态引导

## 数据结构

### 咨询订单数据
```javascript
{
  id: "订单ID",
  orderNo: "订单编号",
  consultantId: "咨询师ID",
  consultantName: "咨询师姓名",
  consultationType: "咨询类型",
  appointmentTime: "预约时间",
  duration: "咨询时长",
  problemDescription: "问题描述",
  paymentAmount: "支付金额",
  status: "订单状态",
  createTime: "创建时间"
}
```

### 咨询记录数据
```javascript
{
  id: "记录ID",
  consultantId: "咨询师ID",
  consultantName: "咨询师姓名",
  consultationType: "咨询类型",
  startTime: "开始时间",
  endTime: "结束时间",
  duration: "实际时长",
  status: "记录状态",
  hasReviewed: "是否已评价"
}
```

### 评价数据
```javascript
{
  id: "评价ID",
  recordId: "咨询记录ID",
  consultantId: "咨询师ID",
  rating: "评分",
  content: "评价内容",
  tags: "评价标签",
  anonymous: "是否匿名",
  recommend: "是否推荐",
  reviewTime: "评价时间"
}
```

## 使用流程

1. **浏览咨询师**: 分类页面 → 咨询师详情
2. **预约咨询**: 预约咨询 → 填写信息 → 支付订单
3. **管理咨询**: 我的咨询 → 查看订单/记录/评价
4. **参与咨询**: 订单详情 → 进入咨询室 → 完成咨询
5. **评价咨询**: 咨询记录 → 评价咨询 → 提交评价

## 设计原则遵循

✅ **不使用自定义导航栏** - 所有页面都使用小程序自带导航栏
✅ **集成支付弹框** - 咨询预约页面使用统一的半屏式支付弹框
✅ **支付成功弹窗** - 支付成功使用弹窗提示而非独立页面
✅ **API接口规范** - 使用专门的API接口文件，不使用原生HTTP调用

## 总结

咨询功能已完整实现，包含了从咨询师选择到咨询完成评价的全流程。所有功能都与后端API完全对接，界面设计优雅，用户体验良好，可以直接投入使用。特别是预约流程的设计和订单状态管理，为用户提供了清晰的咨询服务体验。
