<template>
	<view class="content">
		<!-- <view class="consultant"> -->
		<!-- <view class="title">咨询人</view>
			<view class="visitor-list" v-if="consultantList.length > 0">
				<view 
					v-for="item in consultantList" 
					:key="item.id"
					:class="['visitor-item', { active: selectedConsultant?.id === item.id }]"
					@click="selectedConsultant = item">
					<image 
						:src="item.avatar || userStore.userAvatar" 
						mode="aspectFill" 
						class="avatar">
					</image>
					<text class="name">{{ item.counselorName }}</text>
				</view>
				<view class="visitor-item plus" @click="handleAddAccount">
					<uni-icons color="#666b74" type="plusempty" size="30"></uni-icons>
					<text>新增账户</text>
				</view>
			</view>
			<view class="visitor" v-else>
				<view class="plus" @click="handleAddAccount">
					<uni-icons color="#666b74" type="plusempty" size="30"></uni-icons>
					<text>新增账户</text>
				</view>
			</view> -->
		<!-- </view> -->
		<view class="consultant">
			<view class="title">咨询类型</view>
			<view class="consultation-box">
				<view @click="handleSelectType('individual')" class="consultation-type"
					:class="{ individual: consultationType == 'individual' }">
					个体咨询(60分钟/节)
				</view>
				<view @click="handleSelectType('family')" class="consultation-type"
					:class="{ individual: consultationType == 'family' }">
					家庭咨询(120分钟/节)
				</view>
			</view>
		</view>
		<view class="consultant">
			<view class="title">咨询方式</view>
			<view class="consultation-box">
				<view @click="handleConsultationMethod('interview')" class="consultation-type"
					:class="{ 'consultation-method': consultationMethod == 'interview' }">
					面询
				</view>
				<view @click="handleConsultationMethod('video')" class="consultation-type"
					:class="{ 'consultation-method': consultationMethod == 'video' }">
					视频咨询
				</view>
			</view>
		</view>
		<view class="consultant">
			<view class="title">可约时间</view>
			<view class="msg">如暂不确定具体咨询时间，可在下单后与咨询师协商时间</view>
			<view class="make-appointment-box">
				<view class="scroll">
					<scroll-view class="scroll-view_H" scroll-x @scroll="scroll">
						<view :class="{ 'is-active': item.isToday }" class="temp-box" v-for="(item, index) in dateInfo"
							:key="item.data" @click="handleSelectTime(item, index)">
							<view class="time-date-week">
								{{ item.weekDay }}
							</view>
							<view class="time-date-temp" />
							<view class="border-box">
								<view class="time-date-month">{{ item.shortDate }}</view>
							</view>
						</view>
					</scroll-view>
					<view class="hour-box">
						<view class="morning" v-for="item in timeInfo" :key="item.id">
							<view class="morning-box">
								<image mode="scaleToFill"
									:src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/${encodeURIComponent(item.rangeName)}.png`">
								</image>
								{{ item.rangeName }}
							</view>
							<view class="morning-time-box">
								<view :class="{
									'is-active': selectTime.indexOf(item1.fullDate + item1.time) !== -1,
									'is-disabled': item1.timeStatus == '已过期',
								}" class="time" v-for="item1 in item.slots" :key="item1.id" @click="handleHour(item1)">
									{{ item1.time }}
								</view>
							</view>
						</view>
					</view>
					<view class="not-select" @click="handleHour">
						<label class="radio" @click="handleHour">
							暂不选择，下单后与咨询师协商时间
							<radio value="r1" :checked="selectTime == 'notSelect'" @click="handleHour" />
						</label>
					</view>
					<view class="scroll-bottom">
						<button class="scroll-bottom-btn" type="primary" @click="handleSelect">选好了</button>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="">共计: ¥ {{ totalPrice }}</view>
			<view class="consult">
				<button open-type="getUserInfo" @click="handleConsultNow">提交订单</button>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getCounselorTime } from "@/api/common.js";
import { getUserIdInfo } from "@/api/my.js";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();

// 用户类型开关
const isIndividual = ref(true);

// 咨询类型选择
const consultationType = ref("");
// 咨询方式选择
const consultationMethod = ref("");

// 日期和时间数据
const dateInfo = ref([]);
const timeInfo = ref([]);
const selectTime = ref([]);
const counselorId = ref(null);
const totalPrice = ref(0);

// 添加响应式数据
const consultantList = ref([]); // 咨询人列表
const selectedConsultant = ref(null); // 当前选中的咨询人

// 获取咨询师可约时间
const getTimeList = async (id) => {
	try {
		const { data } = await getCounselorTime(id);
		if (data && data.length > 0) {
			console.log('时间数据:', data); // 添加调试日志
			data[0].isToday = true;
			timeInfo.value = data[0].timeRanges;
			dateInfo.value = data;

			// 检查并处理时间状态
			timeInfo.value.forEach(range => {
				if (range.slots) {
					range.slots.forEach(slot => {
						// 检查所有可能的状态字段
						console.log('时间段状态:', slot.timeStatus, slot.status, slot.state);
					});
				}
			});
		}
	} catch (error) {
		console.error("获取可约时间失败:", error);
		uni.showToast({
			title: "获取可约时间失败",
			icon: "none"
		});
	}
};

// 获取咨询人信息
const getConsultantInfo = async () => {
	try {
		const { data } = await getUserIdInfo(userStore.profile.userId);
		if (data) {
			consultantList.value = [data];
			selectedConsultant.value = data;
		}
	} catch (error) {
		console.error("获取咨询人信息失败:", error);
	}
};

onLoad((options) => {
	counselorId.value = options.counselorId;

	// 如果有预选的时间，设置选中状态
	if (options.selectedTimes) {
		try {
			const decodedTimes = decodeURIComponent(options.selectedTimes);
			selectTime.value = JSON.parse(decodedTimes);
			console.log('解析后的选中时间:', selectTime.value);
		} catch (error) {
			console.error("解析预选时间失败:", error);
			selectTime.value = [];
		}
	} else {
		selectTime.value = [];
	}

	// 获取咨询人信息
	getConsultantInfo();

	if (counselorId.value) {
		getTimeList(counselorId.value);
	}
});

// 方法实现
const handleSelectType = (type) => {
	consultationType.value = type;
};

const handleConsultationMethod = (method) => {
	consultationMethod.value = method;
};

const handleSelectTime = (data, index) => {
	dateInfo.value.forEach((item) => (item.isToday = false));
	data.isToday = true;
	timeInfo.value = dateInfo.value[index].timeRanges;
};

const handleHour = (data) => {
	const fullTime = data.fullDate + data.time;
	console.log(data.timeStatus);

	if (data.timeStatus !== "已过期") {
		const index = selectTime.value.indexOf(fullTime);
		if (index === -1) {
			selectTime.value.push(fullTime);
		} else {
			selectTime.value.splice(index, 1);
		}
	} else {
		uni.showToast({
			title: "该时段已过期",
			icon: "none",
		});
	}
};

const handleSelect = () => {
	if (selectTime.value && (selectTime.value === "notSelect" || selectTime.value.length > 0)) {
		makeAppointmentPopup.value?.close();
	} else {
		uni.showToast({ title: "请选择预约时间", icon: "none" });
	}
};

const handleAddAccount = () => {
	uni.navigateTo({
		url: "/pages/my/my-detail/my-detail?from=order",
		events: {
			// 监听新增咨询人成功事件
			consultantAdded: (consultant) => {
				consultantList.value.push(consultant);
				selectedConsultant.value = consultant;
			}
		}
	});
};

const handleConsultNow = () => {
	if (!consultationType.value) {
		uni.showToast({
			title: "请选择咨询类型",
			icon: "none"
		});
		return;
	}

	if (!consultationMethod.value) {
		uni.showToast({
			title: "请选择咨询方式",
			icon: "none"
		});
		return;
	}

	if (!selectTime.value || (Array.isArray(selectTime.value) && selectTime.value.length === 0)) {
		uni.showToast({
			title: "请选择咨询时间",
			icon: "none"
		});
		return;
	}

	// TODO: 提交订单逻辑
	console.log("提交订单", {
		consultationType: consultationType.value,
		consultationMethod: consultationMethod.value,
		selectTime: selectTime.value,
		counselorId: counselorId.value
	});
};

const scroll = (e) => { };
</script>

<style lang="scss" scoped>
.content {
	.consultant {
		width: calc(100% - 40rpx);
		padding: 20rpx;

		.title {
			width: calc(100% - 40rpx);
			font-size: 36rpx;
			font-weight: 700;
			margin-top: 40rpx;
		}

		.visitor-list {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			padding: 20rpx;

			.visitor-item {
				width: 160rpx;
				height: 160rpx;
				border: 1px dashed #ccc;
				border-radius: 20rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;

				&.active {
					border: 2px solid #52b5f9;
					background-color: #eaf8ff;
				}

				&.plus {
					border-style: dashed;
					background-color: transparent;

					text {
						margin-top: 8rpx;
						color: #666b74;
						font-size: 24rpx;
					}
				}

				.avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 40rpx;
					margin-bottom: 10rpx;
				}

				.name {
					font-size: 26rpx;
					color: #333;
				}
			}
		}

		.visitor {
			width: 100%;
			height: 160rpx;
			border: 1px dashed #ccc;
			margin: 0 auto;
			margin-top: 20rpx;
			border-radius: 20rpx;

			.plus {
				display: flex;
				justify-content: center;
				flex-direction: column;
				margin-top: 14rpx;
				align-items: center;

				text {
					margin-top: 8rpx;
					text-align: center;
					color: #666b74;
				}
			}
		}

		.msg {
			font-size: 26rpx;
			margin-top: 20rpx;
		}

		.consultation-box {
			margin-top: 20px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.consultation-type {
				width: 48%;
				height: 100rpx;
				background-color: #f2f4f5;
				border-radius: 14rpx;
				text-align: center;
				line-height: 100rpx;
				font-size: 30rpx;
				color: #656a73;
				border: 2px solid #f2f4f5;
			}

			.individual {
				background-color: #eaf8ff;
				color: #01a6fe;
				border: 2px solid #01a6fe;
				font-weight: 700;
			}

			.consultation-method {
				background-color: #eaf8ff;
				color: #01a6fe;
				border: 2px solid #01a6fe;
				font-weight: 700;
			}
		}
	}

	.make-appointment-box {
		margin-top: 20rpx;

		.make-appointment-top {
			position: relative;
			background: linear-gradient(to bottom, #e3f3ff, #fff);
			border-radius: 30rpx 30rpx 0 0;
		}

		.scroll {
			.scroll-view_H {
				white-space: nowrap;

				.temp-box {
					width: 140rpx;
					height: 100rpx;
					display: inline-block;
					margin: 0 10rpx 10rpx;
					position: relative;

					&:nth-child(1) {
						// margin-left: 40rpx;
					}

					.border-box {
						border: 1px solid #ccc;
						position: absolute;
						bottom: 0;
						height: 80rpx;
						z-index: 1;
						background-color: #fff;
						width: 100%;
						display: flex;
						align-items: flex-end;
						justify-content: center;
						font-size: 26rpx;
						border-radius: 10rpx;
					}

					.time-date-temp {
						width: 100rpx;
						height: 20rpx;
						position: absolute;
						border-radius: 10rpx;
						top: 8rpx;
						left: 50%;
						background-color: #cacaca;
						transform: translateX(-50%);
					}

					.time-date-week {
						background-color: #f7f7f7;
						position: absolute;
						border-radius: 6rpx;
						top: 8rpx;
						left: 50%;
						transform: translateX(-50%);
						z-index: 2;
						padding: 0 16rpx;
						color: #424242;
						font-size: 26rpx;
						border: 1px solid #e1e1e1;
					}

					.time-date-month {
						z-index: 2;
						text-align: center;
						margin-bottom: 10rpx;
					}
				}

				.is-active {
					.border-box {
						border: 1px solid #5fbaf9;
					}

					.time-date-temp,
					.time-date-week {
						background-color: #52b5f9;
						border: 1px solid #52b5f9;
						color: #fff;
					}
				}

				.is-disabled {
					.border-box {
						border: 1px solid #efefef;
						color: #dcdada;
					}

					.time-date-temp,
					.time-date-week {
						background-color: #f7f7f7;
						border: 1px solid #efefef;
						color: #dcdada;
					}
				}
			}

			.hour-box {
				width: 100%;
				// margin: 40rpx;
				// height: 200px;
				border: 1px solid #ccc;
				border-radius: 10rpx;
				background-color: #f9f9f9;

				image {
					margin-right: 10rpx;
					width: 40rpx;
					height: 40rpx;
				}

				.morning {
					padding: 20rpx;

					.morning-box {
						color: #262626;
						font-size: 28rpx;
						margin-bottom: 10rpx;
						display: flex;
						align-items: center;
					}

					.morning-time-box {
						display: flex;
						flex-wrap: wrap;
						width: 100%;

						.time {
							width: calc(33.333% - 52rpx);
							font-size: 28rpx;
							background-color: #fff;
							padding: 10rpx;
							margin-right: 20rpx;
							margin-bottom: 10rpx;
							text-align: center;
							border-radius: 10rpx;
							border: 1px solid #f9f9f9;
						}

						.is-active {
							border: 1px solid #52b5f9;
							position: relative;

							&:after {
								content: "";
								position: absolute;
								bottom: 0;
								right: 0;
								width: 30rpx;
								height: 20rpx;
								background-color: #fff;
								background-image: url(https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%AF%B9%E5%8B%BE.png);
								background-size: 120% 120%;
								// border-right: 5rpx solid #01a6fe;
								// transform: scale(1.4);
								border-bottom-right-radius: 10rpx;
							}
						}
					}
				}
			}

			.not-select {
				width: 100%;
				text-align: center;
				margin-bottom: 20rpx;
				margin-top: 20rpx;

				:deep .uni-radio-input {
					width: 30rpx !important;
					height: 30rpx !important;
				}
			}

			.scroll-bottom {
				width: 100%;
				height: 80rpx;
				background-color: #fff;
				margin-bottom: 20rpx;

				.scroll-bottom-btn {
					width: 80%;
					height: 100%;
					background-color: #52b5f9;
					border-radius: 100rpx;
					line-height: 80rpx;
					font-size: 30rpx;
				}
			}

			.time-box {
				width: 200rpx;
				height: 120rpx;
				position: relative;
				display: inline-block;

				&:after {
					content: "";
					position: absolute;
					right: 30rpx;
					height: 40rpx;
					width: 2rpx;
					background-color: #eceeef;
					z-index: 1;
					top: 50%;
					transform: translateY(-50%);
				}

				&:nth-last-child(1) {
					&:after {
						content: "";
					}
				}

				.time-top {
					color: #999da6;
				}

				.time-bottom {
					font-size: 36rpx;
					font-weight: 700;

					text {
						font-size: 24rpx;
						margin-left: -5px;
					}
				}
			}
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: calc(100% - 40rpx);
		height: 120rpx;
		background-color: #fff;
		z-index: 9;
		margin-top: 100rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.consult {
			width: calc(60%);

			button {
				background-color: #52b5f9;
				border: none !important;
				color: #fff;
				font-weight: 700;
				border-radius: 70rpx;
			}
		}
	}
}

.morning-time-box {
	.time {
		&.is-disabled {
			background-color: #f7f7f7 !important;
			color: #dcdada !important;
			border: 1px solid #efefef !important;
			pointer-events: none;
		}
	}
}
</style>
