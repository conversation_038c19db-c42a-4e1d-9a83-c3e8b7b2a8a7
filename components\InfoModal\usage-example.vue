<template>
  <view class="usage-example">
    <view class="page-title">InfoModal 快速使用示例</view>
    
    <view class="section">
      <view class="section-title">预设配置使用</view>
      <view class="button-grid">
        <button 
          v-for="type in availableTypes" 
          :key="type.key"
          class="config-btn"
          @click="showPresetInfo(type.key)"
        >
          {{ type.name }}
        </button>
      </view>
    </view>

    <view class="section">
      <view class="section-title">自定义配置使用</view>
      <button class="custom-btn" @click="showCustomInfo">自定义信息</button>
    </view>

    <!-- 信息弹窗组件 -->
    <InfoModal
      ref="infoModal"
      :title="currentConfig.title"
      :sections="currentConfig.sections"
      :notice="currentConfig.notice"
      :show-button="currentConfig.showButton"
      :button-text="currentConfig.buttonText"
      :button-type="currentConfig.buttonType"
      @close="handleClose"
      @button-click="handleButtonClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import InfoModal from './InfoModal.vue'
import { 
  getInfoConfig, 
  getAvailableTypes, 
  createInfoConfig 
} from './config.js'

const infoModal = ref(null)
const currentConfig = ref({})
const availableTypes = getAvailableTypes()

// 显示预设配置信息
const showPresetInfo = (type) => {
  const config = getInfoConfig(type)
  if (config) {
    currentConfig.value = config
    infoModal.value?.open()
  }
}

// 显示自定义信息
const showCustomInfo = () => {
  const customConfig = createInfoConfig({
    title: '自定义标题',
    sections: [
      {
        title: '第一部分',
        content: '这是第一部分的内容，可以包含任何您想要展示的信息。'
      },
      {
        title: '第二部分',
        content: '这是第二部分的内容，展示了如何使用自定义配置来创建信息弹窗。'
      },
      {
        content: '这是没有标题的内容区块，直接显示文本内容。'
      }
    ],
    notice: '这是一个自定义的注意事项提示。',
    showButton: true,
    buttonText: '自定义按钮',
    buttonType: 'secondary'
  })
  
  currentConfig.value = customConfig
  infoModal.value?.open()
}

// 处理关闭事件
const handleClose = () => {
  console.log('弹窗关闭')
}

// 处理按钮点击事件
const handleButtonClick = () => {
  console.log('按钮点击:', currentConfig.value.buttonText)
  
  // 根据不同配置执行不同操作
  switch (currentConfig.value.title) {
    case '测评须知':
      uni.showToast({
        title: '开始测评',
        icon: 'success'
      })
      break
    case '咨询须知':
      uni.showToast({
        title: '预约咨询',
        icon: 'success'
      })
      break
    case '冥想课程':
      uni.showToast({
        title: '开始学习',
        icon: 'success'
      })
      break
    case '服务条款':
      uni.showToast({
        title: '已同意条款',
        icon: 'success'
      })
      break
    case '隐私政策':
      uni.showToast({
        title: '已阅读政策',
        icon: 'success'
      })
      break
    case '购买须知':
      uni.showToast({
        title: '确认购买',
        icon: 'success'
      })
      break
    default:
      uni.showToast({
        title: '操作完成',
        icon: 'success'
      })
  }
  
  // 关闭弹窗
  infoModal.value?.close()
}
</script>

<style lang="scss" scoped>
.usage-example {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 60rpx;
}

.section {
  margin-bottom: 60rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #666;
    margin-bottom: 32rpx;
    text-align: center;
  }
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.config-btn {
  height: 80rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 3rpx 10rpx rgba(102, 126, 234, 0.2);
  }
}

.custom-btn {
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
  margin: 0 auto;
  display: block;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 3rpx 10rpx rgba(255, 107, 53, 0.2);
  }
}
</style>
