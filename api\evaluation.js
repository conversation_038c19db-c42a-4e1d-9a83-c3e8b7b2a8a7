import request from '@/utils/request'

// ==================== 量表浏览相关接口 ====================

/**
 * 查询启用的量表列表
 * @param {Object} query - 查询参数 {pageNum, pageSize}
 */
export function listAssessment(query = {}) {
  return request({
    url: '/miniapp/user/assessment/scales',
    method: 'get',
    params: query
  })
}

/**
 * 查询热门量表
 * @param {Number} limit - 限制数量，默认10
 */
export function getPopularAssessments(limit = 10) {
  return request({
    url: '/miniapp/user/assessment/scales/hot',
    method: 'get',
    params: { limit }
  })
}

/**
 * 查询最新量表
 * @param {Number} limit - 限制数量，默认10
 */
export function getLatestAssessments(limit = 10) {
  return request({
    url: '/miniapp/user/assessment/scales/latest',
    method: 'get',
    params: { limit }
  })
}

/**
 * 根据分类查询量表
 * @param {Number} categoryId - 分类ID
 */
export function getAssessmentsByCategory(categoryId) {
  return request({
    url: `/miniapp/user/assessment/scales/category/${categoryId}`,
    method: 'get'
  })
}

/**
 * 搜索量表
 * @param {Object} params - 搜索参数 {keyword, categoryId, pageNum, pageSize}
 */
export function searchAssessments(params = {}) {
  return request({
    url: '/miniapp/user/assessment/scales/search',
    method: 'get',
    params
  })
}

/**
 * 获取量表详情
 * @param {Number} id - 量表ID
 */
export function getAssessment(id) {
  return request({
    url: `/miniapp/user/assessment/scales/${id}`,
    method: 'get'
  })
}

/**
 * 查询用户收藏的量表
 * @param {Number} userId - 用户ID
 */
export function getFavoriteAssessments(userId) {
  return request({
    url: `/miniapp/user/assessment/favorites/${userId}`,
    method: 'get'
  })
}

/**
 * 查询相似量表推荐
 * @param {Number} scaleId - 量表ID
 * @param {Number} limit - 限制数量，默认5
 */
export function getRecommendedAssessments(scaleId, limit = 5) {
  return request({
    url: `/miniapp/user/assessment/recommendations/${scaleId}`,
    method: 'get',
    params: { limit }
  })
}

// ==================== 测评流程相关接口 ====================

/**
 * 检查用户是否可以开始测评
 * @param {Number} scaleId - 量表ID
 */
export function checkCanStartAssessment(scaleId) {
  return request({
    url: '/miniapp/user/assessment/check-can-start',
    method: 'get',
    params: { scaleId }
  })
}

/**
 * 开始测评
 * @param {Number} scaleId - 量表ID
 */
export function startAssessment(scaleId) {
  return request({
    url: '/miniapp/user/assessment/start',
    method: 'post',
    params: { scaleId }
  })
}

/**
 * 获取测评题目
 * @param {Number} recordId - 测评记录ID
 */
export function getAssessmentQuestions(recordId) {
  return request({
    url: `/miniapp/user/assessment/questions/${recordId}`,
    method: 'get'
  })
}

/**
 * 获取下一题
 * @param {Number} recordId - 测评记录ID
 */
export function getNextQuestion(recordId) {
  return request({
    url: `/miniapp/user/assessment/next-question/${recordId}`,
    method: 'get'
  })
}

/**
 * 获取上一题
 * @param {Number} recordId - 测评记录ID
 */
export function getPreviousQuestion(recordId) {
  return request({
    url: `/miniapp/user/assessment/previous-question/${recordId}`,
    method: 'get'
  })
}

/**
 * 保存答题记录
 * @param {Number} recordId - 测评记录ID
 * @param {Number} questionId - 题目ID
 * @param {Number} optionId - 选项ID（单选/多选题必填）
 * @param {String} answerContent - 答案内容（文本题必填）
 * @param {Number} responseTime - 答题耗时（秒）
 */
export function saveAnswerRecord(recordId, questionId, optionId, answerContent, responseTime) {
  return request({
    url: '/miniapp/user/assessment/answer',
    method: 'post',
    data: {
      recordId,
      questionId,
      optionId,
      answerContent,
      responseTime
    }
  })
}

/**
 * 查询答题进度
 * @param {Number} recordId - 测评记录ID
 */
export function getAssessmentProgress(recordId) {
  return request({
    url: `/miniapp/user/assessment/progress/${recordId}`,
    method: 'get'
  })
}

/**
 * 获取答题历史记录
 * @param {Number} recordId - 测评记录ID
 */
export function getAssessmentAnswers(recordId) {
  return request({
    url: `/miniapp/user/assessment/answers/${recordId}`,
    method: 'get'
  })
}

/**
 * 获取测评详细信息
 * @param {Number} recordId - 测评记录ID
 */
export function getAssessmentDetail(recordId) {
  return request({
    url: `/miniapp/user/assessment/detail/${recordId}`,
    method: 'get'
  })
}

/**
 * 暂停测评
 * @param {Number} recordId - 测评记录ID
 */
export function pauseAssessment(recordId) {
  return request({
    url: `/miniapp/user/assessment/pause/${recordId}`,
    method: 'post'
  })
}

/**
 * 恢复测评
 * @param {Number} recordId - 测评记录ID
 */
export function resumeAssessment(recordId) {
  return request({
    url: `/miniapp/user/assessment/resume/${recordId}`,
    method: 'post'
  })
}

/**
 * 完成测评
 * @param {Number} recordId - 测评记录ID
 */
export function completeAssessment(recordId) {
  return request({
    url: `/miniapp/user/assessment/complete/${recordId}`,
    method: 'post'
  })
}

/**
 * 取消测评
 * @param {Number} recordId - 测评记录ID
 */
export function cancelAssessment(recordId) {
  return request({
    url: `/miniapp/user/assessment/cancel/${recordId}`,
    method: 'post'
  })
}

// ==================== 测评结果相关接口 ====================

/**
 * 查询测评结果
 * @param {Number} recordId - 测评记录ID
 */
export function getAssessmentResult(recordId) {
  return request({
    url: `/miniapp/user/assessment/result/${recordId}`,
    method: 'get'
  })
}

/**
 * 生成测评报告
 * @param {Number} recordId - 测评记录ID
 */
export function generateAssessmentReport(recordId) {
  return request({
    url: `/miniapp/user/assessment/report/${recordId}`,
    method: 'get'
  })
}

// ==================== 测评记录管理接口 ====================

/**
 * 查询用户的测评记录
 */
export function getAssessmentRecords() {
  return request({
    url: '/miniapp/user/assessment/records',
    method: 'get'
  })
}

/**
 * 查询用户最近的测评记录
 * @param {Number} limit - 限制数量，默认10
 */
export function getRecentAssessmentRecords(limit = 10) {
  return request({
    url: '/miniapp/user/assessment/records/recent',
    method: 'get',
    params: { limit }
  })
}

/**
 * 查询用户未完成的测评记录
 */
export function getIncompleteAssessmentRecords() {
  return request({
    url: '/miniapp/user/assessment/records/incomplete',
    method: 'get'
  })
}

/**
 * 查询用户已完成的测评记录
 */
export function getCompletedAssessmentRecords() {
  return request({
    url: '/miniapp/user/assessment/records/completed',
    method: 'get'
  })
}

/**
 * 查询用户测评统计
 */
export function getAssessmentStats() {
  return request({
    url: '/miniapp/user/assessment/stats',
    method: 'get'
  })
}

// ==================== 测评订单相关接口 ====================

/**
 * 查询用户订单列表
 * @param {Object} query - 查询参数
 */
export function getAssessmentOrderList(query = {}) {
  return request({
    url: '/miniapp/user/order/list',
    method: 'get',
    params: query
  })
}

/**
 * 查询订单详情
 * @param {Number} id - 订单ID
 */
export function getAssessmentOrderDetail(id) {
  return request({
    url: `/miniapp/user/order/${id}`,
    method: 'get'
  })
}

/**
 * 创建订单
 * @param {Number} scaleId - 量表ID
 */
export function createAssessmentOrder(scaleId) {
  return request({
    url: '/miniapp/user/order/create',
    method: 'post',
    data: { scaleId }
  })
}

/**
 * 支付订单
 * @param {String} orderNo - 订单号
 * @param {String} paymentMethod - 支付方式
 */
export function payAssessmentOrder(orderNo, paymentMethod = 'WECHAT') {
  return request({
    url: '/miniapp/user/order/pay',
    method: 'post',
    data: {
      orderNo,
      paymentMethod
    }
  })
}

/**
 * 取消订单
 * @param {String} orderNo - 订单号
 * @param {String} cancelReason - 取消原因
 */
export function cancelAssessmentOrder(orderNo, cancelReason) {
  return request({
    url: '/miniapp/user/order/cancel',
    method: 'post',
    data: {
      orderNo,
      cancelReason
    }
  })
}

/**
 * 申请退款
 * @param {String} orderNo - 订单号
 * @param {String} refundReason - 退款原因
 */
export function refundAssessmentOrder(orderNo, refundReason) {
  return request({
    url: '/miniapp/user/order/refund',
    method: 'post',
    data: {
      orderNo,
      refundReason
    }
  })
}

/**
 * 查询待支付订单
 */
export function getPendingAssessmentOrders() {
  return request({
    url: '/miniapp/user/order/pending',
    method: 'get'
  })
}

/**
 * 查询已支付订单
 */
export function getPaidAssessmentOrders() {
  return request({
    url: '/miniapp/user/order/paid',
    method: 'get'
  })
}

// ==================== 测评评价相关接口 ====================

/**
 * 提交测评评价
 * @param {Object} data - 评价数据
 */
export function submitAssessmentReview(data) {
  return request({
    url: '/miniapp/user/assessment/review',
    method: 'post',
    data
  })
}

/**
 * 获取量表评价列表
 * @param {Number} scaleId - 量表ID
 * @param {Object} query - 查询参数
 */
export function getScaleReviews(scaleId, query = {}) {
  return request({
    url: `/miniapp/user/assessment/reviews/${scaleId}`,
    method: 'get',
    params: query
  })
}

/**
 * 获取用户评价列表
 */
export function getUserAssessmentReviews() {
  return request({
    url: '/miniapp/user/assessment/reviews/user',
    method: 'get'
  })
}

/**
 * 获取评价详情
 * @param {Number} id - 评价ID
 */
export function getAssessmentReviewDetail(id) {
  return request({
    url: `/miniapp/user/assessment/review/${id}`,
    method: 'get'
  })
}

/**
 * 根据测评记录获取评价
 * @param {Number} recordId - 测评记录ID
 */
export function getAssessmentReviewByRecord(recordId) {
  return request({
    url: `/miniapp/user/assessment/review/record/${recordId}`,
    method: 'get'
  })
}

/**
 * 检查用户是否可以评价
 * @param {Number} scaleId - 量表ID
 * @param {Number} recordId - 测评记录ID
 */
export function checkReviewPermission(scaleId, recordId) {
  return request({
    url: '/miniapp/user/assessment/review/check',
    method: 'get',
    params: {
      scaleId,
      recordId
    }
  })
}

/**
 * 获取量表评价统计
 * @param {Number} scaleId - 量表ID
 */
export function getAssessmentReviewStats(scaleId) {
  return request({
    url: `/miniapp/user/assessment/review/stats/${scaleId}`,
    method: 'get'
  })
}

/**
 * 获取用户评价统计
 */
export function getUserAssessmentReviewStats() {
  return request({
    url: '/miniapp/user/assessment/review/stats/user',
    method: 'get'
  })
}

/**
 * 获取评价摘要
 * @param {Number} scaleId - 量表ID
 */
export function getAssessmentReviewSummary(scaleId) {
  return request({
    url: `/miniapp/user/assessment/review/summary/${scaleId}`,
    method: 'get'
  })
}

/**
 * 获取热门评价
 * @param {Number} limit - 限制数量，默认10
 */
export function getHotAssessmentReviews(limit = 10) {
  return request({
    url: '/miniapp/user/assessment/review/hot',
    method: 'get',
    params: { limit }
  })
}

/**
 * 搜索评价
 * @param {String} keyword - 搜索关键词
 * @param {Number} scaleId - 量表ID
 * @param {Number} rating - 评分筛选
 */
export function searchAssessmentReviews(keyword, scaleId, rating) {
  return request({
    url: '/miniapp/user/assessment/review/search',
    method: 'get',
    params: {
      keyword,
      scaleId,
      rating
    }
  })
}

// ==================== 兼容性函数 ====================

/**
 * 获取分类列表（兼容函数）
 */
export function getCategories() {
  return request({
    url: '/miniapp/assessment/categories',
    method: 'get'
  })
}

/**
 * 提交评价（兼容函数）
 * @param {Object} data - 评价数据
 */
export function submitReview(data) {
  return submitAssessmentReview(data)
}

/**
 * 获取题目列表（兼容函数）
 * @param {Number} assessmentId - 测评ID
 */
export function getQuestions(assessmentId) {
  return getAssessmentQuestions(assessmentId)
}

/**
 * 获取免费测评（兼容函数）
 */
export function getFreeAssessments() {
  return listAssessment({ payMode: 0 })
}

/**
 * 获取付费测评（兼容函数）
 */
export function getPaidAssessments() {
  return listAssessment({ payMode: 1 })
}