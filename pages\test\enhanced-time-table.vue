<template>
	<view class="test-page">
		<view class="header">
			<text class="title">增强版时间表组件测试</text>
		</view>
		
		<view class="section">
			<text class="section-title">基本使用</text>
			<EnhancedTimeTable
				ref="basicTimeTableRef"
				:showDateSelector="true"
				:dayRange="6"
				:showMessage="true"
				:showActionButtons="true"
				@timeChange="handleTimeChange"
				@intervalChange="handleIntervalChange"
				@confirm="handleConfirm"
				@dateChange="handleDateChange"
			/>
		</view>
		
		<view class="section">
			<text class="section-title">弹框模式（无日期选择器）</text>
			<EnhancedTimeTable
				ref="popupTimeTableRef"
				:showDateSelector="false"
				:showMessage="false"
				:showActionButtons="false"
				@timeChange="handlePopupTimeChange"
				@intervalChange="handlePopupIntervalChange"
			/>
		</view>
		
		<view class="controls">
			<button @click="getSelectedTimes" class="control-btn">获取选择的时间</button>
			<button @click="clearSelection" class="control-btn">清空选择</button>
			<button @click="refreshTimeList" class="control-btn">刷新时间列表</button>
		</view>
		
		<view class="result">
			<text class="result-title">选择结果：</text>
			<text class="result-content">{{ resultText }}</text>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import EnhancedTimeTable from '@/components/EnhancedTimeTable/EnhancedTimeTable.vue'

const basicTimeTableRef = ref(null)
const popupTimeTableRef = ref(null)
const resultText = ref('暂无选择')

// 基本模式事件处理
const handleTimeChange = (selectedTimes) => {
	console.log('基本模式 - 时间变化:', selectedTimes)
	updateResult('基本模式时间选择', selectedTimes)
}

const handleIntervalChange = (intervals) => {
	console.log('基本模式 - 区间变化:', intervals)
}

const handleConfirm = (data) => {
	console.log('基本模式 - 确认选择:', data)
	updateResult('基本模式确认', data.selectTime)
	uni.showToast({
		title: `选择了 ${data.selectTime.length} 个时间点`,
		icon: 'success'
	})
}

const handleDateChange = (dateItem, index) => {
	console.log('基本模式 - 日期变化:', dateItem, index)
}

// 弹框模式事件处理
const handlePopupTimeChange = (selectedTimes) => {
	console.log('弹框模式 - 时间变化:', selectedTimes)
	updateResult('弹框模式时间选择', selectedTimes)
}

const handlePopupIntervalChange = (intervals) => {
	console.log('弹框模式 - 区间变化:', intervals)
}

// 控制方法
const getSelectedTimes = () => {
	const basicTimes = basicTimeTableRef.value?.getSelectedTimes() || []
	const popupTimes = popupTimeTableRef.value?.getSelectedTimes() || []
	
	const result = {
		basic: basicTimes,
		popup: popupTimes
	}
	
	updateResult('获取所有选择', result)
	console.log('所有选择的时间:', result)
}

const clearSelection = () => {
	basicTimeTableRef.value?.clearSelection()
	popupTimeTableRef.value?.clearSelection()
	updateResult('清空选择', [])
	uni.showToast({
		title: '已清空所有选择',
		icon: 'success'
	})
}

const refreshTimeList = () => {
	basicTimeTableRef.value?.refreshTimeList()
	popupTimeTableRef.value?.refreshTimeList()
	uni.showToast({
		title: '已刷新时间列表',
		icon: 'success'
	})
}

// 更新结果显示
const updateResult = (action, data) => {
	const timestamp = new Date().toLocaleTimeString()
	resultText.value = `[${timestamp}] ${action}: ${JSON.stringify(data, null, 2)}`
}
</script>

<style lang="scss" scoped>
.test-page {
	padding: 20rpx;
	background-color: #f8f8fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

.section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	
	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}
}

.controls {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30rpx;
	gap: 20rpx;
	
	.control-btn {
		flex: 1;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #20a3f3;
		color: #fff;
		border-radius: 12rpx;
		font-size: 28rpx;
		border: none;
		
		&:active {
			opacity: 0.8;
		}
	}
}

.result {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	
	.result-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.result-content {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		white-space: pre-wrap;
		word-break: break-all;
	}
}
</style>
