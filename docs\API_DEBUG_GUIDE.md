# API调试指南

## 🐛 当前问题
```
TypeError: Cannot read property 'map' of undefined
```

## 🔍 调试步骤

### 1. 检查API响应
打开浏览器开发者工具，查看控制台输出：

```javascript
// 应该看到以下日志：
console.log('获取咨询师时间表，ID:', counselorId);
console.log('新接口API响应:', res);
console.log('咨询师时间表原始数据:', res.data);
console.log('数据类型:', typeof res.data);
console.log('是否为数组:', Array.isArray(res.data));
```

### 2. 可能的数据格式

#### 格式1: 标准格式（期望的）
```javascript
{
  code: 200,
  data: {
    dates: [
      {
        date: "2025-07-21",
        isToday: true,
        timeRanges: [...]
      }
    ]
  }
}
```

#### 格式2: 数组格式（旧接口）
```javascript
{
  code: 200,
  data: [
    {
      date: "2025年7月21日", // 或 data: "2025-07-21"
      isToday: true,
      timeRanges: [...]
    }
  ]
}
```

#### 格式3: 错误响应
```javascript
{
  code: 500,
  message: "错误信息",
  data: null
}
```

### 3. 故障排除

#### 问题1: 新接口不存在
**现象**: 控制台显示 "新接口调用失败，尝试使用旧接口"
**解决**: 系统会自动回退到旧接口

#### 问题2: 数据为null或undefined
**现象**: `res.data` 为 `null` 或 `undefined`
**检查**: 
- API是否返回了正确的状态码
- 后端是否有数据
- 咨询师ID是否有效

#### 问题3: 数据格式不匹配
**现象**: 数据存在但结构不符合预期
**解决**: 组件已添加多种格式的兼容处理

### 4. 手动测试API

可以在浏览器控制台中手动测试API：

```javascript
// 测试新接口
fetch('/system/timeSlot/formatted/1?startDate=2025-07-21&endDate=2025-07-27')
  .then(res => res.json())
  .then(data => console.log('新接口响应:', data));

// 测试旧接口
fetch('/wechat/appointment/counselor/1?days=7')
  .then(res => res.json())
  .then(data => console.log('旧接口响应:', data));
```

### 5. 常见解决方案

#### 方案1: 检查后端接口
确认后端是否实现了 `/system/timeSlot/formatted/{counselorId}` 接口

#### 方案2: 验证咨询师ID
确认传递的咨询师ID是有效的

#### 方案3: 检查网络连接
确认前端能够正常访问后端API

#### 方案4: 查看后端日志
检查后端是否有相关错误日志

### 6. 预期行为

修复后应该看到：
1. ✅ 控制台显示详细的API调用日志
2. ✅ 时间表正确显示
3. ✅ 没有JavaScript错误
4. ✅ 咨询时长自动计算

### 7. 如果问题仍然存在

请提供以下信息：
- 控制台的完整错误日志
- API响应的具体内容
- 咨询师ID的值
- 网络请求的状态（成功/失败）

这将帮助进一步诊断和解决问题。
