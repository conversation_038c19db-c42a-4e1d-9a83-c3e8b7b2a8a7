/**
 * 咨询师端小程序专用接口
 */
import request from '@/utils/request.js'

// ==================== 导航菜单 ====================
/**
 * 获取咨询师端导航菜单
 */
export function getConsultantTabbar() {
  return request({
    url: '/miniapp/consultant/tabbar',
    method: 'GET'
  })
}

// ==================== 订单管理 ====================
/**
 * 获取咨询师的订单列表
 * @param {Object} params - 查询参数
 * @param {string} params.status - 订单状态
 */
export function getConsultantOrders(params = {}) {
  return request({
    url: '/miniapp/consultant/orderManage/myOrders',
    method: 'GET',
    data: params
  })
}

/**
 * 根据价格和日期筛选咨询师订单
 * @param {Object} params - 筛选参数
 * @param {number} params.minPrice - 最小价格（元）
 * @param {number} params.maxPrice - 最大价格（元）
 * @param {string} params.startDate - 开始日期，格式：yyyy-MM-dd
 * @param {string} params.endDate - 结束日期，格式：yyyy-MM-dd
 * @param {string} params.status - 订单状态
 * @param {number} params.pageNum - 页码，从1开始
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.orderBy - 排序字段
 * @param {string} params.sortOrder - 排序方式，asc或desc
 */
export function filterConsultantOrdersByPriceAndDate(params = {}) {
  return request({
    url: '/miniapp/consultant/orderManage/filterByPriceAndDate',
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询师订单详情
 * @param {string} orderId - 订单ID
 */
export function getConsultantOrderDetail(orderId) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}`,
    method: 'GET'
  })
}

/**
 * 确认订单（咨询师确认接单）
 * @param {string} orderId - 订单ID
 */
export function confirmConsultantOrder(orderId) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/confirm`,
    method: 'POST'
  })
}

/**
 * 拒绝订单
 * @param {string} orderId - 订单ID
 * @param {string} rejectReason - 拒绝原因
 */
export function rejectConsultantOrder(orderId, rejectReason) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/reject`,
    method: 'POST',
    data: { rejectReason }
  })
}

/**
 * 获取今日订单
 */
export function getTodayOrders() {
  return request({
    url: '/miniapp/consultant/orderManage/today',
    method: 'GET'
  })
}

/**
 * 获取咨询师订单统计
 */
export function getConsultantOrderStatistics() {
  return request({
    url: '/miniapp/consultant/orderManage/statistics',
    method: 'GET'
  })
}

/**
 * 获取不同状态的订单数量
 */
export function getOrderStatusCount() {
  return request({
    url: '/miniapp/consultant/orderManage/statusCount',
    method: 'GET'
  })
}

/**
 * 获取即将开始的咨询
 */
export function getUpcomingConsultations() {
  return request({
    url: '/miniapp/consultant/orderManage/upcoming',
    method: 'GET'
  })
}

/**
 * 更新咨询师状态
 * @param {string} status - 状态
 */
export function updateConsultantStatus(status) {
  return request({
    url: '/miniapp/consultant/orderManage/updateStatus',
    method: 'POST',
    data: { status }
  })
}

// ==================== 灵活排班管理 ====================
/**
 * 获取灵活排班数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 */
export function getFlexibleSchedule(params = {}) {
  return request({
    url: '/miniapp/consultant/schedule/flexible',
    method: 'GET',
    data: params
  })
}

/**
 * 保存单日灵活排班
 * @param {string} date - 日期
 * @param {Object} schedule - 排班数据
 */
export function saveFlexibleSchedule(date, schedule) {
  return request({
    url: '/miniapp/consultant/schedule/flexible/save',
    method: 'POST',
    data: {
      date,
      schedule
    }
  })
}

/**
 * 批量保存灵活排班
 * @param {Object} schedules - 多日排班数据
 */
export function batchSaveFlexibleSchedule(schedules) {
  return request({
    url: '/miniapp/consultant/schedule/flexible/batchSave',
    method: 'POST',
    data: { schedules }
  })
}

/**
 * 复制排班设置
 * @param {string} sourceDate - 源日期
 * @param {Array} targetDates - 目标日期数组
 */
export function copyScheduleSettings(sourceDate, targetDates) {
  return request({
    url: '/miniapp/consultant/schedule/flexible/copy',
    method: 'POST',
    data: {
      sourceDate,
      targetDates
    }
  })
}

/**
 * 清空排班设置
 * @param {Array} dates - 要清空的日期数组
 */
export function clearScheduleSettings(dates) {
  return request({
    url: '/miniapp/consultant/schedule/flexible/clear',
    method: 'POST',
    data: { dates }
  })
}

/**
 * 更新订单状态
 * @param {string} orderId - 订单ID
 * @param {Object} data - 更新数据
 */
export function updateOrderStatus(orderId, data) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/status`,
    method: 'PUT',
    data
  })
}

/**
 * 完成订单
 * @param {string} orderId - 订单ID
 */
export function completeOrder(orderId) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/complete`,
    method: 'POST'
  })
}

/**
 * 取消订单
 * @param {string} orderId - 订单ID
 * @param {Object} data - 取消原因
 */
export function cancelConsultantOrder(orderId, data) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/cancel`,
    method: 'POST',
    data
  })
}

// ==================== 咨询记录管理 ====================
/**
 * 获取咨询师咨询记录列表
 * @param {Object} params - 查询参数
 */
export function getConsultantRecords(params = {}) {
  return request({
    url: '/miniapp/consultant/record',
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询记录详情
 * @param {string} recordId - 记录ID
 */
export function getConsultantRecordDetail(recordId) {
  return request({
    url: `/miniapp/consultant/record/${recordId}`,
    method: 'GET'
  })
}

/**
 * 创建咨询记录
 * @param {Object} data - 咨询记录数据
 */
export function createConsultationRecord(data) {
  return request({
    url: '/miniapp/consultant/record',
    method: 'POST',
    data
  })
}

/**
 * 更新咨询记录
 * @param {string} recordId - 记录ID
 * @param {Object} data - 更新数据
 */
export function updateConsultationRecord(recordId, data) {
  return request({
    url: `/miniapp/consultant/record/${recordId}`,
    method: 'PUT',
    data
  })
}

// ==================== 评价管理 ====================
/**
 * 获取咨询师评价管理列表
 * @param {Object} params - 查询参数
 */
export function getConsultantReviewManage(params = {}) {
  return request({
    url: '/miniapp/consultant/reviewManage',
    method: 'GET',
    data: params
  })
}

/**
 * 回复评价
 * @param {string} reviewId - 评价ID
 * @param {Object} data - 回复数据
 */
export function replyToReview(reviewId, data) {
  return request({
    url: `/miniapp/consultant/reviewManage/${reviewId}/reply`,
    method: 'POST',
    data
  })
}

// ==================== 个人信息管理 ====================
/**
 * 获取咨询师个人信息
 */
export function getConsultantProfile() {
  return request({
    url: '/miniapp/consultant/profile',
    method: 'GET'
  })
}

/**
 * 更新咨询师个人信息
 * @param {Object} data - 个人信息数据
 */
export function updateConsultantProfile(data) {
  return request({
    url: '/miniapp/consultant/profile',
    method: 'PUT',
    data
  })
}

/**
 * 上传头像
 * @param {Object} data - 头像数据
 */
export function uploadConsultantAvatar(data) {
  return request({
    url: '/miniapp/consultant/profile/avatar',
    method: 'POST',
    data
  })
}

// ==================== 钱包管理 ====================
/**
 * 获取咨询师钱包信息
 */
export function getConsultantWallet() {
  return request({
    url: '/miniapp/consultant/wallet',
    method: 'GET'
  })
}

/**
 * 获取咨询师收入流水
 * @param {Object} params - 查询参数
 */
export function getConsultantIncomeHistory(params = {}) {
  return request({
    url: '/miniapp/consultant/wallet/income',
    method: 'GET',
    data: params
  })
}

/**
 * 申请提现
 * @param {Object} data - 提现数据
 */
export function requestWithdraw(data) {
  return request({
    url: '/miniapp/consultant/wallet/withdraw',
    method: 'POST',
    data
  })
}

// ==================== 日程管理 ====================
/**
 * 获取咨询师日程安排
 * @param {Object} params - 查询参数
 */
export function getConsultantSchedule(params = {}) {
  return request({
    url: '/miniapp/consultant/schedule',
    method: 'GET',
    data: params
  })
}

/**
 * 设置咨询师可用时间
 * @param {Object} data - 时间设置数据
 */
export function setConsultantAvailableTime(data) {
  return request({
    url: '/miniapp/consultant/schedule/available',
    method: 'POST',
    data
  })
}

/**
 * 更新时间段状态
 * @param {string} slotId - 时间段ID
 * @param {Object} data - 状态数据
 */
export function updateTimeSlotStatus(slotId, data) {
  return request({
    url: `/miniapp/consultant/schedule/slot/${slotId}`,
    method: 'PUT',
    data
  })
}

// ==================== 统计数据 ====================
/**
 * 获取咨询师统计数据
 */
export function getConsultantStatistics() {
  return request({
    url: '/miniapp/consultant/statistics',
    method: 'GET'
  })
}

/**
 * 获取咨询师月度统计
 * @param {Object} params - 查询参数
 */
export function getConsultantMonthlyStats(params = {}) {
  return request({
    url: '/miniapp/consultant/statistics/monthly',
    method: 'GET',
    data: params
  })
}

// ==================== 消息管理 ====================
/**
 * 获取咨询师消息列表
 * @param {Object} params - 查询参数
 */
export function getConsultantMessages(params = {}) {
  return request({
    url: '/miniapp/consultant/messages',
    method: 'GET',
    data: params
  })
}

/**
 * 发送消息
 * @param {Object} data - 消息数据
 */
export function sendConsultantMessage(data) {
  return request({
    url: '/miniapp/consultant/messages/send',
    method: 'POST',
    data
  })
}

/**
 * 标记消息已读
 * @param {string} messageId - 消息ID
 */
export function markMessageRead(messageId) {
  return request({
    url: `/miniapp/consultant/messages/${messageId}/read`,
    method: 'PUT'
  })
}

// ==================== 扫一扫核销相关 ====================

/**
 * 核销订单
 * @param {Object} data - 核销数据
 */
export function verifyOrder(data) {
  return request({
    url: '/miniapp/consultant/verify/order',
    method: 'POST',
    data
  })
}

/**
 * 获取最近核销记录
 * @param {Object} params - 查询参数
 */
export function getRecentVerifyRecords(params = {}) {
  return request({
    url: '/miniapp/consultant/verify/recent',
    method: 'GET',
    data: params
  })
}

/**
 * 获取核销记录详情
 * @param {string} recordId - 记录ID
 */
export function getVerifyRecordDetail(recordId) {
  return request({
    url: `/miniapp/consultant/verify/record/${recordId}`,
    method: 'GET'
  })
}

// ==================== 帮助中心相关 ====================

/**
 * 获取帮助分类
 */
export function getHelpCategories() {
  return request({
    url: '/miniapp/consultant/help/categories',
    method: 'GET'
  })
}

/**
 * 获取热门问题
 * @param {Object} params - 查询参数
 */
export function getHotQuestions(params = {}) {
  return request({
    url: '/miniapp/consultant/help/hot-questions',
    method: 'GET',
    data: params
  })
}

/**
 * 搜索帮助内容
 * @param {Object} params - 搜索参数
 */
export function searchHelpContent(params = {}) {
  return request({
    url: '/miniapp/consultant/help/search',
    method: 'GET',
    data: params
  })
}

/**
 * 获取问题详情
 * @param {string} questionId - 问题ID
 */
export function getQuestionDetail(questionId) {
  return request({
    url: `/miniapp/consultant/help/question/${questionId}`,
    method: 'GET'
  })
}

// ==================== 评价管理相关 ====================

/**
 * 获取咨询师评价列表
 * @param {Object} params - 查询参数
 */
export function getConsultantReviews(params = {}) {
  return request({
    url: '/miniapp/consultant/reviews',
    method: 'GET',
    data: params
  })
}

/**
 * 获取评价统计
 */
export function getReviewStatistics() {
  return request({
    url: '/miniapp/consultant/reviews/statistics',
    method: 'GET'
  })
}
/**
 * 导出统计报告
 * @param {Object} data - 导出参数
 */
export function exportStatisticsReport(data) {
  return request({
    url: '/miniapp/consultant/statistics/export',
    method: 'POST',
    data
  })
}

// ==================== 排期模板相关 ====================

/**
 * 根据咨询师ID获取排班模板
 * @param {number} counselorId - 咨询师ID
 */
export function getTemplatesByCounselorId(counselorId) {
  return request({
    url: `/system/schedule/template/counselor/${counselorId}`,
    method: 'GET'
  })
}

/**
 * 获取默认模板
 * @param {number} counselorId - 咨询师ID
 */
export function getDefaultTemplate(counselorId) {
  return request({
    url: `/system/schedule/template/counselor/${counselorId}/default`,
    method: 'GET'
  })
}

/**
 * 创建排班模板
 * @param {Object} data - 模板数据
 */
export function createTemplate(data) {
  return request({
    url: '/system/schedule/template',
    method: 'POST',
    data
  })
}

/**
 * 更新排班模板
 * @param {Object} data - 模板数据
 */
export function updateTemplate(data) {
  return request({
    url: '/system/schedule/template',
    method: 'PUT',
    data
  })
}

/**
 * 删除排班模板
 * @param {Array} ids - 模板ID数组
 */
export function deleteTemplate(ids) {
  return request({
    url: `/system/schedule/template/${ids.join(',')}`,
    method: 'DELETE'
  })
}

/**
 * 设置默认模板
 * @param {number} counselorId - 咨询师ID
 * @param {number} templateId - 模板ID
 */
export function setDefaultTemplate(counselorId, templateId) {
  return request({
    url: `/system/schedule/template/setDefault/${counselorId}/${templateId}`,
    method: 'PUT'
  })
}

/**
 * 复制模板
 * @param {number} templateId - 模板ID
 * @param {string} newName - 新模板名称
 */
export function copyTemplate(templateId, newName) {
  return request({
    url: `/system/schedule/template/copy/${templateId}`,
    method: 'POST',
    data: { newName }
  })
}

/**
 * 应用模板到排期
 * @param {Object} data - 应用数据
 */
export function applyTemplateToSchedule(data) {
  return request({
    url: '/miniapp/consultant/schedule/apply-template',
    method: 'POST',
    data
  })
}

// ==================== 容错设置相关 ====================

/**
 * 获取咨询师容错设置
 * @param {number} consultantId - 咨询师ID
 * @param {number} centerId - 中心ID
 */
export function getConsultantToleranceSettings(consultantId, centerId) {
  return request({
    url: `/miniapp/consultant/settings/${consultantId}/center/${centerId}`,
    method: 'GET'
  })
}

/**
 * 更新咨询师到店时间
 * @param {number} consultantId - 咨询师ID
 * @param {number} centerId - 中心ID
 * @param {number} hours - 到店时间（小时）
 */
export function updateConsultantArrivalTime(consultantId, centerId, hours) {
  return request({
    url: `/miniapp/consultant/settings/${consultantId}/center/${centerId}/arrivalTime`,
    method: 'PUT',
    data: { hours }
  })
}

/**
 * 更新咨询师过滤开关
 * @param {number} consultantId - 咨询师ID
 * @param {number} centerId - 中心ID
 * @param {boolean} enabled - 是否启用过滤
 */
export function updateConsultantFilterEnabled(consultantId, centerId, enabled) {
  return request({
    url: `/miniapp/consultant/settings/${consultantId}/center/${centerId}/toggleFilter`,
    method: 'PUT',
    data: { enabled }
  })
}

/**
 * 重置咨询师容错设置为默认值
 * @param {number} consultantId - 咨询师ID
 * @param {number} centerId - 中心ID
 */
export function resetConsultantToleranceSettings(consultantId, centerId) {
  return request({
    url: `/miniapp/consultant/settings/${consultantId}/center/${centerId}/reset`,
    method: 'POST'
  })
}
