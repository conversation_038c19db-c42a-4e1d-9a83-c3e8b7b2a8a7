/**
 * InfoModal 组件配置文件
 * 提供常用的信息展示数据配置
 */

// 测评须知配置
export const ASSESSMENT_NOTICE = {
  title: '测评须知',
  sections: [
    {
      title: '测评目的',
      content: '本测评旨在帮助参与者了解自己的心理状态与特质，包括但不限于情绪状态、性格倾向、应对策略等。'
    },
    {
      title: '测评对象',
      content: '本测评适用于年龄在 16 岁及以上的个人。若参与者年龄较小，需要在父母或法定监护人的指导下进行。'
    },
    {
      title: '测评后处理',
      content: '评估报告包括各项心理指标的解读和说明，您可以下载报告永久保存。'
    }
  ],
  notice: '如果您在测评过程中感到不适，可以随时停止。测评不能替代专业的心理诊断。',
  showButton: true,
  buttonText: '开始测评',
  buttonType: 'primary'
}

// 心理咨询须知配置
export const CONSULTATION_NOTICE = {
  title: '咨询须知',
  sections: [
    {
      title: '咨询目的',
      content: '心理咨询旨在帮助您解决心理困扰，提升心理健康水平，改善生活质量。'
    },
    {
      title: '咨询对象',
      content: '适用于有心理困扰、情绪问题或希望自我成长的个人。未成年人需在监护人同意下进行。'
    },
    {
      title: '咨询流程',
      content: '包括初次评估、制定咨询计划、定期咨询会谈、效果评估等环节。'
    },
    {
      title: '保密原则',
      content: '咨询师将严格遵守保密原则，保护您的隐私和个人信息安全。'
    }
  ],
  notice: '心理咨询需要您的积极配合和坚持，如有紧急情况请及时联系专业机构。',
  showButton: true,
  buttonText: '预约咨询',
  buttonType: 'primary'
}

// 冥想课程介绍配置
export const MEDITATION_COURSE = {
  title: '冥想课程',
  sections: [
    {
      title: '课程简介',
      content: '通过系统的冥想练习，帮助您缓解压力、提升专注力、改善睡眠质量，获得内心的平静与安宁。'
    },
    {
      title: '适用人群',
      content: '适合所有希望通过冥想改善身心健康的人群，无需任何基础，初学者友好。'
    },
    {
      title: '课程内容',
      content: '包括呼吸冥想、身体扫描、正念冥想、慈心冥想等多种技巧，循序渐进地引导练习。'
    },
    {
      title: '学习收获',
      content: '掌握基本冥想技巧，学会情绪调节，提升专注力和觉察力，获得更好的睡眠和心理状态。'
    }
  ],
  notice: '建议在安静舒适的环境中练习，每日坚持效果更佳。',
  showButton: true,
  buttonText: '开始学习',
  buttonType: 'primary'
}

// 服务条款配置
export const SERVICE_TERMS = {
  title: '服务条款',
  sections: [
    {
      title: '服务内容',
      content: '我们提供心理咨询、心理测评、心理课程、冥想指导等专业心理健康服务。'
    },
    {
      title: '用户权利',
      content: '用户有权随时终止服务、要求删除个人信息、获得服务质量保障、享受隐私保护等权利。'
    },
    {
      title: '服务限制',
      content: '本服务不能替代专业医疗诊断和治疗，如有严重心理问题请及时就医。'
    },
    {
      title: '免责声明',
      content: '平台提供的内容仅供参考，不构成专业医疗建议。用户应根据自身情况谨慎使用。'
    },
    {
      title: '隐私保护',
      content: '我们严格保护用户隐私，不会未经授权向第三方透露用户个人信息。'
    }
  ],
  notice: '使用本服务即表示您同意遵守以上条款。如有疑问，请联系客服。',
  showButton: true,
  buttonText: '同意并继续',
  buttonType: 'primary'
}

// 隐私政策配置
export const PRIVACY_POLICY = {
  title: '隐私政策',
  sections: [
    {
      title: '信息收集',
      content: '我们仅收集为您提供服务所必需的个人信息，包括基本资料、使用记录等。'
    },
    {
      title: '信息使用',
      content: '收集的信息仅用于提供服务、改善用户体验、进行数据分析等合法目的。'
    },
    {
      title: '信息保护',
      content: '我们采用行业标准的安全措施保护您的个人信息，防止未经授权的访问、使用或泄露。'
    },
    {
      title: '信息共享',
      content: '除法律要求或获得您的明确同意外，我们不会与第三方共享您的个人信息。'
    }
  ],
  notice: '我们会定期更新隐私政策，请关注相关变更。',
  showButton: true,
  buttonText: '我已阅读',
  buttonType: 'secondary'
}

// 课程购买须知配置
export const COURSE_PURCHASE_NOTICE = {
  title: '购买须知',
  sections: [
    {
      title: '课程内容',
      content: '课程包含视频讲解、音频练习、文字资料等多种形式的学习内容。'
    },
    {
      title: '学习期限',
      content: '购买后可永久观看，支持反复学习，建议按照推荐进度循序渐进。'
    },
    {
      title: '退款政策',
      content: '购买后7天内如不满意可申请退款，超过期限或已完成学习的课程不支持退款。'
    },
    {
      title: '技术支持',
      content: '如遇播放问题或其他技术问题，可联系客服获得帮助。'
    }
  ],
  notice: '购买即表示同意以上条款，请仔细阅读后再进行购买。',
  showButton: true,
  buttonText: '确认购买',
  buttonType: 'primary'
}

/**
 * 创建自定义信息配置
 * @param {Object} config 配置对象
 * @param {string} config.title 标题
 * @param {Array} config.sections 内容区块数组
 * @param {string} config.notice 注意事项
 * @param {boolean} config.showButton 是否显示按钮
 * @param {string} config.buttonText 按钮文字
 * @param {string} config.buttonType 按钮类型
 * @returns {Object} 格式化的配置对象
 */
export const createInfoConfig = (config) => {
  return {
    title: config.title || '信息详情',
    sections: config.sections || [],
    notice: config.notice || '',
    showButton: config.showButton || false,
    buttonText: config.buttonText || '确定',
    buttonType: config.buttonType || 'primary'
  }
}

/**
 * 根据类型获取预设配置
 * @param {string} type 配置类型
 * @returns {Object} 对应的配置对象
 */
export const getInfoConfig = (type) => {
  const configs = {
    'assessment': ASSESSMENT_NOTICE,
    'consultation': CONSULTATION_NOTICE,
    'meditation': MEDITATION_COURSE,
    'terms': SERVICE_TERMS,
    'privacy': PRIVACY_POLICY,
    'purchase': COURSE_PURCHASE_NOTICE
  }
  
  return configs[type] || null
}

/**
 * 获取所有可用的配置类型
 * @returns {Array} 配置类型数组
 */
export const getAvailableTypes = () => {
  return [
    { key: 'assessment', name: '测评须知' },
    { key: 'consultation', name: '咨询须知' },
    { key: 'meditation', name: '冥想课程' },
    { key: 'terms', name: '服务条款' },
    { key: 'privacy', name: '隐私政策' },
    { key: 'purchase', name: '购买须知' }
  ]
}
