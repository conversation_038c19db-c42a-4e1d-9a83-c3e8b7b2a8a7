import request from '../utils/request'

// 获取首页分类标签
export function getHomeCategories() {
  return request({
    url: '/miniapp/category/home',
    method: 'get'
  })
}

// 获取咨询师分类
export function getConsultantCategories() {
  return request({
    url: '/system/consultant/categories',
    method: 'get'
  })
}

// 获取课程分类
export function getCourseCategories() {
  return request({
    url: '/miniapp/course/categories',
    method: 'get'
  })
}

// 获取冥想分类
export function getMeditationCategories() {
  return request({
    url: '/miniapp/meditation/categories',
    method: 'get'
  })
}

// 获取测评分类
export function getAssessmentCategories() {
  return request({
    url: '/miniapp/assessment/categories',
    method: 'get'
  })
}

// 获取产品分类树形结构
export function getCategoryTree() {
  return request({
    url: '/psy/category/treeWithProducts',
    method: 'get'
  })
}

// 根据分类ID获取咨询师列表
export function getConsultantsByCategory(categoryId, params) {
  return request({
    url: `/system/consultant/category/${categoryId}`,
    method: 'get',
    params
  })
}

// 根据分类ID获取课程列表
export function getCoursesByCategory(categoryId, params) {
  return request({
    url: `/miniapp/course/category/${categoryId}`,
    method: 'get',
    params
  })
}

// 根据分类ID获取冥想列表
export function getMeditationsByCategory(categoryId, params) {
  return request({
    url: `/miniapp/meditation/category/${categoryId}`,
    method: 'get',
    params
  })
}

// 根据分类ID获取测评列表
export function getAssessmentsByCategory(categoryId, params) {
  return request({
    url: `/miniapp/assessment/category/${categoryId}`,
    method: 'get',
    params
  })
}
