import request from '../utils/request'

// ==================== 订单创建接口 ====================

/**
 * 创建课程订单
 * @param {Object} data - 订单数据
 * @param {Number} data.productId - 课程ID
 * @param {Number} data.paymentAmount - 实际支付金额
 * @param {Number} data.originalPrice - 原价
 * @param {Number} data.couponId - 优惠券ID (可选)
 * @param {Number} data.couponDiscount - 优惠券折扣金额 (可选)
 * @param {Number} data.pointsUsed - 使用积分数量 (可选)
 * @param {Number} data.pointsDiscount - 积分抵扣金额 (可选)
 */
export function createCourseOrder(data) {
  return request({
    url: '/miniapp/order/course/create',
    method: 'post',
    data
  })
}

/**
 * 创建冥想订单
 * @param {Object} data - 订单数据
 * @param {Number} data.productId - 冥想ID
 * @param {Number} data.paymentAmount - 实际支付金额
 * @param {Number} data.originalPrice - 原价
 * @param {Number} data.couponId - 优惠券ID (可选)
 * @param {Number} data.couponDiscount - 优惠券折扣金额 (可选)
 */
export function createMeditationOrder(data) {
  return request({
    url: '/miniapp/order/meditation/create',
    method: 'post',
    data
  })
}

/**
 * 创建咨询订单
 * @param {Object} data - 订单数据
 * @param {Number} data.consultantId - 咨询师ID
 * @param {Number} data.serviceId - 服务ID
 * @param {String} data.appointmentTime - 预约时间
 * @param {Number} data.duration - 咨询时长(分钟)
 * @param {Number} data.paymentAmount - 实际支付金额
 * @param {Number} data.originalPrice - 原价
 * @param {Number} data.couponId - 优惠券ID (可选)
 * @param {Number} data.couponDiscount - 优惠券折扣金额 (可选)
 */
export function createConsultantOrder(data) {
  return request({
    url: '/miniapp/user/consultant/order/create',
    method: 'post',
    data
  })
}

/**
 * 创建测评订单
 * @param {Object} data - 订单数据
 * @param {Number} data.productId - 测评ID
 * @param {Number} data.paymentAmount - 实际支付金额
 * @param {Number} data.originalPrice - 原价
 * @param {Number} data.couponId - 优惠券ID (可选)
 * @param {Number} data.couponDiscount - 优惠券折扣金额 (可选)
 */
export function createAssessmentOrder(data) {
  return request({
    url: '/miniapp/user/order/create',
    method: 'post',
    data
  })
}

// ==================== 支付相关接口 ====================

/**
 * 发起支付
 * @param {String} orderNo - 订单号
 */
export function payOrder(orderNo) {
  return request({
    url: '/miniapp/order/pay',
    method: 'post',
    data: { orderNo }
  })
}

/**
 * 查询支付状态
 * @param {String} orderNo - 订单号
 */
export function queryPayStatus(orderNo) {
  return request({
    url: `/miniapp/order/pay/status/${orderNo}`,
    method: 'get'
  })
}

/**
 * 取消订单
 * @param {String} orderNo - 订单号
 */
export function cancelOrder(orderNo) {
  return request({
    url: `/miniapp/order/cancel/${orderNo}`,
    method: 'post'
  })
}

/**
 * 申请退款
 * @param {Object} data - 退款数据
 * @param {String} data.orderNo - 订单号
 * @param {Number} data.refundAmount - 退款金额
 * @param {String} data.reason - 退款原因
 */
export function requestRefund(data) {
  return request({
    url: '/miniapp/order/refund',
    method: 'post',
    data
  })
}

// ==================== 订单管理接口 ====================

/**
 * 获取订单列表
 * @param {Object} params - 查询参数
 * @param {String} params.orderType - 订单类型 (course, meditation, consultant, assessment)
 * @param {String} params.status - 订单状态
 * @param {Number} params.pageNum - 页码
 * @param {Number} params.pageSize - 每页数量
 */
export function getOrderList(params) {
  return request({
    url: '/miniapp/order/list',
    method: 'get',
    params
  })
}

/**
 * 获取订单详情
 * @param {String} orderNo - 订单号
 */
export function getOrderDetail(orderNo) {
  return request({
    url: `/miniapp/order/detail/${orderNo}`,
    method: 'get'
  })
}

/**
 * 获取课程订单列表
 * @param {Object} params - 查询参数
 */
export function getCourseOrderList(params) {
  return request({
    url: '/miniapp/order/course/list',
    method: 'get',
    params
  })
}

/**
 * 获取冥想订单列表
 * @param {Object} params - 查询参数
 */
export function getMeditationOrderList(params) {
  return request({
    url: '/miniapp/order/meditation/list',
    method: 'get',
    params
  })
}

/**
 * 获取咨询订单列表
 * @param {Object} params - 查询参数
 */
export function getConsultantOrderList(params) {
  return request({
    url: '/miniapp/order/consultant/list',
    method: 'get',
    params
  })
}

/**
 * 获取测评订单列表
 * @param {Object} params - 查询参数
 */
export function getAssessmentOrderList(params) {
  return request({
    url: '/miniapp/user/order/list',
    method: 'get',
    params: {
      ...params,
      orderType: 'assessment'
    }
  })
}

// ==================== 订单类型常量 ====================

/**
 * 订单类型常量
 */
export const ORDER_TYPES = {
  COURSE: 'course',
  MEDITATION: 'meditation',
  CONSULTANT: 'consultant',
  ASSESSMENT: 'assessment'
}

/**
 * 订单状态常量
 */
export const ORDER_STATUS = {
  PENDING: 0,      // 待支付
  PAID: 1,         // 已支付
  PROCESSING: 2,   // 处理中
  COMPLETED: 3,    // 已完成
  CANCELLED: 4,    // 已取消
  REFUNDING: 5,    // 退款中
  REFUNDED: 6      // 已退款
}

/**
 * 订单前缀常量
 */
export const ORDER_PREFIX = {
  [ORDER_TYPES.COURSE]: 'COURSE_',
  [ORDER_TYPES.MEDITATION]: 'MEDITATION_',
  [ORDER_TYPES.CONSULTANT]: 'CONSULTANT_',
  [ORDER_TYPES.ASSESSMENT]: 'ASSESSMENT_'
}

// ==================== 工具函数 ====================

/**
 * 获取订单类型名称
 * @param {String} type - 订单类型
 * @returns {String} 类型名称
 */
export function getOrderTypeName(type) {
  const typeNames = {
    [ORDER_TYPES.COURSE]: '课程',
    [ORDER_TYPES.MEDITATION]: '冥想',
    [ORDER_TYPES.CONSULTANT]: '咨询',
    [ORDER_TYPES.ASSESSMENT]: '测评'
  }
  return typeNames[type] || '未知类型'
}

/**
 * 获取订单状态名称
 * @param {Number} status - 订单状态
 * @returns {String} 状态名称
 */
export function getOrderStatusName(status) {
  const statusNames = {
    [ORDER_STATUS.PENDING]: '待支付',
    [ORDER_STATUS.PAID]: '已支付',
    [ORDER_STATUS.PROCESSING]: '处理中',
    [ORDER_STATUS.COMPLETED]: '已完成',
    [ORDER_STATUS.CANCELLED]: '已取消',
    [ORDER_STATUS.REFUNDING]: '退款中',
    [ORDER_STATUS.REFUNDED]: '已退款'
  }
  return statusNames[status] || '未知状态'
}
