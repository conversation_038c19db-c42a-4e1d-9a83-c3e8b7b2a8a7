<template>
  <view class="course-detail">
    <!-- 课程封面和基本信息 -->
    <view class="course-header">
      <image :src="courseDetail.coverImage || defaultCover" mode="aspectFill"></image>
      <view class="course-overlay">
        <view class="course-title">{{ courseDetail.title }}</view>
        <view class="course-subtitle">{{ courseDetail.summary }}</view>
        <view class="course-stats">
          <text>{{ courseDetail.salesCount || 0 }}人学习</text>
          <text>{{ courseDetail.chapterCount || 0 }}个章节</text>
          <text>{{ formatDuration(courseDetail.durationTotal) }}</text>
        </view>
        <view class="course-price">
          <text v-if="courseDetail.isFree === 1" class="free-tag">免费</text>
          <text v-else class="price">¥{{ courseDetail.price }}</text>
        </view>
        <view class="course-rating">
          <uni-rate :value="courseDetail.ratingAvg || 0" readonly size="16"></uni-rate>
          <text class="rating-text">{{ courseDetail.ratingAvg || 0 }} ({{ courseDetail.ratingCount || 0 }}人评价)</text>
        </view>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', { active: currentTab === index }]"
        @click="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y>
      <!-- 课程介绍 -->
      <view v-if="currentTab === 0" class="tab-content">
        <view class="section">
          <view class="section-title">课程介绍</view>
          <view class="course-description">{{ courseDetail.summary || '暂无介绍' }}</view>
        </view>

        <view class="section">
          <view class="section-title">讲师信息</view>
          <view v-if="courseDetail.instructor" class="instructor-info">
            <view class="instructor-name">{{ courseDetail.instructor.name }}</view>
            <view class="instructor-title">{{ courseDetail.instructor.title }}</view>
            <view class="instructor-qualifications">{{ courseDetail.instructor.qualifications }}</view>
          </view>
        </view>

        <view class="section">
          <view class="section-title">课程标签</view>
          <view class="course-tags">
            <text
              v-for="(tag, index) in parsedTags"
              :key="index"
              class="tag-item"
            >
              {{ tag }}
            </text>
          </view>
        </view>

        <view class="section">
          <view class="section-title">课程分类</view>
          <view class="course-categories">
            <text
              v-for="(category) in courseDetail.categories"
              :key="category.categoryId"
              class="category-item"
            >
              {{ category.categoryName }}
            </text>
          </view>
        </view>
      </view>

      <!-- 课程章节 -->
      <view v-if="currentTab === 1" class="tab-content">
        <view class="chapter-list">
          <!-- 一级章节（目录） -->
          <view v-for="(chapter, index) in chapterList" :key="chapter.id" class="chapter-group">
            <!-- 一级章节标题 -->
            <view class="main-chapter" @click="toggleChapter(chapter.id)">
              <view class="chapter-number">{{ chapter.chapterOrder || (index + 1) }}</view>
              <view class="chapter-info">
                <view class="chapter-title">{{ chapter.chapterTitle }}</view>
                <view class="chapter-meta">
                  <text>{{ formatDuration(chapter.duration) }}</text>
                  <text class="sub-count">{{ chapter.children?.length || 0 }}个小节</text>
                </view>
              </view>
              <view class="chapter-toggle">
                <uni-icons
                  :type="expandedChapters.includes(chapter.id) ? 'arrowdown' : 'arrowright'"
                  size="18"
                  color="#666"
                ></uni-icons>
              </view>
            </view>

            <!-- 二级章节（可播放的视频） -->
            <view v-if="expandedChapters.includes(chapter.id)" class="sub-chapters">
              <view
                v-for="(subChapter, subIndex) in chapter.children"
                :key="subChapter.id"
                class="sub-chapter"
                @click="playSubChapter(subChapter)"
              >
                <view class="chapter-number sub">{{ chapter.chapterOrder }}.{{ subChapter.chapterOrder || (subIndex + 1) }}</view>
                <view class="chapter-info">
                  <view class="chapter-title">{{ subChapter.chapterTitle }}</view>
                  <view class="chapter-meta">
                    <text>{{ formatDuration(subChapter.duration) }}</text>
                    <text v-if="subChapter.isTrial === 1" class="trial-tag">试听</text>
                    <text v-else-if="!courseDetail.purchased && courseDetail.isFree !== 1 && courseDetail.price > 0" class="locked-tag">需购买</text>
                    <text v-if="subChapter.mediaUrl" class="video-tag">视频</text>
                  </view>
                </view>
                <view class="chapter-status">
                  <uni-icons
                    v-if="subChapter.completed"
                    type="checkmarkempty"
                    size="20"
                    color="#4CAF50"
                  ></uni-icons>
                  <uni-icons
                    v-else-if="subChapter.mediaUrl && (subChapter.isTrial === 1 || courseDetail.purchased || courseDetail.isFree === 1 || courseDetail.price == 0)"
                    type="videocam"
                    size="16"
                    color="#007aff"
                  ></uni-icons>
                  <uni-icons
                    v-else-if="!subChapter.mediaUrl"
                    type="compose"
                    size="16"
                    color="#999"
                  ></uni-icons>
                  <uni-icons
                    v-else
                    type="locked"
                    size="16"
                    color="#ccc"
                  ></uni-icons>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 课程评价 -->
      <view v-if="currentTab === 2" class="tab-content">
        <view class="rating-summary">
          <view class="rating-score">{{ courseDetail.ratingAvg || 0 }}</view>
          <view class="rating-stars">
            <uni-rate :value="courseDetail.ratingAvg || 0" readonly size="16"></uni-rate>
          </view>
          <view class="rating-count">{{ courseDetail.ratingCount || 0 }}条评价</view>
        </view>

        <view v-if="reviewList.length > 0" class="review-list">
          <view v-for="review in reviewList" :key="review.id" class="review-item">
            <view class="review-header">
              <image :src="review.userAvatar || defaultAvatar" class="user-avatar"></image>
              <view class="user-info">
                <view class="user-name">{{ review.userName }}</view>
                <view class="review-date">{{ formatDate(review.createTime) }}</view>
              </view>
              <uni-rate :value="review.rating" readonly size="12"></uni-rate>
            </view>
            <view class="review-content">{{ review.content }}</view>
          </view>
        </view>

        <view v-else class="no-reviews">
          <text>暂无评价</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <UniversalGoodsNav
      style="z-index: 1;"
      page-type="course"
      :detail-data="courseDetail"
      :purchased="courseDetail.purchased"
      :price="courseDetail.price"
      :favorited="courseDetail.favorited"
      :favorite-id="courseDetail.favoriteId"
      @favorite="handleFavorite"
      @contact-service="handleContactService"
      @share="handleShare"
      @main-action="handleMainAction"
    />

    <!-- 支付弹框 -->
    <PaymentModal
      ref="paymentModal"
      :order-info="orderInfo"
      @close="onPaymentClose"
      @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail"
    />
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getCourseDetail,
  getCourseChapters,
  getCourseReviews,
  createCourseOrder
} from '@/api/course'
import { useUserStore } from '@/stores/user'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'


// 响应式数据
const courseDetail = ref({})
const chapterList = ref([])
const reviewList = ref([])
const tabs = ['介绍', '章节', '评价']
const currentTab = ref(0)
const courseId = ref(null)
const paymentModal = ref(null)
const orderInfo = ref({})
const expandedChapters = ref([]) // 展开的章节ID列表

// 默认图片
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 计算属性
const parsedTags = computed(() => {
  if (!courseDetail.value.tags) return []
  try {
    return JSON.parse(courseDetail.value.tags)
  } catch (error) {
    return []
  }
})

// 章节展开/收起控制
const toggleChapter = (chapterId) => {
  const index = expandedChapters.value.indexOf(chapterId)
  if (index > -1) {
    // 如果已展开，则收起
    expandedChapters.value.splice(index, 1)
  } else {
    // 如果未展开，则展开
    expandedChapters.value.push(chapterId)
  }
}

// 播放子章节视频
const playSubChapter = (subChapter) => {
  // 检查是否可以观看
  if (subChapter.isTrial !== 1 && !courseDetail.value.purchased && courseDetail.value.isFree !== 1 && courseDetail.value.price > 0) {
    uni.showToast({
      title: '请先购买课程',
      icon: 'none'
    })
    return
  }

  // 检查是否有视频
  if (!subChapter.mediaUrl) {
    uni.showToast({
      title: '该章节暂无视频',
      icon: 'none'
    })
    return
  }

  // 播放视频
  playVideo(subChapter)
}

// 格式化时长（秒转换为分钟）
const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 处理收藏事件
const handleFavorite = (favoriteData) => {
  courseDetail.value.favorited = favoriteData.favorited
  courseDetail.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
  console.log('联系客服')
}

// 处理分享事件
const handleShare = (shareConfig) => {
  console.log('分享配置:', shareConfig)
  uni.showToast({
    title: '转发成功',
    icon: 'success'
  })
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
  if (!courseDetail.value.purchased && courseDetail.value.isFree !== 1 && courseDetail.value.price > 0) {
    buyCourse()
  } else {
    startStudy()
  }
}

// 方法
const switchTab = (index) => {
  currentTab.value = index

  // 切换到章节页面时加载章节列表
  if (index === 1 && chapterList.value.length === 0) {
    loadChapterList()
  }

  // 切换到评价页面时加载评价列表
  if (index === 2 && reviewList.value.length === 0) {
    loadReviewList()
  }
}

const loadCourseDetail = async () => {
  try {
    const res = await getCourseDetail(courseId.value)
    if (res.code === 200) {
      courseDetail.value = res.data

      // 处理章节数据 - 构建层级结构
      if (res.data.chapters && res.data.chapters.length > 0) {
        const processedChapters = processChapterHierarchy(res.data.chapters)
        chapterList.value = processedChapters

        // 默认展开第一个章节
        if (processedChapters.length > 0) {
          expandedChapters.value.push(processedChapters[0].id)
        }
      }

      // 设置导航标题
      uni.setNavigationBarTitle({
        title: res.data.title || '课程详情'
      })

      console.log('课程详情数据:', courseDetail.value)
      console.log('章节数据:', chapterList.value)
    } else {
      uni.showToast({
        title: res.msg || '获取课程详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取课程详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 处理章节层级结构
const processChapterHierarchy = (chapters) => {
  if (!chapters || chapters.length === 0) return []

  // 按照 level 和 chapterOrder 排序
  const sortedChapters = [...chapters].sort((a, b) => {
    if (a.level !== b.level) {
      return a.level - b.level
    }
    return (a.chapterOrder || 0) - (b.chapterOrder || 0)
  })

  const result = []
  const parentMap = new Map()

  sortedChapters.forEach(chapter => {
    if (chapter.level === 1) {
      // 一级章节
      chapter.children = []
      result.push(chapter)
      parentMap.set(chapter.id, chapter)
    } else if (chapter.level === 2) {
      // 二级章节
      const parent = parentMap.get(chapter.parentId)
      if (parent) {
        parent.children.push(chapter)
      } else {
        // 如果找不到父章节，作为一级章节处理
        result.push(chapter)
      }
    }
  })

  return result
}

const loadChapterList = async () => {
  // 如果已经从课程详情中获取了章节，就不需要再次请求
  if (chapterList.value.length > 0) {
    return
  }

  try {
    const res = await getCourseChapters(courseId.value)
    if (res.code === 200) {
      chapterList.value = res.data || []
    }
  } catch (error) {
    console.error('获取章节列表失败:', error)
  }
}

const loadReviewList = async () => {
  try {
    const res = await getCourseReviews(courseId.value)
    if (res.code === 200) {
      reviewList.value = res.data || []
    }
  } catch (error) {
    console.error('获取评价列表失败:', error)
  }
}

const buyCourse = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    uni.showLoading({ title: '创建订单中...' })

    const res = await createCourseOrder(courseId.value)

    uni.hideLoading()

    if (res.code === 200) {
      // 设置订单信息并打开支付弹框
      orderInfo.value = {
        orderNo: res.data.orderNo,
        product: courseDetail.value
      }
      paymentModal.value?.open()
    } else {
      uni.showToast({
        title: res.msg || '创建订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const startStudy = () => {
  // 查找第一个有视频且可以播放的子章节
  for (const chapter of chapterList.value) {
    if (chapter.children && chapter.children.length > 0) {
      for (const subChapter of chapter.children) {
        if (subChapter.mediaUrl && (subChapter.isTrial === 1 || courseDetail.value.purchased || courseDetail.value.isFree === 1 || courseDetail.value.price == 0)) {
          // 自动展开该章节
          if (!expandedChapters.value.includes(chapter.id)) {
            expandedChapters.value.push(chapter.id)
          }
          playVideo(subChapter)
          return
        }
      }
    }
  }

  // 如果没有找到可播放的视频，提示用户
  uni.showToast({
    title: '暂无可播放的视频章节',
    icon: 'none'
  })
}

// 播放视频
const playVideo = (chapter) => {
  // 使用uni-app的视频播放器
  uni.navigateTo({
    url: `/pages/course/video-player/index?videoUrl=${encodeURIComponent(chapter.mediaUrl)}&title=${encodeURIComponent(chapter.chapterTitle)}&chapterId=${chapter.id}&courseId=${courseId.value}`
  })
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 更新课程购买状态
  courseDetail.value.purchased = true

  // 重新加载课程详情
  loadCourseDetail()
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 生命周期
onLoad((options) => {
  courseId.value = options.id
  console.log('课程ID:', courseId.value);

  if (options.id) {
    loadCourseDetail()
  } else {
    // 如果没有ID，使用测试数据
    loadTestData()
  }
})

// 加载测试数据（用于调试）
const loadTestData = () => {
  const testData = {
    "id": 1000,
    "title": "情绪管理入门",
    "summary": "学会识别和调节自己的情绪",
    "price": 199,
    "salesCount": 1,
    "chapterCount": 6,
    "trialChapterCount": 2,
    "instructorId": 100,
    "coverImage": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png",
    "difficultyLevel": 1,
    "durationTotal": 8700,
    "viewCount": 22,
    "ratingAvg": 4.3,
    "ratingCount": 2,
    "isFree": 1,
    "tags": "[\"情绪管理\",\"压力缓解\"]",
    "status": 1,
    "instructor": {
      "id": 100,
      "name": "张老师",
      "title": "资深心理咨询师",
      "qualifications": "国家一级心理咨询师，十年从业经验"
    },
    "chapters": [
      {
        "id": 2000,
        "level": 1,
        "chapterTitle": "第一章 情绪认知",
        "duration": 1800,
        "chapterOrder": 1,
        "isTrial": 1,
        "children": []
      },
      {
        "id": 2003,
        "level": 1,
        "chapterTitle": "第二章 情绪调节方法",
        "duration": 2700,
        "chapterOrder": 2,
        "isTrial": 0,
        "children": []
      },
      {
        "id": 2001,
        "parentId": 2000,
        "level": 2,
        "chapterTitle": "1.1 情绪的基本类型",
        "duration": 1200,
        "chapterOrder": 1,
        "isTrial": 1,
        "mediaUrl": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/video/%E6%88%91%E6%98%AF%E5%A6%82%E4%BD%95%E5%9C%A8B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6%E7%9A%84_%E4%B8%A8%E5%BF%83%E7%90%86%E5%AD%A6%E4%BB%8E%E5%85%A5%E9%97%A8%E5%88%B0%E5%85%A5%E5%9C%9F%20%E2%80%94%E2%80%94%20B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6.mp4"
      },
      {
        "id": 2004,
        "parentId": 2003,
        "level": 2,
        "chapterTitle": "2.1 呼吸调节法",
        "duration": 600,
        "chapterOrder": 1,
        "isTrial": 0
      },
      {
        "id": 2002,
        "parentId": 2000,
        "level": 2,
        "chapterTitle": "1.2 情绪与身体的关系",
        "duration": 900,
        "chapterOrder": 2,
        "isTrial": 0
      },
      {
        "id": 2005,
        "parentId": 2003,
        "level": 2,
        "chapterTitle": "2.2 认知重构法",
        "duration": 1500,
        "chapterOrder": 2,
        "isTrial": 0
      }
    ],
    "categories": [
      {
        "categoryId": 23,
        "categoryName": "课程分类"
      }
    ],
    "purchased": false
  }

  courseDetail.value = testData
  const processedChapters = processChapterHierarchy(testData.chapters)
  chapterList.value = processedChapters

  // 默认展开第一个章节
  if (processedChapters.length > 0) {
    expandedChapters.value.push(processedChapters[0].id)
  }

  uni.setNavigationBarTitle({
    title: testData.title
  })

  console.log('测试数据加载完成:', courseDetail.value)
  console.log('处理后的章节:', chapterList.value)
  console.log('默认展开章节:', expandedChapters.value)
}

// 添加转发支持
const shareConfig = ref(null)

// 监听转发事件
uni.$on('triggerShare', (config) => {
  shareConfig.value = config
  // #ifdef MP-WEIXIN
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none'
  })
  // #endif
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('triggerShare')
})
</script>

<script>
// 支持转发的页面配置
export default {
  onShareAppMessage() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const courseDetail = currentInstance?.ctx?.courseDetail
    if (courseDetail) {
      return {
        title: `推荐课程：${courseDetail.title}`,
        path: `pages/course/detail/index?id=${courseDetail.id}`,
        imageUrl: courseDetail.coverImage
      }
    }

    return {
      title: '熙桓心理课程',
      path: 'pages/index/index'
    }
  },

  onShareTimeline() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        query: '',
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const courseDetail = currentInstance?.ctx?.courseDetail
    if (courseDetail) {
      return {
        title: `推荐课程：${courseDetail.title}`,
        query: `id=${courseDetail.id}`,
        imageUrl: courseDetail.coverImage
      }
    }

    return {
      title: '熙桓心理课程'
    }
  }
}
</script>

<style lang="scss" scoped>
.course-detail {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.course-header {
  position: relative;
  height: 500rpx;

  image {
    width: 100%;
    height: 100%;
  }

  .course-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 40rpx 32rpx;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: #fff;

    .course-title {
      font-size: 40rpx;
      font-weight: 600;
      margin-bottom: 12rpx;
    }

    .course-subtitle {
      font-size: 28rpx;
      opacity: 0.9;
      margin-bottom: 20rpx;
    }

    .course-stats {
      display: flex;
      gap: 32rpx;
      font-size: 26rpx;
      opacity: 0.8;
    }
  }
}

.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 32rpx 0;
    font-size: 30rpx;
    color: #666;
    position: relative;

    &.active {
      color: #ff6b35;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #ff6b35;
        border-radius: 2rpx;
      }
    }
  }
}

.content-area {
  flex: 1;
  padding-bottom: 160rpx;
}

.tab-content {
  padding: 32rpx;
}

.section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .course-description {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
  }

  .learning-goals {
    .goal-item {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 16rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  .target-audience {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
  }
}

.chapter-list {
  .chapter-group {
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid #f0f0f0;
  }

  .main-chapter {
    display: flex;
    align-items: center;
    padding: 32rpx 28rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    transition: all 0.3s ease;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      pointer-events: none;
    }

    .chapter-number {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10rpx);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-weight: 700;
      margin-right: 24rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
    }

    .chapter-info {
      flex: 1;

      .chapter-title {
        font-size: 32rpx;
        color: #fff;
        font-weight: 600;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }

      .chapter-meta {
        display: flex;
        gap: 24rpx;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);

        .sub-count {
          background: rgba(255, 255, 255, 0.2);
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
          backdrop-filter: blur(10rpx);
        }
      }
    }

    .chapter-toggle {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .sub-chapters {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);

    .sub-chapter {
      display: flex;
      align-items: center;
      padding: 28rpx 32rpx;
      border-bottom: 1rpx solid #f0f0f0;
      position: relative;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        transform: translateX(8rpx);
      }

      &:last-child {
        border-bottom: none;
        border-radius: 0 0 20rpx 20rpx;
      }

      &::before {
        content: '';
        position: absolute;
        left: 32rpx;
        top: 0;
        width: 4rpx;
        height: 100%;
        background: linear-gradient(to bottom, #667eea, #764ba2);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }

      .chapter-number {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: 600;
        margin-right: 24rpx;
        margin-left: 20rpx;
        box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

        &.sub {
          width: 64rpx;
          font-size: 18rpx;
        }
      }

      .chapter-info {
        flex: 1;

        .chapter-title {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 12rpx;
          font-weight: 500;
          line-height: 1.4;
        }

        .chapter-meta {
          display: flex;
          gap: 16rpx;
          font-size: 24rpx;
          color: #666;
          flex-wrap: wrap;

          .trial-tag {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: #fff;
            padding: 6rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
          }

          .locked-tag {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: #fff;
            padding: 6rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.3);
          }

          .video-tag {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: #fff;
            padding: 6rpx 16rpx;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
          }
        }
      }

      .chapter-status {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(102, 126, 234, 0.2);
          transform: scale(1.1);
        }
      }
    }
  }
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;

  .rating-score {
    font-size: 48rpx;
    font-weight: 600;
    color: #ff6b35;
  }

  .rating-count {
    font-size: 26rpx;
    color: #999;
  }
}

.review-list {
  .review-item {
    padding: 32rpx;
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 16rpx;

    .review-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .user-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .user-info {
        flex: 1;

        .user-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .review-date {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .review-content {
      font-size: 28rpx;
      line-height: 1.6;
      color: #666;
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;

  .price-info {
    display: flex;
    align-items: baseline;

    .price-symbol {
      font-size: 24rpx;
      color: #ff6b35;
    }

    .price-value {
      font-size: 40rpx;
      font-weight: 600;
      color: #ff6b35;
    }

    .original-price {
      font-size: 24rpx;
      color: #999;
      text-decoration: line-through;
      margin-left: 12rpx;
    }
  }

  .action-buttons {
    .buy-button, .study-button {
      padding: 20rpx 48rpx;
      border-radius: 40rpx;
      font-size: 30rpx;
      font-weight: 600;
      border: none;
    }

    .buy-button {
      background-color: #ff6b35;
      color: #fff;
    }

    .study-button {
      background-color: #4CAF50;
      color: #fff;
    }
  }
}
</style>
