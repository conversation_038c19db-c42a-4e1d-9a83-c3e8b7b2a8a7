<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">排班设置测试</text>
      <text class="test-desc">测试每日独立排班设置功能</text>
    </view>

    <!-- 功能说明 -->
    <view class="feature-section">
      <text class="section-title">新功能特点</text>
      <view class="feature-list">
        <text class="feature-item">✅ 每天可以独立设置工作时间</text>
        <text class="feature-item">✅ 简单的开关控制</text>
        <text class="feature-item">✅ 直观的时间选择</text>
        <text class="feature-item">✅ 清晰的界面设计</text>
      </view>
    </view>

    <!-- 示例数据展示 -->
    <view class="example-section">
      <text class="section-title">示例排班数据</text>
      <view class="example-template">
        <view class="template-header">
          <text class="template-name">灵活排班模板</text>
          <text class="template-desc">每天不同时间段的排班示例</text>
        </view>

        <view class="schedule-preview">
          <view class="day-preview" v-for="(day, index) in exampleSchedule" :key="index">
            <view class="day-name">{{ weekdays[index] }}</view>
            <view class="day-status" v-if="!day.isWorking">休息</view>
            <view class="time-slots" v-else>
              <view class="time-slot">
                {{ day.startTime }} - {{ day.endTime }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="usage-section">
      <text class="section-title">使用说明</text>
      <view class="usage-steps">
        <view class="step-item">
          <text class="step-number">1</text>
          <text class="step-text">点击每天的开关来启用/禁用工作日</text>
        </view>
        <view class="step-item">
          <text class="step-number">2</text>
          <text class="step-text">为每个工作日设置开始和结束时间</text>
        </view>
        <view class="step-item">
          <text class="step-number">3</text>
          <text class="step-text">保存模板并应用到日期范围</text>
        </view>
      </view>
    </view>

    <!-- 跳转按钮 -->
    <view class="action-section">
      <button class="test-btn" @click="goToScheduleSetting">
        前往排班设置页面测试
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 示例排班数据
const exampleSchedule = ref([
  // 周一：全天工作
  { isWorking: true, startTime: '09:00', endTime: '18:00' },
  // 周二：上午工作
  { isWorking: true, startTime: '09:00', endTime: '12:00' },
  // 周三：全天工作
  { isWorking: true, startTime: '08:30', endTime: '17:30' },
  // 周四：下午工作
  { isWorking: true, startTime: '14:00', endTime: '18:00' },
  // 周五：半天工作
  { isWorking: true, startTime: '09:00', endTime: '13:00' },
  // 周六：休息
  { isWorking: false, startTime: '09:00', endTime: '18:00' },
  // 周日：短时间工作
  { isWorking: true, startTime: '10:00', endTime: '15:00' }
])

// 跳转到排班设置页面
const goToScheduleSetting = () => {
  uni.navigateTo({
    url: '/pages/schedule/setting/index'
  })
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.test-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;

  .test-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .test-desc {
    font-size: 24rpx;
    color: #666;
  }
}

.feature-section,
.example-section,
.usage-section {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;

  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
}

.feature-list {
  .feature-item {
    display: block;
    font-size: 26rpx;
    color: #555;
    margin-bottom: 12rpx;
    line-height: 1.5;
  }
}

.example-template {
  .template-header {
    margin-bottom: 20rpx;

    .template-name {
      display: block;
      font-size: 26rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }

    .template-desc {
      font-size: 22rpx;
      color: #666;
    }
  }

  .schedule-preview {
    .day-preview {
      display: flex;
      align-items: center;
      padding: 15rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .day-name {
        width: 100rpx;
        font-size: 24rpx;
        font-weight: bold;
        color: #333;
      }

      .day-status {
        flex: 1;
        font-size: 22rpx;
        color: #999;
      }

      .time-slots {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .time-slot {
          padding: 6rpx 12rpx;
          background-color: #e6f7ff;
          color: #1890ff;
          border-radius: 4rpx;
          font-size: 20rpx;
        }
      }
    }
  }
}

.usage-steps {
  .step-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20rpx;

    .step-number {
      width: 40rpx;
      height: 40rpx;
      background-color: #1890ff;
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      font-weight: bold;
      margin-right: 15rpx;
      flex-shrink: 0;
    }

    .step-text {
      flex: 1;
      font-size: 24rpx;
      color: #555;
      line-height: 1.5;
      padding-top: 8rpx;
    }
  }
}

.action-section {
  text-align: center;

  .test-btn {
    width: 80%;
    padding: 20rpx 0;
    background-color: #1890ff;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: bold;
  }
}
</style>
