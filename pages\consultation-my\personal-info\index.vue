<template>
  <view class="personal-info-container">
    <!-- 头像设置 -->
    <view class="avatar-section">
      <text class="section-title">头像</text>
      <view class="avatar-item" @click="changeAvatar">
        <image class="avatar" :src="profileForm.avatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
        <view class="avatar-edit">
          <image src="/static/icon/camera.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <view class="form-item" @click="editName">
        <text class="item-label">姓名</text>
        <view class="item-content">
          <text class="item-value">{{ profileForm.name || '请设置姓名' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="form-item" @click="editTitle">
        <text class="item-label">职业头衔</text>
        <view class="item-content">
          <text class="item-value">{{ profileForm.title || '请设置职业头衔' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="form-item">
        <text class="item-label">性别</text>
        <view class="item-content">
          <picker :range="genderOptions" range-key="label" @change="onGenderChange">
            <view class="picker-content">
              <text class="item-value">{{ getGenderLabel(profileForm.gender) }}</text>
              <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
      </view>

      <view class="form-item">
        <text class="item-label">生日</text>
        <view class="item-content">
          <picker mode="date" :value="profileForm.birthday" @change="onBirthdayChange">
            <view class="picker-content">
              <text class="item-value">{{ profileForm.birthday || '请选择生日' }}</text>
              <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
            </view>
          </picker>
        </view>
      </view>

      <view class="form-item" @click="editPhone">
        <text class="item-label">手机号</text>
        <view class="item-content">
          <text class="item-value">{{ formatPhone(profileForm.phone) || '请设置手机号' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="form-item" @click="editEmail">
        <text class="item-label">邮箱</text>
        <view class="item-content">
          <text class="item-value">{{ profileForm.email || '请设置邮箱' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 专业信息 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">专业信息</text>
      </view>

      <view class="form-item" @click="editSpecialty">
        <text class="item-label">专业领域</text>
        <view class="item-content">
          <text class="item-value">{{ profileForm.specialty || '请设置专业领域' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="form-item" @click="editExperience">
        <text class="item-label">从业经验</text>
        <view class="item-content">
          <text class="item-value">{{ profileForm.experience || '请设置从业经验' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="form-item" @click="editIntroduction">
        <text class="item-label">个人简介</text>
        <view class="item-content">
          <text class="item-value">{{ profileForm.introduction || '请设置个人简介' }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <button class="save-btn" @click="saveProfile" :disabled="saving">
        {{ saving ? '保存中...' : '保存' }}
      </button>
    </view>

    <!-- 编辑弹窗 -->
    <view class="edit-modal" v-if="showEditModal" @click="closeEditModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">编辑{{ currentEditField.label }}</text>
          <text class="modal-close" @click="closeEditModal">×</text>
        </view>
        <view class="modal-body">
          <textarea 
            v-if="currentEditField.type === 'textarea'"
            class="edit-textarea"
            v-model="editValue"
            :placeholder="`请输入${currentEditField.label}`"
            :maxlength="currentEditField.maxLength || 100"
            :show-count="true"
          ></textarea>
          <input 
            v-else
            class="edit-input"
            v-model="editValue"
            :placeholder="`请输入${currentEditField.label}`"
            :maxlength="currentEditField.maxLength || 50"
          />
        </view>
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeEditModal">取消</button>
          <button class="btn-confirm" @click="confirmEdit">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  getConsultantProfile, 
  updateConsultantProfile, 
  uploadConsultantAvatar 
} from '@/api/consultant-app.js'

// 响应式数据
const saving = ref(false)
const showEditModal = ref(false)
const editValue = ref('')
const currentEditField = ref({})

// 个人资料表单
const profileForm = ref({
  avatar: '',
  name: '',
  title: '',
  gender: '',
  birthday: '',
  phone: '',
  email: '',
  specialty: '',
  experience: '',
  introduction: ''
})

// 性别选项
const genderOptions = ref([
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
])

// 生命周期
onMounted(() => {
  loadProfile()
})

// 加载个人资料
const loadProfile = async () => {
  try {
    const res = await getConsultantProfile()
    if (res.code === 200) {
      profileForm.value = { ...profileForm.value, ...res.data }
    }
  } catch (error) {
    console.error('加载个人资料失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取性别标签
const getGenderLabel = (gender) => {
  const option = genderOptions.value.find(item => item.value === gender)
  return option ? option.label : '请选择性别'
}

// 更换头像
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0]
      
      uni.showLoading({
        title: '上传中...'
      })
      
      try {
        const uploadRes = await uploadConsultantAvatar({
          file: tempFilePath
        })
        
        if (uploadRes.code === 200) {
          profileForm.value.avatar = uploadRes.data.avatarUrl
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('上传头像失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    }
  })
}

// 编辑字段
const editField = (field, label, type = 'input', maxLength = 50) => {
  currentEditField.value = { field, label, type, maxLength }
  editValue.value = profileForm.value[field] || ''
  showEditModal.value = true
}

// 编辑姓名
const editName = () => {
  editField('name', '姓名', 'input', 20)
}

// 编辑职业头衔
const editTitle = () => {
  editField('title', '职业头衔', 'input', 30)
}

// 编辑手机号
const editPhone = () => {
  editField('phone', '手机号', 'input', 11)
}

// 编辑邮箱
const editEmail = () => {
  editField('email', '邮箱', 'input', 50)
}

// 编辑专业领域
const editSpecialty = () => {
  editField('specialty', '专业领域', 'input', 100)
}

// 编辑从业经验
const editExperience = () => {
  editField('experience', '从业经验', 'input', 100)
}

// 编辑个人简介
const editIntroduction = () => {
  editField('introduction', '个人简介', 'textarea', 500)
}

// 性别选择
const onGenderChange = (e) => {
  const index = e.detail.value
  profileForm.value.gender = genderOptions.value[index].value
}

// 生日选择
const onBirthdayChange = (e) => {
  profileForm.value.birthday = e.detail.value
}

// 确认编辑
const confirmEdit = () => {
  if (!editValue.value.trim()) {
    uni.showToast({
      title: `请输入${currentEditField.value.label}`,
      icon: 'none'
    })
    return
  }
  
  profileForm.value[currentEditField.value.field] = editValue.value.trim()
  closeEditModal()
}

// 关闭编辑弹窗
const closeEditModal = () => {
  showEditModal.value = false
  editValue.value = ''
  currentEditField.value = {}
}

// 保存个人资料
const saveProfile = async () => {
  saving.value = true
  
  try {
    const res = await updateConsultantProfile(profileForm.value)
    if (res.code === 200) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  } finally {
    saving.value = false
  }
}
</script>

<style scoped lang="scss">
.personal-info-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.avatar-section {
  background-color: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  
  .section-title {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 30rpx;
  }
  
  .avatar-item {
    position: relative;
    display: inline-block;
    
    .avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      border: 4rpx solid #f0f0f0;
    }
    
    .avatar-edit {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 50rpx;
      height: 50rpx;
      background-color: #1890ff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      image {
        width: 24rpx;
        height: 24rpx;
        filter: brightness(0) invert(1);
      }
    }
  }
}

.form-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  
  .section-header {
    padding: 30rpx 30rpx 0;
    
    .section-title {
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .form-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-label {
      font-size: 30rpx;
      color: #333;
      width: 160rpx;
      flex-shrink: 0;
    }
    
    .item-content,
    .picker-content {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .item-value {
        font-size: 30rpx;
        color: #666;
        flex: 1;
        text-align: right;
        margin-right: 20rpx;
      }
      
      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}

.save-section {
  padding: 30rpx;
  
  .save-btn {
    width: 100%;
    padding: 28rpx 0;
    background-color: #1890ff;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 30rpx;
    font-weight: bold;
    
    &:disabled {
      background-color: #d9d9d9;
      color: #999;
    }
  }
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .modal-content {
    width: 600rpx;
    background-color: #fff;
    border-radius: 20rpx;
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .modal-close {
        font-size: 40rpx;
        color: #999;
      }
    }
    
    .modal-body {
      padding: 30rpx;
      
      .edit-input,
      .edit-textarea {
        width: 100%;
        padding: 20rpx;
        border: 1rpx solid #d9d9d9;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333;
        box-sizing: border-box;
        
        &:focus {
          border-color: #1890ff;
        }
      }
      
      .edit-textarea {
        min-height: 200rpx;
        resize: none;
      }
    }
    
    .modal-footer {
      display: flex;
      gap: 20rpx;
      padding: 30rpx;
      border-top: 1rpx solid #f0f0f0;
      
      .btn-cancel,
      .btn-confirm {
        flex: 1;
        padding: 24rpx 0;
        border-radius: 12rpx;
        font-size: 28rpx;
        border: none;
      }
      
      .btn-cancel {
        background-color: #f5f5f5;
        color: #666;
      }
      
      .btn-confirm {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
}
</style>
