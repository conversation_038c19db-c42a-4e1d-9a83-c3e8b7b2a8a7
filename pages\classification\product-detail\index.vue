<template>
	<view class="content">
		<view class="product-picture">
			<image mode="aspectFill" :src="productInfo.productImage" @error="error"></image>
		</view>
		<view class="info-box">
			<!-- 价格区块 -->
			<view class="product-info">
				<view class="price-symbol">￥</view>
				<view class="current-price">{{ productInfo.discountPrice }}</view>
				<view class="discount-tag">{{ calculateDiscount(productInfo) }}</view>
				<view class="original-price">￥{{ productInfo.originalPrice }}</view>
			</view>
			<view class="product-text-info">
				<view class="product-title">{{ productInfo.productName }}</view>
				<view class="product-text">{{ formatServiceDirection(productInfo.serviceDirection) }}</view>
				<view class="line"></view>
				<view class="group-details">
					<view class="details-title">服务详情</view>
					<template v-if="productInfo.serviceItems?.length">
						<view class="service-base-info">
							<view class="info-item" v-if="consultantGrade">
								<text class="info-label">服务等级</text>
								<text class="info-value">{{ consultantGrade }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">服务方式</text>
								<text class="info-value">{{ formatServiceType }}</text>
							</view>
						</view>
						<view v-for="serviceItem in productInfo.serviceItems" :key="serviceItem.itemId"
							class="service-item-wrapper">
							<view class="service-item-header">
								<text class="service-name">{{ serviceItem.itemName }}</text>
							</view>
							<view class="details-item" v-if="serviceItem.contents?.length">
								<view class="item-label">服务内容</view>
								<view class="item-value service-content">
									<view class="content-item" v-for="(item, index) in serviceItem.contents" :key="item.contentId">
										<text class="item-number">{{ index + 1 }}</text>
										<text class="item-text">{{ item.content }}</text>
										<text class="item-duration">{{ item.duration }}</text>
									</view>
								</view>
							</view>
						</view>
					</template>
				</view>
				<view class="line"></view>
				<view class="notice-box" @click="openNoticePopup">
					<view class="notice-title"><text class="notice-title-text">须知</text>{{ productInfo.needAppointment ? '需预约' :
						'无需预约'
						}}·{{ productInfo.unavailableDates }}·购买后{{ productInfo.validityPeriod }}内有效<uni-icons type="right"
							size="16"></uni-icons></view>
				</view>
			</view>
			<view v-if="productInfo.graphicDetails" class="supplement-info">
				<view class="supplement-title">
					<uni-icons type="info" size="16" color="#088FEB"></uni-icons>
					<text>补充说明</text>
				</view>
				<view class="supplement-content">{{ productInfo.graphicDetails }}</view>
			</view>
			<view class="detail-section">
				<view class="section-title">
					<uni-icons type="image" size="16" color="#088FEB"></uni-icons>
					<text>图文详情</text>
				</view>
				<view v-if="productInfo.supplementInfo" class="section-content">{{ productInfo.supplementInfo }}</view>
				<view class="image-container">
					<view class="image-item" v-for="item in 4" :key="item">
						<!-- <image mode="widthFix" :src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E7%BE%8E%E5%9B%A2%E8%AF%A6%E6%83%85%E9%A1%B5_${item}.jpg`" @error="error"></image> -->
					</view>
				</view>
			</view>
			<view style="height: 140rpx;"></view>
			<view class="footer">
				<uni-goods-nav class="footer-goods-nav" :fill="true" :options="bottomOptions" :button-group="customButtonGroup"
					@click="onClick" @buttonClick="buttonClick" />
			</view>
		</view>
		<uni-popup background-color="#fff" class="bottom-popup" ref="popup" type="bottom" border-radius="30rpx 30rpx 0 0">
			<view class="title">购买须知</view>
			<view v-for="item in noticeList" :key="item.id" class="notice-list">
				<view class="icon-label">
					<view class="text">{{ item.title }}</view>
				</view>
				<view class="notice-content">{{ item.content }}</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted, nextTick, getCurrentInstance, computed, watch } from "vue";
import { useChatStore } from "@/stores/chat";
// import { getProduct } from "@/api/classification.js";
import { onLoad } from "@dcloudio/uni-app";
import { useDict } from "@/utils/index.js";
import { useUserStore } from "@/stores/user";
import { createConversation } from "@/api/message.js";
import { addFavorite, removeFavorite, checkFavorite, FAVORITE_TYPES } from "@/api/favorite.js";

onLoad(async (options) => {
	console.log(options);
	productId.value = options.productId;

	try {
		// 先检查收藏状态
		const checkRes = await checkFavorite({
			targetType: FAVORITE_TYPES.COURSE,
			targetId: options.productId
		});
		if (checkRes.data) {
			nextTick(() => {
				bottomOptions.value[0].icon = "star-filled";
				productInfo.value.favoriteId = checkRes.data.favoriteId;
			});
		}

		// 再加载商品信息
		// const res = await getProduct(options.productId);
		// productInfo.value = res.data;
		// if (res.data.serviceDirectionType) {
		// 	const dictData = await useDict(res.data.serviceDirectionType);
		// 	serviceType.value = dictData[res.data.serviceDirectionType] || [];
		// }
	} catch (error) {
		console.error("加载数据失败:", error);
		uni.showToast({
			title: "加载数据失败",
			icon: "none"
		});
	}
});
const productId = ref("");
const productInfo = ref({});
const chatStore = useChatStore();
const userStore = useUserStore();

const bottomOptions = ref([
	{
		icon: "star",
		text: "收藏",
		infoBackgroundColor: "#007aff",
		infoColor: "#f5f5f5",
		isStar: false,
	},
	{
		icon: "chat",
		text: "客服",
	},
	// {
	// 	icon: "cart",
	// 	text: "购物车",
	// 	info: 2,
	// },
]);
const serviceType = ref([]);

const customButtonGroup = ref([
	{
		text: "立即抢购",
		backgroundColor: "linear-gradient(90deg, #60F3FF, #088FEB)",
		color: "#fff",
	},
]);

const formatServiceDirection = (serviceDirection) => {
	if (!serviceDirection || !serviceType.value.length) return "";
	let serviceDirectionList = serviceDirection.split(",");
	let serviceDirectionListStr = "";
	serviceDirectionList.forEach((item, index) => {
		const found = serviceType.value.find(type => type.value === item);
		if (found?.label) {
			serviceDirectionListStr += found.label + (index < serviceDirectionList.length - 1 ? " | " : "");
		}
	});
	return serviceDirectionListStr.trim();
};

const onClick = async (e) => {
	if (e.index == 0) {
		// 点击收藏按钮
		try {
			if (e.content.icon == "star-filled") {
				e.content.icon = "star";
				await removeFavorite(productInfo.value.favoriteId);
				productInfo.value.favoriteId = null; // 清除收藏ID
				uni.showToast({
					title: '取消收藏成功',
					icon: 'none'
				});
			} else if (e.content.icon == "star") {
				e.content.icon = "star-filled";
				const { data } = await addFavorite({
					targetType: FAVORITE_TYPES.COURSE,
					targetId: productInfo.value.productId,
					targetTitle: productInfo.value.productName,
					targetImage: productInfo.value.productImage
				});
				if (data?.favoriteId) {
					productInfo.value.favoriteId = data.favoriteId; // 保存返回的收藏ID
				} else {
					console.error('收藏成功但未返回ID');
				}
				uni.showToast({
					title: '收藏成功',
					icon: 'none'
				});
			}
			e.content.isStar = !e.content.isStar;
		} catch (error) {
			console.error('收藏操作失败:', error);
			uni.showToast({
				title: '操作失败,请重试',
				icon: 'none'
			});
		}
	} else if (e.index == 1) {
		// 点击客服按钮
		// 固定客服ID为137
		const customerServiceId = "137";
		// 尝试创建会话（如果已存在会返回现有会话）
		try {
			const { data } = await createConversation(customerServiceId);
			const conversationId = data?.conversationId || customerServiceId;
			uni.navigateTo({
				url: `/pages/my/my-message/chat/chat?conversationId=${conversationId}&userId=${userStore.userId}&consultantId=${customerServiceId}&nickname=熙桓心理客服`
			});
		} catch (error) {
			console.error("创建会话失败", error);
			uni.showToast({
				title: "连接客服失败",
				icon: "none"
			});
		}
	}
};

const buttonClick = async (e) => {
	if (e.index == 0) {
		// 私信按钮 - 联系咨询师
		if (!productInfo.value.counselorId) {
			uni.showToast({
				title: "咨询师信息不存在",
				icon: "none"
			});
			return;
		}

		try {
			// 尝试创建会话（如果已存在会返回现有会话）
			const { data } = await createConversation(productInfo.value.counselorId);
			const conversationId = data?.conversationId || productInfo.value.counselorId;
			uni.navigateTo({
				url: `/pages/my/my-message/chat/chat?conversationId=${conversationId}&userId=${userStore.userId}&consultantId=${productInfo.value.counselorId}&nickname=${productInfo.value.consultantName || '咨询师'}`
			});
		} catch (error) {
			console.error("创建会话失败", error);
			uni.showToast({
				title: "连接私信失败",
				icon: "none"
			});
		}
	} else {
		// 立即抢购按钮
		uni.navigateTo({
			url: '/pages/order/order-info/order-info'
		});
	}
};

const calculateDiscount = (product) => {
	// 异常数据过滤
	if (
		!product ||
		typeof product.originalPrice !== "number" ||
		typeof product.discountPrice !== "number" ||
		product.originalPrice <= 0 ||
		product.discountPrice <= 0
	) {
		return "无折扣";
	}

	// 折扣无效情况处理
	if (product.discountPrice >= product.originalPrice) {
		return "无折扣";
	}

	// 计算折扣率（保留一位小数）
	const discountRate = (product.discountPrice / product.originalPrice * 10).toFixed(1);

	// 过滤无效折扣显示
	if (discountRate >= 10 || discountRate <= 0) {
		return "无折扣";
	}

	return `${discountRate}折`;
};

const popup = ref(null);

const noticeList = computed(() => [
	{
		id: 1,
		title: '有效期',
		content: `购买后${productInfo.value.validityPeriod || ''}内有效，请在有效期内使用`
	},
	{
		id: 2,
		title: '预约信息',
		content: productInfo.value.needAppointment ? productInfo.value.needAppointment : '本服务无需预约，可直接使用'
	},
	// {
	// 	id: 3,
	// 	title: '规则提醒',
	// 	content: '购买成功后不支持退款，请谨慎购买'
	// },
	{
		id: 4,
		title: '温馨提示',
		content: '如有任何问题，请联系在线客服咨询'
	}
]);

const openNoticePopup = () => {
	popup.value.open();
};

// 获取咨询师等级
const consultantGrade = ref('');

const loadConsultantGrade = async () => {
	try {
		const dictData = await useDict('psy_consultant_level');
		if (dictData?.psy_consultant_level && productInfo.value.consultantGrade) {
			const grade = dictData.psy_consultant_level.find(item => item.dictValue === productInfo.value.consultantGrade);
			consultantGrade.value = grade?.dictLabel || '';
		}
	} catch (e) {
		console.error('获取咨询师等级失败:', e);
	}
};

// 监听productInfo变化，加载咨询师等级
watch(() => productInfo.value, async (newVal) => {
	if (newVal?.consultantGrade) {
		await loadConsultantGrade();
	}
}, { immediate: true });

// 格式化服务方式
const formatServiceType = computed(() => {
	const types = [];
	if (productInfo.value.isOffline) types.push('到店面谈');
	if (productInfo.value.isOnline) types.push('视频');
	return types.join('、') || '到店面谈、视频';
});

// 获取服务项目内容
const currentServiceItem = computed(() => {
	if (!productInfo.value.serviceItems?.length) return null;
	return productInfo.value.serviceItems[0]; // 默认显示第一个服务项目
});

</script>

<style lang="scss" scoped>
.content {
	width: 100%;

	.product-picture {
		padding: 0 20rpx;
		width: calc(100% - 40rpx);
		height: 400rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.info-box {
		margin: 0 20rpx;
		width: calc(100% - 40rpx);
		background-color: #f4f4f4;
		border-radius: 20rpx;

		.product-info {
			padding: 20rpx;
			display: flex;
			align-items: baseline;

			.price-symbol {
				font-size: 24rpx;
				font-weight: 700;
				color: #fe4611;
				line-height: 1;
			}

			.current-price {
				font-size: 52rpx;
				color: #f30;
				padding-right: 10rpx;
				font-weight: 700;
				line-height: 1;
			}

			.discount-tag {
				color: #ff4444;
				padding: 4rpx 8rpx;
				border: 1rpx solid #fc4c13;
				border-radius: 6rpx;
				font-size: 24rpx;
				line-height: 1;
				margin: 0 10rpx;
				align-self: center;
			}

			.original-price {
				text-decoration: line-through;
				color: #999;
				font-size: 26rpx;
				line-height: 1;
			}
		}

		.product-text-info {
			margin: 20rpx;
			padding: 20rpx;
			border-radius: 20rpx;
			background-color: #fff;

			.product-title {
				font-size: 30rpx;
				font-weight: 700;
				padding-bottom: 10rpx;
			}

			.line {
				width: 100%;
				height: 1rpx;
				background-color: #e5e5e5;
				margin: 10rpx 0;
			}

			.product-text {
				font-size: 26rpx;
				padding-bottom: 10rpx;
			}

			.notice-title {
				font-size: 26rpx;
				padding-bottom: 10rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.notice-title-text {
					font-weight: 700;
					margin-right: 10rpx;
				}
			}

			.product-price {
				color: #d05f41;
				font-weight: 700;
			}

			.group-details {
				margin: 20rpx 0;
				background-color: #fff;
				border-radius: 16rpx;

				.details-title {
					font-size: 32rpx;
					font-weight: bold;
					margin: 24rpx 0;
					color: #333;
					position: relative;
					padding-left: 20rpx;

					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 6rpx;
						height: 28rpx;
						background: linear-gradient(180deg, #60F3FF 0%, #088FEB 100%);
						border-radius: 3rpx;
					}
				}

				.service-base-info {

					.info-item {
						padding: 20rpx 24rpx;
						background: #f8f9fc;
						border-radius: 12rpx;
						margin-bottom: 12rpx;
						display: flex;
						align-items: center;

						&:last-child {
							margin-bottom: 0;
						}

						.info-label {
							font-size: 26rpx;
							color: #666;
							margin-right: 16rpx;
							min-width: 120rpx;
						}

						.info-value {
							flex: 1;
							font-size: 26rpx;
							color: #333;
							background: #fff;
							padding: 8rpx 24rpx;
							border-radius: 10rpx;
							border: 1rpx solid #eef0f6;
						}
					}
				}

				.service-item-wrapper {
					margin: 24rpx 0;
					background: #fff;
					border-radius: 16rpx;
					border: 2rpx solid #eef0f6;
					overflow: hidden;

					.service-item-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 24rpx;
						background: #f8f9fc;
						border-bottom: 2rpx solid #eef0f6;

						.service-name {
							font-size: 28rpx;
							font-weight: 600;
							color: #333;
							flex: 1;
							padding-right: 20rpx;
						}

						.service-price {
							font-size: 30rpx;
							font-weight: bold;
							color: #ff4444;

							&::before {
								content: '￥';
								font-size: 24rpx;
								margin-right: 4rpx;
							}
						}
					}

					.details-item {
						padding: 20rpx 24rpx;

						.item-label {
							font-size: 26rpx;
							color: #666;
							margin-bottom: 16rpx;
						}

						.item-value {
							font-size: 26rpx;
							color: #333;
							line-height: 1.6;

							&.service-content {
								.content-item {
									display: flex;
									align-items: center;
									padding: 16rpx;
									margin-bottom: 12rpx;
									background: #f8f9fc;
									border-radius: 8rpx;

									&:last-child {
										margin-bottom: 0;
									}

									.item-number {
										width: 32rpx;
										height: 32rpx;
										background: linear-gradient(135deg, #60F3FF 0%, #088FEB 100%);
										border-radius: 50%;
										display: flex;
										align-items: center;
										justify-content: center;
										font-size: 22rpx;
										color: #fff;
										margin-right: 16rpx;
									}

									.item-text {
										flex: 1;
										font-size: 26rpx;
										color: #333;
									}

									.item-duration {
										color: #088FEB;
										font-size: 24rpx;
										background: rgba(8, 143, 235, 0.1);
										padding: 4rpx 12rpx;
										border-radius: 20rpx;
									}
								}
							}
						}
					}
				}
			}
		}

		.supplement-info {
			margin: 20rpx;
			padding: 24rpx;
			background: #fff;
			border-radius: 16rpx;

			.supplement-title {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;

				text {
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-left: 8rpx;
				}
			}

			.supplement-content {
				font-size: 26rpx;
				color: #666;
				line-height: 1.6;
				padding-left: 32rpx;
				white-space: pre-wrap;
			}
		}
	}

	.image-container {
		width: 100%;

		image {
			// padding: 0 20rpx;
			width: 100%;
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: calc(100% - 80rpx);
		height: 120rpx;
		background-color: #fff;
		z-index: 9;
		margin-top: 100rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;

		.collection {
			width: 80rpx;
			// height: 60rpx;
			margin-right: 20rpx;
			font-size: 24rpx;
			text-align: center;

			image {
				width: 100%;
				height: 100%;
				margin-bottom: -5rpx;
			}
		}

		.footer-goods-nav {
			width: 100%;

			:deep .uni-tab__cart-button-right-text {
				font-size: 30rpx !important;
			}
		}

		.active {
			color: #06adfc;
		}

		.private-message {
			width: calc(50% - 60rpx);
			margin-right: 20rpx;

			button {
				background-color: #eaf7ff;
				border: none !important;
				color: #52b5f9;
				font-weight: 700;
				border-radius: 70rpx;
			}

			uni-button:after {
				border: none !important;
			}
		}

		.consult {
			width: calc(50% - 60rpx);

			button {
				background-color: #52b5f9;
				border: none !important;
				color: #fff;
				font-weight: 700;
				border-radius: 70rpx;
			}
		}
	}
}

.bottom-popup {
	.title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.notice-list {
		padding: 20rpx 30rpx;

		.icon-label {
			display: flex;
			align-items: center;
			margin-bottom: 10rpx;

			.text {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
			}
		}

		.notice-content {
			font-size: 26rpx;
			color: #666;
			line-height: 1.5;
		}
	}
}

.detail-section {
	margin: 20rpx;
	padding: 24rpx;
	background: #fff;
	border-radius: 16rpx;

	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;

		text {
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			margin-left: 8rpx;
		}
	}

	.section-content {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 20rpx;
		white-space: pre-wrap;
	}

	.image-container {
		.image-item {
			margin-bottom: 20rpx;
			border-radius: 12rpx;
			overflow: hidden;

			&:last-child {
				margin-bottom: 0;
			}

			image {
				width: 100%;
				display: block;
			}
		}
	}
}
</style>
