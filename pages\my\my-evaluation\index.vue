<template>
  <view class="my-evaluation">
    <!-- 分类标签 -->
    <view class="category-tabs">
      <view 
        class="tab-item" 
        v-for="tab in tabList" 
        :key="tab.key"
        :class="{ active: currentTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        <text>{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 测评列表 -->
    <scroll-view class="evaluation-list" scroll-y>
      <view class="list-item" v-for="item in currentList" :key="item.id" @click="viewDetail(item.scaleId)">
        <view class="item-main">
          <text class="title">{{item.scaleName || '未知测评'}}</text>
          <text class="desc">{{item.resultDescription || '暂无描述'}}</text>
        </view>
        <view class="item-footer">
          <view class="footer-left">
            <view class="status-icon" :class="getStatusClass(item.status)">
              <text class="icon">{{getStatusIcon(item.status)}}</text>
            </view>
            <view class="info-group">
              <text v-if="item.totalQuestions" class="question-count">{{item.totalQuestions}}道题</text>
              <text v-if="item.totalScore" class="score">{{item.totalScore}}分</text>
            </view>
          </view>
          <view class="footer-right">
            <text class="time">{{formatTime(item.startTime)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty" v-if="currentList.length === 0">
        <text>暂无测评记录</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from "@dcloudio/uni-app";
import { getAssessmentRecords } from '@/api/evaluation'
import {formatTime} from '@/utils/index'

const currentTab = ref('all')
const evaluationList = ref([])
const loading = ref(false)

// 分类标签配置
const tabList = [
  { key: 'all', name: '全部' },
  { key: 'pending', name: '待完成' },
  { key: 'completed', name: '已完成' }
]

// 计算当前分类下的列表
const currentList = computed(() => {
  if (currentTab.value === 'all') {
    return evaluationList.value
  }
  return evaluationList.value.filter(item => {
    if (currentTab.value === 'pending') {
      return item.status === 0 || item.status === 1 // 进行中或暂停状态
    }
    if (currentTab.value === 'completed') {
      return item.status === 2 // 已完成状态
    }
    return true
  })
})

onLoad(() => {
  getList()
})

// 切换分类
const switchTab = (tabKey) => {
  currentTab.value = tabKey
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '进行中',
    1: '暂停中',
    2: '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'in_progress',
    1: 'paused',
    2: 'completed'
  }
  return classMap[status] || 'pending'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const iconMap = {
    0: '⏳',
    1: '⏸️',
    2: '✅'
  }
  return iconMap[status] || '❓'
}

// 格式化时间
// const formatTime = (timeStr) => {
//   if (!timeStr) return ''
//   const date = new Date(timeStr)
//   return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
// }

// 获取列表数据
const getList = async () => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await getAssessmentRecords()
    if (res.code === 200) {
      evaluationList.value = res.data || []
    } else {
      uni.showToast({
        title: res.msg || '获取列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取测评记录失败:', error)
    uni.showToast({
      title: '获取列表失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = (scaleId) => {
  // 查找对应的测评记录
  const record = evaluationList.value.find(record => record.scaleId === scaleId)
  if (record) {
    // 如果有记录，传递recordId和status
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${scaleId}&recordId=${record.id}&status=${record.status}`
    })
  } else {
    // 没有记录，正常跳转
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${scaleId}`
    })
  }
}
</script>

<style lang="scss" scoped>
.my-evaluation {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.my-evaluation-header {
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  
  .my-evaluation-header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }
}

.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #eee;
  
  .tab-item {
    flex: 1;
    padding: 30rpx 0;
    text-align: center;
    font-size: 28rpx;
    color: #666;
    position: relative;
    
    &.active {
      color: #409eff;
      font-weight: bold;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #409eff;
        border-radius: 2rpx;
      }
    }
  }
}

.evaluation-list {
  width: calc(100vw - 40rpx);
  height: calc(100vh - 140rpx);
  padding: 20rpx;
  
  .list-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    .item-image {
      width: 100%;
      height: 300rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
    }

    .item-main {
      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .desc {
        font-size: 28rpx;
        color: #666;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }

    .item-footer {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .footer-left {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 15rpx;
        
        .status-icon {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .icon {
            font-size: 20rpx;
          }
          
          &.pending {
            background-color: #fff7e6;
            color: #ff9500;
          }
          
          &.in_progress {
            background-color: #f0f9ff;
            color: #409eff;
          }

          &.paused {
            background-color: #fef0f0;
            color: #f56c6c;
          }

          &.completed {
            background-color: #f0f9ff;
            color: #67c23a;
          }
        }
        
        .info-group {
          display: flex;
          align-items: center;
          gap: 15rpx;
          
          .question-count {
            font-size: 22rpx;
            color: #666;
            background-color: #f5f5f5;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
          }
          
          .score {
            font-size: 28rpx;
            color: #fff;
            font-weight: bold;
            background: linear-gradient(135deg, #409eff, #67c23a);
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.3);
          }
        }
      }

      .footer-right {
        display: flex;
        align-items: center;
        gap: 15rpx;
        
        .time {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }

  .empty {
    text-align: center;
    padding: 100rpx 0;
    color: #999;
  }
}
</style>