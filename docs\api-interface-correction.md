# 接口使用修正文档

## 修正说明

根据您的反馈，修正了咨询师接口的使用方式。`/miniapp/user/match/question/quickMatch` 接口是专门用于匹配结果的，分类页面应该直接返回全部咨询师。

## 🔧 修正内容

### **1. 咨询师列表接口修正**

#### **修正前（错误使用）**
```javascript
// 错误：在分类页面使用匹配接口
export function listAvailableConsultants(params = {}) {
  // 默认使用快速匹配获取推荐咨询师
  return request({
    url: '/miniapp/user/match/question/quickMatch',  // ❌ 错误使用
    method: 'POST',
    data: params
  })
}
```

#### **修正后（正确使用）**
```javascript
// 正确：分类页面直接返回全部咨询师
export function listAvailableConsultants(params = {}) {
  // 如果有时间筛选条件，使用时间筛选接口
  if (params.date && params.startTime && params.endTime) {
    return request({
      url: '/miniapp/timeSlot/counselors',
      method: 'GET',
      data: params
    })
  }
  
  // 分类页面直接返回全部咨询师列表
  return request({
    url: '/system/consultant/list',  // ✅ 正确使用
    method: 'GET',
    data: params
  })
}
```

### **2. 分类页面调用修正**

#### **修正前**
```javascript
case 'consultant':
  // 使用新的小程序接口获取咨询师
  res = await listAvailableConsultants({
    quickMatch: true  // ❌ 不必要的参数
  })
```

#### **修正后**
```javascript
case 'consultant':
  // 直接获取全部咨询师列表
  res = await listAvailableConsultants()  // ✅ 简化调用
```

### **3. 接口功能明确划分**

#### **分类页面接口**
```javascript
// 用于分类页面，返回全部咨询师
export function listAvailableConsultants(params = {}) {
  return request({
    url: '/system/consultant/list',
    method: 'GET',
    data: params
  })
}
```

#### **匹配功能接口**
```javascript
// 专门用于匹配结果
export function quickMatchConsultants(quickMatchData = {}) {
  return request({
    url: '/miniapp/user/match/question/quickMatch',
    method: 'POST',
    data: quickMatchData
  })
}

// 根据匹配问题获取咨询师
export function getConsultantsByMatch(matchData) {
  return request({
    url: '/miniapp/user/match/question/match',
    method: 'POST',
    data: matchData
  })
}
```

## 📊 接口使用场景

### **分类页面场景**
- **目的**: 展示所有可用的咨询师
- **接口**: `/system/consultant/list`
- **方法**: GET
- **特点**: 返回完整的咨询师列表，支持基础筛选

### **匹配功能场景**
- **目的**: 根据用户选择的问题选项匹配合适的咨询师
- **接口**: `/miniapp/user/match/question/match`
- **方法**: POST
- **特点**: 基于算法匹配，返回推荐的咨询师

### **快速匹配场景**
- **目的**: 快速为用户推荐咨询师
- **接口**: `/miniapp/user/match/question/quickMatch`
- **方法**: POST
- **特点**: 简化的匹配流程，快速返回结果

### **时间筛选场景**
- **目的**: 根据时间段筛选可用的咨询师
- **接口**: `/miniapp/timeSlot/counselors`
- **方法**: GET
- **特点**: 基于时间可用性筛选

## 🎯 正确的使用方式

### **1. 分类页面**
```javascript
// 获取全部咨询师（分类页面）
const consultants = await listAvailableConsultants()

// 根据时间筛选咨询师
const availableConsultants = await listAvailableConsultants({
  date: '2024-01-15',
  startTime: '09:00',
  endTime: '10:00'
})
```

### **2. 匹配页面**
```javascript
// 根据问题选项匹配咨询师
const matchedConsultants = await getConsultantsByMatch({
  optionIds: [1, 2, 3],
  preferences: {...}
})

// 快速匹配咨询师
const quickMatched = await quickMatchConsultants({
  userProfile: {...}
})
```

### **3. 搜索功能**
```javascript
// 搜索咨询师
const searchResults = await searchConsultants({
  keyword: '心理咨询',
  specialty: '焦虑'
})
```

## 🔄 数据流程

### **分类页面数据流程**
```
用户访问分类页面
    ↓
调用 listAvailableConsultants()
    ↓
请求 /system/consultant/list
    ↓
返回全部咨询师列表
    ↓
页面展示咨询师信息
```

### **匹配功能数据流程**
```
用户选择问题选项
    ↓
调用 getConsultantsByMatch(options)
    ↓
请求 /miniapp/user/match/question/match
    ↓
算法匹配合适的咨询师
    ↓
返回匹配结果
```

## 📝 接口参数说明

### **listAvailableConsultants 参数**
```javascript
{
  date?: string,        // 可选：筛选日期 'YYYY-MM-DD'
  startTime?: string,   // 可选：开始时间 'HH:mm'
  endTime?: string,     // 可选：结束时间 'HH:mm'
  pageNum?: number,     // 可选：页码
  pageSize?: number     // 可选：每页数量
}
```

### **getConsultantsByMatch 参数**
```javascript
{
  optionIds: number[],  // 必需：问题选项ID数组
  preferences?: object, // 可选：用户偏好
  filters?: object      // 可选：其他筛选条件
}
```

### **quickMatchConsultants 参数**
```javascript
{
  userProfile?: object, // 可选：用户画像
  quickFilters?: object // 可选：快速筛选条件
}
```

## ✅ 修正总结

1. **明确接口职责**: 分类页面使用 `/system/consultant/list`，匹配功能使用匹配相关接口
2. **简化调用方式**: 移除不必要的参数，直接调用基础接口
3. **保持功能完整**: 保留时间筛选等必要功能
4. **接口分离**: 将匹配功能和列表功能明确分离

现在接口使用更加合理和清晰，分类页面直接返回全部咨询师，匹配功能使用专门的匹配接口！
