<template>
	<view class="content" :class="{ 'has-open': hasOpenPanel }">
	<view style="padding: 20rpx; width: calc(100% - 40rpx);">
		<uni-forms ref="consultantForm" :rules="rules" :modelValue="consultantInfo"  >
			<view class="consultant-info" >
				<view class="custom-collapse">
					<view class="custom-collapse-header" @click="toggleConsultantInfo">
						<text class="title">咨询人信息</text>
						<uni-icons :type="isConsultantInfoOpen ? 'up' : 'down'" size="14"></uni-icons>
					</view>
					<view class="custom-collapse-content" :class="{ 'content-open': isConsultantInfoOpen }">
						<view class="collapse-content">
							<view class="avatar-upload">
								<button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
									<image :src="consultantInfo.avatar || userStore.userAvatar" mode="aspectFill" class="avatar-image"></image>
								</button>
							</view>
							<uni-forms-item label="姓名" required name="counselorName">
								<uni-easyinput v-model="consultantInfo.counselorName" placeholder="请输入姓名(昵称或化名)" />
							</uni-forms-item>
							<uni-forms-item label="出生年月" required name="birthdate">
								<view class="uni-list-cell-db">
									<picker
										mode="date"
										:value="consultantInfo.birthdate"
										:start="startDate"
										:end="endDate"
										@change="handleDateChange"
										fields="month">
										<view class="uni-input">
											<text v-if="consultantInfo.birthdate">{{ consultantInfo.birthdate }}</text>
											<text v-else class="placeholder">请选择出生年月</text>
										</view>
									</picker>
								</view>
							</uni-forms-item>
							<uni-forms-item label="性别" required name="sex">
								<uni-data-checkbox v-model="consultantInfo.sex" :localdata="sexsOptions" />
							</uni-forms-item>
							<uni-forms-item label="联系方式" required name="userPhone">
								<uni-easyinput v-model="consultantInfo.userPhone" placeholder="请输入联系方式" />
							</uni-forms-item>
						</view>
					</view>
				</view>
			</view>
			<view class="consultant-info">
				<view class="custom-collapse">
					<view class="custom-collapse-header" @click="toggleEmergencyContact">
						<text class="title">紧急联系人</text>
						<uni-icons :type="isEmergencyContactOpen ? 'up' : 'down'" size="14"></uni-icons>
					</view>
					<view class="custom-collapse-content" :class="{ 'content-open': isEmergencyContactOpen }">
						<view class="collapse-content">
							<uni-forms-item label="真实姓名" required name="emergencyContactName">
								<uni-easyinput v-model="consultantInfo.emergencyContactName" placeholder="请输入真实姓名" />
							</uni-forms-item>
							<uni-forms-item label="TA是您的" required name="emergencyRelation">
								<view class="uni-list-cell-db">
									<picker
										mode="selector"
										:range="emergencyRelationOptions"
										range-key="label"
										:value="consultantInfo.emergencyRelation"
										@change="handleEmergencyRelation"
										fields="month">
										<view class="uni-input">
											<text v-if="consultantInfo.emergencyRelation">{{ getEmergencyRelation(consultantInfo.emergencyRelation) }}</text>
											<text v-else class="placeholder">请选择紧急联系人关系</text>
										</view>
									</picker>
								</view>
							</uni-forms-item>
							<uni-forms-item label="联系方式" required name="emergencyPhone">
								<uni-easyinput v-model="consultantInfo.emergencyPhone" placeholder="请输入联系方式" />
							</uni-forms-item>
						</view>
					</view>
				</view>
			</view>
			<view class="consultant-info">
				<view class="custom-collapse">
					<view class="custom-collapse-header" @click="togglePastExperience">
						<text class="title">过往经历</text>
						<uni-icons :type="isPastExperienceOpen ? 'up' : 'down'" size="14"></uni-icons>
					</view>
					<view class="custom-collapse-content" :class="{ 'content-open': isPastExperienceOpen }">
						<view class="collapse-content label-position-top">
							<view class="form-wrap">
								<uni-forms-item class="label-position-top" label="你是否接受过心理咨询" required name="isCounseling">
									<uni-data-checkbox v-model="consultantInfo.isCounseling" :localdata="yesNoOptions" />
								</uni-forms-item>
								<view v-show="consultantInfo.isCounseling === 'Y'" class="counseling-effect">
									<uni-forms-item class="label-position-top" label="介绍咨询效果" required name="counselingEffect">
										<uni-easyinput type="textarea" v-model="consultantInfo.counselingEffect" placeholder="请输入咨询效果" />
									</uni-forms-item>
								</view>
							</view>
							<uni-forms-item class="label-position-top long-label" label="既往病史及家族病史,目前健康状况,是否在服药中,及所服用药物信息" required name="isPsychiatry">
								<template #label>
									<view class="label-text">
										<text class="is-required">*</text>
										<text>既往病史及家族病史,目前健康状况,是否在服药中,及所服用药物信息</text>
									</view>
								</template>
								<uni-easyinput type="textarea" v-model="consultantInfo.medicalHistory" placeholder="请输入既往病史及家族病史,目前健康状况,是否在服药中,及所服用药物信息" />
							</uni-forms-item>
							<uni-forms-item class="label-position-top" label="你是否有过自伤/自杀的想法活行为" required name="isSuicide">
								<uni-data-checkbox v-model="consultantInfo.isSuicide" :localdata="ideaOptions" />
							</uni-forms-item>
						</view>
					</view>
				</view>
			</view>
			<view class="consultant-info">
				<view class="custom-collapse">
					<view class="custom-collapse-header" @click="toggleOtherInfo">
						<text class="title">其他信息</text>
						<uni-icons :type="isOtherInfoOpen ? 'up' : 'down'" size="14"></uni-icons>
					</view>
					<view class="custom-collapse-content" :class="{ 'content-open': isOtherInfoOpen }">
						<view class="collapse-content">
							<uni-forms-item label="民族">
								<uni-easyinput v-model="consultantInfo.nation" placeholder="请输入民族" />
							</uni-forms-item>
							<uni-forms-item label="宗教信仰">
								<uni-easyinput v-model="consultantInfo.religion" placeholder="请输入宗教信仰" />
							</uni-forms-item>
							<uni-forms-item label="职业">
								<uni-easyinput v-model="consultantInfo.occupation" placeholder="请输入职业" />
							</uni-forms-item>
							<uni-forms-item label="收入水平">
								<uni-data-select v-model="consultantInfo.incomeLevel" :localdata="incomeLevelOptions"></uni-data-select>
							</uni-forms-item>
							<uni-forms-item label="情感状态">
								<uni-data-select v-model="consultantInfo.maritalStatus" :localdata="maritalStatusOptions"></uni-data-select>
							</uni-forms-item>
							<uni-forms-item label="有无子女">
								<uni-data-select v-model="consultantInfo.hasChildren" :localdata="hasChildrenOptions"></uni-data-select>
							</uni-forms-item>
							<uni-forms label-position="top">
								<uni-forms-item class="label-position-top" label="咨询的主要原因" name="problemDescription">
									<uni-easyinput type="textarea" v-model="consultantInfo.problemDescription" placeholder="请输入咨询的主要原因" />
								</uni-forms-item>
								<uni-forms-item class="label-position-top" label="请描述您的性格特点与兴趣爱好" name="tellConsultant">
									<uni-easyinput type="textarea" v-model="consultantInfo.tellConsultant" placeholder="请描述您的性格特点与兴趣爱好" />
								</uni-forms-item>
								<uni-forms-item class="label-position-top" name="situationDescription">
									<template #label>
										<view class="label-text">
											<text class="is-required">*</text>
											<text>请描述您目前的情况(工作、家庭、人际关系等)</text>
										</view>
									</template>
									<uni-easyinput type="textarea" v-model="consultantInfo.situationDescription" placeholder="请描述您目前的情况(工作、家庭、人际关系等)" />
								</uni-forms-item>
								<uni-forms-item class="label-position-top" name="earlyEnvironment">
									<template #label>
										<view class="label-text">
											<text class="is-required">*</text>
											<text>请描述您的早年成长环境,以及您认为早起环境中的哪些方面影响了现在的您</text>
										</view>
									</template>
									<uni-easyinput type="textarea" v-model="consultantInfo.earlyEnvironment" placeholder="请描述您的早年成长环境,以及您认为早起环境中的哪些方面影响了现在的您" />
								</uni-forms-item>
								<uni-forms-item class="label-position-top" name="majorLifeEvent">
									<template #label>
										<view class="label-text">
											<text>近半年内是否发生过重大生活事件,如有请描述</text>
										</view>
									</template>
									<uni-easyinput type="textarea" v-model="consultantInfo.majorLifeEvent" placeholder="请描述近半年内是否发生过重大生活事件,如有请描述" />
								</uni-forms-item>
								<uni-forms-item class="label-position-top" name="expectConsultation">
									<template #label>
										<view class="label-text">
											<text>您期望从咨询中得到什么样的启发与领悟</text>
										</view>
									</template>
									<uni-easyinput type="textarea" v-model="consultantInfo.expectConsultation" placeholder="请描述您期望从咨询中得到什么样的启发与领悟" />
								</uni-forms-item>
							</uni-forms>
						</view>
					</view>
				</view>
			</view>
		</uni-forms>
		</view>
		
		<view class="footer">
			<view class="consult">
				<button open-type="getUserInfo" @click="handleSaveInfo">保存</button>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useDict } from "@/utils/index.js";
import { getCityInformation } from "@/api/index.js";
import { postWxUser, getUserIdInfo } from "@/api/my.js";
import { useUserStore } from "@/stores/user";
const userStore = useUserStore();

// 生命周期
const isFromOrder = ref(false);
const eventChannel = ref(null);

onMounted(() => {
	if (options.from === 'order') {
		isFromOrder.value = true;
		// 获取页面通信通道
		eventChannel.value = getOpenerEventChannel();
	}
	getDict(); // 初始化加载字典数据
});

// DOM引用
const formRef = ref(null); // 表单组件引用
const dataTree = ref([]); // 城市数据树
// 表单数据
const consultantInfo = reactive({
	avatar: "", // 头像
	counselorName: "", // 真实姓名
	birthdate: "", // 出生年月
	sex: "", // 性别
	userPhone: "", // 联系方式
	emergencyContactName: "", // 紧急联系人姓名
	emergencyRelation: "", // 紧急联系人关系
	emergencyPhone: "", // 紧急联系人电话
	isCounseling: "", // 心理咨询经历
	isPsychiatry: "", // 精神科诊疗经历
	isSuicide: "", // 自伤/自杀行为
	occupation: "", // 职业
	incomeLevel: "", // 收入水平
	maritalStatus: "", // 婚姻状况
	hasChildren: "", // 是否有子女
	problemDescription: "", // 问题描述
	tellConsultant: "", // 想对咨询师说的话
});

// 字典选项
const sexsOptions = ref([]); // 性别选项（sys_user_sex）
const yesNoOptions = ref([
	{
		text: "是",
		value: "Y",
	},
	{
		text: "否",
		value: "N",
	},
]); // 是否选项（sys_yes_no）
const ideaOptions = ref([
	{
		text: "有想过",
		value: "0",
	},
	{
		text: "有做过",
		value: "1",
	},
	{
		text: "均没有",
		value: "2",
	},
]); // 自杀/自伤选项（psy_self_harm_behavior）
const emergencyRelationOptions = ref([]); // 紧急关系选项（psy_emergency_relation）
const incomeLevelOptions = ref([]); // 收入等级选项（psy_income_level）
const maritalStatusOptions = ref([]); // 婚姻状态选项（psy_emotion_status）
const hasChildrenOptions = ref([]); // 子女情况选项（psy_has_children）

// 折叠面板状态
const isConsultantInfoOpen = ref(true);
const isEmergencyContactOpen = ref(true);
const isPastExperienceOpen = ref(true);
const isOtherInfoOpen = ref(true);

// 日期范围计算
const startDate = computed(() => getDate("start")); // 开始日期（当前年份-60年）
const endDate = computed(() => getDate("end")); // 结束日期（当前年份+2年）

// 表单验证规则
const rules = reactive({
	counselorName: {
		rules: [
			{ required: true, errorMessage: "真实姓名不能为空" },
			{ minLength: 2, maxLength: 10, errorMessage: "姓名长度为2-10个字符" },
		],
	},
	birthdate: {
		rules: [{ required: true, errorMessage: "出生年月不能为空" }],
	},
	sex: {
		rules: [{ required: true, errorMessage: "请选择性别" }],
	},
	userPhone: {
		rules: [
			{ required: true, errorMessage: "联系方式不能为空" },
			{ pattern: /^1[3-9]\d{9}$/, errorMessage: "手机号格式不正确" },
		],
	},
	emergencyContactName: {
		rules: [
			{ required: true, errorMessage: "紧急联系人姓名不能为空" },
			{ minLength: 2, maxLength: 10, errorMessage: "姓名长度为2-10个字符" },
		],
	},
	emergencyRelation: {
		rules: [{ required: true, errorMessage: "请选择联系人关系" }],
	},
	emergencyPhone: {
		rules: [
			{ required: true, errorMessage: "紧急联系方式不能为空" },
			{ pattern: /^1[3-9]\d{9}$/, errorMessage: "手机号格式不正确" },
		],
	},
	isCounseling: {
		rules: [{ required: true, errorMessage: "请选择心理咨询经历" }],
	},
	isPsychiatry: {
		rules: [{ required: true, errorMessage: "请选择精神科诊疗经历" }],
	},
	isSuicide: {
		rules: [{ required: true, errorMessage: "请选择自伤/自杀相关情况" }],
	},
});

// 折叠面板切换函数
const toggleConsultantInfo = () => {
	isConsultantInfoOpen.value = !isConsultantInfoOpen.value;
};

const toggleEmergencyContact = () => {
	isEmergencyContactOpen.value = !isEmergencyContactOpen.value;
};

const togglePastExperience = () => {
	isPastExperienceOpen.value = !isPastExperienceOpen.value;
};

const toggleOtherInfo = () => {
	isOtherInfoOpen.value = !isOtherInfoOpen.value;
};

// 方法
const handleDateChange = (e) => {
	consultantInfo.birthdate = e.detail.value; // 更新出生日期选择
};

const handleEmergencyRelation = (e) => {
	consultantInfo.emergencyRelation = e.detail.value;
};

const getEmergencyRelation = (val) => {
	return emergencyRelationOptions.value.find((item) => item.value == val).label || "";
};

const getDate = (type) => {
	const date = new Date();
	let year = date.getFullYear();
	let month = date.getMonth() + 1;
	if (type === "start") year -= 60; // 开始日期为60年前
	if (type === "end") year += 2; // 结束日期为2年后
	month = month > 9 ? month : `0${month}`;
	return `${year}-${month}`;
};

// 表单提交
const handleSaveInfo = async () => {
	try {
		await formRef.value.validate();
		const { code, data } = await postWxUser(consultantInfo);
		
		if (code === 200) {
			uni.showToast({
				title: "保存成功",
				icon: "success"
			});
			
			// 如果是从订单页面来的，触发回调并返回
			if (isFromOrder.value && eventChannel.value) {
				eventChannel.value.emit('consultantAdded', data);
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		} else {
			uni.showToast({
				title: "保存失败，请重试",
				icon: "none"
			});
		}
	} catch (errors) {
		const errorMsg = generateErrorMessage(errors);
		errorMsg && uni.showModal({ title: "提示", content: `${errorMsg}，请检查` });
	}
};

// 错误信息处理
const generateErrorMessage = (errors) => {
	const errorMap = new Map([
		[["counselorName", "birthdate", "userPhone", "sex"], "咨询人信息填写有误"],
		[["emergencyContactName", "emergencyRelation", "emergencyPhone"], "紧急联系人填写有误"],
		[["isCounseling", "isPsychiatry", "isSuicide"], "过往经历填写有误"],
	]);

	const messages = new Set();
	errors.forEach(({ key }) => {
		for (const [keys, msg] of errorMap) {
			if (keys.includes(key)) messages.add(msg);
		}
	});
	return Array.from(messages).join("，");
};

// 初始化数据
const getDict = async () => {
	// 并行加载所有字典数据
	const [
		{ psy_emotion_status }, // 情感状态
		{ psy_income_level }, // 收入水平
		{ psy_has_children }, // 有无子女
		{ psy_emergency_relation }, // 紧急关系
		{ sys_user_sex }, // 性别
		cityData, // 城市数据
		userData, // 用户数据
	] = await Promise.all([
		useDict("psy_emotion_status"),
		useDict("psy_income_level"),
		useDict("psy_has_children"),
		useDict("psy_emergency_relation"),
		useDict("sys_user_sex"),
		getCityInformation({ key: "5eb9e59558fc6fdf4af2026d6e358698", subdistrict: 3 }),
		getUserIdInfo(userStore.profile.userId),
	]);

	// 分配字典数据
	maritalStatusOptions.value = [...psy_emotion_status];
	incomeLevelOptions.value = [...psy_income_level];
	hasChildrenOptions.value = [...psy_has_children];
	emergencyRelationOptions.value = [...psy_emergency_relation];
	sexsOptions.value = [...sys_user_sex];
	// yesNoOptions.value = [...sys_yes_no];
	// ideaOptions.value = [...psy_self_harm_behavior];

	// 初始化表单数据
	Object.assign(consultantInfo, userData.data);
	dataTree.value = cityData.districts[0].districts;
};

// 头像选择处理
const onChooseAvatar = (e) => {
	consultantInfo.avatar = e.detail.avatarUrl;
	uni.showToast({
		title: '头像更新成功',
		icon: 'success'
	});
};

// 计算是否有展开的面板
const hasOpenPanel = computed(() => {
	return isConsultantInfoOpen.value || 
		isEmergencyContactOpen.value || 
		isPastExperienceOpen.value || 
		isOtherInfoOpen.value;
});
</script>

<style lang="scss" scoped>
.content {
	// padding: 20rpx;
	background-color: #f5f6f7;
	.consultant-info {
		margin-bottom: 10rpx;
		.custom-collapse {
			background-color: #fff;
			border-radius: 20rpx;
			margin-bottom: 10rpx;
			
			.custom-collapse-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx;
				cursor: pointer;
				.title {
					font-size: 34rpx;
					font-weight: 700;
				}
			}
			
			.custom-collapse-content {
				max-height: 0;
				overflow: hidden;
				transition: max-height 0.3s ease-out;
				&.content-open {
					max-height: 2000px; // 设置一个足够大的值
				}
				.collapse-content {
					padding: 20rpx;
				}
			}
		}
		:deep .uni-collapse {
			display: none;
		}
		:deep .uni-collapse-item__wrap {
			transition: all 0.3s;
		}
		:deep .uni-collapse-item__wrap-content {
			transition: all 0.3s;
		}
		:deep uni-text.uni-collapse-item__title-text,
		:deep uni-view.uni-collapse,
		:deep uni-view.uni-collapse-item__title-box,
		:deep uni-view.consultant-info {
			border-radius: 20rpx;
		}
		:deep uni-text.uni-collapse-item__title-text,
		:deep .uni-collapse-item__title-text {
			font-size: 34rpx;
			font-weight: 700;
		}
		.collapse-content {
			padding: 20rpx;
			border-radius: 20rpx;
		}
		:deep uni-view.uni-collapse-item__wrap.is--transition {
			border-radius: 20rpx;
		}
		:deep uni-text,
		:deep .uni-forms-item__label {
			white-space: nowrap;
		}
		.is-expand {
			overflow: visible;
			:deep .uni-collapse-item__wrap {
				overflow: visible !important;
			}
			:deep uni-view.uni-collapse-item__wrap.is--transition {
				overflow: visible !important;
			}
		}
		:deep .label-position-top {
			&:nth-last-child(1) {
				.uni-forms-item.is-direction-left {
					flex-direction: column !important;
				}
			}
		}
		.uni-list-cell-db {
			width: 100%;
			height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: end;
			border: 1px solid #dcdfe6;
			border-radius: 4px;
			picker {
				width: 100%;
				height: 100%;
			}
			.uni-input {
				height: 70rpx;
				line-height: 70rpx;
				padding-left: 20rpx;
				.placeholder {
					color: #999;
					font-size: 24rpx;
				}
			}
		}
		.avatar-upload {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 20rpx 0;
			.avatar-btn {
				background: none;
				border: none;
				padding: 0;
				margin: 0;
				line-height: 1;
				&::after {
					border: none;
				}
			}
			.avatar-image {
				width: 150rpx;
				height: 150rpx;
				border-radius: 75rpx;
			}
		}
		:deep .label-text {
			width: 100%;
			display: flex;
			padding: 6px 0;
			line-height: 1.5;
			text {
				white-space: normal;
				word-break: break-all;
				color: #606266;
				font-size: 14px;
				&.is-required {
					color: #dd524d;
					font-weight: bold;
					margin-right: 4px;
				}
			}
		}
	}
	
	.footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 120rpx;
		background-color: #fff;
		z-index: 9;
		margin-top: 100rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		.consult {
			width: calc(100% - 40rpx);
			button {
				background-color: #52b5f9;
				border: none !important;
				color: #fff;
				font-weight: 700;
				border-radius: 70rpx;
			}
		}
	}
}

.has-open {
				padding-bottom: 140rpx;
			}
.counseling-effect {
	width: 100%;
	transition: all 0.3s;
}
</style>
