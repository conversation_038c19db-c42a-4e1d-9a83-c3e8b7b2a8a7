<template>
	<view class="content">
		<view class="img-box">
			<image mode="heightFix" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E7%99%BB%E5%BD%95%E9%A1%B5%E8%83%8C%E6%99%AF%E5%9B%BE.jpg"></image>
		</view>
		<view class="login-btn">
			<button type="primary" open-type="getPhoneNumber" @getphonenumber="login">手机快捷登录</button>
			<view class="phone-number" @click="handlePhoneNumberLogin">短信验证码登录</view>
		</view>
		<uni-popup background-color="#fff" class="bottom-popup" ref="loginPopup" type="bottom" border-radius="30rpx 30rpx 0 0">
			<view class="title">手机号码登录注册</view>
			<view class="login-form">
				<uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData">
					<view class="input-group">
						<uni-forms-item name="phone">
							<uni-easyinput type="number" v-model="valiFormData.phone" placeholder="请输入手机号"></uni-easyinput>
						</uni-forms-item>
					</view>
					<!-- 验证码输入框 -->
					<view class="input-group-code">
						<uni-forms-item name="code">
							<uni-easyinput type="number" v-model="valiFormData.code" placeholder="请输入验证码"></uni-easyinput>
						</uni-forms-item>
						<button class="code" :disabled="smsCountdown != 0" @click="sendCode">
							{{ smsCountdown > 0 ? `${smsCountdown}秒后重试` : "获取验证码" }}
						</button>
					</view>
				</uni-forms>
				<button type="primary" @click="submit('valiForm')">登录/注册</button>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { wxPhoneLogin } from "@/api/index.js";
import { useUserStore } from "@/stores/user";
const userStore = useUserStore();

// 响应式数据
const valiFormData = reactive({
	phone: "13552697925",
	code: "570791",
});
const smsCountdown = computed(() => userStore.smsCountdown);
const isCountingDown = ref(false);
const countdown = ref(0);

// 验证规则
const rules = reactive({
	phone: {
		rules: [
			{
				required: true,
				errorMessage: "请输入手机号",
			},
			{
				format: "number",
				errorMessage: "请输入正确的手机号",
			},
			{
				validateFunction: (rule, value, data, callback) => {
					if (!/^1[3-9]\d{9}$/.test(value)) {
						callback("请输入正确的手机号");
					}
					return true;
				},
			},
		],
	},
	code: {
		rules: [
			{
				required: true,
				errorMessage: "验证码",
			},
		],
	},
});

// 表单引用
const loginPopup = ref(null);
const valiForm = ref(null);

//登录按钮
const login = async (e) => {
	console.log(e)
	const detail = e.detail
	if (detail.errMsg === "getPhoneNumber:ok") {
		console.log("用户同意授权")
		try {
			// 使用store的统一登录方法
			const success = await userStore.login(null, detail.code, 'wxPhone')
			if (!success) {
				throw new Error('登录失败')
			}
		} catch (error) {
			console.error('登录失败:', error)
			uni.showModal({
				title: '登录失败',
				content: error.message || '网络错误，请稍后重试',
				showCancel: false
			})
		}
	} else {
		uni.showToast({
			title: '您已取消授权',
			icon: 'none'
		})
	}
}

// 方法
const handlePhoneNumberLogin = () => {
	loginPopup.value?.open();
};

const submit = async (refName) => {
	try {
		await valiForm.value.validate();
		// 使用store的统一登录方法
		await userStore.login(valiFormData.phone, valiFormData.code, 'mobile');
	} catch (error) {
		console.log("登录失败:", error);
	}
};
// 发送验证码
const sendCode = async () => {
	if (userStore.isCountingDown) return;
	const success = await userStore.sendSmsCode(valiFormData.phone);
	if (success) {
		uni.showToast({ title: "验证码已发送" });
	}
};
</script>

<style lang="scss" scoped>
.content {
	width: 100%;
	.img-box {
		width: 100%;
		background-color: #ccc;
	}
	.login-btn {
		width: 100%;
		height: 200rpx;
		margin-top: 50rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		button {
			width: 80%;
			font-size: 34rpx;
		}
		.phone-number {
			width: 100%;
			text-align: center;
			margin-top: 20rpx;
			font-size: 28rpx;
			color: #007aff;
		}
	}
	.bottom-popup {
		.title {
			font-size: 30rpx;
			font-weight: 700;
			padding: 40rpx;
		}
		.login-form {
			padding: 40rpx;
			.input-group {
				margin-bottom: 20rpx;
			}
			.input-group-code {
				width: 100%;
				position: relative;
				margin-bottom: 20rpx;
				:deep .uni-easyinput {
					width: calc(100% - 200rpx);
				}
				:deep .is-input-border {
					border-top-right-radius: 0px !important;
					border-bottom-right-radius: 0px !important;
				}
				:deep uni-view.uni-forms-item.is-direction-left {
					width: calc(100% - 200rpx);
				}
				:deep uni-view.uni-easyinput__content.is-input-border {
					border-top-right-radius: 0px !important;
					border-bottom-right-radius: 0px !important;
				}
				:deep uni-button.code {
					border-top-left-radius: 0px !important;
					border-bottom-left-radius: 0px !important;
					border-left: none !important;
					&:after {
						border-top-left-radius: 0px !important;
						border-bottom-left-radius: 0px !important;
						border-left: none !important;
					}
				}
				.code {
					width: 200rpx;
					height: 100%;
					font-size: 26rpx;
					position: absolute;
					right: 0;
					top: 0;
					border-radius: 8rpx;
					z-index: 1;
					border-top-left-radius: 0px !important;
					border-bottom-left-radius: 0px !important;
					border-left: none !important;
				}
			}
		}
	}
}
</style>
