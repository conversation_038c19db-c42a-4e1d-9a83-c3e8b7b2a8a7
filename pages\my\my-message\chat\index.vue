<template>
  <view class="chat-container">
    <!-- 头部区域 -->
    <!-- <view class="header">
      <view class="nickname">{{ nickname }}</view>
    </view> -->
    
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      :scroll-top="scrollTop" 
      :scroll-with-animation="true"
      @scrolltoupper="loadMoreMessages"
      @scroll="onScroll"
      upper-threshold="50"
      id="message-list"
    >
      <view class="loading" v-if="isLoading">
        <uni-load-more status="loading" :contentText="loadingText"></uni-load-more>
      </view>
      
      <view v-for="(group, groupIndex) in groupedMessages" :key="groupIndex">
        <!-- 该日期下的消息 -->
        <view class="message-wrapper" v-for="(item, index) in group.messages" :key="index">
          <!-- 消息时间 - 当两条消息间隔超过5分钟时显示 -->
          <view class="time-divider" v-if="shouldShowTime(item, index, group.messages)">
            <text>{{ group.dateLabel }}</text>
          </view>
          
          <!-- 消息气泡 -->
          <view
            class="message-item"
            :class="{
              'self-message': item.senderId === String(userStore.userId),
              'other-message': item.senderId !== String(userStore.userId)
            }"
          >
            <!-- 头像 -->
            <image
              class="avatar"
              :src="item.senderId === String(userStore.userId) ? userStore.userAvatar : consultantAvatar"
              mode="aspectFill"
            ></image>
            
            <!-- 消息内容 -->
            <view class="message-content">
              <!-- 已撤回消息 -->
              <view class="withdrawn-message" v-if="item.isWithdrawn === '1'">
                <text>消息已撤回</text>
              </view>
              
              <!-- 正常消息 -->
              <view v-else class="bubble" @longpress="handleLongPress(item)">
                <!-- 文本消息 -->
                <text v-if="item.messageType === '0' || !item.messageType" class="text-content">{{ item.content }}</text>
                
                <!-- 图片消息 -->
                <image 
                  v-else-if="item.messageType === '1'" 
                  class="image-content" 
                  :src="item.fileUrl" 
                  mode="widthFix" 
                  @tap="previewImage(item.fileUrl)"
                  @error="onImageError"
                ></image>
                
                <!-- 文件消息 -->
                <view v-else-if="item.messageType === '2'" class="file-content" @tap="openFile(item.fileUrl)">
                  <uni-icons type="file" size="24"></uni-icons>
                  <text>{{ getFileName(item.fileUrl) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部留白，确保新消息能完全显示 -->
      <view class="bottom-space" :style="{ height: showMoreActions ? '400rpx' : '100rpx', transition: 'height 0.3s' }"></view>
    </scroll-view>
    
    <!-- 新消息提示 -->
    <view 
      class="new-message-tip" 
      v-if="showNewMessageTip" 
      @tap="scrollToBottom"
    >
      <view class="tip-content">
        <uni-icons type="chatbubble" size="18" color="#fff"></uni-icons>
        <text>{{ newMessageCount }}条新消息</text>
      </view>
    </view>
    
    <!-- 输入框区域 -->
    <view class="input-area" :style="{ bottom: keyboardHeight + 'px' }">
      <view class="input-wrapper">
        <!-- 语音按钮 -->
        <view class="action-button">
          <uni-icons type="mic-filled" size="24" color="#666"></uni-icons>
        </view>
        
        <!-- 文本输入框 -->
        <input 
          class="message-input" 
          type="text" 
          v-model="inputMessage" 
          placeholder="请输入消息..." 
          confirm-type="send" 
          :adjust-position="false"
          @confirm="sendTextMessage"
          @focus="onInputFocus"
          @blur="onInputBlur"
        />
        
        <!-- 表情按钮 -->
        <view class="action-button">
           <!--<uni-icons type="emotion" size="24" color="#666"></uni-icons>-->
          <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/emoji.png" mode="widthFix" style="width: 24px; height: 24px;"></image>
        </view>
        
        <!-- 更多功能按钮或发送按钮 -->
        <view class="action-button" @tap="inputMessage.trim() ? sendTextMessage() : toggleMoreActions()">
          <uni-icons :type="inputMessage.trim() ? 'paperplane-filled' : 'plusempty'" size="24" :color="inputMessage.trim() ? '#1989fa' : '#666'"></uni-icons>
        </view>
      </view>
      
      <!-- 更多功能面板 -->
      <view class="more-panel" v-if="showMoreActions">
        <view class="action-item" @tap="chooseImage">
          <view class="action-icon">
            <uni-icons type="image" size="24" color="#07c160"></uni-icons>
          </view>
          <text>图片</text>
        </view>
        
        <view class="action-item" @tap="chooseFile">
          <view class="action-icon">
            <uni-icons type="folder" size="24" color="#1989fa"></uni-icons>
          </view>
          <text>文件</text>
        </view>
      </view>
    </view>
    
    <!-- 消息操作菜单 -->
    <uni-popup ref="messageActionPopup" type="bottom" id="message-action-popup">
      <view class="action-menu">
        <view class="action-menu-item" @tap="copyMessage" v-if="selectedMessage && selectedMessage.messageType === '0'">
          <text>复制</text>
        </view>
        <view class="action-menu-item" @tap="withdrawMessage" v-if="canWithdraw">
          <text>撤回</text>
        </view>
        <view class="action-menu-item cancel" @tap="closeActionMenu">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { onLoad, onUnload, onShow } from '@dcloudio/uni-app';
import { useChatStore } from '@/stores/chat';
import { useUserStore } from '@/stores/user';

// Store
const chatStore = useChatStore();
const userStore = useUserStore();
// 获取路由参数
const conversationId = ref(null);
const userId = ref(null);
const consultantId = ref(null);
const nickname = ref('');

// 状态变量
const inputMessage = ref('');
const messageList = ref([]);
const scrollTop = ref(0);
const isLoading = ref(false);
const page = ref(1);
const hasMore = ref(true);
const showMoreActions = ref(false);
const keyboardHeight = ref(0);
const selectedMessage = ref(null);
const messageActionPopup = ref(null);
const userAvatar = ref(userStore.userAvatar);
const consultantAvatar = ref();

// 新消息提示相关状态
const isAtBottom = ref(true); // 默认在底部
const showNewMessageTip = ref(false);
const newMessageCount = ref(0);

// 计算属性
const loadingText = computed(() => ({
  contentdown: '上拉加载更多',
  contentrefresh: '加载中...',
  contentnomore: '没有更多消息了'
}));

// 计算属性 - 按日期分组消息
const groupedMessages = computed(() => {
  const groups = [];
  const now = new Date();
  
  // 临时存储已处理日期，防止重复
  const processedDates = new Set();
  
  // 遍历所有消息
  messageList.value.forEach(message => {
    const date = parseDateSafely(message.sendTime);
    
    // 获取该消息的日期标识(今天/昨天/前天等)
    const dateKey = getDateKey(date); // 例如: "2023-06-10"
    const dateLabel = formatDateWithContext(date, now);
    
    // 查找或创建该日期的消息组
    let group = groups.find(g => g.dateKey === dateKey);
    
    if (!group) {
      group = {
        dateKey,
        dateLabel,
        messages: []
      };
      groups.push(group);
    }
    
    // 添加消息到对应日期组
    group.messages.push(message);
  });
  
  return groups;
});

// 获取日期的唯一键值
const getDateKey = (date) => {
  return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
};

// 当前选中消息是否可撤回（自己发的且5分钟内）
const canWithdraw = computed(() => {
  if (!selectedMessage.value) return false;

  // 是自己发送的消息 - 使用当前登录用户ID判断
  const isSelf = selectedMessage.value.senderId === String(userStore.userId);

  // 5分钟内的消息
  const sendTime = new Date(selectedMessage.value.sendTime).getTime();
  const now = Date.now();
  const isWithinTimeLimit = now - sendTime < 5 * 60 * 1000;

  return isSelf && isWithinTimeLimit;
});

// 页面加载
onLoad((options) => {
  conversationId.value = options.conversationId;
  userId.value = options.userId;
  consultantId.value = options.consultantId;
  nickname.value = options.nickname || '聊天';
  consultantAvatar.value = options.consultantAvatar;

  console.log('聊天页面参数:', options);
  console.log('传入的用户ID:', userId.value);
  console.log('传入的咨询师ID:', consultantId.value);
  console.log('当前登录用户ID:', userStore.userId);
  console.log('是否是咨询师:', options.isConsultant);

  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: nickname.value
  });

  // 设置当前会话 - 使用当前登录用户的ID
  chatStore.currentConversation = {
    conversationId: conversationId.value,
    userId: userStore.userId
  };

  // 加载消息
  loadMessages();
});

// 页面挂载完成
onMounted(() => {
  // 获取uni-popup引用
  nextTick(() => {
    // 注意：uni-app的选择器在小程序中有一些特殊要求
    const query = uni.createSelectorQuery();
    query.select('#message-action-popup').node(res => {
      if (res && res.node) {
        messageActionPopup.value = res.node;
      } else {
        console.error('找不到popup元素');
      }
    }).exec();
  });
  
  console.log('挂载完成,设置监听器,当前会话ID:', conversationId.value, '用户ID:', userId.value);
  
  // 先移除可能存在的旧监听器,避免重复监听
  uni.$off('chat-new-message');
  
  // 监听新消息事件
  uni.$on('chat-new-message', (data) => {
    console.log('聊天页面收到新消息事件:', data);
    console.log('当前会话ID:', conversationId.value, '消息会话ID:', data.conversationId);
    
    // 如果是当前会话的消息，将其添加到消息列表并滚动到底部
    if (String(data.conversationId) === String(conversationId.value)) {
      console.log('消息属于当前会话,准备添加到列表');
      
      // 确保senderId格式一致
      const formattedMessage = {
        ...data,
        senderId: String(data.senderId)
      };
      
      // 检查消息是否已经存在于列表中
      const exists = messageList.value.some(msg => msg.messageId === formattedMessage.messageId);
      
      if (!exists) {
        console.log('消息不存在,添加到列表');
        messageList.value.push(formattedMessage);
        
        // 强制页面刷新
        nextTick(() => {
          console.log('消息列表更新后长度:', messageList.value.length);
          
          // 强制重新计算时间分隔线
          messageList.value = [...messageList.value];
        });
      } else {
        console.log('消息已存在,不重复添加');
      }
      
      if (isAtBottom.value) {
        // 如果用户视角在底部，直接滚动到底部
        console.log('用户在底部,滚动到最新消息');
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      } else {
        // 如果用户视角不在底部，显示新消息提示
        console.log('用户不在底部,显示新消息提示');
        newMessageCount.value++;
        showNewMessageTip.value = true;
      }
    } else {
      console.log('消息不属于当前会话,忽略');
    }
  });
});

// 页面显示
onShow(() => {
  console.log('页面显示,当前会话ID:', conversationId.value, '传入用户ID:', userId.value);
  console.log('当前登录用户ID:', userStore.userId);

  // 初始化WebSocket - 使用当前登录用户的ID
  if (!uni.webSocketTask || !chatStore.wsConnected) {
    console.log('WebSocket未连接,使用当前登录用户ID初始化连接:', userStore.userId);
    chatStore.initWebSocket(userStore.userId);
  } else {
    console.log('WebSocket已连接');
  }

  // 设置当前会话 - 使用当前登录用户的ID
  chatStore.currentConversation = {
    conversationId: conversationId.value,
    userId: userStore.userId
  };
  console.log('已设置当前会话:', chatStore.currentConversation);

  // 标记消息为已读
  markAsRead();

  // 页面显示时滚动到底部
  if (messageList.value.length > 0) {
    setTimeout(() => {
      scrollToBottom();
    }, 500);
  }
});

// 页面卸载
onUnload(() => {
  console.log('页面卸载,重置未读数');
  
  // 再次标记消息为已读,确保所有消息都被标记
  markAsRead();
  
  // 如果是聊天用户,重置用户未读数;如果是咨询师,重置咨询师未读数
  const isUser = userId.value !== consultantId.value;
  
  // 更新会话列表中的未读数
  const index = chatStore.conversationList.findIndex(
    conv => String(conv.conversationId) === String(conversationId.value)
  );
  
  if (index !== -1) {
    if (isUser) {
      chatStore.conversationList[index].userUnreadCount = 0;
    } else {
      chatStore.conversationList[index].consultantUnreadCount = 0;
    }
  }
  
  // 不要在这里关闭WebSocket连接，因为可能还需要在后台接收消息
  // 仅释放当前会话的状态
  chatStore.currentConversation = null;
  
  // 移除事件监听
  uni.$off('chat-new-message');
});

// 加载消息列表
const loadMessages = async () => {
  isLoading.value = true;
  try {
    const res = await chatStore.getMessageList(conversationId.value, page.value);
    
    // 检查是否有消息数据
    if (!res || !res.rows || res.rows.length === 0) {
      hasMore.value = false;
      messageList.value = [];
    } else {
      console.log('加载的历史消息:', res.rows);
      console.log('当前用户ID:', userId.value);
      
      // 确保senderId和userId的类型一致，都转为字符串进行比较
      const formattedMessages = res.rows.map(msg => ({
        ...msg,
        senderId: String(msg.senderId),
      }));
      
      // 打印消息和用户ID的关系
      formattedMessages.forEach(msg => {
        // 检查日期格式，确保日期解析正确
        const msgDate = parseDateSafely(msg.sendTime);
        console.log('消息时间:', msg.sendTime, '解析后:', msgDate.toLocaleString());
      });
      
      messageList.value = [...formattedMessages].reverse(); // 倒序显示，最新的在底部
      
      // 判断是否已加载全部数据
      if (res.rows.length < 20) { // 假设每页20条数据
        hasMore.value = false;
      }
    }
    
    // 等待DOM更新后滚动到底部
    setTimeout(() => {
      scrollToBottom();
    }, 500);
    
    // 标记为已读
    markAsRead();
  } catch (error) {
    console.error('加载消息失败', error);
    uni.showToast({
      title: '加载消息失败',
      icon: 'none'
    });
  } finally {
    isLoading.value = false;
  }
};

// 加载更多历史消息
const loadMoreMessages = async () => {
  if (!hasMore.value || isLoading.value) return;
  
  isLoading.value = true;
  try {
    page.value++;
    const oldHeight = await getScrollViewHeight();
    
    const res = await chatStore.getMessageList(conversationId.value, page.value);
    // 判断是否没有更多数据
    if (!res || !res.rows || res.rows.length === 0) {
      hasMore.value = false;
      uni.showToast({
        title: '没有更多消息了',
        icon: 'none',
        duration: 1500
      });
      return;
    }
    
    // 确保senderId和userId的类型一致，都转为字符串进行比较
    const formattedMessages = res.rows.map(msg => ({
      ...msg,
      senderId: String(msg.senderId),
    }));
    
    const newMessages = [...formattedMessages || []].reverse();
    
    // 判断是否已加载全部数据
    if (newMessages.length < 20) { // 假设每页20条数据
      hasMore.value = false;
    }
    
    // 将新消息添加到列表前面
    messageList.value = [...newMessages, ...messageList.value];
    
    // 保持滚动位置
    nextTick(() => {
      const newHeight = getScrollViewHeight();
      scrollTop.value = newHeight - oldHeight;
    });
  } catch (error) {
    console.error('加载更多消息失败', error);
    page.value--; // 恢复页码
  } finally {
    isLoading.value = false;
  }
};

// 获取滚动区域高度
const getScrollViewHeight = () => {
  return new Promise((resolve) => {
    uni.createSelectorQuery()
      .select('#message-list')
      .boundingClientRect(data => {
        resolve(data?.height || 0);
      })
      .exec();
  });
};

// 监听滚动事件，判断是否在底部
const onScroll = (e) => {
  // 获取滚动位置信息
  const scrollHeight = e.detail.scrollHeight; // 内容总高度
  const scrollTop = e.detail.scrollTop; // 滚动条位置
  const clientHeight = e.detail.scrollHeight - e.detail.deltaY; // 可视区域高度
  
  // 判断是否在底部（允许10px的误差）
  const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
  isAtBottom.value = distanceFromBottom < 30; // 如果距离底部小于30px，认为是在底部
};

// 重置新消息提示
const resetNewMessageTip = () => {
  showNewMessageTip.value = false;
  newMessageCount.value = 0;
};

// 滚动到底部
const scrollToBottom = () => {
  console.log('尝试滚动到底部');
  
  // 重置新消息提示
  resetNewMessageTip();
  
  // 使用setTimeout确保DOM更新后再滚动
  setTimeout(() => {
    try {
      const query = uni.createSelectorQuery();
      query.select('#message-list').boundingClientRect();
      query.selectAll('.message-wrapper').boundingClientRect();
      query.exec(res => {
        if (res[0] && res[1] && res[1].length > 0) {
          const scrollViewHeight = res[0].height;
          const messagesHeight = res[1].reduce((total, item) => total + item.height, 0);
          
          
          // 设置一个足够大的值确保滚动到底部
          const scrollPosition = messagesHeight * 2;
          
          scrollTop.value = scrollPosition;
          
          // 滚动后认为用户在底部
          isAtBottom.value = true;
        } else {
          console.error('获取消息元素失败', res);
        }
      });
    } catch (error) {
      console.error('滚动到底部出错', error);
    }
  }, 300); // 延迟300ms等待DOM更新
};

// 发送文本消息
const sendTextMessage = async () => {
  if (!inputMessage.value.trim()) return;

  // 发送者始终是当前登录用户
  const currentUserId = userStore.userId;

  // 确定接收者ID
  let receiverId;

  // 判断当前用户是否是咨询师
  const isCurrentUserConsultant = String(currentUserId) === String(consultantId.value);

  if (isCurrentUserConsultant) {
    // 当前用户是咨询师，接收者是客户
    receiverId = String(userId.value);

    // 如果userId.value是undefined，说明参数传递有问题，需要从其他地方获取
    if (!userId.value || userId.value === 'undefined') {
      console.error('客户ID未正确传递，userId.value:', userId.value);
      uni.showToast({
        title: '发送失败：接收者信息错误',
        icon: 'none'
      });
      return;
    }
  } else {
    // 当前用户是客户，接收者是咨询师
    receiverId = String(consultantId.value);

    if (!consultantId.value || consultantId.value === 'undefined') {
      console.error('咨询师ID未正确传递，consultantId.value:', consultantId.value);
      uni.showToast({
        title: '发送失败：接收者信息错误',
        icon: 'none'
      });
      return;
    }
  }

  const message = {
    senderId: String(currentUserId),
    receiverId: receiverId,
    conversationId: String(conversationId.value),
    content: inputMessage.value,
    messageType: '0', // 文本消息
    sendTime: new Date().toISOString(), // 添加发送时间
    isUser: !isCurrentUserConsultant, // 如果当前用户不是咨询师，则是普通用户
    source: 'miniprogram', // 标识消息来源是小程序
    fromClient: true // 标识消息是从客户端发送的
  };

  console.log('发送消息:', message);
  console.log('当前登录用户ID:', currentUserId);
  console.log('是否是咨询师:', isCurrentUserConsultant);
  console.log('接收者ID:', receiverId);

  // 将消息添加到本地消息列表
  messageList.value.push({...message});
  console.log('添加到消息列表后:', messageList.value);

  // 通过WebSocket发送消息
  chatStore.sendChatMessage(message);

  // 清空输入框
  inputMessage.value = '';

  // 滚动到底部
  scrollToBottom();
};

// 选择图片发送
const chooseImage = async () => {
  uni.chooseImage({
    count: 1,
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0];
      
      // 显示上传中提示
      uni.showLoading({
        title: '发送中...'
      });
      
      try {
        // 上传图片
        const uploadRes = await chatStore.uploadImage(tempFilePath);
        
        if (uploadRes && uploadRes.url) {
          // 根据当前用户身份确定发送者和接收者
          const currentUserId = userStore.userId;
          let receiverId;

          // 判断当前用户是否是咨询师
          const isCurrentUserConsultant = String(currentUserId) === String(consultantId.value);

          if (isCurrentUserConsultant) {
            // 当前用户是咨询师，接收者是客户
            receiverId = String(userId.value);
            if (!userId.value || userId.value === 'undefined') {
              console.error('发送图片失败：客户ID未正确传递');
              uni.showToast({
                title: '发送失败：接收者信息错误',
                icon: 'none'
              });
              return;
            }
          } else {
            // 当前用户是客户，接收者是咨询师
            receiverId = String(consultantId.value);
            if (!consultantId.value || consultantId.value === 'undefined') {
              console.error('发送图片失败：咨询师ID未正确传递');
              uni.showToast({
                title: '发送失败：接收者信息错误',
                icon: 'none'
              });
              return;
            }
          }

          // 发送图片消息
          const message = {
            senderId: String(currentUserId),
            receiverId: receiverId,
            conversationId: String(conversationId.value),
            content: '[图片]',
            messageType: '1', // 图片消息
            fileUrl: uploadRes.url,
            sendTime: new Date().toISOString(), // 添加发送时间
            source: 'miniprogram', // 标识消息来源是小程序
            fromClient: true // 标识消息是从客户端发送的
          };

          // 将消息添加到本地消息列表
          messageList.value.push({...message});

          chatStore.sendChatMessage(message);
          scrollToBottom();
        }
      } catch (error) {
        console.error('发送图片失败', error);
        uni.showToast({
          title: '发送图片失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
        showMoreActions.value = false;
      }
    }
  });
};

// 选择文件发送
const chooseFile = async () => {
  // 在H5环境下使用
  // #ifdef H5
  uni.chooseFile({
    count: 1,
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0];
      
      // 显示上传中提示
      uni.showLoading({
        title: '发送中...'
      });
      
      try {
        // 上传文件
        const uploadRes = await chatStore.uploadFile(tempFilePath);
        
        if (uploadRes && uploadRes.url) {
          // 根据当前用户身份确定发送者和接收者
          const currentUserId = userStore.userId;
          let receiverId;

          // 判断当前用户是否是咨询师
          const isCurrentUserConsultant = String(currentUserId) === String(consultantId.value);

          if (isCurrentUserConsultant) {
            // 当前用户是咨询师，接收者是客户
            receiverId = String(userId.value);
            if (!userId.value || userId.value === 'undefined') {
              console.error('发送文件失败：客户ID未正确传递');
              uni.showToast({
                title: '发送失败：接收者信息错误',
                icon: 'none'
              });
              return;
            }
          } else {
            // 当前用户是客户，接收者是咨询师
            receiverId = String(consultantId.value);
            if (!consultantId.value || consultantId.value === 'undefined') {
              console.error('发送文件失败：咨询师ID未正确传递');
              uni.showToast({
                title: '发送失败：接收者信息错误',
                icon: 'none'
              });
              return;
            }
          }

          // 发送文件消息
          const message = {
            senderId: String(currentUserId),
            receiverId: receiverId,
            conversationId: String(conversationId.value),
            content: '[文件]',
            messageType: '2', // 文件消息
            fileUrl: uploadRes.url,
            sendTime: new Date().toISOString(), // 添加发送时间
            source: 'miniprogram', // 标识消息来源是小程序
            fromClient: true // 标识消息是从客户端发送的
          };

          // 将消息添加到本地消息列表
          messageList.value.push({...message});

          chatStore.sendChatMessage(message);
          scrollToBottom();
        }
      } catch (error) {
        console.error('发送文件失败', error);
        uni.showToast({
          title: '发送文件失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
        showMoreActions.value = false;
      }
    }
  });
  // #endif
  
  // 在小程序环境下提示
  // #ifdef MP
  uni.showToast({
    title: '小程序暂不支持发送文件',
    icon: 'none'
  });
  // #endif
  
  showMoreActions.value = false;
};

// 长按消息显示操作菜单
const handleLongPress = (message) => {
  selectedMessage.value = message;
  // 通过模板引用获取组件实例
  nextTick(() => {
    if (messageActionPopup.value) {
      messageActionPopup.value.open();
    } else {
      uni.showToast({
        title: '操作菜单初始化失败',
        icon: 'none'
      });
    }
  });
};

// 关闭操作菜单
const closeActionMenu = () => {
  if (messageActionPopup.value) {
    messageActionPopup.value.close();
  }
  selectedMessage.value = null;
};

// 复制消息内容
const copyMessage = () => {
  if (!selectedMessage.value) return;
  
  uni.setClipboardData({
    data: selectedMessage.value.content,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      });
      closeActionMenu();
    }
  });
};

// 撤回消息
const withdrawMessage = async () => {
  if (!selectedMessage.value) return;
  
  try {
    // 发送撤回请求
    chatStore.sendWithdrawNotification(selectedMessage.value.messageId);
    
    // 撤回成功后，更新本地消息状态
    const index = messageList.value.findIndex(msg => msg.messageId === selectedMessage.value.messageId);
    if (index !== -1) {
      messageList.value[index].isWithdrawn = '1';
    }
    
    closeActionMenu();
  } catch (error) {
    console.error('撤回消息失败', error);
    uni.showToast({
      title: '撤回消息失败',
      icon: 'none'
    });
  }
};

// 预览图片
const previewImage = (url) => {
  uni.previewImage({
    urls: [url],
    current: url
  });
};

// 打开文件
const openFile = (url) => {
  uni.showLoading({
    title: '打开中...'
  });
  
  uni.downloadFile({
    url: url,
    success: (res) => {
      uni.hideLoading();
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          fail: () => {
            uni.showToast({
              title: '无法打开此文件',
              icon: 'none'
            });
          }
        });
      }
    },
    fail: () => {
      uni.hideLoading();
      uni.showToast({
        title: '文件下载失败',
        icon: 'none'
      });
    }
  });
};

// 获取文件名
const getFileName = (url) => {
  if (!url) return '文件';
  return url.substring(url.lastIndexOf('/') + 1);
};

// 图片加载失败处理
const onImageError = () => {
  uni.showToast({
    title: '图片加载失败',
    icon: 'none'
  });
};

// 切换更多功能面板
const toggleMoreActions = () => {
  // 记录当前滚动位置
  const currentScrollTop = scrollTop.value;
  
  showMoreActions.value = !showMoreActions.value;
  
  // 在下一个 tick 恢复滚动位置
  nextTick(() => {
    scrollTop.value = currentScrollTop;
  });
};

// 输入框获取焦点
const onInputFocus = (e) => {
  keyboardHeight.value = e.detail?.height || 0;
  showMoreActions.value = false;
};

// 输入框失去焦点
const onInputBlur = () => {
  keyboardHeight.value = 0;
};

// 判断是否显示时间
const shouldShowTime = (message, index, messages) => {
  // 第一条消息总是显示时间
  if (index === 0) return true;
  
  const currentDate = parseDateSafely(message.sendTime);
  const prevDate = parseDateSafely(messages[index - 1].sendTime);
  
  // 计算时间差(分钟)
  const minutesDiff = (currentDate - prevDate) / (1000 * 60);
  
  // 如果间隔超过5分钟,显示时间
  return minutesDiff > 5;
};

// 根据上下文格式化日期(今天/昨天/本周/更早)
const formatDateWithContext = (date, now) => {
  if (!date) return '';
  
  // 获取纯日期（不含时间）
  const dateWithoutTime = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayWithoutTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  // 计算日期差（天数）
  const dayDiff = Math.floor((todayWithoutTime - dateWithoutTime) / (24 * 60 * 60 * 1000));
  
  // 获取时间部分
  const timeStr = formatTimeOnly(date);
  
  // 今天 - 只显示时间
  if (dayDiff === 0) {
    return timeStr;
  }
  
  // 昨天 - 显示 "昨天 HH:mm"
  if (dayDiff === 1) {
    return `昨天 ${timeStr}`;
  }
  
  // 一周内（2-7天）- 显示 "星期X HH:mm"
  if (dayDiff < 7) {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return `${weekdays[date.getDay()]} ${timeStr}`;
  }
  
  // 一年内 - 显示 "MM/DD HH:mm"
  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}/${date.getDate()} ${timeStr}`;
  }
  
  // 更早 - 显示 "YYYY/MM/DD HH:mm"
  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()} ${timeStr}`;
};

// 格式化时间（只显示小时和分钟）
const formatTimeOnly = (date) => {
  if (!date) return '';
  const hours = date.getHours();
  const minutes = date.getMinutes();
  return `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
};

// 标记消息为已读
const markAsRead = async () => {
  if (!conversationId.value) return;
  
  try {
    // 标记会话所有消息为已读
    const isUser = userId.value !== consultantId.value;
    
    await chatStore.markAllMessagesAsRead(conversationId.value, isUser);
  } catch (error) {
    console.error('标记已读失败', error);
  }
};

// 安全的日期解析函数,兼容各种平台包括iOS
const parseDateSafely = (dateString) => {
  if (!dateString) return new Date();
  
  try {
    // 如果是ISO格式的字符串，直接返回
    if (dateString.includes('T')) {
      return new Date(dateString);
    }
    
    // 处理 yyyy-MM-dd HH:mm:ss.SSS 格式
    if (dateString.includes('-') && dateString.includes(':')) {
      // 1. 分离日期和时间
      const [datePart, timePart] = dateString.split(' ');
      
      // 2. 处理时间部分（移除毫秒）
      const cleanTime = timePart.split('.')[0];
      
      // 3. 将日期中的'-'替换为'/'（iOS兼容格式）
      const iOSDate = datePart.replace(/-/g, '/');
      
      // 4. 组合成iOS支持的格式：yyyy/MM/dd HH:mm:ss
      const iOSDateString = `${iOSDate} ${cleanTime}`;
      
      return new Date(iOSDateString);
    }
    
    // 处理纯日期格式 yyyy-MM-dd
    if (dateString.includes('-') && !dateString.includes(':')) {
      return new Date(dateString.replace(/-/g, '/'));
    }
    
    // 如果是时间戳（数字字符串）
    if (!isNaN(dateString)) {
      return new Date(parseInt(dateString));
    }
    
    // 其他情况尝试直接解析
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date;
    }
    
    // 如果所有方法都失败，返回当前时间
    console.error('无法解析日期:', dateString);
    return new Date();
  } catch (e) {
    console.error('日期解析失败:', dateString, e);
    return new Date();
  }
};
</script>

<style scoped lang="scss">
.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120rpx);
  background-color: #f5f5f5;
  
  .header {
    padding: 20rpx;
    text-align: center;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
    
    .nickname {
      font-size: 32rpx;
      font-weight: 500;
    }
  }
  
  .message-list {
    flex: 1;
    width: calc(100% - 20rpx);
    padding: 20rpx;
    padding-right: 0;
    height: calc(100vh - 120rpx);
    
    .loading {
      padding: 20rpx 0;
    }
    
    .time-divider {
      text-align: center;
      margin: 20rpx 0;
      
      text {
        font-size: 24rpx;
        color: rgba(0, 0, 0, 0.45);
        background-color: rgba(0, 0, 0, 0.03);
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
      }
    }
    
    .message-wrapper {
      margin-bottom: 30rpx;
      padding-right: 30rpx;
    }
    
    .message-item {
      display: flex;
      margin-bottom: 10rpx;
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #eee;
      }
      
      .message-content {
        max-width: 60%;
        margin: 0 20rpx;
        
        .bubble {
          padding: 20rpx;
          border-radius: 10rpx;
          word-break: break-word;
          
          .text-content {
            font-size: 28rpx;
            line-height: 1.4;
          }
          
          .image-content {
            max-width: 100%;
            border-radius: 8rpx;
          }
          
          .file-content {
            display: flex;
            align-items: center;
            font-size: 28rpx;
            
            uni-icons {
              margin-right: 10rpx;
            }
          }
        }
        
        .withdrawn-message {
          padding: 20rpx;
          border-radius: 10rpx;
          background-color: #f1f1f1;
          color: #999;
          font-style: italic;
          font-size: 26rpx;
        }
      }
    }
    
    .self-message {
      flex-direction: row-reverse;
      
      .message-content {
        align-items: flex-end;
        
        .bubble {
          background-color: #a0cfff;
          color: #333;
        }
      }
    }
    
    .other-message {
      .message-content {
        .bubble {
          background-color: #fff;
          color: #333;
        }
      }
    }
    
    .bottom-space {
      transition: height 0.3s;
    }
  }
  
  .input-area {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f5f5f5;
    border-top: 1rpx solid #eee;
    padding: 20rpx;
    
    .input-wrapper {
      display: flex;
      align-items: center;
      
      .action-button {
        flex-shrink: 0;
        width: 70rpx;
        height: 70rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      .message-input {
        flex: 1;
        background-color: #fff;
        border-radius: 8rpx;
        padding: 16rpx;
        margin: 0 10rpx;
        font-size: 28rpx;
      }
    }
    
    .more-panel {
      display: flex;
      flex-wrap: wrap;
      padding: 30rpx 0;
      
      .action-item {
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 30rpx;
        
        .action-icon {
          width: 100rpx;
          height: 100rpx;
          background-color: #fff;
          border-radius: 20rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10rpx;
        }
        
        text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

.action-menu {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  
  .action-menu-item {
    padding: 30rpx 0;
    text-align: center;
    font-size: 32rpx;
    color: #333;
    border-bottom: 1rpx solid #eee;
    
    &:active {
      background-color: #f5f5f5;
    }
    
    &.cancel {
      color: #999;
      margin-top: 16rpx;
      border-bottom: none;
    }
  }
}

.new-message-tip {
  position: fixed;
  right: 30rpx;
  bottom: 140rpx;
  z-index: 999;
  
  .tip-content {
    background-color: #1989fa;
    border-radius: 40rpx;
    padding: 12rpx 24rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    uni-icons {
      margin-right: 10rpx;
    }
    
    text {
      font-size: 26rpx;
      color: #fff;
    }
  }
}
</style>
