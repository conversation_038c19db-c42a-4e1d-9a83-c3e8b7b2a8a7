<template>
	<view class="enhanced-time-table">
		<!-- 日期选择器 -->
		<view class="date-selector" v-if="showDateSelector">
			<scroll-view class="scroll-view_H" scroll-x>
				<view :class="{ 'is-active': item.isToday }" class="temp-box" v-for="(item, index) in dateInfo" :key="item.data"
					@click="handleSelectDate(item, index)">
					<view class="time-date-week">
						{{ item.weekDay }}
					</view>
					<view class="time-date-temp" />
					<view class="border-box">
						<view class="time-date-month">{{ item.shortDate }}</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 时间段选择器 -->
		<view class="time-grid">
			<view class="time-period" v-for="item in timeInfo" :key="item.id">
				<view class="period-title">
					<image mode="scaleToFill" :src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/${encodeURIComponent(
						item.rangeName
					)}.png`"></image>
					{{ item.rangeName }}
				</view>
				<view class="time-row">
					<view v-for="slot in item.slots" :key="slot.id" :class="{
						'time-slot': true,
						'is-active': selectTime.indexOf(slot.fullDate + slot.time) !== -1,
						'is-disabled': slot.isDisabled || slot.timeStatus === '已约满',
						'is-start': isIntervalStart(slot.fullDate + slot.time),
						'is-end': isIntervalEnd(slot.fullDate + slot.time),
						'is-middle': isIntervalMiddle(slot.fullDate + slot.time)
					}" @click="handleTimeClick(slot)">
						<view class="time-text">{{ slot.time }}</view>
						<view class="availability-text" v-if="slot.availabilityText">
							{{ slot.availabilityText }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提示信息 -->
		<view class="msg" v-if="showMessage">
			{{ message }}
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons" v-if="showActionButtons">
			<button class="clear-btn" @click="clearSelection" v-if="selectTime.length > 0">
				清空选择
			</button>
			<button class="confirm-btn" type="primary" @click="confirmSelection" :disabled="selectTime.length === 0">
				确认选择
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, nextTick, onMounted, watch } from 'vue'
import { getFormattedTimeSlots } from '@/api/timeSlot.js'
import { getCounselorFormattedTimeSlots, getCounselorTime } from '@/api/common.js'

// Props
const props = defineProps({
	// 是否显示日期选择器
	showDateSelector: {
		type: Boolean,
		default: true
	},
	// 获取时间数据的天数范围
	dayRange: {
		type: Number,
		default: 6
	},
	// 是否显示提示信息
	showMessage: {
		type: Boolean,
		default: true
	},
	// 提示信息内容
	message: {
		type: String,
		default: '请选择咨询时间，每次咨询时长为1小时,不在当前时间表内请下单后与客服预约时间'
	},
	// 是否显示操作按钮
	showActionButtons: {
		type: Boolean,
		default: true
	},
	// 咨询师ID（可选，如果提供则获取特定咨询师的时间表）
	counselorId: {
		type: [String, Number],
		default: null
	}
})

// Emits
const emit = defineEmits(['timeChange', 'intervalChange', 'confirm', 'dateChange'])

// 响应式数据
const dateInfo = ref([])
const timeInfo = ref([])
const selectTime = ref([])
const timeIntervals = ref([])
const selectedHours = ref([])

// 常量
const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

// 检查时间是否已过期
const isTimeExpired = (date, time) => {
	const now = new Date();
	const currentDate = now.toISOString().split('T')[0]; // 获取当前日期 YYYY-MM-DD
	const currentTime = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');

	// 如果是今天之前的日期，则已过期
	if (date < currentDate) {
		return true;
	}

	// 如果是今天，检查时间是否已过
	if (date === currentDate && time < currentTime) {
		return true;
	}

	return false;
}

// 获取时间列表
const getTimeList = async () => {
	try {
		let res;

		// 如果有咨询师ID，获取特定咨询师的时间表
		if (props.counselorId) {
			console.log('获取咨询师时间表，ID:', props.counselorId);

			try {
				// 首先尝试新的标准化接口
				const startDate = new Date();
				const endDate = new Date();
				endDate.setDate(startDate.getDate() + props.dayRange);

				// 格式化日期为 YYYY-MM-DD
				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					return `${year}-${month}-${day}`;
				};

				res = await getCounselorFormattedTimeSlots(
					props.counselorId,
					formatDate(startDate),
					formatDate(endDate)
				);

				console.log('新接口API响应:', res);
			} catch (newApiError) {
				console.warn('新接口调用失败，尝试使用旧接口:', newApiError);

				try {
					// 备用方案：使用旧接口
					res = await getCounselorTime(props.counselorId);
					console.log('旧接口API响应:', res);
				} catch (oldApiError) {
					console.error('新旧接口都调用失败:', { newApiError, oldApiError });
					throw oldApiError;
				}
			}

			if (res.code === 200) {


				// 检查数据结构
				if (res.data && res.data.dates) {
					console.log('找到dates字段:', res.data.dates);
					// 处理日期信息（与系统时间表处理逻辑一致）
					dateInfo.value = res.data.dates.map(dateData => {
						const currentDate = new Date(dateData.date.replace(/-/g, '/'));
						const month = String(currentDate.getMonth() + 1).padStart(2, '0');
						const day = String(currentDate.getDate()).padStart(2, '0');

						return {
							data: dateData.date,
							shortDate: `${month}月${day}日`,
							weekDay: weekDays[currentDate.getDay()],
							isToday: dateData.isToday,
							timeRanges: dateData.timeRanges
						};
					});

					// 处理时间段信息（与系统时间表处理逻辑一致）
					if (dateInfo.value.length > 0) {
						const firstDateData = dateInfo.value[0];
						timeInfo.value = firstDateData.timeRanges.map(range => ({
							id: range.endHour,
							rangeName: range.rangeName,
							slots: range.slots.map(slot => {
								const timeStr = slot.startTime.substring(0, 5);
								const isExpired = isTimeExpired(firstDateData.data, timeStr);

								return {
									id: slot.slotId,
									time: timeStr,
									fullDate: firstDateData.data,
									timeStatus: isExpired ? '已过期' : (slot.status === 2 ? '已过期' : (slot.available ? '可预约' : '已约满')),
									isDisabled: isExpired || slot.status === 2 || !slot.available,
									availabilityText: isExpired ? '已过期' : slot.statusText,
									timeDisplay: slot.timeDisplay
								};
							})
						}));
					}
				} else if (Array.isArray(res.data)) {
					// 如果返回的是数组格式（类似旧接口）

					const data = res.data;
					data[0].isToday = true;

					dateInfo.value = data.map((dateData, index) => {
						// 处理可能的不同日期格式
						let dateStr = dateData.date || dateData.data;
						let currentDate;

						if (dateStr.includes('年')) {
							// 处理中文格式
							const dateMatch = dateStr.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
							if (dateMatch) {
								const [, year, month, day] = dateMatch;
								currentDate = new Date(year, month - 1, day);
								dateStr = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
							}
						} else {
							currentDate = new Date(dateStr.replace(/-/g, '/'));
						}

						const month = String(currentDate.getMonth() + 1).padStart(2, '0');
						const day = String(currentDate.getDate()).padStart(2, '0');

						return {
							data: dateStr,
							shortDate: dateData.shortDate || `${month}月${day}日`,
							weekDay: dateData.weekDay || weekDays[currentDate.getDay()],
							isToday: dateData.isToday || index === 0,
							timeRanges: dateData.timeRanges
						};
					});

					// 处理时间段信息
					if (dateInfo.value.length > 0) {
						const firstDateData = dateInfo.value[0];
						timeInfo.value = firstDateData.timeRanges.map(range => ({
							id: range.endHour,
							rangeName: range.rangeName,
							slots: range.slots.map(slot => ({
								id: slot.slotId || slot.id,
								time: slot.time || (slot.startTime ? slot.startTime.substring(0, 5) : '00:00'),
								fullDate: firstDateData.data,
								timeStatus: slot.status === 2 ? '已过期' : (slot.available ? '可预约' : '已约满'),
								isDisabled: slot.status === 2 || !slot.available,
								availabilityText: slot.statusText || slot.availabilityText,
								timeDisplay: slot.timeDisplay
							}))
						}));
					}
				} else {
					console.error('未知的数据格式:', res.data);
					uni.showToast({
						title: '数据格式异常',
						icon: 'none'
					});
				}
			}
		} else {
			// 获取系统通用时间表
			const startDate = new Date();
			const endDate = new Date();
			endDate.setDate(startDate.getDate() + props.dayRange);

			// 格式化日期为 YYYY-MM-DD
			const formatDate = (date) => {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			};

			res = await getFormattedTimeSlots(
				formatDate(startDate),
				formatDate(endDate)
			);

			if (res.code === 200) {
				// 处理系统时间表数据格式
				dateInfo.value = res.data.dates.map(dateData => {
					const currentDate = new Date(dateData.date.replace(/-/g, '/'));
					const month = String(currentDate.getMonth() + 1).padStart(2, '0');
					const day = String(currentDate.getDate()).padStart(2, '0');

					return {
						data: dateData.date,
						shortDate: `${month}月${day}日`,
						weekDay: weekDays[currentDate.getDay()],
						isToday: dateData.isToday,
						timeRanges: dateData.timeRanges
					};
				});

				// 处理时间段信息
				if (dateInfo.value.length > 0) {
					const firstDateData = dateInfo.value[0];
					timeInfo.value = firstDateData.timeRanges.map(range => ({
						id: range.endHour,
						rangeName: range.rangeName,
						slots: range.slots.map(slot => ({
							id: slot.slotId,
							time: slot.startTime.substring(0, 5),
							fullDate: firstDateData.data,
							timeStatus: slot.status === 2 ? '已过期' : (slot.hasAvailable ? '可预约' : '已约满'),
							isDisabled: slot.status === 2 || !slot.hasAvailable,
							availabilityText: slot.availabilityText,
							timeDisplay: slot.timeDisplay
						}))
					}));
				}
			}
		}

		if (!res || res.code !== 200) {
			const errorMsg = props.counselorId ? '获取咨询师时间表失败' : '获取时间列表失败';
			console.error(errorMsg, res);
			uni.showToast({
				title: errorMsg,
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('获取时间列表错误：', error);
		const errorMsg = props.counselorId ? '获取咨询师时间表异常' : '网络异常，请稍后重试';
		uni.showToast({
			title: errorMsg,
			icon: 'none'
		});
	}
};

// 获取时间的分钟表示，用于计算时间差
const getMinutes = (timeStr) => {
	const [hours, minutes] = timeStr.split(':').map(Number);
	return hours * 60 + minutes;
};

// 根据分钟数生成时间字符串
const getTimeString = (minutes) => {
	const hours = Math.floor(minutes / 60);
	const mins = minutes % 60;
	return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

// 获取指定时间段内的所有时间点
const getAllTimesBetween = (startTime, date) => {
	const startMinutes = getMinutes(startTime);
	const endMinutes = startMinutes + 60; // 一小时后
	const times = [];

	// 获取所有时间段的slots
	const allSlots = timeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	// 生成一小时内的5个时间点（包括结束时间）
	for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
		const timeStr = getTimeString(mins);
		const slot = allSlots.find(s => s.time === timeStr && s.fullDate === date);
		if (slot && slot.timeStatus !== '已过期') {
			times.push(date + timeStr);
		}
	}

	return times;
};

// 检查是否可以创建区间
const canCreateInterval = (startTime, date) => {
	const startMinutes = getMinutes(startTime);
	const endMinutes = startMinutes + 60; // 一小时后

	// 获取所有时间段的slots
	const allSlots = timeInfo.value.reduce((acc, range) => {
		return acc.concat(range.slots);
	}, []);

	let availableSlots = 0;

	// 检查一小时内的所有时间点（包括结束时间）
	for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
		const timeStr = getTimeString(mins);

		// 在所有时间段中查找该时间点
		const slot = allSlots.find(s => s.time === timeStr && s.fullDate === date);

		// 只检查时间点是否存在且可用，不检查是否在已选区间内
		if (slot && slot.timeStatus !== '已约满' && slot.timeStatus !== '已过期') {
			// 如果时间点在已选区间内，也算作可用（因为可能是相邻扩展）
			availableSlots++;
		}
	}

	// 必须有5个可用的时间点（完整的一小时，包括结束时间）
	return availableSlots === 5;
};

// 检查时间点是否在任何已选区间内
const isTimeInInterval = (fullTime) => {
	return timeIntervals.value.some(interval => {
		return interval.timePoints.includes(fullTime);
	});
};

// 获取时间点所属的区间
const getTimeInterval = (fullTime) => {
	return timeIntervals.value.find(interval =>
		interval.timePoints.includes(fullTime)
	);
};

// 创建新的时间区间
const createTimeInterval = (startTime, timePoints) => {
	return {
		startTime,
		timePoints,
		duration: (timePoints.length - 1) * 15 // 根据时间点数量计算时长（n个点代表(n-1)*15分钟）
	};
};

// 检查时间点是否与已选时间段有冲突
const checkTimeConflict = (date, startMinutes, endMinutes) => {
	// 获取所有已选时间区间的范围
	const selectedRanges = timeIntervals.value.map(interval => {
		const firstTime = interval.timePoints[0].slice(-5);
		const lastTime = interval.timePoints[interval.timePoints.length - 1].slice(-5);
		return {
			date: interval.timePoints[0].slice(0, 10),
			start: getMinutes(firstTime),
			end: getMinutes(lastTime) + 15 // 加上最后一个时间点的持续时间（15分钟）
		};
	});

	// 检查是否与任何已选时间段重叠
	return selectedRanges.some(range => {
		// 只检查同一天的时间段
		if (range.date !== date) {
			return false;
		}

		// 如果新时间段正好连接到已有时间段（结束时间等于已有时间段的开始时间，或开始时间等于已有时间段的结束时间），则不算冲突
		if (endMinutes === range.start || startMinutes === range.end) {
			return false;
		}

		// 检查时间段是否重叠
		return (startMinutes < range.end && endMinutes > range.start);
	});
};

// 更新时间点的禁用状态
const updateDisabledStatus = () => {
	timeInfo.value.forEach(range => {
		range.slots.forEach(slot => {
			// 重置状态
			slot.isDisabled = false;

			// 如果时间已过期或已约满，则禁用
			if (slot.timeStatus === '已过期' || slot.timeStatus === '已约满') {
				slot.isDisabled = true;
				return;
			}

			// 检查是否在任何已选区间内
			const fullTime = slot.fullDate + slot.time;
			if (isTimeInInterval(fullTime)) {
				return; // 如果在已选区间内，保持可选状态
			}

			// 如果没有已选区间，所有可用时间点都应该可选（除了已过期/已约满的）
			if (timeIntervals.value.length === 0) {
				// 没有已选区间时，检查是否有足够的连续时间点来创建一小时区间
				const startMinutes = getMinutes(slot.time);
				const endMinutes = startMinutes + 60;

				// 获取所有时间段的slots
				const allSlots = timeInfo.value.reduce((acc, range) => {
					return acc.concat(range.slots);
				}, []);

				let availableSlots = 0;
				for (let mins = startMinutes; mins <= endMinutes; mins += 15) {
					const timeStr = getTimeString(mins);
					const timeSlot = allSlots.find(s => s.time === timeStr && s.fullDate === slot.fullDate);
					if (timeSlot && timeSlot.timeStatus !== '已约满' && timeSlot.timeStatus !== '已过期') {
						availableSlots++;
					}
				}

				// 如果没有足够的时间点创建完整一小时，则禁用
				if (availableSlots < 5) {
					slot.isDisabled = true;
				}
			} else {
				// 有已选区间时，检查是否与已选时间段冲突
				const startMinutes = getMinutes(slot.time);
				const endMinutes = startMinutes + 60;

				// 检查是否与已选时间段重叠
				const hasConflict = checkTimeConflict(slot.fullDate, startMinutes, endMinutes);
				if (hasConflict) {
					slot.isDisabled = true;
					return;
				}

				// 检查是否可以创建完整的一小时区间
				if (!canCreateInterval(slot.time, slot.fullDate)) {
					// 检查是否与已选时间段相邻（相邻的完整一小时应该可选）
					const isAdjacentToSelected = timeIntervals.value.some(interval => {
						const firstTime = interval.timePoints[0].slice(-5);
						const lastTime = interval.timePoints[interval.timePoints.length - 1].slice(-5);
						const intervalStart = getMinutes(firstTime);
						const intervalEnd = getMinutes(lastTime);

						// 检查是否相邻：新区间的结束时间等于已有区间的开始时间，或新区间的开始时间等于已有区间的结束时间+15分钟
						return (endMinutes === intervalStart || startMinutes === intervalEnd + 15);
					});

					// 如果不是相邻的，且无法创建完整区间，则禁用
					if (!isAdjacentToSelected) {
						slot.isDisabled = true;
					}
				}
			}
		});
	});
};

// 查找相邻的时间区间
const findAdjacentInterval = (data) => {
	const currentMinutes = getMinutes(data.time);
	const currentDate = data.fullDate;



	return timeIntervals.value.find(interval => {
		const firstPoint = interval.timePoints[0];
		const lastPoint = interval.timePoints[interval.timePoints.length - 1];

		const firstTime = firstPoint.slice(-5); // 获取时间部分 HH:mm
		const lastTime = lastPoint.slice(-5);
		const firstMinutes = getMinutes(firstTime);
		const lastMinutes = getMinutes(lastTime);

		const isAfterEnd = currentMinutes === lastMinutes + 15; // 正好接在区间末尾的下一个时间点
		const isBeforeStart = currentMinutes + 60 === firstMinutes; // 正好接在区间开头
		const isSameDay = currentDate === firstPoint.slice(0, 10);



		// 检查是否紧邻现有区间
		return (isSameDay && (isAfterEnd || isBeforeStart));
	});
};

// 扩展现有区间
const extendInterval = (existingInterval, data) => {
	const currentMinutes = getMinutes(data.time);
	const firstPoint = existingInterval.timePoints[0];
	const lastPoint = existingInterval.timePoints[existingInterval.timePoints.length - 1];
	const firstTime = firstPoint.slice(-5);
	const lastTime = lastPoint.slice(-5);
	const firstMinutes = getMinutes(firstTime);
	const lastMinutes = getMinutes(lastTime);



	let newTimePoints = [];

	// 检查是否是向后扩展（点击区间后的相邻时间点）
	if (currentMinutes === lastMinutes + 15) {

		// 向后扩展：从当前点击的时间开始，扩展到下一个整点
		const extendStartMinutes = currentMinutes; // 从点击的时间开始
		const extendEndMinutes = currentMinutes + 45; // 扩展到下一个整点（14:15 + 45分钟 = 15:00）



		// 检查扩展区域的时间点是否都可用
		let canExtend = true;
		const allSlots = timeInfo.value.reduce((acc, range) => acc.concat(range.slots), []);

		for (let mins = extendStartMinutes; mins <= extendEndMinutes; mins += 15) {
			const timeStr = getTimeString(mins);
			const slot = allSlots.find(s => s.time === timeStr && s.fullDate === data.fullDate);



			if (!slot || slot.timeStatus === '已约满' || slot.timeStatus === '已过期') {
				canExtend = false;
				break;
			}
		}

		if (canExtend) {
			// 生成新的时间点
			for (let mins = extendStartMinutes; mins <= extendEndMinutes; mins += 15) {
				const timeStr = getTimeString(mins);
				newTimePoints.push(data.fullDate + timeStr);
			}

			// 扩展现有区间
			existingInterval.timePoints = [...existingInterval.timePoints, ...newTimePoints];
			// 更新区间时长（n个点代表(n-1)*15分钟）
			existingInterval.duration = (existingInterval.timePoints.length - 1) * 15;
			selectTime.value = [...selectTime.value, ...newTimePoints];
		} else {
			uni.showToast({
				title: "无法扩展时间区间",
				icon: "none",
				duration: 2000
			});
		}
	} else if (currentMinutes + 60 === firstMinutes) {
		// 向前扩展：在区间开头添加新的时间点
		const extendStartMinutes = currentMinutes;
		const extendEndMinutes = extendStartMinutes + 60;

		// 检查扩展区域的时间点是否都可用
		let canExtend = true;
		const allSlots = timeInfo.value.reduce((acc, range) => acc.concat(range.slots), []);

		for (let mins = extendStartMinutes; mins < extendEndMinutes; mins += 15) {
			const timeStr = getTimeString(mins);
			const slot = allSlots.find(s => s.time === timeStr && s.fullDate === data.fullDate);

			if (!slot || slot.timeStatus === '已约满' || slot.timeStatus === '已过期') {
				canExtend = false;
				break;
			}
		}

		if (canExtend) {
			// 生成新的时间点
			for (let mins = extendStartMinutes; mins < extendEndMinutes; mins += 15) {
				const timeStr = getTimeString(mins);
				newTimePoints.push(data.fullDate + timeStr);
			}

			// 扩展现有区间
			existingInterval.timePoints = [...newTimePoints, ...existingInterval.timePoints];
			// 更新区间时长（n个点代表(n-1)*15分钟）
			existingInterval.duration = (existingInterval.timePoints.length - 1) * 15;
			selectTime.value = [...selectTime.value, ...newTimePoints];
		} else {
			uni.showToast({
				title: "无法扩展时间区间",
				icon: "none",
				duration: 2000
			});
		}
	}
};

// 创建新区间
const createNewInterval = (data) => {
	if (canCreateInterval(data.time, data.fullDate)) {
		const hourTimePoints = getAllTimesBetween(data.time, data.fullDate);

		if (hourTimePoints.length === 5) {
			const newInterval = createTimeInterval(data.fullDate + data.time, hourTimePoints);
			timeIntervals.value.push(newInterval);
			selectTime.value = [...selectTime.value, ...hourTimePoints];
		} else {
			uni.showToast({
				title: "无法创建完整的一小时区间",
				icon: "none",
				duration: 2000
			});
		}
	} else {
		uni.showToast({
			title: "无法创建时间区间，请检查时间是否充足或是否与其他区间冲突",
			icon: "none",
			duration: 2000
		});
	}
};

// 处理时间点选择的核心逻辑
const handleTimeSelection = (data) => {
	const fullTime = data.fullDate + data.time;

	// 检查时间槽是否已禁用
	if (data.isDisabled || data.timeStatus === '已约满' || data.timeStatus === '已过期') {
		uni.showToast({
			title: "该时段已约满或不可选",
			icon: "none",
			duration: 2000
		});
		return;
	}

	// 检查是否点击了已选区间内的时间点
	const existingInterval = getTimeInterval(fullTime);
	if (existingInterval) {

		// 点击已选区间内的任何时间点都取消整个区间
		const index = timeIntervals.value.indexOf(existingInterval);
		timeIntervals.value.splice(index, 1);
		selectTime.value = selectTime.value.filter(time =>
			!existingInterval.timePoints.includes(time)
		);
		// 重新计算所有时间点的状态
		nextTick(() => {
			updateDisabledStatus();
			emitChanges();
		});
		return;
	}

	// 查找是否有相邻的区间（优先检查扩展）
	const adjacentInterval = findAdjacentInterval(data);
	if (adjacentInterval) {

		// 扩展现有区间
		extendInterval(adjacentInterval, data);
	} else {
		// 检查新时间段是否与已有时间段冲突
		const startMinutes = getMinutes(data.time);
		const endMinutes = startMinutes + 60;
		if (checkTimeConflict(data.fullDate, startMinutes, endMinutes)) {
			uni.showToast({
				title: "该时段与已选时间冲突",
				icon: "none",
				duration: 2000
			});
			return;
		}


		// 创建新区间
		createNewInterval(data);
	}

	// 更新禁用状态
	updateDisabledStatus();
	emitChanges();
};

// 发送变化事件
const emitChanges = () => {
	emit('timeChange', selectTime.value);
	emit('intervalChange', timeIntervals.value);
};

// 处理时间点击
const handleTimeClick = (slot) => {
	handleTimeSelection(slot);
};

// 处理日期选择
const handleSelectDate = (item, index) => {
	dateInfo.value.forEach((dateItem) => (dateItem.isToday = false));
	dateInfo.value[index].isToday = true;

	// 更新时间段信息
	const selectedDateData = dateInfo.value[index];
	timeInfo.value = selectedDateData.timeRanges.map(range => ({
		id: range.endHour,
		rangeName: range.rangeName,
		slots: range.slots.map(slot => {
			const timeStr = slot.startTime.substring(0, 5);
			const isExpired = isTimeExpired(selectedDateData.data, timeStr);

			return {
				id: slot.slotId,
				time: timeStr,
				fullDate: selectedDateData.data,
				timeStatus: isExpired ? '已过期' : (slot.status === 2 ? '已过期' : (slot.available ? '可预约' : '已约满')),
				isDisabled: isExpired || slot.status === 2 || !slot.available,
				availabilityText: isExpired ? '已过期' : slot.statusText,
				timeDisplay: slot.timeDisplay
			};
		})
	}));

	// 切换日期时清空已选择的时间
	selectTime.value = [];
	timeIntervals.value = [];

	// 重新计算禁用状态
	nextTick(() => {
		updateDisabledStatus();
		emitChanges();
	});

	emit('dateChange', item, index);
};

// 检查时间点是否是区间的开始
const isIntervalStart = (fullTime) => {
	return timeIntervals.value.some(interval => interval.timePoints && interval.timePoints[0] === fullTime)
}

// 检查时间点是否是区间的结束
const isIntervalEnd = (fullTime) => {
	return timeIntervals.value.some(interval =>
		interval.timePoints && interval.timePoints[interval.timePoints.length - 1] === fullTime
	)
}

// 检查时间点是否是区间的中间点
const isIntervalMiddle = (fullTime) => {
	return timeIntervals.value.some(interval => {
		if (!interval.timePoints) return false
		const index = interval.timePoints.indexOf(fullTime)
		return index > 0 && index < interval.timePoints.length - 1
	})
}

// 清空选择
const clearSelection = () => {
	selectTime.value = [];
	timeIntervals.value = [];
	updateDisabledStatus();
	emitChanges();
};

// 确认选择
const confirmSelection = () => {
	if (selectTime.value.length === 0) {
		uni.showToast({
			title: '请先选择咨询时间',
			icon: 'none',
			duration: 2000
		});
		return;
	}

	// 构建时间参数
	const timeParams = selectTime.value.map(time => {
		const date = time.substring(0, 10);
		const timeStr = time.substring(10);
		return {
			date,
			time: timeStr
		};
	});

	emit('confirm', {
		selectTime: selectTime.value,
		timeIntervals: timeIntervals.value,
		timeParams
	});
};

// 暴露方法给父组件
const getSelectedTimes = () => {
	return selectTime.value;
};

const getTimeIntervals = () => {
	return timeIntervals.value;
};

const refreshTimeList = () => {
	getTimeList();
};

// 初始化
onMounted(() => {
	getTimeList();
	nextTick(() => {
		updateDisabledStatus();
	});
});

// 暴露给父组件的方法
defineExpose({
	getSelectedTimes,
	getTimeIntervals,
	clearSelection,
	refreshTimeList,
	confirmSelection
})
</script>

<style lang="scss" scoped>
.enhanced-time-table {
	width: 100%;
}

/* 日期选择器样式 */
.date-selector {
	.scroll-view_H {
		white-space: nowrap;

		.temp-box {
			width: 140rpx;
			height: 100rpx;
			display: inline-block;
			margin: 0 10rpx 10rpx;
			position: relative;

			.border-box {
				border: 1px solid #ccc;
				position: absolute;
				bottom: 0;
				height: 80rpx;
				z-index: 1;
				background-color: #fff;
				width: 100%;
				display: flex;
				align-items: flex-end;
				justify-content: center;
				font-size: 26rpx;
				border-radius: 10rpx;
			}

			.time-date-temp {
				width: 100rpx;
				height: 20rpx;
				position: absolute;
				border-radius: 10rpx;
				top: 8rpx;
				left: 50%;
				background-color: #cacaca;
				transform: translateX(-50%);
			}

			.time-date-week {
				background-color: #f7f7f7;
				position: absolute;
				border-radius: 6rpx;
				top: 8rpx;
				left: 50%;
				transform: translateX(-50%);
				z-index: 2;
				padding: 0 16rpx;
				color: #424242;
				font-size: 26rpx;
				border: 1px solid #e1e1e1;
			}

			.time-date-month {
				z-index: 2;
				text-align: center;
				margin-bottom: 10rpx;
			}
		}

		.is-active {
			.border-box {
				border: 1px solid #5fbaf9;
			}

			.time-date-temp,
			.time-date-week {
				background-color: #52b5f9;
				border: 1px solid #52b5f9;
				color: #fff;
			}
		}
	}
}

/* 时间网格样式 */
.time-grid {
	width: 100%;
	border: 1px solid #eee;
	border-radius: 10rpx;
	background-color: #fff;
	margin-bottom: 20rpx;

	.time-period {
		padding: 20rpx;
		border-bottom: 1px solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.period-title {
			display: flex;
			align-items: center;
			color: #262626;
			font-size: 28rpx;
			margin-bottom: 20rpx;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}

		.time-row {
			display: flex;
			flex-wrap: wrap;

			.time-slot {
				width: 20%;
				height: 70rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				position: relative;
				cursor: pointer;

				.time-text {
					font-size: 26rpx;
					color: #333;
					margin-bottom: 6rpx;
				}

				.availability-text {
					font-size: 20rpx;
					color: #999;
					margin-top: 4rpx;
				}

				&.is-disabled {
					background-color: #f5f5f5;
					cursor: not-allowed;

					.time-text {
						color: #999;
					}
				}

				&.is-active {
					background-color: #52b5f9;
					border-color: #1890ff;

					.time-text {
						color: #fff;
					}

					.availability-text {
						color: #fff;
					}
				}

				&.is-start {
					border-top-right-radius: 0;
					border-bottom-right-radius: 0;
				}

				&.is-middle {
					border-radius: 0;
					border-left: none;
					border-right: none;
					background-color: #52b5f9;

					&:after {
						content: '';
						position: absolute;
						left: 0;
						right: 0;
						top: -1px;
						bottom: -1px;
						background-color: #52b5f9;
						z-index: -1;
					}
				}

				&.is-end {
					border-top-left-radius: 0;
					border-bottom-left-radius: 0;
				}
			}
		}
	}
}

/* 提示信息样式 */
.msg {
	font-size: 26rpx;
	margin: 20rpx 0;
	color: #666;
	text-align: center;
}

/* 操作按钮样式 */
.action-buttons {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
	gap: 20rpx;

	.clear-btn {
		flex: 1;
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 100rpx;
		line-height: 80rpx;
		font-size: 30rpx;
		color: #666;
		border: none;

		&:active {
			opacity: 0.8;
		}
	}

	.confirm-btn {
		flex: 2;
		height: 80rpx;
		background-color: #52b5f9;
		border-radius: 100rpx;
		line-height: 80rpx;
		font-size: 30rpx;
		border: none;

		&:active {
			opacity: 0.9;
		}

		&:disabled {
			background-color: #ccc;
			color: #999;
		}
	}
}
</style>
