<template>
	<view class="store-environment">

		<!-- 门店环境图片展示 -->
		<view class="environment-container">
			<!-- 主要展示图片 -->
			<view class="main-image-section">
				<view class="image-item large" @click="previewImage(0)">
					<image :src="environmentImages[0]?.url" mode="aspectFill" :lazy-load="true">
					</image>
					<view class="image-label">{{ environmentImages[0]?.label }}</view>
				</view>
			</view>

			<!-- 小图片网格 -->
			<view class="grid-images-section">
				<view v-for="(item, index) in gridImages" :key="index" class="image-item small"
					@click="previewImage(index + 1)">
					<image :src="item.url" mode="aspectFill" :lazy-load="true">
					</image>
					<view class="image-label">{{ item.label }}</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getStore } from '@/api/store.js'

// 门店环境图片数据
const environmentImages = ref([
	{
		url: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE1.jpg',
		label: '门店外观'
	},
	{
		url: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE2.jpg',
		label: '咨询室'
	},
	{
		url: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE1.jpg',
		label: '接待区'
	},
	{
		url: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE2.jpg',
		label: '休息区'
	},
	{
		url: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E5%B1%95%E7%A4%BA%E5%9B%BE1.jpg',
		label: '走廊'
	}
])

// 网格显示的图片（除第一张外的其他图片）
const gridImages = ref(environmentImages.value.slice(1))

// 门店信息
const storeInfo = ref({
	name: '',
	branchName: '',
	phone: '',
	address: '',
	longitude: 0,
	latitude: 0,
	mapName: '',
	mapAddress: ''
})

// 页面加载
onLoad(async (options) => {
	await getStoreInfo()
})

// 获取门店信息
const getStoreInfo = async () => {
	try {
		const storeId = 1; // 默认门店ID
		const res = await getStore(storeId);
		if (res.code === 200) {
			storeInfo.value = {
				...res.data.store,
				contacts: res.data.contacts,
				businessHours: res.data.businessHours,
				businessDays: res.data.businessDays
			};
		} else {
			// 使用默认门店信息
			storeInfo.value = {
				name: '熙桓心理',
				branchName: '丽泽天街店',
				phone: '134508664532',
				address: '北京丰台区平安幸福中心A座3617（地铁14号线C口）',
				longitude: 116.322074,
				latitude: 39.866596,
				mapName: '熙桓心理丽泽天街店',
				mapAddress: '北京市丰台区丽泽路平安幸福A座36层3617（地铁14号线东管头站C口）'
			};
		}
	} catch (error) {
		console.error('获取门店信息失败:', error);
		// 使用默认门店信息
		storeInfo.value = {
			name: '熙桓心理',
			branchName: '丽泽天街店',
			phone: '134508664532',
			address: '北京丰台区平安幸福中心A座3617（地铁14号线C口）',
			longitude: 116.322074,
			latitude: 39.866596,
			mapName: '熙桓心理丽泽天街店',
			mapAddress: '北京市丰台区丽泽路平安幸福A座36层3617（地铁14号线东管头站C口）'
		};
	}
}

// 预览图片
const previewImage = (index) => {
	const urls = environmentImages.value.map(item => item.url)
	uni.previewImage({
		current: index,
		urls: urls
	})
}

// 拨打电话
const callStore = () => {
	if (!storeInfo.value.phone) return;
	uni.makePhoneCall({
		phoneNumber: storeInfo.value.phone
	})
}

// 导航到门店
const navigateToStore = () => {
	if (!storeInfo.value) return;
	uni.openLocation({
		longitude: Number(storeInfo.value.longitude),
		latitude: Number(storeInfo.value.latitude),
		name: storeInfo.value.mapName || `${storeInfo.value.name}${storeInfo.value.branchName}`,
		address: storeInfo.value.mapAddress || storeInfo.value.address,
		fail: (err) => {
			console.log("导航失败:", err);
			uni.showToast({
				title: '导航失败',
				icon: 'none'
			});
		},
	})
}
</script>

<style scoped lang="scss">
.store-environment {
	background-color: #f8f8fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #f0f0f0;

	.nav-left,
	.nav-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.nav-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

.environment-container {
	padding: 24rpx;
}

.main-image-section {
	margin-bottom: 24rpx;

	.image-item.large {
		width: 100%;
		height: 400rpx;
		border-radius: 16rpx;
		overflow: hidden;
		position: relative;

		image {
			width: 100%;
			height: 100%;
		}

		.image-label {
			position: absolute;
			bottom: 16rpx;
			left: 16rpx;
			background-color: rgba(0, 0, 0, 0.6);
			color: #fff;
			padding: 8rpx 16rpx;
			border-radius: 20rpx;
			font-size: 24rpx;
		}
	}
}

.grid-images-section {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;

	.image-item.small {
		height: 240rpx;
		border-radius: 12rpx;
		overflow: hidden;
		position: relative;

		image {
			width: 100%;
			height: 100%;
		}

		.image-label {
			position: absolute;
			bottom: 12rpx;
			left: 12rpx;
			background-color: rgba(0, 0, 0, 0.6);
			color: #fff;
			padding: 6rpx 12rpx;
			border-radius: 16rpx;
			font-size: 22rpx;
		}
	}
}

.store-info {
	background-color: #fff;
	margin: 24rpx;
	padding: 32rpx;
	border-radius: 16rpx;

	.store-name {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
	}

	.store-address,
	.store-phone {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
		font-size: 28rpx;
		color: #666;

		text {
			margin-left: 8rpx;
		}
	}

	.store-phone {
		margin-bottom: 0;
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 24rpx;

	.action-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		font-weight: 600;

		&.secondary {
			background-color: #f0f8ff;
			color: #007AFF;
			border: 2rpx solid #007AFF;

			text {
				margin-left: 8rpx;
			}
		}

		&.primary {
			background-color: #007AFF;
			color: #fff;

			text {
				margin-left: 8rpx;
			}
		}
	}
}
</style>
