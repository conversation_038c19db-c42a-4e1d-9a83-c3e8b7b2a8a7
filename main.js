import App from './App'

// ================== Vue2 配置 ==================
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import {
	createPinia
} from 'pinia'
import {
	createUnistorage
} from '@/uni_modules/pinia-plugin-unistorage'


Vue.config.productionTip = false
App.mpType = 'app'

const pinia = createPinia()
pinia.use(createUnistorage())

const app = new Vue({
	...App,
	pinia // 正确挂载 Pinia
})
app.$mount()
// #endif

// ================== Vue3 配置 ==================
// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import {
	createPinia
} from 'pinia'
import {
	createUnistorage
} from '@/uni_modules/pinia-plugin-unistorage'

export function createApp() {
	const app = createSSRApp(App)

	// 初始化 Pinia
	const pinia = createPinia()
	pinia.use(createUnistorage())

	// 挂载到应用
	app.use(pinia)

	return {
		app,
		pinia // 返回 Pinia 实例
	}
}
// #endif