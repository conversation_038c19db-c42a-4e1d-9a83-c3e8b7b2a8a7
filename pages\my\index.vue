<template>
	<view class="content">
		<view class="user-box">
			<!-- <view class="edit-user-info">
				编辑资料
				<uni-icons type="right" size="16"></uni-icons>
			</view> -->
			<view class="user-info" @click="handleToLogin">
				<view class="avatar">
					<image :src="avatar" mode=""></image>
				</view>
				<view class="info">
					<view class="nickname">
						<text>{{ userStore.profile.counselorName || nickName }}</text>
					</view>
					<view class="user-id" v-if="!userStore.token">点击登录,使用更多功能</view>
					<!-- <view class="is-vip">非会员</view> -->
				</view>
				<!-- <view class="vip-code">
					<view class="code">
						<image src="../../static/images/img/会员码.png" mode=""></image>
					</view>
					<text>会员码</text>
				</view> -->
			</view>
			<view class="balance-info">
				<!-- <view class="coupon">
					<view class="count">0</view>
					<view class="text">优惠券</view>
				</view>
				<view class="balance">
					<view class="count">
						<text class="icon">￥</text>
						<text>0</text>
					</view>
					<view class="text">可用余额</view>
				</view>
				<view class="integral">
					<view class="count">0</view>
					<view class="text">积分</view>
				</view> -->

				
			</view>
		</view>
		<view class="order-box">
			<view>
				<image></image>
				<text>全部订单</text>
			</view>
			<view>
				<image></image>
				<text>待使用</text>
			</view>
			<view>
				<image></image>
				<text>进行中</text>
			</view>
			<view>
				<image></image>
				<text>已完成</text>
			</view>
			<view>
				<image></image>
				<text>退款中</text>
			</view>
		</view>
		<view class="option-box">
			<view v-for="item in navigateToList" :key="item.title" @click="handleToUserItem(item)" class="option-item">
					<view class="option-content">
						<image mode="scaleToFill" :src="item.icon"></image>
						<text>{{ item.title }}</text>
						<!-- 消息角标 -->
						<view v-if="item.title === '我的消息' && unreadCount > 0" class="message-badge">
							{{ unreadCount > 99 ? '99+' : unreadCount }}
						</view>
					</view>
			</view>
		</view>
		<cc-myTabbar :tabBarShow="4"></cc-myTabbar>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getWXLogin, getInfo } from "../../api/my.js";
import { setToken, getToken, removeToken } from "../../utils/auth.js";
import { useUserStore } from "@/stores/user";
import { useChatStore } from "@/stores/chat";
import { onReady, onShow } from "@dcloudio/uni-app";
const userStore = useUserStore();
const chatStore = useChatStore();

// 响应式数据
const nickName = ref("欢迎来到熙桓心理");
const avatar = ref("");
const unreadCount = ref(0);

// 生命周期
onMounted(() => {
	if (getToken()) {
		avatar.value = userStore.userAvatar || avatar.value;
	}

	// 监听未读消息数量变化
	uni.$on('unread-count-changed', (count) => {
		console.log('我的页面收到未读消息数量变化:', count);
		unreadCount.value = count;
	});

	// 初始化未读消息数量
	loadUnreadCount();
});

onShow(() => {
	// 每次显示页面时刷新未读消息数量
	loadUnreadCount();
});

onReady(() => {
	uni.hideTabBar();
});

// 加载未读消息数量
const loadUnreadCount = async () => {
	if (userStore.userId) {
		await chatStore.getUnreadCount();
		unreadCount.value = chatStore.unreadTotal;
		console.log('我的页面加载未读消息数量:', unreadCount.value);
	}
};

const navigateToList = ref([
	{
		title: "我的测评",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA.png",
		url: "/pages/my/my-evaluation/index"
	},
	{
		title: "我的冥想",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/冥想.png",
		url: "/pages/meditation/my-meditations/index"
	},
	{
		title: "我的课程",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/课程.png",
		url: "/pages/course/my-courses/index"
	},
	{
		title: "我的消息",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/tab-icon/%E6%B6%88%E6%81%AF.png",
		url: "/pages/my/my-message/index"
	},
	{
		title: "我的收藏",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA.png",
		url: "/pages/my/my-star/index"
	},
	{
		title: "我的客服",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%AE%A2%E6%9C%8D.png",
		url: "/pages/my/my-contact/index"
	},
	{
		title: "关于我们",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%A5%BC%E6%88%BF.png",
		url: "/pages/my/my-introduction/index"
	},
	{
		title: "加入我们",
		icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%A5%BC%E6%88%BF.png",
		url: "/pages/my/my-introduction/index"
	},
	// {
	// 	title: "我的咨询",
	// 	icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/咨询.png",
	// 	url: "/pages/consultation/my-consultations/index"
	// },
	// {
	// 	title: "我的中心",
	// 	icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E4%B8%AA%E4%BA%BA.png",
	// 	url: "/pages/my/my-detail/index"
	// },
	// {
	// 	title: "帮助中心",
	// 	icon: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E5%B8%AE%E5%8A%A9.png",
	// 	url: "/pages/my/my-help/index"
	// },
]);

const handleToUserItem = (item) => {
	// 如果点击的是"我的消息"，清除未读计数
	if (item.title === '我的消息') {
		unreadCount.value = 0;
		// 同时清除store中的未读计数
		chatStore.unreadTotal = 0;
		console.log('点击我的消息，清除未读计数');
	}
	uni.navigateTo({ url: item.url });
};

</script>

<style scoped lang="scss">
.content {
	height: 100vh;
	background-color: #f5f5f6;
	.user-box {
		width: 100%;
		height: 400rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		background-image: url("https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E6%88%91%E7%9A%84%E8%83%8C%E6%99%AF.jpeg");
		background-size: 100% 400rpx;
		.edit-user-info {
			margin-bottom: 20rpx;
			margin-right: 60rpx;
			text-align: end;
		}
		.user-info {
			width: 100%;
			height: 150rpx;
			display: flex;
			align-items: center;
			.avatar {
				width: 150rpx;
				height: 150rpx;
				image {
					margin: 20rpx;
					width: 110rpx;
					height: 110rpx;
					border-radius: 10px;
					margin-left: 26rpx;
				}
				button {
					width: 150rpx;
					height: 150rpx;
					border-radius: 50%;
					padding: 0;
					opacity: 1;
					background-color: #f8f8f800 !important;
				}
			}
			.info {
				margin-left: 10rpx;
				width: calc(100% - 410rpx);
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;
				.user-id {
					margin-top: 6rpx;
					font-size: 26rpx;
				}
				.is-vip {
					width: 100rpx;
					height: 38rpx;
					line-height: 38rpx;
					font-size: 22rpx;
					background-color: #999999;
					text-align: center;
					color: #f9f9f9;
					border-radius: 20rpx;
				}
			}
			.login-btn {
				width: 200rpx;
				button {
					height: 100rpx;
					color: 28rpx;
				}
			}
			.vip-code {
				width: 150rpx;
				height: 150rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				.code {
					width: 60rpx;
					height: 60rpx;
					margin-bottom: 6rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}
			.login-btn {
				font-size: 26rpx;
				white-space: nowrap;
			}
		}
		.balance-info {
			width: 100%;
			height: 120rpx;
			margin-bottom: 30rpx;
			display: flex;
			justify-content: center;
			.coupon,
			.balance,
			.integral {
				width: 120rpx;
				height: 120rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-evenly;
				.count {
					font-size: 40rpx;
					.icon {
						font-size: 24rpx;
					}
				}
				.text {
					font-size: 22rpx;
				}
			}
		}
	}
	.option-box {
		width: calc(100% - 80rpx);
		background-color: #fff;
		border-radius: 20rpx;
		margin: 20rpx 40rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;

		.option-item {
			width: 25%;
			padding: 10rpx 0;

			.option-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;

				image {
					width: 80rpx;
					height: 80rpx;
				}

				text {
					font-size: 24rpx;
				}

				.message-badge {
					position: absolute;
					top: -5rpx;
					right: 15rpx;
					background-color: #ff4757;
					color: white;
					border-radius: 20rpx;
					min-width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					font-weight: bold;
					padding: 0 8rpx;
					box-sizing: border-box;
					border: 2rpx solid #fff;
				}
			}
		}
	}
	.order-box {
		width: calc(100% - 80rpx);
		height: 200rpx;
		background-color: #fff;
		border-radius: 20rpx;
		margin: 20rpx 40rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		view {
			width: 20%;
			height: 120rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			image {
				width: 80rpx;
				height: 80rpx;
			}
			text {
				font-size: 24rpx;
			}
		}
	}
}
</style>
