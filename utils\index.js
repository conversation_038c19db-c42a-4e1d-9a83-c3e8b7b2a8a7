import {
	getDict
} from '../api/index.js'

/**
 * 获取字典数据
 */
export async function useDict(...args) {
	let res = {}
	try {
		// 使用 Promise.all 来并行处理所有字典请求
		const promises = args.map(async (dictType) => {
			try {
				const resp = await getDict(dictType);
				return {
					dictType,
					data: resp.data.map(p => ({
						label: p.dictLabel,
						text: p.dictLabel,
						value: p.dictValue,
						dictValue: p.dictValue, // 保持原有的 dictValue 字段
						dictLabel: p.dictLabel, // 保持原有的 dictLabel 字段
						elTagType: p.listClass,
						elTagClass: p.cssClass
					}))
				};
			} catch (error) {
				console.error(`获取字典 ${dictType} 失败:`, error);
				return {
					dictType,
					data: []
				};
			}
		});

		const results = await Promise.all(promises);

		// 将结果组装成对象
		results.forEach(({ dictType, data }) => {
			res[dictType] = data;
		});

		return res;
	} catch (error) {
		console.error('useDict 执行失败:', error);
		// 返回空对象，避免组件报错
		args.forEach(dictType => {
			res[dictType] = [];
		});
		return res;
	}
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
	let config = {
		id: id || 'id',
		parentId: parentId || 'parentId',
		childrenList: children || 'children'
	};

	var childrenListMap = {};
	var nodeIds = {};
	var tree = [];

	for (let d of data) {
		let parentId = d[config.parentId];
		if (childrenListMap[parentId] == null) {
			childrenListMap[parentId] = [];
		}
		nodeIds[d[config.id]] = d;
		childrenListMap[parentId].push(d);
	}

	for (let d of data) {
		let parentId = d[config.parentId];
		if (nodeIds[parentId] == null) {
			tree.push(d);
		}
	}

	for (let t of tree) {
		adaptToChildrenList(t);
	}

	function adaptToChildrenList(o) {
		if (childrenListMap[o[config.id]] !== null) {
			o[config.childrenList] = childrenListMap[o[config.id]];
		}
		if (o[config.childrenList]) {
			for (let c of o[config.childrenList]) {
				adaptToChildrenList(c);
			}
		}
	}
	return tree;
}
/**
 * iOS 兼容的日期解析函数
 * @param {string|number|Date} input - 日期输入
 * @returns {Date} 解析后的日期对象
 */
export function parseCompatibleDate(input) {
	if (!input) return new Date()

	if (input instanceof Date) return input

	if (typeof input === 'number') return new Date(input)

	if (typeof input === 'string') {
		// 将 "yyyy-MM-dd HH:mm:ss" 格式转换为 iOS 兼容的格式
		const processedString = input.replace(/(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})/, '$1T$2')
		return new Date(processedString)
	}

	return new Date(input)
}

/**
 * 安全的时间格式化工具（全平台兼容）
 * @param {string|number|Date} input - 支持多种时间格式
 * @param {string} [type='default'] - 输出格式类型
 * @returns {string} 格式化后的时间
 */
export function formatTime(input, type = 'default') {
	// 预处理函数（关键修复）
	const preprocess = (str) => {
		// 统一分隔符为横杠，并转换空格分隔的时间格式
		let processed = str.replace(/\//g, '-');

		// 正则匹配并转换格式：yyyy-MM-dd HH:mm 或 yyyy-MM-dd HH:mm:ss
		const match = processed.match(/^(\d{4}-\d{2}-\d{2})[ T](\d{2}:\d{2})(:\d{2}(\.\d{1,3})?)?/);
		if (match) {
			const timePart = match[3] ? match[2] + match[3] : match[2] + ':00';
			return `${match[1]}T${timePart}`;
		}
		return processed;
	}

	let date;
	try {
		if (typeof input === 'string') {
			const processedString = preprocess(input);
			date = new Date(processedString);

			// 备用解析方案：处理特殊格式（如部分Android返回的时间格式）
			if (isNaN(date.getTime())) {
				const [datePart, timePart] = processedString.split(/[ T]/);
				if (datePart && timePart) {
					const [year, month, day] = datePart.split('-');
					const [hours, minutes] = timePart.split(':');
					// 注意：months从0开始计数（0=1月）
					date = new Date(year, month - 1, day, hours, minutes);
				} else {
					throw new Error('Unsupported format after preprocessing');
				}
			}
		} else {
			date = new Date(input);
		}

		// 最终验证
		if (isNaN(date.getTime())) throw new Error('Invalid Date');
	} catch (e) {
		console.error('时间解析失败:', e.message, '原始输入:', input);
		return '--:--';
	}

	// 格式化工具函数
	const pad = n => n.toString().padStart(2, '0');
	const year = date.getFullYear();
	const month = pad(date.getMonth() + 1);
	const day = pad(date.getDate());
	const hours = pad(date.getHours());
	const minutes = pad(date.getMinutes());

	// 输出格式选择
	return {
		detail: `${year}-${month}-${day} ${hours}:${minutes}`, // 详细格式
		date: `${year}-${month}-${day}`, // 仅日期
		time: `${hours}:${minutes}`, // 仅时间
		chat: `${month}-${day} ${hours}:${minutes}`, // 聊天列表
		iso: date.toISOString(), // 标准ISO格式
		timestamp: date.getTime(), // 时间戳
	}[type] || `${year}-${month}-${day} ${hours}:${minutes}`; // 默认格式
}

function padZero(n) {
	return n < 10 ? `0${n}` : n
}