/**
 * 高级音频播放器
 * 支持真实的倍速播放功能
 */

class AdvancedAudioPlayer {
  constructor() {
    this.audioContext = null
    this.backgroundAudioManager = null
    this.isPlaying = false
    this.currentTime = 0
    this.duration = 0
    this.src = ''
    this.playbackRate = 1.0
    this.volume = 1.0
    this.useBackgroundAudio = false
    
    // 事件回调
    this.callbacks = {
      onPlay: null,
      onPause: null,
      onTimeUpdate: null,
      onEnded: null,
      onError: null,
      onCanplay: null,
      onPlaybackRateChange: null
    }
    
    // 定时器用于模拟时间更新
    this.timeUpdateTimer = null
  }

  /**
   * 初始化播放器
   */
  init(src, options = {}) {
    this.destroy()
    
    this.src = src
    this.playbackRate = options.playbackRate || 1.0
    this.useBackgroundAudio = options.useBackgroundAudio || false
    
    console.log('初始化音频播放器:', {
      src,
      useBackgroundAudio: this.useBackgroundAudio,
      playbackRate: this.playbackRate
    })
    
    if (this.useBackgroundAudio) {
      this.initBackgroundAudio(src, options)
    } else {
      this.initInnerAudio(src, options)
    }
    
    return this
  }

  /**
   * 初始化内部音频
   */
  initInnerAudio(src, options) {
    this.audioContext = uni.createInnerAudioContext()
    this.audioContext.src = src
    this.audioContext.autoplay = options.autoplay || false
    this.audioContext.loop = options.loop || false
    this.audioContext.volume = options.volume || 1.0
    
    this.bindInnerAudioEvents()
  }

  /**
   * 初始化背景音频
   */
  initBackgroundAudio(src, options) {
    this.backgroundAudioManager = uni.getBackgroundAudioManager()
    
    // 设置音频信息
    this.backgroundAudioManager.title = options.title || '音频播放'
    this.backgroundAudioManager.epname = options.epname || ''
    this.backgroundAudioManager.singer = options.singer || ''
    this.backgroundAudioManager.coverImgUrl = options.coverImgUrl || ''
    this.backgroundAudioManager.src = src
    
    this.bindBackgroundAudioEvents()
    
    // 尝试设置倍速
    this.trySetBackgroundAudioRate()
  }

  /**
   * 尝试设置背景音频倍速
   */
  trySetBackgroundAudioRate() {
    if (!this.backgroundAudioManager) return
    
    try {
      // 检查是否支持倍速
      if ('playbackRate' in this.backgroundAudioManager) {
        console.log('背景音频支持倍速，设置为:', this.playbackRate)
        this.backgroundAudioManager.playbackRate = this.playbackRate
        
        // 验证设置
        setTimeout(() => {
          const actualRate = this.backgroundAudioManager.playbackRate
          console.log('背景音频实际倍速:', actualRate)
          if (Math.abs(actualRate - this.playbackRate) < 0.01) {
            console.log('倍速设置成功')
            this.callbacks.onPlaybackRateChange && this.callbacks.onPlaybackRateChange(actualRate)
          } else {
            console.warn('倍速设置失败，期望:', this.playbackRate, '实际:', actualRate)
          }
        }, 100)
      } else {
        console.warn('背景音频不支持 playbackRate 属性')
      }
    } catch (error) {
      console.error('设置背景音频倍速失败:', error)
    }
  }

  /**
   * 绑定内部音频事件
   */
  bindInnerAudioEvents() {
    if (!this.audioContext) return

    this.audioContext.onPlay(() => {
      this.isPlaying = true
      this.startTimeUpdateTimer()
      this.callbacks.onPlay && this.callbacks.onPlay()
    })

    this.audioContext.onPause(() => {
      this.isPlaying = false
      this.stopTimeUpdateTimer()
      this.callbacks.onPause && this.callbacks.onPause()
    })

    this.audioContext.onTimeUpdate(() => {
      this.currentTime = this.audioContext.currentTime
      this.duration = this.audioContext.duration || 0
      this.callbacks.onTimeUpdate && this.callbacks.onTimeUpdate({
        currentTime: this.currentTime,
        duration: this.duration
      })
    })

    this.audioContext.onEnded(() => {
      this.isPlaying = false
      this.stopTimeUpdateTimer()
      this.callbacks.onEnded && this.callbacks.onEnded()
    })

    this.audioContext.onError((error) => {
      console.error('内部音频错误:', error)
      this.callbacks.onError && this.callbacks.onError(error)
    })

    this.audioContext.onCanplay(() => {
      this.duration = this.audioContext.duration || 0
      this.callbacks.onCanplay && this.callbacks.onCanplay()
    })
  }

  /**
   * 绑定背景音频事件
   */
  bindBackgroundAudioEvents() {
    if (!this.backgroundAudioManager) return

    this.backgroundAudioManager.onPlay(() => {
      this.isPlaying = true
      this.startTimeUpdateTimer()
      this.callbacks.onPlay && this.callbacks.onPlay()
    })

    this.backgroundAudioManager.onPause(() => {
      this.isPlaying = false
      this.stopTimeUpdateTimer()
      this.callbacks.onPause && this.callbacks.onPause()
    })

    this.backgroundAudioManager.onTimeUpdate(() => {
      this.currentTime = this.backgroundAudioManager.currentTime
      this.duration = this.backgroundAudioManager.duration || 0
      this.callbacks.onTimeUpdate && this.callbacks.onTimeUpdate({
        currentTime: this.currentTime,
        duration: this.duration
      })
    })

    this.backgroundAudioManager.onEnded(() => {
      this.isPlaying = false
      this.stopTimeUpdateTimer()
      this.callbacks.onEnded && this.callbacks.onEnded()
    })

    this.backgroundAudioManager.onError((error) => {
      console.error('背景音频错误:', error)
      this.callbacks.onError && this.callbacks.onError(error)
    })
  }

  /**
   * 启动时间更新定时器
   */
  startTimeUpdateTimer() {
    this.stopTimeUpdateTimer()
    this.timeUpdateTimer = setInterval(() => {
      if (this.isPlaying) {
        if (this.useBackgroundAudio && this.backgroundAudioManager) {
          this.currentTime = this.backgroundAudioManager.currentTime || 0
          this.duration = this.backgroundAudioManager.duration || 0
        } else if (this.audioContext) {
          this.currentTime = this.audioContext.currentTime || 0
          this.duration = this.audioContext.duration || 0
        }
        
        this.callbacks.onTimeUpdate && this.callbacks.onTimeUpdate({
          currentTime: this.currentTime,
          duration: this.duration
        })
      }
    }, 500) // 每500ms更新一次
  }

  /**
   * 停止时间更新定时器
   */
  stopTimeUpdateTimer() {
    if (this.timeUpdateTimer) {
      clearInterval(this.timeUpdateTimer)
      this.timeUpdateTimer = null
    }
  }

  /**
   * 播放
   */
  play() {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.play()
    } else if (this.audioContext) {
      this.audioContext.play()
    }
    return this
  }

  /**
   * 暂停
   */
  pause() {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.pause()
    } else if (this.audioContext) {
      this.audioContext.pause()
    }
    return this
  }

  /**
   * 停止
   */
  stop() {
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.backgroundAudioManager.stop()
    } else if (this.audioContext) {
      this.audioContext.stop()
    }
    return this
  }

  /**
   * 跳转
   */
  seek(time) {
    console.log('跳转到时间:', time)

    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      try {
        // 背景音频需要重新设置src和startTime
        const currentSrc = this.backgroundAudioManager.src
        this.backgroundAudioManager.src = ''
        this.backgroundAudioManager.startTime = time
        this.backgroundAudioManager.src = currentSrc
        console.log('背景音频跳转到:', time)
      } catch (error) {
        console.error('背景音频跳转失败:', error)
      }
    } else if (this.audioContext) {
      try {
        this.audioContext.seek(time)
        console.log('内部音频跳转到:', time)
      } catch (error) {
        console.error('内部音频跳转失败:', error)
      }
    }

    // 更新当前时间
    this.currentTime = time

    return this
  }

  /**
   * 设置倍速
   */
  setPlaybackRate(rate) {
    const validRate = Math.max(0.5, Math.min(2.0, rate))
    this.playbackRate = validRate
    
    console.log('设置倍速:', validRate, '模式:', this.useBackgroundAudio ? '背景音频' : '内部音频')
    
    if (this.useBackgroundAudio && this.backgroundAudioManager) {
      this.trySetBackgroundAudioRate()
    } else {
      console.warn('内部音频不支持倍速，当前设置仅为记录')
      uni.showToast({
        title: `倍速设置为 ${validRate}x (需背景音频模式)`,
        icon: 'none'
      })
    }
    
    return this
  }

  /**
   * 获取倍速
   */
  getPlaybackRate() {
    return this.playbackRate
  }

  /**
   * 切换倍速
   */
  togglePlaybackRate(speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]) {
    const currentIndex = speeds.indexOf(this.playbackRate)
    const nextIndex = (currentIndex + 1) % speeds.length
    const nextRate = speeds[nextIndex]
    
    this.setPlaybackRate(nextRate)
    return nextRate
  }

  /**
   * 获取状态
   */
  getState() {
    return {
      isPlaying: this.isPlaying,
      currentTime: this.currentTime,
      duration: this.duration,
      src: this.src,
      playbackRate: this.playbackRate,
      useBackgroundAudio: this.useBackgroundAudio
    }
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (this.callbacks.hasOwnProperty(`on${event.charAt(0).toUpperCase() + event.slice(1)}`)) {
      this.callbacks[`on${event.charAt(0).toUpperCase() + event.slice(1)}`] = callback
    }
    return this
  }

  /**
   * 销毁
   */
  destroy() {
    this.stopTimeUpdateTimer()
    
    if (this.audioContext) {
      this.audioContext.destroy()
      this.audioContext = null
    }
    
    if (this.backgroundAudioManager) {
      try {
        this.backgroundAudioManager.stop()
      } catch (e) {
        // 忽略停止错误
      }
      this.backgroundAudioManager = null
    }
    
    // 重置状态
    this.isPlaying = false
    this.currentTime = 0
    this.duration = 0
    this.src = ''
    this.playbackRate = 1.0
    this.useBackgroundAudio = false
    
    // 清除回调
    Object.keys(this.callbacks).forEach(key => {
      this.callbacks[key] = null
    })
    
    return this
  }
}

/**
 * 创建高级音频播放器
 */
export function createAdvancedAudioPlayer(src, options = {}) {
  return new AdvancedAudioPlayer().init(src, options)
}

export default AdvancedAudioPlayer
