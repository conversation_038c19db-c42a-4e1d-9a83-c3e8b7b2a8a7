<template>
  <view class="consultation-order">
    <!-- 咨询师信息 -->
    <view class="consultant-info">
      <image :src="consultantInfo.avatar || defaultAvatar" class="consultant-avatar"></image>
      <view class="consultant-details">
        <view class="consultant-name">{{ consultantInfo.name }}</view>
        <view class="consultant-title">{{ consultantInfo.title }}</view>
        <view class="consultant-rating">
          <uni-rate :value="consultantInfo.rating || 0" readonly size="16"></uni-rate>
          <text class="rating-text">{{ consultantInfo.rating || 0 }}分</text>
        </view>
      </view>
      <view class="consultant-price">
        <text class="price-symbol">¥</text>
        <text class="price-value">{{ consultantInfo.price }}</text>
        <text class="price-unit">/小时</text>
      </view>
    </view>

    <!-- 预约信息 -->
    <view class="appointment-info">
      <view class="section-title">预约信息</view>

      <!-- 咨询类型 -->
      <view class="form-item">
        <view class="label">咨询类型</view>
        <picker :value="selectedTypeIndex" :range="consultationTypes" range-key="name" @change="onTypeChange">
          <view class="picker-value">
            {{ consultationTypes[selectedTypeIndex]?.name || '请选择咨询类型' }}
          </view>
        </picker>
      </view>

      <!-- 预约时间 -->
      <view class="form-item">
        <view class="label">预约时间</view>
        <view class="time-selector">
          <picker mode="date" :value="selectedDate" :start="minDate" :end="maxDate" @change="onDateChange">
            <view class="picker-value">{{ selectedDate || '选择日期' }}</view>
          </picker>
          <picker :value="selectedTimeIndex" :range="availableTimes" @change="onTimeChange">
            <view class="picker-value">{{ availableTimes[selectedTimeIndex] || '选择时间' }}</view>
          </picker>
        </view>
      </view>

      <!-- 咨询时长 -->
      <view class="form-item">
        <view class="label">咨询时长</view>
        <picker :value="selectedDurationIndex" :range="durations" range-key="label" @change="onDurationChange">
          <view class="picker-value">
            {{ durations[selectedDurationIndex]?.label || '请选择时长' }}
          </view>
        </picker>
      </view>

      <!-- 咨询问题描述 -->
      <view class="form-item">
        <view class="label">问题描述</view>
        <textarea v-model="problemDescription" class="problem-textarea" placeholder="请简要描述您的咨询问题，以便咨询师更好地为您服务..."
          :maxlength="500" show-confirm-bar></textarea>
        <view class="char-count">{{ problemDescription.length }}/500</view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="cost-details">
      <view class="section-title">费用明细</view>
      <view class="cost-item">
        <text>咨询费用</text>
        <text>¥{{ consultationFee }}</text>
      </view>
      <view class="cost-item">
        <text>服务费</text>
        <text>¥{{ serviceFee }}</text>
      </view>
      <view class="cost-total">
        <text>总计</text>
        <text class="total-amount">¥{{ totalAmount }}</text>
      </view>
    </view>

    <!-- 预约须知 -->
    <view class="notice-section">
      <view class="section-title">预约须知</view>
      <view class="notice-content">
        <text>1. 请提前15分钟进入咨询室准备</text>
        <text>2. 咨询过程中请保持网络稳定</text>
        <text>3. 如需取消请提前24小时联系客服</text>
        <text>4. 咨询内容严格保密，请放心交流</text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="total-info">
        <text class="total-label">总计：</text>
        <text class="total-price">¥{{ totalAmount }}</text>
      </view>
      <button class="submit-btn" :disabled="!canSubmit" @click="submitOrder">
        确认预约
      </button>
    </view>

    <!-- 支付弹框 -->
    <PaymentModal ref="paymentModal" :order-info="orderInfo" @close="onPaymentClose" @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { createConsultationOrder, checkTimeAvailable } from '@/api/consultation'
import { useUserStore } from '@/stores/user'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'

// 响应式数据
const consultantInfo = ref({})
const consultantId = ref(null)
const paymentModal = ref(null)
const orderInfo = ref({})

// 表单数据
const selectedTypeIndex = ref(0)
const selectedDate = ref('')
const selectedTimeIndex = ref(0)
const selectedDurationIndex = ref(0)
const problemDescription = ref('')

// 选项数据
const consultationTypes = ref([
  { id: 1, name: '心理咨询', code: 'psychological' },
  { id: 2, name: '情感咨询', code: 'emotional' },
  { id: 3, name: '职场咨询', code: 'career' },
  { id: 4, name: '家庭咨询', code: 'family' }
])

const durations = ref([
  { value: 60, label: '60分钟', price: 1.0 },
  { value: 90, label: '90分钟', price: 1.5 },
  { value: 120, label: '120分钟', price: 2.0 }
])

const availableTimes = ref([
  '09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '19:00', '20:00'
])

// 默认头像
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 计算属性
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const maxDate = computed(() => {
  const maxDate = new Date()
  maxDate.setDate(maxDate.getDate() + 30)
  return maxDate.toISOString().split('T')[0]
})

const consultationFee = computed(() => {
  if (!consultantInfo.value.price || !durations.value[selectedDurationIndex.value]) {
    return 0
  }
  return Math.round(consultantInfo.value.price * durations.value[selectedDurationIndex.value].price)
})

const serviceFee = computed(() => {
  return Math.round(consultationFee.value * 0.1) // 10% 服务费
})

const totalAmount = computed(() => {
  return consultationFee.value + serviceFee.value
})

const canSubmit = computed(() => {
  return selectedDate.value &&
    selectedTimeIndex.value >= 0 &&
    selectedDurationIndex.value >= 0 &&
    problemDescription.value.trim().length >= 10
})

// 方法
const onTypeChange = (e) => {
  selectedTypeIndex.value = e.detail.value
}

const onDateChange = (e) => {
  selectedDate.value = e.detail.value
  // 重置时间选择
  selectedTimeIndex.value = 0
}

const onTimeChange = (e) => {
  selectedTimeIndex.value = e.detail.value
}

const onDurationChange = (e) => {
  selectedDurationIndex.value = e.detail.value
}

const submitOrder = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  if (!canSubmit.value) {
    uni.showToast({
      title: '请完善预约信息',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({ title: '创建订单中...' })

    // 检查时间是否可用
    const startTime = new Date(`${selectedDate.value} ${availableTimes.value[selectedTimeIndex.value]}`)
    const duration = durations.value[selectedDurationIndex.value].value

    const checkRes = await checkTimeAvailable({
      consultantId: consultantId.value,
      startTime: startTime.toISOString(),
      duration
    })

    if (checkRes.code !== 200 || !checkRes.data.available) {
      uni.hideLoading()
      uni.showToast({
        title: checkRes.data.message || '该时间段不可预约',
        icon: 'none'
      })
      return
    }

    // 创建订单
    const orderData = {
      consultantId: consultantId.value,
      consultationType: consultationTypes.value[selectedTypeIndex.value].code,
      appointmentTime: startTime.toISOString(),
      duration,
      problemDescription: problemDescription.value.trim(),
      paymentAmount: totalAmount.value
    }

    const res = await createConsultationOrder(orderData)

    uni.hideLoading()

    if (res.code === 200) {
      // 设置订单信息并打开支付弹框
      orderInfo.value = {
        orderNo: res.data.orderNo,
        orderId: res.data.orderId,
        amount: res.data.paymentAmount,
        product: {
          title: `${consultantInfo.value.name} - ${consultationTypes.value[selectedTypeIndex.value].name}`,
          description: `${selectedDate.value} ${availableTimes.value[selectedTimeIndex.value]} (${durations.value[selectedDurationIndex.value].label})`
        }
      }
      paymentModal.value?.open()
    } else {
      uni.showToast({
        title: res.msg || '创建订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 显示支付成功提示
  uni.showToast({
    title: '支付成功',
    icon: 'success'
  })

  // 延迟跳转到咨询登记表页面
  setTimeout(() => {
    uni.navigateTo({
      url: `/pages/consultation/registration-form/index?orderId=${paymentData.orderId || orderInfo.value.orderId}&showSuccess=true`
    })
  }, 1500)
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 生命周期
onLoad((options) => {
  consultantId.value = options.consultantId
  if (options.consultantInfo) {
    try {
      consultantInfo.value = JSON.parse(decodeURIComponent(options.consultantInfo))
    } catch (e) {
      console.error('解析咨询师信息失败:', e)
    }
  }
})
</script>

<style lang="scss" scoped>
.consultation-order {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.consultant-info {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: #fff;
  margin-bottom: 16rpx;

  .consultant-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }

  .consultant-details {
    flex: 1;

    .consultant-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }

    .consultant-title {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 12rpx;
    }

    .consultant-rating {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .rating-text {
        font-size: 24rpx;
        color: #ff6b35;
      }
    }
  }

  .consultant-price {
    text-align: right;

    .price-symbol {
      font-size: 24rpx;
      color: #ff6b35;
    }

    .price-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff6b35;
    }

    .price-unit {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.appointment-info,
.cost-details,
.notice-section {
  background-color: #fff;
  margin-bottom: 16rpx;
  padding: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.form-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
  }

  .picker-value {
    padding: 24rpx;
    background-color: #f8f8f8;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;

    &:empty::before {
      content: attr(placeholder);
      color: #999;
    }
  }

  .time-selector {
    display: flex;
    gap: 16rpx;

    .picker-value {
      flex: 1;
    }
  }

  .problem-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 24rpx;
    background-color: #f8f8f8;
    border-radius: 12rpx;
    font-size: 28rpx;
    line-height: 1.6;
    border: none;
    resize: none;
  }

  .char-count {
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 12rpx;
  }
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.cost-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0 0;
  margin-top: 16rpx;
  border-top: 2rpx solid #f0f0f0;

  text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .total-amount {
    color: #ff6b35;
  }
}

.notice-content {
  text {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;

  .total-info {
    .total-label {
      font-size: 28rpx;
      color: #666;
    }

    .total-price {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff6b35;
    }
  }

  .submit-btn {
    padding: 20rpx 48rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 600;

    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }
}
</style>
