<template>
  <view class="order-detail-container">
    <!-- 订单状态 -->
    <view class="order-status-card">
      <view class="status-info">
        <view class="status-badge" :class="getStatusClass(orderDetail.status)">
          {{ getStatusText(orderDetail.status) }}
        </view>
        <text class="status-desc">{{ getStatusDesc(orderDetail.status) }}</text>
      </view>
      <view class="order-time">
        <text>订单创建时间：{{ formatDateTime(orderDetail.createTime) }}</text>
      </view>
    </view>

    <!-- 来访者信息 -->
    <view class="client-info-card">
      <view class="card-title">来访者信息</view>
      <view class="client-basic">
        <image class="avatar" :src="orderDetail.clientAvatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
        <view class="basic-info">
          <text class="name">{{ orderDetail.clientName }}</text>
          <text class="phone">{{ formatPhone(orderDetail.clientPhone) }}</text>
          <text class="gender-age">{{ orderDetail.clientGender }} · {{ orderDetail.clientAge }}岁</text>
        </view>
      </view>
      
      <!-- 基本信息登记表 -->
      <view class="registration-form" v-if="orderDetail.registrationInfo">
        <view class="form-title">基本信息登记表</view>
        <view class="form-item" v-for="(item, key) in orderDetail.registrationInfo" :key="key">
          <text class="item-label">{{ getFieldLabel(key) }}：</text>
          <text class="item-value">{{ item }}</text>
        </view>
      </view>
    </view>

    <!-- 预约详情 -->
    <view class="appointment-card">
      <view class="card-title">预约详情</view>
      <view class="appointment-info">
        <view class="info-item">
          <text class="label">预约时间</text>
          <text class="value">{{ formatDateTime(orderDetail.appointmentTime) }}</text>
        </view>
        <view class="info-item">
          <text class="label">咨询类型</text>
          <text class="value">{{ orderDetail.consultationType }}</text>
        </view>
        <view class="info-item">
          <text class="label">咨询时长</text>
          <text class="value">{{ orderDetail.duration }}分钟</text>
        </view>
        <view class="info-item">
          <text class="label">咨询方式</text>
          <text class="value">{{ orderDetail.consultationMethod }}</text>
        </view>
        <view class="info-item">
          <text class="label">咨询地点</text>
          <text class="value">{{ orderDetail.consultationLocation || '线上咨询' }}</text>
        </view>
      </view>
    </view>

    <!-- 订单金额 -->
    <view class="amount-card">
      <view class="card-title">费用详情</view>
      <view class="amount-info">
        <view class="amount-item">
          <text class="label">服务费用</text>
          <text class="value">￥{{ orderDetail.originalPrice }}</text>
        </view>
        <view class="amount-item" v-if="orderDetail.discountAmount > 0">
          <text class="label">优惠金额</text>
          <text class="value discount">-￥{{ orderDetail.discountAmount }}</text>
        </view>
        <view class="amount-item total">
          <text class="label">实付金额</text>
          <text class="value">￥{{ orderDetail.paymentAmount }}</text>
        </view>
      </view>
    </view>

    <!-- 咨询记录 -->
    <view class="record-card" v-if="orderDetail.consultationRecord">
      <view class="card-title">咨询记录</view>
      <view class="record-content">
        <view class="record-item">
          <text class="label">主要症状</text>
          <text class="value">{{ orderDetail.consultationRecord.symptoms }}</text>
        </view>
        <view class="record-item">
          <text class="label">咨询内容</text>
          <text class="value">{{ orderDetail.consultationRecord.content }}</text>
        </view>
        <view class="record-item">
          <text class="label">建议方案</text>
          <text class="value">{{ orderDetail.consultationRecord.suggestions }}</text>
        </view>
        <view class="record-item">
          <text class="label">记录时间</text>
          <text class="value">{{ formatDateTime(orderDetail.consultationRecord.createTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="btn-secondary" 
        v-if="orderDetail.status === 'pending'"
        @click="contactClient"
      >
        已完成联系
      </button>
      <button 
        class="btn-primary" 
        v-if="orderDetail.status === 'pending'"
        @click="replyConsultation"
      >
        回复咨询
      </button>
      <button 
        class="btn-warning" 
        v-if="orderDetail.status === 'ongoing'"
        @click="fillConsultationForm"
      >
        填写咨询结果
      </button>
      <button 
        class="btn-info" 
        v-if="orderDetail.status === 'completed' && !orderDetail.consultationRecord"
        @click="fillConsultationForm"
      >
        补充咨询记录
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getConsultantOrderDetail, updateOrderStatus } from '@/api/consultant-app.js'

// 响应式数据
const orderDetail = ref({})
const orderId = ref('')

// 生命周期
onLoad((options) => {
  orderId.value = options.orderId
  loadOrderDetail()
})

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    const res = await getConsultantOrderDetail(orderId.value)
    if (res.code === 200) {
      orderDetail.value = res.data
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hour}:${minute}`
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    'pending': 'status-pending',
    'ongoing': 'status-ongoing',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'ongoing': '进行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取状态描述
const getStatusDesc = (status) => {
  const statusMap = {
    'pending': '请及时联系来访者确认咨询时间',
    'ongoing': '咨询正在进行中',
    'completed': '咨询已完成，感谢您的服务',
    'cancelled': '订单已取消'
  }
  return statusMap[status] || ''
}

// 获取字段标签
const getFieldLabel = (key) => {
  const labelMap = {
    'education': '学历',
    'occupation': '职业',
    'maritalStatus': '婚姻状况',
    'medicalHistory': '病史',
    'consultationExpectation': '咨询期望',
    'emergencyContact': '紧急联系人',
    'emergencyPhone': '紧急联系电话'
  }
  return labelMap[key] || key
}

// 联系来访者
const contactClient = async () => {
  try {
    const res = await updateOrderStatus(orderId.value, { status: 'contacted' })
    if (res.code === 200) {
      uni.showToast({
        title: '操作成功',
        icon: 'success'
      })
      loadOrderDetail()
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 回复咨询
const replyConsultation = () => {
  uni.navigateTo({
    url: `/pages/consultation-order/consultation-reply/index?orderId=${orderId.value}`
  })
}

// 填写咨询表单
const fillConsultationForm = () => {
  uni.navigateTo({
    url: `/pages/consultation-order/consultation-form/index?orderId=${orderId.value}`
  })
}
</script>

<style scoped lang="scss">
.order-detail-container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.order-status-card,
.client-info-card,
.appointment-card,
.amount-card,
.record-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.order-status-card {
  .status-info {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .status-badge {
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 24rpx;
      margin-right: 20rpx;
      
      &.status-pending {
        background-color: #fff7e6;
        color: #fa8c16;
      }
      
      &.status-ongoing {
        background-color: #f6ffed;
        color: #52c41a;
      }
      
      &.status-completed {
        background-color: #f0f0f0;
        color: #666;
      }
      
      &.status-cancelled {
        background-color: #fff2f0;
        color: #ff4d4f;
      }
    }
    
    .status-desc {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .order-time {
    font-size: 24rpx;
    color: #999;
  }
}

.client-basic {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  
  .avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .basic-info {
    flex: 1;
    
    .name {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .phone,
    .gender-age {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 4rpx;
    }
  }
}

.registration-form {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
  
  .form-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .form-item {
    display: flex;
    margin-bottom: 16rpx;
    
    .item-label {
      font-size: 26rpx;
      color: #666;
      width: 200rpx;
      flex-shrink: 0;
    }
    
    .item-value {
      font-size: 26rpx;
      color: #333;
      flex: 1;
      word-break: break-all;
    }
  }
}

.appointment-info,
.amount-info,
.record-content {
  .info-item,
  .amount-item,
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
      flex-shrink: 0;
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
      flex: 1;
      text-align: right;
      word-break: break-all;
      
      &.discount {
        color: #52c41a;
      }
    }
    
    &.total {
      border-top: 1rpx solid #f0f0f0;
      padding-top: 20rpx;
      margin-top: 20rpx;
      
      .label,
      .value {
        font-weight: bold;
        font-size: 30rpx;
      }
      
      .value {
        color: #ff4d4f;
      }
    }
  }
}

.record-content {
  .record-item {
    flex-direction: column;
    align-items: flex-start;
    
    .label {
      margin-bottom: 10rpx;
    }
    
    .value {
      text-align: left;
      line-height: 1.6;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  
  button {
    flex: 1;
    padding: 24rpx 0;
    border-radius: 12rpx;
    font-size: 28rpx;
    border: none;
    
    &.btn-primary {
      background-color: #1890ff;
      color: #fff;
    }
    
    &.btn-secondary {
      background-color: #f5f5f5;
      color: #666;
    }
    
    &.btn-info {
      background-color: #e6f7ff;
      color: #1890ff;
    }
    
    &.btn-warning {
      background-color: #fff7e6;
      color: #fa8c16;
    }
  }
}
</style>
