import request from '../utils/request'

// 获取冥想列表
export function getExploreMeditationList(params) {
  return request({
    url: '/miniapp/meditation/list',
    method: 'get',
    params
  })
}

// 获取冥想分类
export function getMeditationCategories() {
  return request({
    url: '/miniapp/meditation/categories',
    method: 'get'
  })
}

// 获取测评列表
export function getAssessmentList(params) {
  return request({
    url: '/miniapp/user/assessment/scales',
    method: 'get',
    params
  })
}

// 获取测评分类
export function getAssessmentCategories() {
  return request({
    url: '/miniapp/assessment/categories',
    method: 'get'
  })
}

// 获取推荐内容
export function getRecommendedContent(params) {
  return request({
    url: '/miniapp/explore/recommended',
    method: 'get',
    params
  })
}

// 搜索内容
export function searchContent(params) {
  return request({
    url: '/miniapp/explore/search',
    method: 'get',
    params
  })
}
