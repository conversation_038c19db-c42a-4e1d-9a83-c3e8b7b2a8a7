<template>
  <view class="test-page">
    <view class="header">
      <text class="title">收藏系统测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试重构后的收藏系统：</text>
          <text>1. 支持咨询师、课程、冥想、测评四种类型收藏</text>
          <text>2. 支持收藏分组管理功能</text>
          <text>3. 统一的收藏API接口</text>
          <text>4. 收藏状态检查和统计功能</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">收藏按钮测试</text>
        <view class="favorite-tests">
          <view class="test-item">
            <text class="test-label">咨询师收藏：</text>
            <FavoriteButton 
              page-type="consultant"
              :target-id="123"
              :target-info="consultantInfo"
              show-text
              @change="onFavoriteChange"
            />
          </view>
          
          <view class="test-item">
            <text class="test-label">课程收藏：</text>
            <FavoriteButton 
              page-type="course"
              :target-id="456"
              :target-info="courseInfo"
              show-text
              @change="onFavoriteChange"
            />
          </view>
          
          <view class="test-item">
            <text class="test-label">冥想收藏：</text>
            <FavoriteButton 
              page-type="meditation"
              :target-id="789"
              :target-info="meditationInfo"
              show-text
              @change="onFavoriteChange"
            />
          </view>
          
          <view class="test-item">
            <text class="test-label">测评收藏：</text>
            <FavoriteButton 
              page-type="assessment"
              :target-id="101"
              :target-info="assessmentInfo"
              show-text
              @change="onFavoriteChange"
            />
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">API测试</text>
        <view class="api-tests">
          <button class="api-btn" @click="testCheckFavorite">检查收藏状态</button>
          <button class="api-btn" @click="testGetStats">获取收藏统计</button>
          <button class="api-btn" @click="testGetGroups">获取收藏分组</button>
          <button class="api-btn" @click="testCreateGroup">创建测试分组</button>
        </view>
        
        <view v-if="apiResult" class="api-result">
          <text class="result-title">API测试结果：</text>
          <text class="result-content">{{ JSON.stringify(apiResult, null, 2) }}</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">页面跳转测试</text>
        <view class="page-tests">
          <button class="page-btn" @click="goToFavorites">我的收藏</button>
          <button class="page-btn" @click="goToOldFavorites">旧版收藏</button>
          <button class="page-btn" @click="goToConsultantDetail">咨询师详情</button>
          <button class="page-btn" @click="goToCourseDetail">课程详情</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">收藏类型说明</text>
        <view class="type-list">
          <view class="type-item" v-for="(name, type) in favoriteTypes" :key="type">
            <text class="type-code">{{ type }}</text>
            <text class="type-name">{{ name }}</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">功能特性</text>
        <view class="features">
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>统一的收藏API接口</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>支持四种内容类型收藏</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>收藏分组管理功能</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>收藏状态实时检查</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>收藏统计和分析</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>通用收藏按钮组件</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FavoriteButton from '@/components/FavoriteButton/FavoriteButton.vue'
import { 
  checkFavorite, 
  getFavoriteStats, 
  getFavoriteGroups,
  createFavoriteGroup,
  FAVORITE_TYPE_NAMES 
} from '@/api/favorite.js'

// 响应式数据
const apiResult = ref(null)

// 收藏类型
const favoriteTypes = FAVORITE_TYPE_NAMES

// 测试数据
const consultantInfo = {
  title: '张医生',
  name: '张医生',
  image: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/consultant/default-avatar.png',
  level: '高级咨询师',
  consultStyles: ['认知行为疗法', '人本主义疗法']
}

const courseInfo = {
  title: '情绪管理入门课程',
  image: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
  price: 199,
  originalPrice: 299
}

const meditationInfo = {
  title: '焦虑缓解冥想',
  image: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
  duration: 900,
  narrator: '张大师'
}

const assessmentInfo = {
  title: '心理健康测评',
  image: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
  price: 39,
  questionCount: 50
}

// 收藏状态变化
const onFavoriteChange = (data) => {
  console.log('收藏状态变化:', data)
  uni.showToast({
    title: data.favorited ? '收藏成功' : '取消收藏成功',
    icon: 'success'
  })
}

// API测试方法
const testCheckFavorite = async () => {
  try {
    const res = await checkFavorite({
      targetType: 1,
      targetId: 123
    })
    apiResult.value = res
    uni.showToast({
      title: '检查完成',
      icon: 'success'
    })
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    apiResult.value = { error: error.message }
  }
}

const testGetStats = async () => {
  try {
    const res = await getFavoriteStats()
    apiResult.value = res
    uni.showToast({
      title: '获取统计完成',
      icon: 'success'
    })
  } catch (error) {
    console.error('获取统计失败:', error)
    apiResult.value = { error: error.message }
  }
}

const testGetGroups = async () => {
  try {
    const res = await getFavoriteGroups()
    apiResult.value = res
    uni.showToast({
      title: '获取分组完成',
      icon: 'success'
    })
  } catch (error) {
    console.error('获取分组失败:', error)
    apiResult.value = { error: error.message }
  }
}

const testCreateGroup = async () => {
  try {
    const res = await createFavoriteGroup({
      groupName: '测试分组',
      groupIcon: '🧪',
      groupColor: '#FF6B6B',
      description: '这是一个测试分组'
    })
    apiResult.value = res
    uni.showToast({
      title: '创建分组完成',
      icon: 'success'
    })
  } catch (error) {
    console.error('创建分组失败:', error)
    apiResult.value = { error: error.message }
  }
}

// 页面跳转
const goToFavorites = () => {
  uni.navigateTo({
    url: '/pages/my/favorites/index'
  })
}

const goToOldFavorites = () => {
  uni.navigateTo({
    url: '/pages/my/my-star/index'
  })
}

const goToConsultantDetail = () => {
  uni.navigateTo({
    url: '/pages/consultant/detail/index?id=123'
  })
}

const goToCourseDetail = () => {
  uni.navigateTo({
    url: '/pages/course/detail/index?id=456'
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.favorite-tests {
  .test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .test-label {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.api-tests {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
  
  .api-btn {
    flex: 1;
    min-width: 200rpx;
    height: 64rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border-radius: 32rpx;
    font-size: 26rpx;
    border: none;
  }
}

.api-result {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  
  .result-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .result-content {
    font-family: monospace;
    font-size: 24rpx;
    color: #666;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.page-tests {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .page-btn {
    height: 80rpx;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
  }
}

.type-list {
  .type-item {
    display: flex;
    align-items: center;
    gap: 24rpx;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .type-code {
      width: 60rpx;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
      color: #667eea;
      background: rgba(102, 126, 234, 0.1);
      padding: 8rpx;
      border-radius: 8rpx;
    }
    
    .type-name {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.features {
  .feature-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}
</style>
