# 搜索结果数据处理修改总结

## 修改概述

根据后端返回的搜索结果数据结构，修改了前端的搜索逻辑和数据处理，确保能正确解析和显示搜索结果。

## 后端搜索结果数据结构

```javascript
{
  data: {
    keyword: "情绪",
    searchType: "consultant", 
    totalCount: 7,
    searchTime: 243,
    categories: [
      {
        type: "consultant",
        typeName: "咨询师", 
        count: 7,
        items: [
          {
            id: 1,
            type: "consultant",
            title: "李虹",
            coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/consultant/lihong/image_url.webp",
            description: "国家二级心理咨询师，社会工作师...",
            highlightDescription: "国家二级心理咨询师，社会工作师...擅长领域：自闭症、多动症等特殊儿童心理健康，<em>情绪</em>压力、婚姻家庭...",
            highlightTitle: "李虹",
            price: "680.00",
            rating: null,
            relevanceScore: 0,
            tags: null,
            viewCount: null
          }
        ]
      }
    ],
    suggestions: ["情绪管理课程", "情绪测评", "亲子", "咨询师", ...]
  }
}
```

## 主要修改内容

### 1. 搜索逻辑重构

**文件**: `pages/search/search.vue`

#### 修改前
```javascript
// 并行搜索所有类型的数据
const searchPromises = [
    searchConsultants({...}),
    searchAssessments({...}),
    searchMeditations({...}),
    searchCourses({...})
]

const results = await Promise.allSettled(searchPromises)

// 分别处理每个类型的搜索结果
if (results[0].status === 'fulfilled' && results[0].value?.code === 200) {
    consultantResults.value = results[0].value.data || []
}
```

#### 修改后
```javascript
// 使用全局搜索接口获取所有类型的搜索结果
const res = await globalSearch({
    keyword: searchKeyword.value,
    type: 'all',
    pageNum: 1,
    pageSize: 20
})

if (res && res.code === 200 && res.data) {
    const searchData = res.data
    
    // 如果有 categories 数组，按类型分配结果
    if (searchData.categories && Array.isArray(searchData.categories)) {
        searchData.categories.forEach(category => {
            const items = category.items || []
            
            switch (category.type) {
                case 'consultant':
                    consultantResults.value = items
                    break
                case 'assessment':
                    assessmentResults.value = items
                    break
                case 'meditation':
                    meditationResults.value = items
                    break
                case 'course':
                    courseResults.value = items
                    break
            }
        })
    }
}
```

### 2. 导入清理

移除了不再使用的分类搜索接口导入：

```javascript
// 移除的导入
import {
    searchConsultants,
    searchCourses, 
    searchMeditations,
    searchAssessments,
    // ...
} from '@/api/search.js'

// 保留的导入
import {
    globalSearch,
    getHotSearches,
    getSearchSuggestions,
    getUserSearchHistory,
    clearUserSearchHistory,
    saveSearchRecord
} from '@/api/search.js'
```

### 3. 高亮描述支持

**文件**: `components/UniversalListItem/UniversalListItem.vue`

#### 描述获取逻辑
```javascript
// 获取描述信息
const getDescription = () => {
    // 优先使用高亮描述（搜索结果中的高亮文本）
    if (props.item.highlightDescription) {
        return props.item.highlightDescription
    }
    return props.item.personalIntro || props.item.description || props.item.intro || props.item.summary || ''
}
```

#### 模板显示
```vue
<!-- 描述信息 -->
<view class="info">
    <rich-text v-if="item.highlightDescription" :nodes="getDescription()"></rich-text>
    <text v-else>{{ getDescription() }}</text>
</view>
```

## 数据字段映射

### 通用字段
- `id`: 项目ID
- `type`: 项目类型 (consultant, course, meditation, assessment)
- `title`: 显示标题
- `coverImage`: 封面图片
- `description`: 描述信息
- `highlightDescription`: 高亮描述（搜索结果）
- `highlightTitle`: 高亮标题（搜索结果）
- `price`: 价格
- `tags`: 标签数组

### 咨询师特有字段
- `rating`: 评分
- `serviceCount`: 服务人数
- `totalCases`: 总案例数
- `startYear`: 开始从业年份

### 课程特有字段
- `lessonCount`: 课程章节数
- `studentCount`: 学习人数

### 冥想特有字段
- `duration`: 时长
- `playCount`: 播放次数

### 测评特有字段
- `questionCount`: 题目数量
- `duration`: 预估完成时间

## 搜索结果处理流程

1. **发起搜索**: 调用 `globalSearch` 接口，传入关键词和搜索类型
2. **数据解析**: 检查返回数据中的 `categories` 数组
3. **结果分配**: 根据 `category.type` 将搜索结果分配到对应的结果数组
4. **组件显示**: UniversalListItem 组件根据 `type` 和数据字段显示相应内容
5. **高亮处理**: 如果有 `highlightDescription`，使用 `rich-text` 组件显示高亮效果

## 高亮文本处理

搜索结果中的高亮文本使用 HTML `<em>` 标签标记：
```html
"擅长领域：自闭症、多动症等特殊儿童心理健康，<em>情绪</em>压力、婚姻家庭"
```

前端使用 `rich-text` 组件渲染，自动显示高亮效果。

## 容错处理

1. **数据结构容错**: 支持有无 `categories` 数组的两种数据格式
2. **字段容错**: 支持多种字段名称的映射
3. **类型容错**: 根据 `item.type` 自动分配到正确的结果数组
4. **显示容错**: 字段缺失时使用默认值或空字符串

## 性能优化

1. **单次请求**: 使用全局搜索接口一次获取所有类型的结果
2. **按需渲染**: 通过标签切换显示不同类型的结果，避免重复渲染
3. **数据缓存**: 搜索结果按类型缓存，切换标签时无需重新请求

## 注意事项

1. 确保后端返回的数据结构与前端期望的格式一致
2. 高亮文本中的 HTML 标签需要是安全的，避免 XSS 攻击
3. 图片字段支持 `coverImage` 格式，确保图片能正常显示
4. 价格字段为字符串格式，需要在显示时进行数值转换
5. 搜索建议数组可用于搜索提示功能

## 测试建议

1. 测试不同关键词的搜索结果显示
2. 验证高亮文本的显示效果
3. 检查各种数据字段的容错处理
4. 测试标签切换和结果筛选功能
5. 验证图片加载和错误处理
