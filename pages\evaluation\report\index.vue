<template>
  <view class="report-page">
    <!-- 顶部信息 -->
    <view class="report-header">
      <view class="assessment-info">
        <text class="title">{{reportData.scaleName || '测评报告'}}</text>
        <text class="subtitle" v-if="reportData.endTime">测评完成时间：{{formatTime(reportData.endTime)}}</text>
      </view>
      <view class="score-section">
        <view class="score-circle">
          <text class="score-number">{{reportData.totalScore || 0}}</text>
          <text class="score-label">总分</text>
        </view>
      </view>
    </view>

    <!-- 测评结果 -->
    <view class="result-section">
      <view class="section-title">
        <text>测评结果</text>
      </view>
      <view class="result-content">
        <view class="result-item">
          <text class="label">测评状态：</text>
          <view class="status-badge completed">
            <text class="status-text">已完成</text>
          </view>
        </view>
        <view class="result-item">
          <text class="label">开始时间：</text>
          <text class="value" v-if="reportData.startTime">{{formatTime(reportData.startTime)}}</text>
        </view>
        <view class="result-item">
          <text class="label">完成时间：</text>
          <text class="value" v-if="reportData.endTime">{{formatTime(reportData.endTime)}}</text>
        </view>
        <view class="result-item">
          <text class="label">用时：</text>
          <text class="value">{{getDuration()}}</text>
        </view>
        <view class="result-item">
          <text class="label">得分率：</text>
          <text class="value">{{reportData.scorePercentage || 0}}%</text>
        </view>
        <view class="result-item">
          <text class="label">结果等级：</text>
          <text class="value">{{reportData.resultLevel || '未知'}}</text>
        </view>
        <view class="result-item">
          <text class="label">测评报告：</text>
<!-- eslint-disable -->
          <view class="value" v-text="reportData.resultDescription || reportData.report?.content"></view>
        </view>
      </view>
    </view>

    <!-- 维度得分 -->
    <view class="dimension-section" v-if="reportData.dimensionScores && Object.keys(reportData.dimensionScores).length > 0">
      <view class="section-title">
        <text>维度得分</text>
      </view>
      <view class="dimension-content">
        <view
          class="dimension-item"
          v-for="(score, dimension) in reportData.dimensionScores"
          :key="dimension"
        >
          <view class="dimension-info">
            <text class="dimension-name">{{dimension}}</text>
            <text class="dimension-score">{{score}}分</text>
          </view>
          <view class="dimension-bar">
            <view
              class="dimension-progress"
              :style="{width: (score / getMaxDimensionScore()) * 100 + '%'}"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细分析 -->
    <view class="analysis-section" v-if="reportData.analysis">
      <view class="section-title">
        <text>详细分析</text>
      </view>
      <view class="analysis-content">
        <text class="analysis-text">{{reportData.analysis}}</text>
      </view>
    </view>

    <!-- 建议 -->
    <view class="suggestion-section" v-if="reportData.suggestions">
      <view class="section-title">
        <text>建议</text>
      </view>
      <view class="suggestion-content">
        <text class="suggestion-text">{{reportData.suggestions}}</text>
      </view>
    </view>

    <!-- 答案详情 -->
    <view class="answers-section" v-if="reportData.answerDetails && reportData.answerDetails.length > 0">
      <view class="section-title">
        <text>答案详情</text>
      </view>
      <view class="answers-content">
        <view 
          class="answer-item" 
          v-for="(answer, index) in reportData.answerDetails" 
          :key="index"
        >
          <view class="question-info">
            <text class="question-number">Q{{index + 1}}</text>
            <text class="question-text">{{answer.questionContent}}</text>
          </view>
          <view class="answer-info">
            <text class="answer-label">您的答案：</text>
            <text class="answer-text">{{answer.optionContent}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-btns">
      <button class="review-btn" @click="submitReview">评价测评</button>
      <button class="share-btn" @click="shareReport">分享报告</button>
      <button class="retake-btn" @click="retakeTest">重新测评</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAssessmentResult, generateAssessmentReport } from '@/api/evaluation'
import { formatTime, parseCompatibleDate } from '@/utils/index'

const reportData = ref({})
const recordId = ref('')
const sessionId = ref('')
const loading = ref(false)

onLoad(async (options) => {
  if (options.recordId) {
    recordId.value = options.recordId
    await getReportData()

    // 设置页面标题
    if (options.title) {
      uni.setNavigationBarTitle({
        title: decodeURIComponent(options.title) + ' - 报告'
      })
    }
  } else {
    uni.showToast({
      title: '缺少测评记录ID',
      icon: 'none'
    })
  }
})

// 获取报告数据
const getReportData = async () => {
  if (loading.value) return
  loading.value = true

  try {
    // 获取测评结果
    const resultRes = await getAssessmentResult(recordId.value)
    if (resultRes.code === 200) {
      reportData.value = resultRes.data || {}

      // 尝试获取详细报告
      try {
        const reportRes = await generateAssessmentReport(recordId.value)
        if (reportRes.code === 200) {
          reportData.value.report = reportRes.data
        }
      } catch (error) {
        console.error('获取详细报告失败:', error)
      }
    } else {
      uni.showToast({
        title: resultRes.msg || '获取报告失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取报告失败:', error)
    uni.showToast({
      title: '获取报告失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}



// 计算用时
const getDuration = () => {
  if (reportData.value.duration) {
    // 新版本API直接返回用时（秒）
    const totalSeconds = reportData.value.duration
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60
    return `${minutes}分${seconds}秒`
  }

  if (!reportData.value.startTime || !reportData.value.endTime) {
    return '未知'
  }

  const startTime = parseCompatibleDate(reportData.value.startTime)
  const endTime = parseCompatibleDate(reportData.value.endTime)
  const duration = endTime - startTime

  const minutes = Math.floor(duration / (1000 * 60))
  const seconds = Math.floor((duration % (1000 * 60)) / 1000)

  return `${minutes}分${seconds}秒`
}

// 获取最大维度得分（用于计算进度条）
const getMaxDimensionScore = () => {
  if (!reportData.value.dimensionScores) return 100
  const scores = Object.values(reportData.value.dimensionScores)
  return Math.max(...scores, 100) // 至少为100，避免除零
}



// 提交评价
const submitReview = () => {
  const scaleId = reportData.value.scaleId
  const scaleName = reportData.value.scaleName
  const currentRecordId = recordId.value

  if (scaleId) {
    uni.navigateTo({
      url: `/pages/evaluation/submit-review/index?scaleId=${scaleId}&scaleName=${encodeURIComponent(scaleName)}&recordId=${currentRecordId}`
    })
  } else {
    uni.showToast({
      title: '无法获取测评信息',
      icon: 'none'
    })
  }
}

// 分享报告
const shareReport = () => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

// 重新测评
const retakeTest = () => {
  const scaleId = reportData.value.scaleId
  const scaleName = reportData.value.scaleName

  if (scaleId) {
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${scaleId}`
    })
  } else {
    uni.showToast({
      title: '无法获取测评信息',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.report-page {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
}

.report-header {
  background: #fff;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .assessment-info {
    flex: 1;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
      display: block;
    }

    .subtitle {
      font-size: 26rpx;
      color: #666;
    }
  }

  .score-section {
    .score-circle {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff, #67c23a);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;

      .score-number {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 4rpx;
      }

      .score-label {
        font-size: 22rpx;
      }
    }
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #409eff;
}

.result-section,
.analysis-section,
.suggestion-section,
.answers-section,
.dimension-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  padding-bottom: 100rpx;
  margin-bottom: 30rpx;

  .result-content {
    .result-item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .label {
        font-size: 28rpx;
        color: #666;
        width: 160rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }

      .status-badge {
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.completed {
          background-color: #f6ffed;
          .status-text {
            color: #52c41a;
          }
        }
      }
    }
  }

  .analysis-content,
  .suggestion-content {
    .analysis-text,
    .suggestion-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }
  }

  .answers-content {
    .answer-item {
      border-bottom: 1rpx solid #f0f0f0;
      padding: 20rpx 0;

      &:last-child {
        border-bottom: none;
      }

      .question-info {
        margin-bottom: 15rpx;

        .question-number {
          font-size: 26rpx;
          color: #409eff;
          font-weight: bold;
          margin-right: 10rpx;
        }

        .question-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.5;
        }
      }

      .answer-info {
        display: flex;
        align-items: flex-start;

        .answer-label {
          font-size: 26rpx;
          color: #666;
          margin-right: 10rpx;
        }

        .answer-text {
          font-size: 26rpx;
          color: #333;
          flex: 1;
        }
      }
    }
  }

  .dimension-content {
    .dimension-item {
      margin-bottom: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .dimension-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15rpx;

        .dimension-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .dimension-score {
          font-size: 28rpx;
          color: #409eff;
          font-weight: bold;
        }
      }

      .dimension-bar {
        height: 12rpx;
        background-color: #f0f0f0;
        border-radius: 6rpx;
        overflow: hidden;

        .dimension-progress {
          height: 100%;
          background: linear-gradient(90deg, #409eff, #67c23a);
          border-radius: 6rpx;
          transition: width 0.3s ease;
        }
      }
    }
  }
}

.footer-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 30rpx;
  background: #fff;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 30rpx;
    border-radius: 40rpx;

    &.review-btn {
      background: #ff6b35;
      color: #fff;
    }

    &.share-btn {
      background: #67c23a;
      color: #fff;
    }

    &.retake-btn {
      background: #409eff;
      color: #fff;
    }
  }
}
</style> 