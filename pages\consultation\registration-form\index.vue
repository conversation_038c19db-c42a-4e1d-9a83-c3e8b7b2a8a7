<template>
	<view class="registration-form">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="navbar-title">咨询登记表</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="form-item">
					<text class="label">姓名</text>
					<input v-model="formData.name" class="input" placeholder="请填写" maxlength="20" />
				</view>

				<view class="form-item" @click="showGenderPicker">
					<text class="label">性别</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.gender }">
							{{ formData.gender || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item">
					<text class="label">年龄</text>
					<input v-model="formData.age" class="input" placeholder="请填写" type="number" maxlength="3" />
				</view>

				<view class="form-item">
					<text class="label">籍贯</text>
					<input v-model="formData.hometown" class="input" placeholder="请填写" maxlength="50" />
				</view>

				<view class="form-item" @click="showEthnicityPicker">
					<text class="label">民族</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.ethnicity }">
							{{ formData.ethnicity || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item" @click="showReligionPicker">
					<text class="label">宗教信仰</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.religion }">
							{{ formData.religion || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item" @click="showEducationPicker">
					<text class="label">学历</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.education }">
							{{ formData.education || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item" @click="showOccupationPicker">
					<text class="label">从事职业</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.occupation }">
							{{ formData.occupation || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item" @click="showMaritalPicker">
					<text class="label">婚姻状态</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.maritalStatus }">
							{{ formData.maritalStatus || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item" @click="showChildrenPicker">
					<text class="label">生育状态</text>
					<view class="picker-content">
						<text class="value" :class="{ placeholder: !formData.hasChildren }">
							{{ formData.hasChildren || '请选择' }}
						</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>

				<view class="form-item">
					<text class="label">紧急联系人</text>
					<input v-model="formData.emergencyContact" class="input" placeholder="请填写" maxlength="20" />
				</view>

				<view class="form-item">
					<text class="label">联系人电话</text>
					<input v-model="formData.emergencyPhone" class="input" placeholder="请填写" type="number" maxlength="11" />
				</view>
			</view>

			<!-- 既往病史 -->
			<view class="form-section">
				<view class="section-title">既往病史</view>
				<view class="checkbox-group">
					<view class="checkbox-item" v-for="item in medicalHistoryOptions" :key="item.value"
						@click="toggleMedicalHistory(item.value)">
						<view class="checkbox" :class="{ checked: formData.medicalHistory.includes(item.value) }">
							<uni-icons v-if="formData.medicalHistory.includes(item.value)" type="checkmarkempty" size="14"
								color="#fff"></uni-icons>
						</view>
						<text class="checkbox-label">{{ item.label }}</text>
					</view>
				</view>
			</view>

			<!-- 健康状态 -->
			<view class="form-section">
				<view class="section-title">健康状态</view>
				<view class="radio-group">
					<view class="radio-item" v-for="item in healthStatusOptions" :key="item.value"
						@click="formData.healthStatus = item.value">
						<view class="radio" :class="{ checked: formData.healthStatus === item.value }">
							<view class="radio-inner" v-if="formData.healthStatus === item.value"></view>
						</view>
						<text class="radio-label">{{ item.label }}</text>
					</view>
				</view>
			</view>

			<!-- 是否服药 -->
			<view class="form-section">
				<view class="section-title">是否服药</view>
				<view class="radio-group">
					<view class="radio-item" v-for="item in medicationOptions" :key="item.value"
						@click="formData.takingMedication = item.value">
						<view class="radio" :class="{ checked: formData.takingMedication === item.value }">
							<view class="radio-inner" v-if="formData.takingMedication === item.value"></view>
						</view>
						<text class="radio-label">{{ item.label }}</text>
					</view>
				</view>

				<view class="textarea-container" v-if="formData.takingMedication === 'yes'">
					<textarea v-model="formData.medicationDetails" class="textarea" placeholder="请写所服药物信息"
						maxlength="200"></textarea>
				</view>
			</view>

			<!-- 咨询经历 -->
			<view class="form-section">
				<view class="section-title">咨询经历</view>
				<view class="textarea-container">
					<textarea v-model="formData.consultationHistory" class="textarea" placeholder="请写咨询经历"
						maxlength="500"></textarea>
				</view>
			</view>

			<!-- 咨询原因 -->
			<view class="form-section">
				<view class="section-title">咨询原因</view>
				<view class="textarea-container">
					<textarea v-model="formData.consultationReason" class="textarea" placeholder="请写咨询原因"
						maxlength="500"></textarea>
				</view>
			</view>

			<!-- 性格特点和兴趣爱好 -->
			<view class="form-section">
				<view class="section-title">请描述您的性格特点和兴趣爱好</view>
				<view class="textarea-container">
					<textarea v-model="formData.personalityAndHobbies" class="textarea" placeholder="请描述"
						maxlength="500"></textarea>
				</view>
			</view>

			<!-- 目前情况 -->
			<view class="form-section">
				<view class="section-title">请描述您目前的情况（工作、家庭、人际关系）</view>
				<view class="textarea-container">
					<textarea v-model="formData.currentSituation" class="textarea" placeholder="请描述" maxlength="500"></textarea>
				</view>
			</view>

			<!-- 早年成长环境 -->
			<view class="form-section">
				<view class="section-title">请描述您的早年成长环境，以及您认为早年环境中的那些方面影响了现在的您</view>
				<view class="textarea-container">
					<textarea v-model="formData.childhoodEnvironment" class="textarea" placeholder="请描述"
						maxlength="500"></textarea>
				</view>
			</view>

			<!-- 咨询期待 -->
			<view class="form-section">
				<view class="section-title">您期待从咨询中得到什么样的启发和领悟</view>
				<view class="textarea-container">
					<textarea v-model="formData.expectations" class="textarea" placeholder="请描述" maxlength="500"></textarea>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-container">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '提交中...' : '提交' }}
				</button>
			</view>
		</view>

		<!-- 选择器 -->
		<uni-popup ref="pickerPopup" type="bottom">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-cancel" @click="cancelPicker">取消</text>
					<text class="picker-title">{{ currentPickerTitle }}</text>
					<text class="picker-confirm" @click="confirmPicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="pickerValue" @change="onPickerChange">
					<picker-view-column>
						<view class="picker-item" v-for="item in currentPickerOptions" :key="item.value">
							{{ item.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>

		<!-- 预约成功弹窗 -->
		<uni-popup ref="successPopup" type="center">
			<view class="success-modal">
				<view class="success-icon">
					<uni-icons type="checkmarkempty" size="60" color="#4CAF50"></uni-icons>
				</view>
				<view class="success-title">预约成功</view>
				<view class="success-content">
					您的咨询登记表已提交成功，咨询师将在24小时内与您联系确认咨询时间。
				</view>
				<button class="success-btn" @click="goToMyConsultations">查看我的咨询</button>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { submitRegistrationForm } from '@/api/consultation.js'

// 响应式数据
const orderId = ref('')
const submitting = ref(false)
const pickerPopup = ref(null)
const successPopup = ref(null)
const currentPickerType = ref('')
const currentPickerTitle = ref('')
const currentPickerOptions = ref([])
const pickerValue = ref([0])
const selectedPickerIndex = ref(0)

// 表单数据
const formData = reactive({
	name: '',
	gender: '',
	age: '',
	hometown: '',
	ethnicity: '',
	religion: '',
	education: '',
	occupation: '',
	maritalStatus: '',
	hasChildren: '',
	emergencyContact: '',
	emergencyPhone: '',
	medicalHistory: [],
	healthStatus: '',
	takingMedication: '',
	medicationDetails: '',
	consultationHistory: '',
	consultationReason: '',
	personalityAndHobbies: '',
	currentSituation: '',
	childhoodEnvironment: '',
	expectations: ''
})

// 选项数据
const genderOptions = [
	{ value: '男', label: '男' },
	{ value: '女', label: '女' }
]

const ethnicityOptions = [
	{ value: '汉族', label: '汉族' },
	{ value: '蒙古族', label: '蒙古族' },
	{ value: '回族', label: '回族' },
	{ value: '藏族', label: '藏族' },
	{ value: '维吾尔族', label: '维吾尔族' },
	{ value: '苗族', label: '苗族' },
	{ value: '彝族', label: '彝族' },
	{ value: '壮族', label: '壮族' },
	{ value: '布依族', label: '布依族' },
	{ value: '朝鲜族', label: '朝鲜族' },
	{ value: '满族', label: '满族' },
	{ value: '侗族', label: '侗族' },
	{ value: '瑶族', label: '瑶族' },
	{ value: '白族', label: '白族' },
	{ value: '土家族', label: '土家族' },
	{ value: '哈尼族', label: '哈尼族' },
	{ value: '哈萨克族', label: '哈萨克族' },
	{ value: '傣族', label: '傣族' },
	{ value: '黎族', label: '黎族' },
	{ value: '傈僳族', label: '傈僳族' },
	{ value: '佤族', label: '佤族' },
	{ value: '畲族', label: '畲族' },
	{ value: '高山族', label: '高山族' },
	{ value: '拉祜族', label: '拉祜族' },
	{ value: '水族', label: '水族' },
	{ value: '东乡族', label: '东乡族' },
	{ value: '纳西族', label: '纳西族' },
	{ value: '景颇族', label: '景颇族' },
	{ value: '柯尔克孜族', label: '柯尔克孜族' },
	{ value: '土族', label: '土族' },
	{ value: '达斡尔族', label: '达斡尔族' },
	{ value: '仫佬族', label: '仫佬族' },
	{ value: '羌族', label: '羌族' },
	{ value: '布朗族', label: '布朗族' },
	{ value: '撒拉族', label: '撒拉族' },
	{ value: '毛南族', label: '毛南族' },
	{ value: '仡佬族', label: '仡佬族' },
	{ value: '锡伯族', label: '锡伯族' },
	{ value: '阿昌族', label: '阿昌族' },
	{ value: '普米族', label: '普米族' },
	{ value: '塔吉克族', label: '塔吉克族' },
	{ value: '怒族', label: '怒族' },
	{ value: '乌孜别克族', label: '乌孜别克族' },
	{ value: '俄罗斯族', label: '俄罗斯族' },
	{ value: '鄂温克族', label: '鄂温克族' },
	{ value: '德昂族', label: '德昂族' },
	{ value: '保安族', label: '保安族' },
	{ value: '裕固族', label: '裕固族' },
	{ value: '京族', label: '京族' },
	{ value: '塔塔尔族', label: '塔塔尔族' },
	{ value: '独龙族', label: '独龙族' },
	{ value: '鄂伦春族', label: '鄂伦春族' },
	{ value: '赫哲族', label: '赫哲族' },
	{ value: '门巴族', label: '门巴族' },
	{ value: '珞巴族', label: '珞巴族' },
	{ value: '基诺族', label: '基诺族' },
	{ value: '其他', label: '其他' }
]

const religionOptions = [
	{ value: '无宗教信仰', label: '无宗教信仰' },
	{ value: '佛教', label: '佛教' },
	{ value: '道教', label: '道教' },
	{ value: '伊斯兰教', label: '伊斯兰教' },
	{ value: '基督教', label: '基督教' },
	{ value: '天主教', label: '天主教' },
	{ value: '其他', label: '其他' }
]

const educationOptions = [
	{ value: '小学', label: '小学' },
	{ value: '初中', label: '初中' },
	{ value: '高中/中专', label: '高中/中专' },
	{ value: '大专', label: '大专' },
	{ value: '本科', label: '本科' },
	{ value: '硕士', label: '硕士' },
	{ value: '博士', label: '博士' },
	{ value: '其他', label: '其他' }
]

const occupationOptions = [
	{ value: '学生', label: '学生' },
	{ value: '教师', label: '教师' },
	{ value: '医生', label: '医生' },
	{ value: '护士', label: '护士' },
	{ value: '工程师', label: '工程师' },
	{ value: '程序员', label: '程序员' },
	{ value: '销售', label: '销售' },
	{ value: '市场营销', label: '市场营销' },
	{ value: '财务', label: '财务' },
	{ value: '人力资源', label: '人力资源' },
	{ value: '行政', label: '行政' },
	{ value: '法律', label: '法律' },
	{ value: '设计师', label: '设计师' },
	{ value: '艺术家', label: '艺术家' },
	{ value: '记者', label: '记者' },
	{ value: '公务员', label: '公务员' },
	{ value: '军人', label: '军人' },
	{ value: '警察', label: '警察' },
	{ value: '司机', label: '司机' },
	{ value: '服务员', label: '服务员' },
	{ value: '厨师', label: '厨师' },
	{ value: '农民', label: '农民' },
	{ value: '工人', label: '工人' },
	{ value: '个体经营', label: '个体经营' },
	{ value: '企业家', label: '企业家' },
	{ value: '自由职业', label: '自由职业' },
	{ value: '退休', label: '退休' },
	{ value: '无业', label: '无业' },
	{ value: '其他', label: '其他' }
]

const maritalStatusOptions = [
	{ value: '未婚', label: '未婚' },
	{ value: '已婚', label: '已婚' },
	{ value: '离异', label: '离异' },
	{ value: '丧偶', label: '丧偶' },
	{ value: '其他', label: '其他' }
]

const childrenOptions = [
	{ value: '无子女', label: '无子女' },
	{ value: '有子女', label: '有子女' },
	{ value: '准备要孩子', label: '准备要孩子' },
	{ value: '不打算要孩子', label: '不打算要孩子' }
]

const medicalHistoryOptions = [
	{ value: 'hypertension', label: '高血压' },
	{ value: 'diabetes', label: '糖尿病' },
	{ value: 'hyperlipidemia', label: '高血脂' },
	{ value: 'stroke', label: '脑梗' },
	{ value: 'fatty_liver', label: '脂肪肝' }
]

const healthStatusOptions = [
	{ value: 'good', label: '良好' },
	{ value: 'average', label: '一般' },
	{ value: 'poor', label: '亚健康' }
]

const medicationOptions = [
	{ value: 'yes', label: '是' },
	{ value: 'no', label: '否' }
]

// 方法
const goBack = () => {
	uni.navigateBack()
}

// 显示选择器
const showGenderPicker = () => {
	currentPickerType.value = 'gender'
	currentPickerTitle.value = '选择性别'
	currentPickerOptions.value = genderOptions
	const currentIndex = genderOptions.findIndex(item => item.value === formData.gender)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

const showEthnicityPicker = () => {
	currentPickerType.value = 'ethnicity'
	currentPickerTitle.value = '选择民族'
	currentPickerOptions.value = ethnicityOptions
	const currentIndex = ethnicityOptions.findIndex(item => item.value === formData.ethnicity)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

const showReligionPicker = () => {
	currentPickerType.value = 'religion'
	currentPickerTitle.value = '选择宗教信仰'
	currentPickerOptions.value = religionOptions
	const currentIndex = religionOptions.findIndex(item => item.value === formData.religion)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

const showEducationPicker = () => {
	currentPickerType.value = 'education'
	currentPickerTitle.value = '选择学历'
	currentPickerOptions.value = educationOptions
	const currentIndex = educationOptions.findIndex(item => item.value === formData.education)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

const showOccupationPicker = () => {
	currentPickerType.value = 'occupation'
	currentPickerTitle.value = '选择职业'
	currentPickerOptions.value = occupationOptions
	const currentIndex = occupationOptions.findIndex(item => item.value === formData.occupation)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

const showMaritalPicker = () => {
	currentPickerType.value = 'marital'
	currentPickerTitle.value = '选择婚姻状态'
	currentPickerOptions.value = maritalStatusOptions
	const currentIndex = maritalStatusOptions.findIndex(item => item.value === formData.maritalStatus)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

const showChildrenPicker = () => {
	currentPickerType.value = 'children'
	currentPickerTitle.value = '选择生育状态'
	currentPickerOptions.value = childrenOptions
	const currentIndex = childrenOptions.findIndex(item => item.value === formData.hasChildren)
	pickerValue.value = [currentIndex >= 0 ? currentIndex : 0]
	selectedPickerIndex.value = currentIndex >= 0 ? currentIndex : 0
	pickerPopup.value?.open()
}

// 选择器事件
const onPickerChange = (e) => {
	selectedPickerIndex.value = e.detail.value[0]
}

const cancelPicker = () => {
	pickerPopup.value?.close()
}

const confirmPicker = () => {
	const selectedOption = currentPickerOptions.value[selectedPickerIndex.value]
	if (selectedOption) {
		switch (currentPickerType.value) {
			case 'gender':
				formData.gender = selectedOption.value
				break
			case 'ethnicity':
				formData.ethnicity = selectedOption.value
				break
			case 'religion':
				formData.religion = selectedOption.value
				break
			case 'education':
				formData.education = selectedOption.value
				break
			case 'occupation':
				formData.occupation = selectedOption.value
				break
			case 'marital':
				formData.maritalStatus = selectedOption.value
				break
			case 'children':
				formData.hasChildren = selectedOption.value
				break
		}
	}
	pickerPopup.value?.close()
}

// 切换病史选择
const toggleMedicalHistory = (value) => {
	const index = formData.medicalHistory.indexOf(value)
	if (index > -1) {
		formData.medicalHistory.splice(index, 1)
	} else {
		formData.medicalHistory.push(value)
	}
}

// 表单验证
const validateForm = () => {
	if (!formData.name.trim()) {
		uni.showToast({ title: '请填写姓名', icon: 'none' })
		return false
	}
	if (!formData.gender) {
		uni.showToast({ title: '请选择性别', icon: 'none' })
		return false
	}
	if (!formData.age || formData.age <= 0) {
		uni.showToast({ title: '请填写正确的年龄', icon: 'none' })
		return false
	}
	if (!formData.emergencyContact.trim()) {
		uni.showToast({ title: '请填写紧急联系人', icon: 'none' })
		return false
	}
	if (!formData.emergencyPhone.trim()) {
		uni.showToast({ title: '请填写联系人电话', icon: 'none' })
		return false
	}
	if (!/^1[3-9]\d{9}$/.test(formData.emergencyPhone)) {
		uni.showToast({ title: '请填写正确的手机号码', icon: 'none' })
		return false
	}
	if (!formData.healthStatus) {
		uni.showToast({ title: '请选择健康状态', icon: 'none' })
		return false
	}
	if (!formData.takingMedication) {
		uni.showToast({ title: '请选择是否服药', icon: 'none' })
		return false
	}
	if (!formData.consultationReason.trim()) {
		uni.showToast({ title: '请填写咨询原因', icon: 'none' })
		return false
	}
	return true
}

// 提交表单
const submitForm = async () => {
	if (!validateForm()) {
		return
	}

	try {
		submitting.value = true
		uni.showLoading({ title: '提交中...' })

		// 构建提交数据
		const submitData = {
			orderId: orderId.value,
			...formData,
			medicalHistory: formData.medicalHistory.join(',')
		}

		// 提交到后端
		const res = await submitRegistrationForm(submitData)

		uni.hideLoading()

		if (res.code === 200) {
			// 显示成功弹窗
			successPopup.value?.open()
		} else {
			uni.showToast({
				title: res.msg || '提交失败',
				icon: 'none'
			})
		}
	} catch (error) {
		uni.hideLoading()
		console.error('提交表单失败:', error)
		uni.showToast({
			title: '网络请求失败',
			icon: 'none'
		})
	} finally {
		submitting.value = false
	}
}

// 跳转到我的咨询
const goToMyConsultations = () => {
	successPopup.value?.close()
	uni.navigateTo({
		url: '/pages/consultation/my-consultations/index'
	})
}

// 生命周期
onLoad((options) => {
	orderId.value = options.orderId || ''

	// 如果是支付成功后跳转，显示预约成功弹窗
	if (options.showSuccess === 'true') {
		setTimeout(() => {
			successPopup.value?.open()
		}, 500)
	}
})
</script>

<style lang="scss" scoped>
.registration-form {
	min-height: 100vh;
	background-color: #f8f8f8;
}

// 自定义导航栏
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background-color: #fff;
	border-bottom: 1rpx solid #eee;

	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 32rpx;
		padding-top: var(--status-bar-height);

		.navbar-left {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.navbar-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}

		.navbar-right {
			width: 60rpx;
		}
	}
}

// 表单容器
.form-container {
	padding-top: calc(88rpx + var(--status-bar-height));
	padding-bottom: 120rpx;
}

// 表单区块
.form-section {
	background-color: #fff;
	margin-bottom: 20rpx;
	padding: 0 32rpx;

	.section-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		padding: 32rpx 0 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 24rpx;
	}
}

// 表单项
.form-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}

	.label {
		font-size: 28rpx;
		color: #333;
		width: 160rpx;
		flex-shrink: 0;
	}

	.input {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		text-align: right;

		&::placeholder {
			color: #999;
		}
	}

	.picker-content {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.value {
			font-size: 28rpx;
			color: #333;
			margin-right: 16rpx;

			&.placeholder {
				color: #999;
			}
		}
	}
}

// 复选框组
.checkbox-group {
	display: flex;
	flex-wrap: wrap;
	gap: 24rpx;
	padding: 24rpx 0;

	.checkbox-item {
		display: flex;
		align-items: center;
		width: calc(50% - 12rpx);

		.checkbox {
			width: 32rpx;
			height: 32rpx;
			border: 2rpx solid #ddd;
			border-radius: 6rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;
			background-color: #fff;

			&.checked {
				background-color: #b85a9b;
				border-color: #b85a9b;
			}
		}

		.checkbox-label {
			font-size: 26rpx;
			color: #333;
			flex: 1;
		}
	}
}

// 单选框组
.radio-group {
	display: flex;
	gap: 48rpx;
	padding: 24rpx 0;

	.radio-item {
		display: flex;
		align-items: center;

		.radio {
			width: 32rpx;
			height: 32rpx;
			border: 2rpx solid #ddd;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;
			background-color: #fff;

			&.checked {
				border-color: #b85a9b;
			}

			.radio-inner {
				width: 16rpx;
				height: 16rpx;
				background-color: #b85a9b;
				border-radius: 50%;
			}
		}

		.radio-label {
			font-size: 26rpx;
			color: #333;
		}
	}
}

// 文本域容器
.textarea-container {
	padding: 24rpx 0;

	.textarea {
		width: 100%;
		min-height: 160rpx;
		padding: 20rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		font-size: 26rpx;
		color: #333;
		line-height: 1.6;
		border: 1rpx solid #e9ecef;

		&::placeholder {
			color: #999;
		}
	}
}

// 提交按钮
.submit-container {
	padding: 32rpx;
	background-color: #fff;

	.submit-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #b85a9b 0%, #a04571 100%);
		border-radius: 44rpx;
		border: none;
		font-size: 32rpx;
		font-weight: 600;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;

		&:disabled {
			opacity: 0.6;
		}

		&::after {
			border: none;
		}
	}
}

// 选择器样式
.picker-container {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;

	.picker-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.picker-cancel,
		.picker-confirm {
			font-size: 28rpx;
			color: #b85a9b;
		}

		.picker-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}

	.picker-view {
		height: 400rpx;

		.picker-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
}

// 成功弹窗
.success-modal {
	width: 560rpx;
	background-color: #fff;
	border-radius: 24rpx;
	padding: 60rpx 40rpx 40rpx;
	text-align: center;

	.success-icon {
		width: 120rpx;
		height: 120rpx;
		background-color: rgba(76, 175, 80, 0.1);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 32rpx;
	}

	.success-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 24rpx;
	}

	.success-content {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 48rpx;
	}

	.success-btn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient(135deg, #b85a9b 0%, #a04571 100%);
		border-radius: 40rpx;
		border: none;
		font-size: 28rpx;
		font-weight: 600;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;

		&::after {
			border: none;
		}
	}
}
</style>
