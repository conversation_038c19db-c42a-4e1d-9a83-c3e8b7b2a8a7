<template>
	<view class="search-page">
		<!-- 搜索头部 -->
		<view class="search-header">
			<view class="search-input-wrap">
				<image src="../../static/icon/icon_sousuo.png"></image>
				<input class="search-input" v-model="searchKeyword" placeholder="搜索咨询师、课程、冥想、测评..." confirm-type="search"
					@confirm="handleSearch" @input="onSearchInput" focus />
				<uni-icons v-if="searchKeyword" type="clear" size="16" color="#999" @click="clearSearch" class="clear-icon" />
				<!-- <image @click="clearSearch" class="clear-icon" v-if="searchKeyword" src="../../static/icon/关闭按钮.png"></image> -->
				<button class="search-btn" @click="handleSearch">搜索</button>
			</view>
		</view>

		<!-- 搜索历史 -->
		<view class="search-history" v-if="!searchKeyword && searchHistory.length">
			<view class="history-header">
				<text class="title">搜索历史</text>
				<view class="history-actions">
					<text class="toggle-btn" v-if="searchHistory.length > 6" @click="toggleHistory">{{ isHistoryExpanded ? '收起' :
						'展开' }}</text>
					<image @click="clearHistory" src="../../static/icon/形状 4.png"></image>
				</view>
			</view>
			<view class="history-list" :class="{ 'collapsed': !isHistoryExpanded }">
				<view class="history-item" v-for="(item, index) in displayedHistory" :key="index" @click="useHistoryItem(item)">
					<text class="history-text">{{ item }}</text>
				</view>
			</view>
		</view>

		<!-- 热门搜索 -->
		<view class="hot-search" v-if="!searchKeyword">
			<view class="hot-header">
				<image src="../../static/icon/形状 5.png" />
				<text class="title">热门搜索</text>
			</view>

			<!-- 分类标签页 -->
			<view class="category-tabs">
				<view v-for="(tab, index) in tabs" :key="tab.categoryId" :class="['tab-item', { active: currentTab === index }]"
					@click="switchTab(index, tab.categoryId)">
					{{ tab.name }}
					<image v-if="currentTab === index" class="tab-icon" src="../../static/icon/形状 6.png"></image>
				</view>
			</view>
			<swiper class="hot-swiper" :current="currentHotTab" @change="onHotSwiperChange" circular>
				<swiper-item v-for="(tab, index) in hotTabs" :key="index">
					<view class="hot-card" :class="{ 'gradient-card': index < 3 }">
						<image v-if="index < 3" class="card-icon" :src="tab.icon" mode="aspectFit" />
						<view class="hot-list">
							<view class="hot-item" v-for="(item, idx) in tab.items" :key="idx"
								@click="useHotSearchItem(item.keyword || item, tab.type || 'all')">
								<text class="hot-rank" :class="{
									'top3': idx < 3,
									'rank-first': idx === 0,
									'rank-second': idx === 1,
									'rank-third': idx === 2
								}">{{ idx + 1 }}</text>
								<text class="hot-keyword">{{ item.keyword || item }}</text>
								<text class="hot-count">{{ item.searchCount || item.count || '' }}</text>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
			<!--<view class="swiper-dots">
				<view 
					class="dot" 
					v-for="(tab, index) in hotTabs" 
					:key="index"
					:class="{ 'active': currentHotTab === index }"
				></view>
			</view>-->
		</view>

		<!-- 搜索建议 -->
		<view class="search-suggestions" v-if="showSuggestions && suggestions.length > 0">
			<view class="suggestion-item" v-for="(item, index) in suggestions" :key="index"
				@click="useHistoryItem(item.keyword || item)">
				<uni-icons type="search" size="14" color="#999" />
				<text class="suggestion-text">{{ item.keyword || item }}</text>
			</view>
		</view>

		<!-- 搜索结果 -->
		<view class="search-result" v-if="hasSearched">
			<!-- 加载状态 -->
			<view class="loading-state" v-if="loading">
				<uni-icons type="spinner-cycle" size="20" color="#999" />
				<text>搜索中...</text>
			</view>

			<!-- 搜索结果内容 -->
			<view v-else class="result-content">
				<!-- 搜索结果分类标签页（只在手动搜索时显示） -->
				<view class="result-category-tabs" v-if="!isFromHotSearch">
					<view v-for="(tab, index) in resultTabs" :key="tab.type"
						:class="['tab-item', { active: currentResultTab === index }]" @click="switchResultTab(index)">
						{{ tab.name }}
						<image v-if="currentResultTab === index" class="tab-icon" src="../../static/icon/形状 6-active.png"></image>
					</view>
				</view>

				<!-- 搜索结果列表 -->
				<scroll-view scroll-y class="result-list-container">
					<view class="result-list">
						<view v-if="currentResultList.length === 0" class="empty-state">
							<image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png"
								mode="aspectFit"></image>
							<text>暂无搜索结果</text>
						</view>

						<view v-else class="list-container">
							<UniversalListItem v-for="item in currentResultList" :key="item.id" :item="item" :type="currentResultType"
								@click="goToDetail" />
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'
import {
	globalSearch,
	getHotSearches,
	getSearchSuggestions,
	getUserSearchHistory,
	clearUserSearchHistory,
	saveSearchRecord
} from '@/api/search.js'
import { getCategoryTree } from '@/api/category.js'

const searchKeyword = ref('')
const searchHistory = ref([])
const searchResults = ref([])
const searchCategories = ref([])
const isHistoryExpanded = ref(false)
const currentHotTab = ref(0)
const searchType = ref('all') // all, consultant, course, meditation, assessment
const loading = ref(false)
const suggestions = ref([])
const showSuggestions = ref(false)
const hasSearched = ref(false)
const isFromHotSearch = ref(false) // 标记是否来自热门搜索
const hotSearchType = ref('') // 记录热门搜索的类型

// 分类标签
const tabs = ref([])
const currentTab = ref(0)
const isTabsInitialized = ref(false)
// 热门搜索标签页数据
const hotTabs = ref([])

// 搜索结果分类标签
const resultTabs = ref([
	{ name: '咨询师', type: 'consultant' },
	{ name: '测评', type: 'assessment' },
	{ name: '冥想', type: 'meditation' },
	{ name: '课程', type: 'course' }
])
const currentResultTab = ref(0)

// 分类搜索结果
const consultantResults = ref([])
const assessmentResults = ref([])
const meditationResults = ref([])
const courseResults = ref([])

// 获取所有热门搜索数据
const getAllHotSearchData = () => {
	return [
		{
			name: '全部热搜',
			type: 'all',
			icon: '/static/images/hot-all.png',
			items: [
				{ keyword: '心理咨询师', count: '999+' },
				{ keyword: '失眠', count: '888+' },
				{ keyword: '焦虑症', count: '777+' },
				{ keyword: '情感问题', count: '666+' },
				{ keyword: '职场压力', count: '555+' },
				{ keyword: '婚姻咨询', count: '444+' },
				{ keyword: '青少年心理', count: '333+' },
				{ keyword: '抑郁症', count: '222+' }
			]
		},
		{
			name: '咨询师热搜',
			type: 'consultant',
			icon: '/static/images/hot-counselor.png',
			items: [
				{ keyword: '婚姻咨询师', count: '800+' },
				{ keyword: '青少年心理咨询师', count: '700+' },
				{ keyword: '女咨询师', count: '650+' },
				{ keyword: '抑郁症咨询师', count: '600+' },
				{ keyword: '情感咨询师', count: '550+' },
				{ keyword: '家庭治疗师', count: '500+' },
				{ keyword: '儿童心理咨询师', count: '450+' },
				{ keyword: '认知行为治疗师', count: '400+' }
			]
		},
		{
			name: '课程热搜',
			type: 'course',
			icon: '/static/images/hot-course.png',
			items: [
				{ keyword: '心理学课程', count: '550+' },
				{ keyword: '自我成长', count: '500+' },
				{ keyword: '压力管理', count: '450+' },
				{ keyword: '情绪管理', count: '400+' },
				{ keyword: '人际关系', count: '350+' },
				{ keyword: '亲子教育', count: '300+' },
				{ keyword: '职场心理', count: '250+' },
				{ keyword: '心理健康', count: '200+' }
			]
		},
		{
			name: '冥想热搜',
			type: 'meditation',
			icon: '/static/images/hot-meditation.png',
			items: [
				{ keyword: '放松冥想', count: '450+' },
				{ keyword: '睡眠冥想', count: '400+' },
				{ keyword: '正念冥想', count: '350+' },
				{ keyword: '呼吸冥想', count: '300+' },
				{ keyword: '减压冥想', count: '250+' },
				{ keyword: '专注力训练', count: '200+' },
				{ keyword: '身体扫描', count: '150+' },
				{ keyword: '慈悲冥想', count: '100+' }
			]
		},
		{
			name: '测评热搜',
			type: 'assessment',
			icon: '/static/images/hot-assessment.png',
			items: [
				{ keyword: '心理健康测评', count: '350+' },
				{ keyword: '性格测试', count: '300+' },
				{ keyword: '抑郁测评', count: '250+' },
				{ keyword: '焦虑测评', count: '200+' },
				{ keyword: '压力测评', count: '180+' },
				{ keyword: '情商测试', count: '150+' },
				{ keyword: '职业测评', count: '120+' },
				{ keyword: '人格测试', count: '100+' }
			]
		}
	];
}
// 计算属性
const currentTabType = computed(() => {
	return tabs.value[currentTab.value]?.type || 'consultant';
});

// 计算当前搜索结果类型
const currentResultType = computed(() => {
	return resultTabs.value[currentResultTab.value]?.type || 'all';
});

// 计算当前显示的搜索结果列表
const currentResultList = computed(() => {
	// 如果来自热门搜索，直接显示对应类型的结果
	if (isFromHotSearch.value && hotSearchType.value) {
		switch (hotSearchType.value) {
			case 'consultant':
				return consultantResults.value;
			case 'assessment':
				return assessmentResults.value;
			case 'meditation':
				return meditationResults.value;
			case 'course':
				return courseResults.value;
			default:
				return [
					...consultantResults.value,
					...assessmentResults.value,
					...meditationResults.value,
					...courseResults.value
				];
		}
	}

	// 手动搜索时，根据当前选中的标签显示结果
	const type = currentResultType.value;
	switch (type) {
		case 'consultant':
			return consultantResults.value;
		case 'assessment':
			return assessmentResults.value;
		case 'meditation':
			return meditationResults.value;
		case 'course':
			return courseResults.value;
		default:
			// 全部：合并所有结果
			return [
				...consultantResults.value,
				...assessmentResults.value,
				...meditationResults.value,
				...courseResults.value
			];
	}
});

// 计算展示的历史记录
const displayedHistory = computed(() => {
	return isHistoryExpanded.value ? searchHistory.value : searchHistory.value.slice(0, 6)
})

// 切换历史记录展开/收起
const toggleHistory = () => {
	isHistoryExpanded.value = !isHistoryExpanded.value
}

// 切换搜索结果标签
const switchResultTab = (index) => {
	currentResultTab.value = index
}

// 切换热门搜索标签
const switchHotTab = (index) => {
	currentHotTab.value = index
}

// 热门搜索滑动切换
const onHotSwiperChange = (e) => {
	currentHotTab.value = e.detail.current
}

// 从本地存储和后端获取搜索历史
const loadSearchHistory = async () => {
	try {
		// 优先从后端获取搜索历史（需要登录）
		const res = await getUserSearchHistory(10)
		if (res && res.code === 200 && res.data) {
			searchHistory.value = res.data
			// 同步到本地存储
			uni.setStorageSync('searchHistory', JSON.stringify(res.data))
			return
		}
	} catch (error) {
		console.log('获取后端搜索历史失败，使用本地存储:', error)
	}

	// 后端获取失败时，从本地存储获取
	const history = uni.getStorageSync('searchHistory')
	if (history) {
		try {
			searchHistory.value = JSON.parse(history)
		} catch (e) {
			console.error('解析本地搜索历史失败:', e)
			searchHistory.value = []
		}
	}
}

// 保存搜索历史
const saveSearchHistory = (keyword) => {
	if (!keyword) return
	let history = searchHistory.value
	// 删除已存在的相同关键词
	history = history.filter(item => item !== keyword)
	// 将新关键词添加到开头
	history.unshift(keyword)
	// 只保留最近10条
	history = history.slice(0, 10)
	searchHistory.value = history
	uni.setStorageSync('searchHistory', JSON.stringify(history))
}

// 搜索处理
const handleSearch = async () => {
	if (!searchKeyword.value.trim()) {
		uni.showToast({
			title: '请输入搜索内容',
			icon: 'none'
		})
		return
	}

	loading.value = true
	hasSearched.value = true
	showSuggestions.value = false

	// 如果不是来自热门搜索，重置标记
	if (!isFromHotSearch.value) {
		hotSearchType.value = ''
	}

	try {
		saveSearchHistory(searchKeyword.value)

		// 保存搜索记录到后端
		await saveSearchRecord({
			keyword: searchKeyword.value,
			type: searchType.value
		})

		// 清空之前的搜索结果
		consultantResults.value = []
		assessmentResults.value = []
		meditationResults.value = []
		courseResults.value = []

		// 使用全局搜索接口获取所有类型的搜索结果
		const res = await globalSearch({
			keyword: searchKeyword.value,
			type: 'all',
			pageNum: 1,
			pageSize: 20
		})

		if (res && res.code === 200 && res.data) {
			// 处理搜索结果数据
			const searchData = res.data

			// 如果有 categories 数组，按类型分配结果
			if (searchData.categories && Array.isArray(searchData.categories)) {
				searchData.categories.forEach(category => {
					const items = category.items || []

					switch (category.type) {
						case 'consultant':
							consultantResults.value = items
							break
						case 'assessment':
							assessmentResults.value = items
							break
						case 'meditation':
							meditationResults.value = items
							break
						case 'course':
							courseResults.value = items
							break
					}
				})
			} else {
				// 如果没有 categories，尝试直接使用 data 作为结果
				// 根据 searchType 判断结果类型
				const items = searchData.items || searchData || []
				switch (searchType.value) {
					case 'consultant':
						consultantResults.value = items
						break
					case 'assessment':
						assessmentResults.value = items
						break
					case 'meditation':
						meditationResults.value = items
						break
					case 'course':
						courseResults.value = items
						break
					default:
						// 如果是 'all' 类型，尝试根据 item.type 分配
						items.forEach(item => {
							switch (item.type) {
								case 'consultant':
									consultantResults.value.push(item)
									break
								case 'assessment':
									assessmentResults.value.push(item)
									break
								case 'meditation':
									meditationResults.value.push(item)
									break
								case 'course':
									courseResults.value.push(item)
									break
							}
						})
				}
			}
		}

		// 重置到全部标签
		currentResultTab.value = 0

	} catch (error) {
		console.error('搜索失败:', error)
		// 清空搜索结果
		consultantResults.value = []
		assessmentResults.value = []
		meditationResults.value = []
		courseResults.value = []
		uni.showToast({
			title: '搜索失败，请重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 清空搜索
const clearSearch = () => {
	searchKeyword.value = ''
	searchResults.value = []
	searchCategories.value = []
	consultantResults.value = []
	assessmentResults.value = []
	meditationResults.value = []
	courseResults.value = []
	hasSearched.value = false
	showSuggestions.value = false
	currentResultTab.value = 0
	// 重置热门搜索标记
	isFromHotSearch.value = false
	hotSearchType.value = ''
}

// 清空历史
const clearHistory = () => {
	uni.showModal({
		title: '提示',
		content: '确定要清空搜索历史吗？',
		success: async (res) => {
			if (res.confirm) {
				try {
					// 清除后端搜索历史
					await clearUserSearchHistory()
				} catch (error) {
					console.log('清除后端搜索历史失败:', error)
				}

				// 清除本地搜索历史
				searchHistory.value = []
				uni.removeStorageSync('searchHistory')

				uni.showToast({
					title: '清除成功',
					icon: 'success'
				})
			}
		}
	})
}

// 使用历史记录项
const useHistoryItem = (keyword) => {
	searchKeyword.value = keyword
	// 重置热门搜索标记
	isFromHotSearch.value = false
	hotSearchType.value = ''
	handleSearch()
}

// 使用热门搜索项
const useHotSearchItem = (keyword, type) => {
	searchKeyword.value = keyword

	// 标记为来自热门搜索
	isFromHotSearch.value = true
	hotSearchType.value = type

	// 执行搜索
	handleSearch()
}

// 根据搜索类型获取结果标签的索引
const getResultTabIndexByType = (type) => {
	const typeMap = {
		'consultant': 0,  // 咨询师
		'assessment': 1,  // 测评
		'meditation': 2,  // 冥想
		'course': 3       // 课程
	}
	return typeMap[type] !== undefined ? typeMap[type] : -1
}

// 获取搜索建议
const getSearchSuggestionsData = async (keyword) => {
	if (!keyword || keyword.length < 2) {
		suggestions.value = []
		showSuggestions.value = false
		return
	}

	try {
		const res = await getSearchSuggestions(keyword)
		if (res && res.code === 200) {
			suggestions.value = res.data || []
			showSuggestions.value = suggestions.value.length > 0
		}
	} catch (error) {
		console.error('获取搜索建议失败:', error)
		suggestions.value = []
		showSuggestions.value = false
	}
}


// 加载分类标签
const loadCategories = async () => {
	try {
		const res = await getCategoryTree();
		if (res.code === 200 && res.data && res.data.categories) {
			// 添加"全部"选项，然后添加其他分类
			tabs.value = [
				...res.data.categories.map(category => {
					// 根据分类名称映射到对应的类型
					let type = 'consultant'; // 默认类型
					switch (category.categoryName) {
						case '咨询师':
							type = 'consultant';
							break;
						case '课程':
							type = 'course';
							break;
						case '冥想':
							type = 'meditation';
							break;
						case '测评':
							type = 'assessment';
							break;
					}

					return {
						name: category.categoryName,
						type: type,
						categoryId: category.categoryId
					};
				})
			];
		} else {
			// 如果接口失败，使用默认分类
			tabs.value = [
				{ name: '咨询师', type: 'consultant' },
				{ name: '测评', type: 'assessment' },
				{ name: '冥想', type: 'meditation' },
				{ name: '课程', type: 'course' }
			];
		}

		// 只在首次初始化时加载默认标签的数据
		if (tabs.value.length > 0 && !isTabsInitialized.value) {
			switchTab(0);
			isTabsInitialized.value = true;
		}
	} catch (error) {
		console.error('加载分类标签失败:', error);
		// 使用默认分类
		tabs.value = [
			{ name: '咨询师', type: 'consultant' },
			{ name: '测评', type: 'assessment' },
			{ name: '冥想', type: 'meditation' },
			{ name: '课程', type: 'course' }
		];

		// 只在首次初始化时加载默认标签的数据
		if (tabs.value.length > 0 && !isTabsInitialized.value) {
			switchTab(0);
			isTabsInitialized.value = true;
		}
	}
};


// 切换标签
const switchTab = (index) => {
	currentTab.value = index;

	// 更新搜索类型
	const type = tabs.value[index]?.type;
	searchType.value = type || 'all';

	// 重新加载对应类型的热门搜索数据
	loadHotSearches();
};

// 根据类型更新热门搜索（保留作为备用函数，但现在主要使用 loadHotSearches）
const updateHotSearchByType = (type) => {
	// 根据选中的tab类型，筛选对应的热门搜索数据
	const allHotData = getAllHotSearchData();

	if (type === 'all') {
		// 显示所有类型的热门搜索
		hotTabs.value = allHotData;
	} else {
		// 只显示选中类型的热门搜索
		const filteredData = allHotData.filter(tab =>
			tab.type === type || tab.name.includes(getSearchTypeText(type))
		);
		hotTabs.value = filteredData.length > 0 ? filteredData : [allHotData[0]]; // 如果没有对应数据，显示第一个
	}

	// 重置当前热门搜索tab
	currentHotTab.value = 0;
};


// 加载热门搜索
const loadHotSearches = async () => {
	try {
		const res = await getHotSearches({
			type: searchType.value,
			limit: 10
		})
		if (res && res.code === 200) {
			// 更新热门搜索数据
			const hotData = res.data || []
			if (hotData.length > 0) {
				// 根据新的API格式处理数据
				// 将热门搜索按类型分组
				const groupedData = {}
				hotData.forEach(item => {
					const type = item.searchType || 'all'
					if (!groupedData[type]) {
						groupedData[type] = {
							name: getSearchTypeText(type),
							type: type, // 添加 type 字段
							icon: getSearchTypeIcon(type),
							items: []
						}
					}
					groupedData[type].items.push({
						keyword: item.keyword,
						searchCount: item.searchCount,
						hotScore: item.hotScore
					})
				})

				// 转换为数组格式，按热度排序
				hotTabs.value = Object.values(groupedData).map(group => ({
					...group,
					items: group.items
						.sort((a, b) => (b.hotScore || 0) - (a.hotScore || 0))
						.slice(0, 8) // 每个分类最多显示8个
				}))

				// 如果没有分组数据，使用默认格式
				if (hotTabs.value.length === 0) {
					hotTabs.value = [{
						name: '热门搜索',
						type: 'all', // 添加 type 字段
						icon: '/static/images/hot-search.png',
						items: hotData
							.sort((a, b) => (b.hotScore || 0) - (a.hotScore || 0))
							.slice(0, 10)
							.map(item => ({
								keyword: item.keyword,
								searchCount: item.searchCount,
								hotScore: item.hotScore
							}))
					}]
				}
			} else {
				// 如果API没有数据，使用您提供的测试数据
				loadTestHotSearches()
			}
		} else {
			// API调用失败时使用测试数据
			loadTestHotSearches()
		}
	} catch (error) {
		console.error('获取热门搜索失败:', error)
		// 出错时使用测试数据
		loadTestHotSearches()
	}
}

// 加载测试热门搜索数据
const loadTestHotSearches = () => {
	const testHotData = [
		{
			"id": 1,
			"keyword": "心理咨询师",
			"searchType": "all",
			"searchCount": 150,
			"hotScore": 1500,
			"status": "0"
		},
		{
			"id": 2,
			"keyword": "失眠",
			"searchType": "all",
			"searchCount": 121,
			"hotScore": 1210,
			"status": "0"
		},
		{
			"id": 3,
			"keyword": "焦虑症",
			"searchType": "all",
			"searchCount": 110,
			"hotScore": 1100,
			"status": "0"
		},
		{
			"id": 4,
			"keyword": "情感问题",
			"searchType": "all",
			"searchCount": 95,
			"hotScore": 950,
			"status": "0"
		},
		{
			"id": 5,
			"keyword": "职场压力",
			"searchType": "all",
			"searchCount": 85,
			"hotScore": 850,
			"status": "0"
		},
		{
			"id": 6,
			"keyword": "婚姻咨询师",
			"searchType": "consultant",
			"searchCount": 80,
			"hotScore": 800,
			"status": "0"
		},
		{
			"id": 7,
			"keyword": "青少年心理咨询师",
			"searchType": "consultant",
			"searchCount": 70,
			"hotScore": 700,
			"status": "0"
		},
		{
			"id": 8,
			"keyword": "女咨询师",
			"searchType": "consultant",
			"searchCount": 65,
			"hotScore": 650,
			"status": "0"
		},
		{
			"id": 9,
			"keyword": "抑郁症咨询师",
			"searchType": "consultant",
			"searchCount": 60,
			"hotScore": 600,
			"status": "0"
		},
		{
			"id": 10,
			"keyword": "心理学课程",
			"searchType": "course",
			"searchCount": 55,
			"hotScore": 550,
			"status": "0"
		},
		{
			"id": 11,
			"keyword": "放松冥想",
			"searchType": "meditation",
			"searchCount": 45,
			"hotScore": 450,
			"status": "0"
		},
		{
			"id": 12,
			"keyword": "睡眠冥想",
			"searchType": "meditation",
			"searchCount": 40,
			"hotScore": 400,
			"status": "0"
		},
		{
			"id": 13,
			"keyword": "心理健康测评",
			"searchType": "assessment",
			"searchCount": 35,
			"hotScore": 350,
			"status": "0"
		},
		{
			"id": 14,
			"keyword": "性格测试",
			"searchType": "assessment",
			"searchCount": 30,
			"hotScore": 300,
			"status": "0"
		}
	]

	// 按类型分组处理测试数据
	const groupedData = {}
	testHotData.forEach(item => {
		const type = item.searchType || 'all'
		if (!groupedData[type]) {
			groupedData[type] = {
				name: getSearchTypeText(type),
				type: type, // 添加 type 字段
				icon: getSearchTypeIcon(type),
				items: []
			}
		}
		groupedData[type].items.push({
			keyword: item.keyword,
			searchCount: item.searchCount,
			hotScore: item.hotScore
		})
	})

	// 转换为数组格式，按热度排序
	hotTabs.value = Object.values(groupedData).map(group => ({
		...group,
		items: group.items
			.sort((a, b) => (b.hotScore || 0) - (a.hotScore || 0))
			.slice(0, 8)
	}))

	console.log('加载测试热门搜索数据:', hotTabs.value)
}

// 监听搜索输入变化
const onSearchInput = (e) => {
	searchKeyword.value = e.detail.value

	// 用户开始输入时，重置热门搜索标记
	isFromHotSearch.value = false
	hotSearchType.value = ''

	if (searchKeyword.value.trim()) {
		getSearchSuggestionsData(searchKeyword.value.trim())
	} else {
		// 当输入框为空时，清空所有搜索相关状态
		suggestions.value = []
		showSuggestions.value = false
		searchResults.value = []
		searchCategories.value = []
		consultantResults.value = []
		assessmentResults.value = []
		meditationResults.value = []
		courseResults.value = []
		hasSearched.value = false
		currentResultTab.value = 0
	}
}

// 跳转到详情页
const goToDetail = (item) => {
	let url = ''
	switch (item.type) {
		case 'consultant':
			url = `/pages/classification/counselor-detail/index?id=${item.id}`
			break
		case 'course':
			url = `/pages/course/detail/index?id=${item.id}`
			break
		case 'meditation':
			url = `/pages/meditation/detail/index?id=${item.id}`
			break
		case 'assessment':
			url = `/pages/evaluation/detail/index?id=${item.id}`
			break
		default:
			// 默认跳转到咨询师详情
			url = `/pages/classification/counselor-detail/index?id=${item.id}`
	}

	uni.navigateTo({ url })
}

// 获取项目类型文本
const getItemTypeText = (type) => {
	const typeMap = {
		consultant: '咨询师',
		course: '课程',
		meditation: '冥想',
		assessment: '测评'
	}
	return typeMap[type] || '咨询师'
}

// 格式化浏览次数
const formatViewCount = (count) => {
	if (count >= 10000) {
		return (count / 10000).toFixed(1) + '万'
	} else if (count >= 1000) {
		return (count / 1000).toFixed(1) + 'k'
	}
	return count
}

// 获取搜索类型文本
const getSearchTypeText = (type) => {
	const typeMap = {
		consultant: '咨询师',
		course: '课程',
		meditation: '冥想',
		assessment: '测评'
	}
	return typeMap[type] || '热门搜索'
}

// 获取搜索类型图标
const getSearchTypeIcon = (type) => {
	const iconMap = {
		all: '/static/images/hot-search.png',
		consultant: '/static/images/consultant-icon.png',
		course: '/static/images/course-icon.png',
		meditation: '/static/images/meditation-icon.png',
		assessment: '/static/images/assessment-icon.png'
	}
	return iconMap[type] || '/static/images/hot-search.png'
}

// 处理显示更多
const handleShowMore = (categoryType) => {
	// 切换到对应的搜索类型并重新搜索
	searchType.value = categoryType
	handleSearch()
}



// 页面加载时获取搜索历史
onMounted(() => {
	loadSearchHistory()
	loadCategories()
	// 加载热门搜索数据（使用真实接口）
	loadHotSearches()
})

// 页面加载时处理参数
onLoad((options) => {
	if (options.type) {
		searchType.value = options.type
	}
	if (options.keyword) {
		searchKeyword.value = options.keyword
		handleSearch()
	}
})
</script>

<style lang="scss" scoped>
.search-page {
	min-height: 100vh;
	background-color: #fff;
	padding-top: 1px; // 防止margin塌陷
}

.search-header {
	position: sticky;
	top: 0;
	z-index: 100;
	background-color: #fff;
	padding: 16rpx 32rpx;
	display: flex;
	align-items: center;

	.search-input-wrap {
		flex: 1;
		height: 64rpx;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		position: relative;

		image {
			width: 30rpx;
			height: 30rpx;
			margin-left: 24rpx;
		}

		.search-input {
			flex: 1;
			font-size: 22rpx;
			margin-left: 8rpx;
			padding-right: 40rpx; // 为清除按钮留出空间
		}

		.clear-icon {
			z-index: 999;
			position: absolute;
			right: 124rpx;
			top: 50%;
			transform: translateY(-50%);
			padding: 10rpx; // 增大点击区域
		}
	}

	.search-btn {
		margin: 0;
		padding: 0 30rpx;
		height: 54rpx;
		line-height: 54rpx;
		font-size: 24rpx;
		color: #fff;
		background: #A04571;
		border-radius: 28rpx;
		border: none;

		&::after {
			border: none;
		}

		&:active {
			opacity: 0.8;
		}
	}
}

.search-history {
	margin-top: 64rpx;
	padding: 0 32rpx;

	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		image {
			width: 34rpx;
			height: 34rpx;
		}

		.title {
			font-size: 30rpx;
		}

		.history-actions {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.toggle-btn {
				font-size: 24rpx;
				color: #666;
			}
		}
	}

	.history-list {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;

		&.collapsed {
			max-height: 160rpx;
			overflow: hidden;
		}

		.history-item {
			display: flex;
			align-items: center;
			padding: 10rpx;
			background-color: #F7F7F7;
			border-radius: 4rpx;
			// height: 40rpx;
			// line-height: 40rpx;
			text-align: center;

			.history-text {
				font-size: 22rpx;
				color: #8A8788;
			}
		}
	}
}

.hot-search {
	margin-top: 50rpx;
	padding: 0 32rpx;

	.hot-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		image {
			width: 38rpx;
			height: 38rpx;
		}

		.title {
			font-size: 30rpx;
		}
	}

	.hot-swiper {
		height: 700rpx;
		width: 100%;
	}

	.hot-card {
		background: linear-gradient(to bottom, #f9f9f9 0%, #fff 47%, #ffffff 45%, #ffffff 100%);
		border-radius: 24rpx;
		padding: 24rpx;
		position: relative;
		height: calc(100% - 48rpx);
		padding-top: 15rpx;

		.card-icon {
			width: 48rpx;
			height: 48rpx;
			position: absolute;
			right: 24rpx;
			top: 24rpx;
		}

		.hot-list {
			.hot-item {
				display: flex;
				align-items: center;
				border-bottom: 1rpx solid #f5f5f5;
				padding: 18rpx 0;

				.hot-rank {
					width: 40rpx;
					height: 40rpx;
					font-size: 24rpx;
					color: #999;
					text-align: center;
					line-height: 40rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					&.top3 {
						color: #fff;
						font-weight: bold;
					}

					// 第一名 - 深红色
					&.rank-first {
						background-color: #8c1e54;
						color: #fff;
					}

					// 第二名 - 灰色
					&.rank-second {
						background-color: #d9d9d9;
						color: #fff;
					}

					// 第三名 - 橙色
					&.rank-third {
						background-color: #ffc884;
						color: #fff;
					}
				}

				.hot-keyword {
					flex: 1;
					font-size: 28rpx;
					color: #333;
					margin: 0 20rpx;
				}

				.hot-count {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}

	.swiper-dots {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20rpx;
		gap: 12rpx;

		.dot {
			width: 12rpx;
			height: 12rpx;
			background: #ddd;
			border-radius: 50%;

			&.active {
				width: 24rpx;
				border-radius: 6rpx;
				background: var(--primary, #2c3e50);
			}
		}
	}
}

.search-result {
	padding: 32rpx;

	.loading-state {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 60rpx 0;
		color: #999;
		font-size: 28rpx;

		uni-icons {
			margin-right: 12rpx;
		}
	}
}

// 搜索建议样式
.search-suggestions {
	background-color: #fff;
	border-radius: 0 0 16rpx 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	max-height: 400rpx;
	overflow-y: auto;

	.suggestion-item {
		display: flex;
		align-items: center;
		padding: 24rpx 32rpx;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background-color: #f8f8f8;
		}

		.suggestion-text {
			margin-left: 16rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
}

// 搜索结果样式
.search-result {
	.loading-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 120rpx 0;

		text {
			margin-top: 16rpx;
			font-size: 28rpx;
			color: #999;
		}
	}

	.result-list {
		.result-item {
			display: flex;
			align-items: center;
			padding: 24rpx 32rpx;
			border-bottom: 1rpx solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}

			.item-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
				margin-right: 24rpx;
			}

			.item-info {
				flex: 1;

				.item-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
				}

				.item-desc {
					font-size: 26rpx;
					color: #666;
					line-height: 1.4;
					margin-bottom: 12rpx;
				}

				.item-meta {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.item-type {
						font-size: 24rpx;
						color: #20a3f3;

						background-color: #e8f6ff;
						padding: 4rpx 12rpx;
						border-radius: 12rpx;
					}

					.item-price {
						font-size: 28rpx;
						color: #ff6b35;
						font-weight: 600;
					}
				}
			}
		}
	}

	.no-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 120rpx 32rpx;
		text-align: center;

		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 32rpx;
		}

		.no-result-text {
			font-size: 32rpx;
			color: #333;
			margin-bottom: 16rpx;
		}

		.no-result-tip {
			font-size: 28rpx;
			color: #999;
		}
	}
}

// 分类标签样式（与首页一致）
.category-tabs {
	display: flex;
	background: #fff;
	align-items: center;
	margin-bottom: 15rpx;

	.tab-item {
		margin-right: 54rpx;
		position: relative;
		font-size: 24rpx;
		color: #8A8788;
		display: flex;
		flex-direction: column;
		align-items: center;

		&.active {
			font-weight: 500;
			font-size: 28rpx;
			color: #000;
		}

		.tab-icon {
			width: 28rpx;
			height: 12rpx;
		}
	}
}

// 搜索结果分类标签样式
.result-category-tabs {
	display: flex;
	background: #fff;

	// border-bottom: 1px solid #eee;
	.tab-item {
		position: relative;
		// padding: 24rpx 32rpx;
		font-size: 28rpx;
		color: #8A8788;
		position: relative;
		margin-right: 138rpx;

		&.active {
			font-size: 32rpx;
			color: #A04571;
			display: flex;
			flex-direction: column;
			align-items: center;

			.tab-icon {
				width: 28rpx;
				height: 12rpx;
			}
		}
	}

	.tab-item:last-child {
		margin-right: 0;
	}

	.tab-icon {
		width: 26rpx;
		height: 26rpx;
	}
}

// 搜索结果内容样式
.result-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: calc(100vh - 200rpx);
}

.result-list-container {
	flex: 1;
	margin-top: 23rpx;
}

.result-list {
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 0;

		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 32rpx;
		}

		text {
			font-size: 28rpx;
			color: #999;
		}
	}

	.list-container {
		padding-bottom: 40rpx;
	}
}
</style>