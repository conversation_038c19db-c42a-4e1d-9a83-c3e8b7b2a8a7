<template>
  <view class="orders-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <image src="/static/icon/search.png" mode="aspectFit"></image>
        <input type="text" placeholder="搜索来访者姓名或手机号" v-model="searchKeyword" @input="onSearchInput" />
      </view>
      <view class="filter-btn" @click="showFilterModal">
        <image src="/static/icon/filter.png" mode="aspectFit"></image>
        <text>筛选</text>
      </view>
    </view>

    <!-- 状态标签 -->
    <view class="status-tabs">
      <view class="tab-item" v-for="(tab, index) in statusTabs" :key="index" :class="{ active: currentTab === index }"
        @click="switchTab(index)">
        <text>{{ tab.name }}</text>
        <text class="tab-count" v-if="tab.count > 0">{{ tab.count }}</text>
      </view>
    </view>

    <!-- 订单列表 -->
    <scroll-view class="order-list" scroll-y @scrolltolower="loadMore" :refresher-enabled="true"
      :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
      <view class="order-item" v-for="order in orderList" :key="order.id" @click="goToDetail(order.id)">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="client-info">
            <image class="avatar" :src="order.userAvatar || '/static/icon/default-avatar.png'" mode="aspectFill">
            </image>
            <view class="info">
              <text class="name">{{ order.userName || order.clientName }}</text>
              <text class="phone">{{ formatPhone(order.userPhone || order.clientPhone) }}</text>
            </view>
          </view>
          <view class="order-status" :class="getStatusClass(order.status)">
            {{ getStatusText(order.status) }}
          </view>
        </view>

        <!-- 预约信息 -->
        <view class="appointment-info">
          <view class="info-row">
            <text class="label">预约时间：</text>
            <text class="value">{{ formatDateTime(order.scheduledTime) }}</text>
          </view>
          <view class="info-row">
            <text class="label">咨询类型：</text>
            <text class="value">{{ order.consultType }}</text>
          </view>
          <view class="info-row">
            <text class="label">咨询时长：</text>
            <text class="value">{{ order.duration }}分钟</text>
          </view>
          <view class="info-row">
            <text class="label">订单号：</text>
            <text class="value">{{ order.orderNo }}</text>
          </view>
        </view>

        <!-- 订单金额 -->
        <view class="order-amount">
          <text class="label">实付：</text>
          <text class="amount">￥{{ order.paymentAmount }}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="order-actions">
          <!-- 待确认状态：已支付，需要咨询师确认 -->
          <button class="btn-success" v-if="order.status === '已支付'" @click.stop="confirmOrder(order)">
            确认接单
          </button>
          <button class="btn-danger" v-if="order.status === '已支付'" @click.stop="rejectOrder(order)">
            拒绝订单
          </button>

          <!-- 待咨询状态：等待咨询开始 -->
          <button class="btn-primary" v-if="order.status === '待咨询'" @click.stop="startConsultation(order)">
            开始咨询
          </button>

          <!-- 咨询中状态：正在进行咨询 -->
          <button class="btn-warning" v-if="order.status === '咨询中'" @click.stop="completeOrder(order)">
            完成咨询
          </button>

          <!-- 已完成状态：查看记录 -->
          <button class="btn-info" v-if="order.status === '已完成'" @click.stop="viewRecord(order)">
            查看记录
          </button>
        </view>
      </view>

      <!-- 加载更多 -->
      <!-- <view class="load-more" v-if="hasMore">
        <text>加载更多...</text>
      </view> -->

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && orderList.length > 0">
        <text>没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="orderList.length === 0 && !loading">
        <image src="/static/icon/empty-order.png" mode="aspectFit"></image>
        <text>暂无订单</text>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <text class="modal-close" @click="closeFilterModal">×</text>
        </view>

        <view class="modal-content">
          <!-- 价格范围 -->
          <view class="filter-section">
            <text class="section-title">价格范围（元）</text>
            <view class="price-range">
              <input type="number" placeholder="最低价格" v-model="filterForm.minPrice" class="price-input" />
              <text class="range-separator">-</text>
              <input type="number" placeholder="最高价格" v-model="filterForm.maxPrice" class="price-input" />
            </view>
          </view>

          <!-- 日期范围 -->
          <view class="filter-section">
            <text class="section-title">创建日期</text>
            <view class="date-range">
              <picker mode="date" :value="filterForm.startDate" @change="onStartDateChange">
                <view class="date-picker">
                  {{ filterForm.startDate || '开始日期' }}
                </view>
              </picker>
              <text class="range-separator">至</text>
              <picker mode="date" :value="filterForm.endDate" @change="onEndDateChange">
                <view class="date-picker">
                  {{ filterForm.endDate || '结束日期' }}
                </view>
              </picker>
            </view>
          </view>

          <!-- 排序方式 -->
          <view class="filter-section">
            <text class="section-title">排序方式</text>
            <view class="sort-options">
              <view class="sort-item" v-for="sort in sortOptions" :key="sort.value"
                :class="{ active: filterForm.orderBy === sort.value }" @click="selectSort(sort.value)">
                {{ sort.label }}
              </view>
            </view>
          </view>

          <!-- 排序顺序 -->
          <view class="filter-section">
            <text class="section-title">排序顺序</text>
            <view class="sort-order">
              <view class="order-item" :class="{ active: filterForm.sortOrder === 'desc' }"
                @click="filterForm.sortOrder = 'desc'">
                降序
              </view>
              <view class="order-item" :class="{ active: filterForm.sortOrder === 'asc' }"
                @click="filterForm.sortOrder = 'asc'">
                升序
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn-reset" @click="resetFilter">重置</button>
          <button class="btn-apply" @click="applyFilter">应用筛选</button>
        </view>
      </view>
    </uni-popup>

    <cc-myTabbar :tabBarShow="1"></cc-myTabbar>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getConsultantOrders,
  filterConsultantOrdersByPriceAndDate,
  confirmConsultantOrder,
  rejectConsultantOrder,
  getOrderStatusCount
} from '@/api/consultant-app.js'

// 响应式数据
const searchKeyword = ref('')
const currentTab = ref(0)
const orderList = ref([])
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const pageNum = ref(1)
const pageSize = ref(10)

// 筛选相关
const filterPopup = ref(null)
const isFiltering = ref(false)
const filterForm = ref({
  minPrice: '',
  maxPrice: '',
  startDate: '',
  endDate: '',
  orderBy: 'createTime',
  sortOrder: 'desc'
})

// 排序选项
const sortOptions = ref([
  { label: '创建时间', value: 'createTime' },
  { label: '支付金额', value: 'paymentAmount' },
  { label: '预约时间', value: 'scheduledTime' },
  { label: '支付时间', value: 'paymentTime' },
  { label: '更新时间', value: 'updateTime' }
])

// 状态标签
const statusTabs = ref([
  { name: '全部', value: '', count: 0 },
  { name: '待确认', value: '已支付', count: 0 },
  { name: '待咨询', value: '待咨询', count: 0 },
  { name: '咨询中', value: '咨询中', count: 0 },
  { name: '已完成', value: '已完成', count: 0 },
  { name: '已取消', value: '已取消', count: 0 }
])

// 生命周期
onMounted(() => {
  loadOrders()
  loadStatusCounts()
})

onShow(() => {
  loadOrders()
  loadStatusCounts()
})

// 加载订单列表
const loadOrders = async (isRefresh = false) => {
  if (loading.value) return

  loading.value = true

  try {
    if (isRefresh) {
      pageNum.value = 1
      hasMore.value = true
    }

    // 检查是否有筛选条件
    const hasFilterConditions = filterForm.value.minPrice ||
      filterForm.value.maxPrice ||
      filterForm.value.startDate ||
      filterForm.value.endDate

    let res
    if (hasFilterConditions) {
      // 使用筛选接口
      const filterParams = {
        minPrice: filterForm.value.minPrice || undefined,
        maxPrice: filterForm.value.maxPrice || undefined,
        startDate: filterForm.value.startDate || undefined,
        endDate: filterForm.value.endDate || undefined,
        status: statusTabs.value[currentTab.value].value || undefined,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        orderBy: filterForm.value.orderBy,
        sortOrder: filterForm.value.sortOrder
      }

      // 移除undefined值
      Object.keys(filterParams).forEach(key => {
        if (filterParams[key] === undefined) {
          delete filterParams[key]
        }
      })

      res = await filterConsultantOrdersByPriceAndDate(filterParams)
      isFiltering.value = true
    } else {
      // 使用普通接口
      const params = {
        status: statusTabs.value[currentTab.value].value || undefined
      }

      // 移除undefined值
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key]
        }
      })
      res = await getConsultantOrders(params)
      isFiltering.value = false
    }

    if (res.code === 200) {
      let newOrders = []

      if (hasFilterConditions) {
        // 筛选接口返回的数据结构
        newOrders = res.data.orders || []
        // 更新分页信息
        if (res.data.totalPages) {
          hasMore.value = res.data.currentPage < res.data.totalPages
        } else {
          hasMore.value = newOrders.length === pageSize.value
        }
      } else {
        // 普通接口直接返回订单数组
        newOrders = Array.isArray(res.data) ? res.data : []
        hasMore.value = newOrders.length === pageSize.value
      }

      if (isRefresh) {
        orderList.value = newOrders
      } else {
        orderList.value.push(...newOrders)
      }

      // 加载状态标签计数
      loadStatusCounts()
    }
  } catch (error) {
    console.error('加载订单失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 加载状态标签计数
const loadStatusCounts = async () => {
  try {
    const res = await getOrderStatusCount()
    if (res.code === 200) {
      const counts = res.data
      // 根据新接口的状态名称更新计数
      statusTabs.value[0].count = Object.values(counts).reduce((sum, count) => sum + count, 0) // 全部
      statusTabs.value[1].count = counts['待确认'] || 0 // 待确认
      statusTabs.value[2].count = counts['待咨询'] || 0 // 待咨询
      statusTabs.value[3].count = counts['咨询中'] || 0 // 咨询中
      statusTabs.value[4].count = counts['已完成'] || 0 // 已完成
      statusTabs.value[5].count = counts['已取消'] || 0 // 已取消
    }
  } catch (error) {
    console.error('加载状态计数失败:', error)
  }
}



// 切换标签
const switchTab = (index) => {
  if (currentTab.value === index) return

  currentTab.value = index
  pageNum.value = 1
  orderList.value = []
  loadOrders(true)
}

// 搜索输入
const onSearchInput = () => {
  // 防抖处理
  clearTimeout(onSearchInput.timer)
  onSearchInput.timer = setTimeout(() => {
    pageNum.value = 1
    orderList.value = []
    loadOrders(true)
  }, 500)
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  loadOrders(true)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    pageNum.value++
    loadOrders()
  }
}

// 显示筛选弹窗
const showFilterModal = () => {
  filterPopup.value?.open()
}

// 关闭筛选弹窗
const closeFilterModal = () => {
  filterPopup.value?.close()
}

// 开始日期选择
const onStartDateChange = (e) => {
  filterForm.value.startDate = e.detail.value
}

// 结束日期选择
const onEndDateChange = (e) => {
  filterForm.value.endDate = e.detail.value
}

// 选择排序方式
const selectSort = (sortValue) => {
  filterForm.value.orderBy = sortValue
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.value = {
    minPrice: '',
    maxPrice: '',
    startDate: '',
    endDate: '',
    orderBy: 'createTime',
    sortOrder: 'desc'
  }
}

// 应用筛选
const applyFilter = () => {
  // 验证价格范围
  if (filterForm.value.minPrice && filterForm.value.maxPrice) {
    const minPrice = parseFloat(filterForm.value.minPrice)
    const maxPrice = parseFloat(filterForm.value.maxPrice)
    if (minPrice > maxPrice) {
      uni.showToast({
        title: '最小价格不能大于最大价格',
        icon: 'none'
      })
      return
    }
  }

  // 验证日期范围
  if (filterForm.value.startDate && filterForm.value.endDate) {
    if (new Date(filterForm.value.startDate) > new Date(filterForm.value.endDate)) {
      uni.showToast({
        title: '开始日期不能晚于结束日期',
        icon: 'none'
      })
      return
    }
  }

  closeFilterModal()
  pageNum.value = 1
  orderList.value = []
  loadOrders(true)
}

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${month}/${day} ${hour}:${minute}`
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    '待支付': 'status-pending',
    '已支付': 'status-pending',
    '待咨询': 'status-ongoing',
    '咨询中': 'status-ongoing',
    '已完成': 'status-completed',
    '已取消': 'status-cancelled'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '待支付': '待支付',
    '已支付': '待确认',
    '待咨询': '待咨询',
    '咨询中': '咨询中',
    '已完成': '已完成',
    '已取消': '已取消'
  }
  return statusMap[status] || status || '未知'
}

// 确认订单
const confirmOrder = async (order) => {
  try {
    const res = await confirmConsultantOrder(order.id)
    if (res.code === 200) {
      uni.showToast({
        title: '确认成功',
        icon: 'success'
      })
      loadOrders(true)
    } else {
      uni.showToast({
        title: res.msg || '确认失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('确认订单失败:', error)
    uni.showToast({
      title: '确认失败',
      icon: 'none'
    })
  }
}

// 拒绝订单
const rejectOrder = async (order) => {
  uni.showModal({
    title: '拒绝订单',
    content: '请输入拒绝原因',
    editable: true,
    placeholderText: '请输入拒绝原因',
    success: async (res) => {
      if (res.confirm) {
        try {
          const rejectReason = res.content || '咨询师拒绝'
          const result = await rejectConsultantOrder(order.id, rejectReason)
          if (result.code === 200) {
            uni.showToast({
              title: '已拒绝订单',
              icon: 'success'
            })
            loadOrders(true)
          } else {
            uni.showToast({
              title: result.msg || '操作失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('拒绝订单失败:', error)
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 开始咨询
const startConsultation = (order) => {
  uni.showModal({
    title: '开始咨询',
    content: `确定要开始与${order.clientName}的咨询吗？`,
    success: (res) => {
      if (res.confirm) {
        // 跳转到咨询页面或更新状态为咨询中
        uni.navigateTo({
          url: `/pages/consultation-room/index?orderId=${order.id}`
        })
      }
    }
  })
}

// 回复咨询
const replyConsultation = (order) => {
  uni.navigateTo({
    url: `/pages/consultation-order/consultation-reply/index?orderId=${order.id}`
  })
}

// 查看记录
const viewRecord = (order) => {
  uni.navigateTo({
    url: `/pages/consultation-order/consultation-record/index?orderId=${order.id}`
  })
}

// 完成订单
const completeOrder = (order) => {
  uni.navigateTo({
    url: `/pages/consultation-order/consultation-form/index?orderId=${order.id}`
  })
}

// 跳转到详情
const goToDetail = (orderId) => {
  uni.navigateTo({
    url: `/pages/consultation-order/order-detail/index?orderId=${orderId}`
  })
}
</script>

<style scoped lang="scss">
.orders-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 25rpx;
    padding: 0 20rpx;
    height: 70rpx;

    image {
      width: 30rpx;
      height: 30rpx;
      margin-right: 15rpx;
    }

    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
  }

  .filter-btn {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    padding: 15rpx 20rpx;
    background-color: #1890ff;
    border-radius: 20rpx;

    image {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }

    text {
      font-size: 24rpx;
      color: #fff;
    }
  }
}

.status-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
    position: relative;

    text {
      font-size: 28rpx;
      color: #666;
    }

    .tab-count {
      background-color: #ff4d4f;
      color: #fff;
      font-size: 20rpx;
      min-width: 32rpx;
      height: 32rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8rpx;
      padding: 0 6rpx;
    }

    &.active {
      text {
        color: #1890ff;
        font-weight: bold;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #1890ff;
        border-radius: 2rpx;
      }
    }
  }
}

.order-list {
  height: calc(100vh - 200rpx);
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .client-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .info {
        .name {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }

        .phone {
          font-size: 24rpx;
          color: #666;
        }
      }
    }

    .order-status {
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 24rpx;

      &.status-pending {
        background-color: #fff7e6;
        color: #fa8c16;
      }

      &.status-ongoing {
        background-color: #f6ffed;
        color: #52c41a;
      }

      &.status-completed {
        background-color: #f0f0f0;
        color: #666;
      }

      &.status-cancelled {
        background-color: #fff2f0;
        color: #ff4d4f;
      }
    }
  }

  .appointment-info {
    margin-bottom: 20rpx;

    .info-row {
      display: flex;
      margin-bottom: 12rpx;

      .label {
        font-size: 26rpx;
        color: #666;
        width: 160rpx;
      }

      .value {
        font-size: 26rpx;
        color: #333;
        flex: 1;
      }
    }
  }

  .order-amount {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .label {
      font-size: 26rpx;
      color: #666;
    }

    .amount {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff4d4f;
      margin-left: 10rpx;
    }
  }

  .order-actions {
    display: flex;
    gap: 20rpx;

    button {
      flex: 1;
      padding: 20rpx 0;
      border-radius: 12rpx;
      font-size: 26rpx;
      border: none;

      &.btn-primary {
        background-color: #1890ff;
        color: #fff;
      }

      &.btn-secondary {
        background-color: #f5f5f5;
        color: #666;
      }

      &.btn-info {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.btn-warning {
        background-color: #fff7e6;
        color: #fa8c16;
      }

      &.btn-success {
        background-color: #f6ffed;
        color: #52c41a;
      }

      &.btn-danger {
        background-color: #fff2f0;
        color: #ff4d4f;
      }
    }
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  text {
    color: #999;
    font-size: 28rpx;
  }
}

// 筛选弹窗样式
.filter-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .modal-close {
      font-size: 40rpx;
      color: #999;
    }
  }

  .modal-content {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;

    .filter-section {
      margin-bottom: 40rpx;

      .section-title {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        font-weight: 500;
      }

      .price-range,
      .date-range {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .price-input,
        .date-picker {
          flex: 1;
          padding: 20rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          font-size: 28rpx;
          text-align: center;
        }

        .range-separator {
          font-size: 24rpx;
          color: #666;
        }
      }

      .sort-options {
        display: flex;
        flex-wrap: wrap;
        gap: 15rpx;

        .sort-item {
          padding: 16rpx 24rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 20rpx;
          font-size: 24rpx;
          color: #666;
          background-color: #fff;

          &.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
          }
        }
      }

      .sort-order {
        display: flex;
        gap: 20rpx;

        .order-item {
          flex: 1;
          padding: 20rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          text-align: center;
          font-size: 28rpx;
          color: #666;
          background-color: #fff;

          &.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .btn-reset,
    .btn-apply {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }

    .btn-reset {
      background-color: #f5f5f5;
      color: #666;
    }

    .btn-apply {
      background-color: #1890ff;
      color: #fff;
    }
  }
}
</style>