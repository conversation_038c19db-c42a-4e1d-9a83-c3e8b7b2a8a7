<template>
	<view class="universal-list-item" @click="handleClick">
		<!-- 左侧图片 -->
		<view class="item-left" :style="imageAreaStyle">
			<image class="avatar" mode="widthFix" :src="getImageUrl()" @error="handleImageError"></image>
		</view>

		<!-- 右侧内容 -->
		<view class="item-right" :style="contentAreaStyle">
			<!-- 标题行 -->
			<view class="name-box">
				<view class="name-grade">
					<view class="name">{{ getDisplayName() }}</view>
					<view v-if="getGradeText" class="grade">{{ getGradeText }}</view>
				</view>
			</view>

			<!-- 统计信息行 -->
			<view class="stats-row">
				<view class="stats-container" v-if="props.type !== 'assessment'">
					<view v-for="(stat, index) in getStatsArray()" :key="index" class="stat-item">
						<text class="stat-value">{{ stat.value }}</text>
						<text class="stat-label">{{ stat.label }}</text>
					</view>
				</view>
				<scroll-view v-else scroll-x="true" class="professional-box">
					<!-- 专业型标签 -->
					<text v-for="(tag, index) in getAssessmentProfessionalTags()" :key="'prof-' + index" :class="[
						'professional-tag',
						'professional-type-tag'
					]">
						{{ tag.text }}
					</text>
				</scroll-view>
				<!-- 价格信息 -->
				<view class="price-box">
					<view v-if="getActualPrice() && parseFloat(getActualPrice()) > 0" class="price-main">
						<text class="currency-symbol">¥</text>
						<text class="price-integer">{{ getPriceInteger() }}</text>
						<text v-if="getPriceDecimal()" class="price-dot">.</text>
						<text v-if="getPriceDecimal()" class="price-decimal">{{ getPriceDecimal() }}</text>
						<text class="price-slash">/</text>
						<text class="price-unit">{{ getPriceUnit() }}</text>
					</view>
					<view v-if="getOriginalPrice() && getOriginalPrice() > getActualPrice()" class="original-price">¥{{
						getOriginalPrice() }}</view>
					<!-- 免费时不显示任何价格信息 -->
				</view>
			</view>

			<!-- 描述信息 -->
			<view class="info">
				<rich-text v-if="item.highlightDescription" :nodes="getDescription()" class="description-text"></rich-text>
				<text v-else class="description-text">{{ getDescription() }}</text>
			</view>

			<!-- 标签列表 -->
			<scroll-view v-if="getTagList().length > 0" scroll-x="true" class="professional-box">
				<text v-for="(tag, index) in getTagList()" :key="index" :class="[
					'professional-tag',
					tag.type === 'free' ? 'free-tag' : '',
					tag.type === 'paid' ? 'paid-tag' : '',
					type === 'assessment' && tag.type === 'question' ? 'assessment-question-tag' : '',
					type === 'assessment' && tag.type === 'duration' ? 'assessment-duration-tag' : ''
				]">
					{{ typeof tag === 'object' ? tag.text : tag }}
				</text>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
	item: {
		type: Object,
		required: true
	},
	type: {
		type: String,
		default: 'consultant' // 'consultant', 'meditation', 'course', 'assessment'
	},
	dictData: {
		type: Object,
		default: () => ({})
	},
	imageSize: {
		type: Number,
		default: 186 // 默认186rpx，可以传入128等其他尺寸
	}
})

// Emits
const emit = defineEmits(['click'])

// 获取图片URL
const getImageUrl = () => {
	const defaultImages = {
		consultant: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
		meditation: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
		course: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
		assessment: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
	}

	// 优先使用实体对象中的图片
	if (props.item.consultant?.avatar) return props.item.consultant.avatar
	if (props.item.assessment?.imageUrl) return props.item.assessment.imageUrl
	if (props.item.course?.coverImage) return props.item.course.coverImage
	if (props.item.meditation?.coverImage) return props.item.meditation.coverImage

	// 其次使用搜索结果中的图片字段
	return props.item.coverImage || props.item.avatar || props.item.cover || props.item.imageUrl || defaultImages[props.type]
}

// 处理图片加载错误
const handleImageError = (e) => {
	const defaultImages = {
		consultant: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
		meditation: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
		course: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
		assessment: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
	}

	e.target.src = defaultImages[props.type]
}

// 获取显示名称
const getDisplayName = () => {
	// 优先使用实体对象中的名称
	if (props.item.consultant?.name) return props.item.consultant.name
	if (props.item.assessment?.name) return props.item.assessment.name
	if (props.item.course?.title) return props.item.course.title
	if (props.item.meditation?.title) return props.item.meditation.title

	// 其次使用搜索结果中的标题字段
	return props.item.title || props.item.scaleName || props.item.name || '未知'
}

// 获取等级文本 - 使用计算属性确保响应式更新
const getGradeText = computed(() => {
	if (props.type === 'consultant') {
		// 获取咨询师等级，支持多种字段名
		const level = props.item.counselorLevel ||
			props.item.level ||
			props.item.consultant?.personalTitle ||
			props.item.personalTitle ||
			props.item.consultant?.counselorLevel ||
			props.item.consultant?.level
		return getPsy_consultant_level(level)
	}

	if (props.type === 'assessment') {
		// 测评显示适用年龄
		const age = props.item.assessment?.applicableAge || props.item.applicableAge
		if (age) return age
	}

	if (props.type === 'course') {
		// 课程显示难度等级
		const difficulty = props.item.course?.difficulty || props.item.difficulty
		if (difficulty) return difficulty
	}

	// 其他类型的等级标识
	if (props.item.level) return props.item.level
	if (props.item.difficulty) return props.item.difficulty
	if (props.item.grade) return props.item.grade

	return ''
})

// 咨询师等级转换函数
const getPsy_consultant_level = (level) => {
	// 如果 level 为空或未定义，返回空字符串
	if (level === undefined || level === null || level === '') {
		return ''
	}

	// 优先使用传入的字典数据
	if (props.dictData.psy_consultant_level && Array.isArray(props.dictData.psy_consultant_level)) {
		const found = props.dictData.psy_consultant_level.find((item) => item.dictValue == level)
		if (found?.dictLabel) {
			return found.dictLabel
		}
	}

	// 降级处理：使用默认映射（根据您提供的字典数据）
	const levelMap = {
		'0': '咨询助理',
		'1': '初级咨询师',
		'2': '中级咨询师',
		'3': '成熟咨询师',
		'4': '高级咨询师',
		'5': '资深咨询师',
		'6': '咨询督导'
	}

	// 转换为字符串进行匹配
	const levelStr = String(level)
	return levelMap[levelStr] || levelStr || ''
}

// 获取统计信息数组（用于分列显示）
const getStatsArray = () => {
	let stats = []

	if (props.type === 'consultant') {
		// 咨询师统计信息 - 使用实体对象中的详细数据
		const consultant = props.item.consultant || props.item
		const currentYear = new Date().getFullYear()

		// 从业经验
		const startYear = consultant.startYear || consultant.practiceStartYear
		const experience = startYear ? currentYear - parseInt(startYear) : 0
		if (experience > 0) {
			stats.push({ value: `${experience}年`, label: '从业经验' })
		}

		// 咨询人数
		const serviceCount = consultant.serviceCount || consultant.totalCases || consultant.consultationCount
		if (serviceCount) {
			stats.push({ value: `${serviceCount}+`, label: '咨询人数' })
		}

		// 评分
		const rating = consultant.rating || consultant.averageRating
		if (rating) {
			stats.push({ value: `${rating}分`, label: '评分' })
		}
	} else if (props.type === 'course') {
		// 课程统计信息
		const course = props.item.course || props.item

		// 章节数
		const chapterCount = course.lessonCount || course.chapterCount || course.sectionCount
		if (chapterCount) {
			stats.push({ value: `${chapterCount}章`, label: '总章节' })
		}

		// 学习人数
		const studentCount = course.studentCount || course.enrollCount || course.learnerCount
		if (studentCount) {
			stats.push({ value: `${studentCount}+`, label: '学习人数' })
		}

		// 评分
		const rating = course.rating || course.averageRating
		if (rating) {
			stats.push({ value: `${rating}分`, label: '评分' })
		}
	} else if (props.type === 'meditation') {
		// 冥想统计信息
		const meditation = props.item.meditation || props.item

		// 时长
		const duration = meditation.duration
		if (duration) {
			stats.push({ value: `${duration}min`, label: '时长' })
		}

		// 使用人数
		const playCount = meditation.playCount || meditation.favoriteCount || meditation.userCount
		if (playCount) {
			stats.push({ value: `${playCount}+`, label: '使用人数' })
		}

		// 评分
		const rating = meditation.rating || meditation.averageRating
		if (rating) {
			stats.push({ value: `${rating}分`, label: '评分' })
		}
	} else if (props.type === 'assessment') {
		// 测评统计信息
		const assessment = props.item.assessment || props.item

		// 题目数量
		const questionCount = assessment.questionCount
		if (questionCount) {
			stats.push({ value: `${questionCount}题`, label: '题目数' })
		}

		// 测试人数（使用viewCount作为参考）
		const testCount = assessment.monthTestCount || assessment.todayTestCount || props.item.viewCount
		if (testCount) {
			stats.push({ value: `${testCount}+`, label: '测试人数' })
		}

		// 评分
		const rating = props.item.rating || assessment.rating
		if (rating) {
			stats.push({ value: `${rating}分`, label: '评分' })
		}
	}

	return stats.slice(0, 2) // 最多显示2个统计项
}

// 获取描述信息
const getDescription = () => {
	// 优先使用高亮描述（搜索结果中的高亮文本）
	if (props.item.highlightDescription) {
		return props.item.highlightDescription
	}

	// 根据类型获取相应的描述信息
	if (props.type === 'consultant') {
		const consultant = props.item.consultant || props.item
		return consultant.personalIntro || consultant.introduction || consultant.description || consultant.summary || ''
	} else if (props.type === 'assessment') {
		const assessment = props.item.assessment || props.item
		return assessment.description || assessment.introduction || assessment.summary || ''
	} else if (props.type === 'course') {
		const course = props.item.course || props.item
		return course.description || course.introduction || course.summary || ''
	} else if (props.type === 'meditation') {
		const meditation = props.item.meditation || props.item
		return meditation.description || meditation.introduction || meditation.summary || ''
	}

	// 通用描述字段
	return props.item.description || props.item.intro || props.item.summary || ''
}

// 获取标签列表
const getTagList = () => {
	let tags = []

	if (props.type === 'consultant') {
		// 咨询师的专业领域
		const consultant = props.item.consultant || props.item
		if (consultant.consultStyles && Array.isArray(consultant.consultStyles)) {
			tags = consultant.consultStyles.map(style => style.dictLabel || style.name || style)
		} else if (consultant.specialties && Array.isArray(consultant.specialties)) {
			tags = consultant.specialties.map(specialty => specialty.name || specialty)
		}
	} else if (props.type === 'course') {
		// 课程的标签
		const course = props.item.course || props.item
		tags = []

		// 添加收费/免费标签
		const isFree = course.free || course.isFree || (course.price && parseFloat(course.price) === 0) || parseFloat(props.item.price) === 0
		if (isFree) {
			tags.push({ text: '免费', type: 'free' })
		} else {
			tags.push({ text: '收费', type: 'paid' })
		}

		// 添加课程时长
		const duration = course.totalDuration || course.duration
		if (duration) {
			tags.push({ text: `${duration}分钟`, type: 'duration' })
		}

		// 添加其他标签 - 处理字符串格式的tags
		let otherTags = course.tags || course.categories || props.item.tags || []

		// 如果tags是字符串格式，需要解析
		if (typeof otherTags === 'string') {
			try {
				// 尝试解析JSON格式
				otherTags = JSON.parse(otherTags)
			} catch (e) {
				// 如果不是JSON，按逗号分割
				otherTags = otherTags.split(',').map(tag => tag.trim()).filter(Boolean)
			}
		}

		if (Array.isArray(otherTags)) {
			tags = tags.concat(otherTags.slice(0, 2)) // 最多再添加2个标签
		}
	} else if (props.type === 'meditation') {
		// 冥想的标签
		const meditation = props.item.meditation || props.item
		tags = []

		// 添加收费/免费标签
		const isFree = meditation.free || meditation.isFree || (meditation.price && parseFloat(meditation.price) === 0) || parseFloat(props.item.price) === 0
		if (isFree) {
			tags.push({ text: '免费', type: 'free' })
		} else {
			tags.push({ text: '收费', type: 'paid' })
		}

		// 添加时长
		const duration = meditation.duration
		if (duration) {
			tags.push({ text: `${duration}分钟`, type: 'duration' })
		}

		// 添加其他标签 - 处理字符串格式的tags
		let otherTags = meditation.tags || meditation.categories || props.item.tags || []

		// 如果tags是字符串格式，需要解析
		if (typeof otherTags === 'string') {
			try {
				// 尝试解析JSON格式
				otherTags = JSON.parse(otherTags)
			} catch (e) {
				// 如果不是JSON，按逗号分割
				otherTags = otherTags.split(',').map(tag => tag.trim()).filter(Boolean)
			}
		}

		if (Array.isArray(otherTags)) {
			tags = tags.concat(otherTags.slice(0, 2)) // 最多再添加2个标签
		}
	} else if (props.type === 'assessment') {
		// 测评显示题目数和预估时间，不显示免费标签
		const assessment = props.item.assessment || props.item
		tags = []

		// 添加收费/免费标签
		const isFree = assessment.free || assessment.isFree || (assessment.price && parseFloat(assessment.price) === 0) || parseFloat(props.item.price) === 0
		if (isFree) {
			tags.push({ text: '免费', type: 'free' })
		} else {
			tags.push({ text: '收费', type: 'paid' })
		}

		// 添加题目数
		const questionCount = assessment.questionCount
		if (questionCount) {
			tags.push({ text: `共${questionCount}题`, type: 'question' })
		}

		// 添加预估时间
		const duration = assessment.duration
		if (duration) {
			tags.push({ text: `${duration}`, type: 'duration' })
		}

		return tags // 直接返回，不需要后续处理
	}

	// 处理不同格式的标签数据
	if (typeof tags === 'string') {
		try {
			// 尝试解析JSON格式的字符串
			tags = JSON.parse(tags)
		} catch (e) {
			// 如果不是JSON格式，按逗号分割
			tags = tags.split(',').map(tag => tag.trim()).filter(Boolean)
		}
	}

	if (!Array.isArray(tags)) {
		tags = []
	}

	// 过滤掉空值和无效标签
	tags = tags.filter(tag => {
		if (typeof tag === 'string') {
			return tag.trim().length > 0
		}
		if (typeof tag === 'object' && tag.text) {
			return tag.text.trim().length > 0
		}
		return false
	})

	return tags.slice(0, 3) // 最多显示3个标签
}

// 获取测评专业型标签列表（专门用于测评类型的专业标签）
const getAssessmentProfessionalTags = () => {
	if (props.type !== 'assessment') {
		return []
	}

	const assessment = props.item.assessment || props.item
	let tags = []

	// 添加专业型标签
	const assessmentType = assessment.assessmentType || assessment.type || assessment.category
	if (assessmentType) {
		// 根据测评类型添加专业型标签
		const typeMap = {
			'personality': '专业型',
			'professional': '专业型',
			'mbti': '专业型',
			'career': '职业型',
			'emotion': '情感型',
			'psychology': '心理型',
			'intelligence': '智力型',
			'ability': '能力型'
		}

		const professionalType = typeMap[assessmentType.toLowerCase()] || '专业型'
		tags.push({
			text: professionalType,
			type: 'professional',
			style: 'professional-type'
		})
	} else {
		// 默认显示专业型
		tags.push({
			text: '专业型',
			type: 'professional',
			style: 'professional-type'
		})
	}

	// 可以根据需要添加其他专业标签
	const difficulty = assessment.difficulty || assessment.level
	if (difficulty) {
		const difficultyMap = {
			'easy': '入门级',
			'medium': '进阶级',
			'hard': '专家级',
			'beginner': '入门级',
			'intermediate': '进阶级',
			'advanced': '专家级'
		}

		const difficultyText = difficultyMap[difficulty.toLowerCase()] || difficulty
		tags.push({
			text: difficultyText,
			type: 'difficulty',
			style: 'difficulty-type'
		})
	}

	return tags.slice(0, 2) // 最多显示2个专业标签
}

// 获取实际价格
const getActualPrice = () => {
	// 根据类型从实体对象中获取价格
	if (props.type === 'consultant') {
		const consultant = props.item.consultant || props.item
		return consultant.price || consultant.consultationFee || props.item.price
	} else if (props.type === 'assessment') {
		const assessment = props.item.assessment || props.item
		return assessment.price || props.item.price
	} else if (props.type === 'course') {
		const course = props.item.course || props.item
		return course.price || props.item.price
	} else if (props.type === 'meditation') {
		const meditation = props.item.meditation || props.item
		return meditation.price || props.item.price
	}

	return props.item.price
}

// 获取原价
const getOriginalPrice = () => {
	// 根据类型从实体对象中获取原价
	if (props.type === 'consultant') {
		const consultant = props.item.consultant || props.item
		return consultant.originalPrice || props.item.originalPrice
	} else if (props.type === 'assessment') {
		const assessment = props.item.assessment || props.item
		return assessment.originalPrice || props.item.originalPrice
	} else if (props.type === 'course') {
		const course = props.item.course || props.item
		return course.originalPrice || props.item.originalPrice
	} else if (props.type === 'meditation') {
		const meditation = props.item.meditation || props.item
		return meditation.originalPrice || props.item.originalPrice
	}

	return props.item.originalPrice
}

// 获取价格单位
const getPriceUnit = () => {
	const unitMap = {
		consultant: '节',
		course: '课程',
		meditation: '次',
		assessment: '次'
	}

	return props.item.priceUnit || unitMap[props.type] || '次'
}

// 获取价格整数部分
const getPriceInteger = () => {
	const price = getActualPrice()
	if (!price) return ''
	return Math.floor(parseFloat(price)).toString()
}

// 获取价格小数部分
const getPriceDecimal = () => {
	const price = getActualPrice()
	if (!price) return ''
	const priceNum = parseFloat(price)
	const decimal = priceNum - Math.floor(priceNum)
	if (decimal === 0) return ''
	// 保留两位小数，去掉末尾的0
	return decimal.toFixed(2).substring(2).replace(/0+$/, '')
}

// 计算图片区域样式
const imageAreaStyle = computed(() => ({
	width: `${props.imageSize}rpx`,
	height: `${props.imageSize}rpx`
}))

// 计算右侧内容区域样式
const contentAreaStyle = computed(() => ({
	width: `calc(100% - ${props.imageSize + 20}rpx)` // 20rpx是margin-right
}))

// 处理点击事件
const handleClick = () => {
	emit('click', props.item)
}
</script>

<style lang="scss" scoped>
.universal-list-item {
	width: 100%;
	padding: 10rpx 0;
	display: flex;
	background-color: #fff;
	border-bottom: 1rpx solid #f0f0f0;

	.item-left {
		width: 186rpx;
		height: 186rpx;
		margin-right: 20rpx;

		.avatar {
			width: 100%;
			height: 100%;
			border-radius: 20rpx;
			z-index: -1;
		}
	}

	.item-right {
		width: calc(100% - 206rpx);

		.name-box {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 16rpx;

			.name-grade {
				display: flex;
				align-items: center;
				flex: 1;

				.name {
					font-size: 26rpx;
					font-weight: 700;
					color: #000;
				}

				.grade {
					font-size: 20rpx;
					background-color: #f7f1f4;
					padding: 4rpx 12rpx;
					color: #A04571;
					border-radius: 4rpx;
					white-space: nowrap;
				}
			}


		}

		.stats-row {
			display: flex;
			gap: 32rpx;
			margin-bottom: 16rpx;
			justify-content: space-between;

			.stats-container {
				display: flex;
			}

			.stat-item {
				display: flex;
				flex-direction: column;
				margin-right: 30rpx;
				min-width: 40rpx;

				&:last-child {
					margin-right: 0;
				}

				.stat-value {
					font-size: 24rpx;
					font-weight: 700;
				}

				.stat-label {
					font-size: 20rpx;
					color: #ACA8AA;
					margin-top: 4rpx;
					line-height: 1.2;
					white-space: nowrap;
				}
			}

			.price-box {
				display: flex;
				flex-direction: column;
				align-items: flex-end;

				.price-main {
					display: flex;
					align-items: baseline;
					color: #A04571;

					.currency-symbol {
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					.price-integer {
						font-weight: bold;
						font-size: 36rpx;
					}

					.price-dot {
						font-size: 44rpx;
						font-weight: bold;
						margin: 0 1rpx;
					}

					.price-decimal {
						font-size: 28rpx;
						font-weight: bold;
					}

					.price-slash {
						font-size: 24rpx;
						margin: 0 2rpx;
					}

					.price-unit {
						font-size: 24rpx;
					}
				}

				.free-text {
					font-size: 32rpx;
					color: #4CAF50;
					font-weight: 600;
				}

				.original-price {
					font-size: 24rpx;
					color: #ACA8AA;
					text-decoration: line-through;
					margin-top: 4rpx;
				}
			}
		}

		.info {
			margin-bottom: 16rpx;

			.description-text {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
				font-size: 20rpx;
				color: #666;
				line-height: 1.4;
				word-break: break-all;
			}
		}

		.professional-box {
			width: 100%;
			white-space: nowrap;
			font-size: 24rpx;

			.professional-tag {
				display: inline-block;
				padding: 6rpx 16rpx;
				margin-right: 12rpx;
				background-color: #f5f5f5;
				color: #666;
				font-size: 19rpx;
				border-radius: 4rpx;
				border: 1rpx solid transparent;
			}

			// 免费标签样式
			.free-tag {
				background: #F0F2F7;
				font-size: 19rpx;
				color: #455DA0;
			}

			// 收费标签样式
			.paid-tag {
				background: #F6F1F4;
				font-size: 19rpx;
				color: #A04571;
			}

			.assessment-question-tag {
				background-color: #F0F2F7;
				color: #4161a6;
				border: 1rpx solid #455DA0;
				margin-right: 0;
				border-right: none;
			}

			.assessment-duration-tag {
				background-color: transparent;
				color: #8A8788;
				border: 1rpx solid #455DA0;
				border-left: 0;
			}

			// 专业型标签样式
			.professional-type-tag {
				background: #F7F7F7;
				color: #8A8788;
				font-size: 20rpx;
				border-radius: 4rpx;
			}
		}
	}
}
</style>
