# 用户ID参数错误修复总结

## 问题描述

接口请求时出现参数类型不匹配错误：
```
请求参数类型不匹配，参数[userId]要求类型为：'java.lang.Long'，但输入值为：'current'
```

**错误接口**：`http://localhost:8080/miniapp/user/assessment/records/current?scaleId=8`

## 问题原因

在兼容函数中使用了字符串 `'current'` 作为 userId 的占位符，但后端接口要求的是 `Long` 类型的用户ID。

## 解决方案

### 1. 添加用户ID获取工具函数

在 `api/evaluation.js` 中添加了获取当前用户ID的工具函数：

```javascript
import { useUserStore } from '@/stores/user'

// 获取当前用户ID的工具函数
function getCurrentUserId() {
  const userStore = useUserStore()
  const userId = userStore.userId
  if (!userId) {
    throw new Error('用户未登录或用户ID不存在')
  }
  return userId
}
```

### 2. 修正兼容函数

#### 2.1 修正 `getUserTestRecords` 函数

```javascript
// 修正前
export function getUserTestRecords(query = {}) {
  const userId = query.userId || 'current'  // ❌ 使用字符串占位符
  return getAssessmentRecords(userId, query)
}

// 修正后
export function getUserTestRecords(query = {}) {
  try {
    // 优先使用query中的userId，如果没有则获取当前用户ID
    const userId = query.userId || getCurrentUserId()
    return getAssessmentRecords(userId, query)
  } catch (error) {
    console.warn('getUserTestRecords: 获取用户ID失败', error)
    return Promise.reject(error)
  }
}
```

#### 2.2 修正 `getTestHistory` 函数

```javascript
// 修正前
export function getTestHistory(scaleId) {
  return getAssessmentRecords('current', { scaleId })  // ❌ 使用字符串占位符
}

// 修正后
export function getTestHistory(scaleId, userId) {
  try {
    // 如果没有传递userId，获取当前用户ID
    const finalUserId = userId || getCurrentUserId()
    return getAssessmentRecords(finalUserId, { scaleId })
  } catch (error) {
    console.warn('getTestHistory: 获取用户ID失败', error)
    return Promise.reject(error)
  }
}
```

#### 2.3 修正 `getAssessmentHistory` 函数

```javascript
// 修正前
export function getAssessmentHistory(scaleId) {
  return getAssessmentRecords('current', { scaleId })  // ❌ 使用字符串占位符
}

// 修正后
export function getAssessmentHistory(scaleId, userId) {
  try {
    // 如果没有传递userId，获取当前用户ID
    const finalUserId = userId || getCurrentUserId()
    return getAssessmentRecords(finalUserId, { scaleId })
  } catch (error) {
    console.warn('getAssessmentHistory: 获取用户ID失败', error)
    return Promise.reject(error)
  }
}
```

### 3. 修正 my.js 中的函数

#### 3.1 修正 `getTestRecords` 函数

```javascript
// 修正前
export function getTestRecords(userId = 'current') {  // ❌ 默认使用字符串占位符
  return request({
    url: `/miniapp/user/assessment/records/${userId}`,
    method: 'get'
  })
}

// 修正后
export function getTestRecords(userId) {
  // 如果没有传递userId，需要页面传递正确的用户ID
  if (!userId) {
    console.warn('getTestRecords: userId is required')
    return Promise.reject(new Error('用户ID不能为空，请传递正确的userId参数'))
  }
  return request({
    url: `/miniapp/user/assessment/records/${userId}`,
    method: 'get'
  })
}
```

## 用户状态管理集成

项目使用 Pinia 进行状态管理，用户信息存储在 `stores/user.js` 中：

```javascript
// stores/user.js
export const useUserStore = defineStore('user', {
  state: () => ({
    profile: uni.getStorageSync('profile') || null,
    // ...
  }),
  
  getters: {
    userId: (state) => state.profile?.userId || null,
    // ...
  }
})
```

## 修正后的调用方式

### 1. 自动获取当前用户ID（推荐）

```javascript
// 函数会自动获取当前登录用户的ID
await getAssessmentHistory(scaleId)
await getTestHistory(scaleId)
await getUserTestRecords(query)
```

### 2. 手动传递用户ID

```javascript
// 如果需要查询特定用户的数据
await getAssessmentHistory(scaleId, userId)
await getTestHistory(scaleId, userId)
await getUserTestRecords({ userId, ...otherParams })
```

## 错误处理

所有修正后的函数都包含了完善的错误处理：

1. **用户未登录**：如果用户未登录或用户ID不存在，会抛出明确的错误信息
2. **参数验证**：验证必要参数是否存在
3. **错误日志**：记录警告信息便于调试

## 页面调用建议

### 1. 推荐的调用方式

```javascript
// 在页面中调用，函数会自动获取当前用户ID
try {
  const history = await getAssessmentHistory(scaleId)
  // 处理数据
} catch (error) {
  console.error('获取测评历史失败:', error)
  uni.showToast({
    title: error.message || '获取数据失败',
    icon: 'none'
  })
}
```

### 2. 如果需要传递特定用户ID

```javascript
// 从用户store获取当前用户ID
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const currentUserId = userStore.userId

if (currentUserId) {
  const history = await getAssessmentHistory(scaleId, currentUserId)
}
```

## 注意事项

1. **用户登录状态**：确保用户已登录且用户信息已正确存储在store中
2. **参数类型**：后端要求userId为Long类型，确保传递的是数字类型的用户ID
3. **错误处理**：在页面中添加适当的错误处理逻辑
4. **向后兼容**：修正后的函数仍然支持手动传递userId参数

## 测试建议

1. **登录状态测试**：测试用户登录和未登录状态下的接口调用
2. **参数传递测试**：测试自动获取用户ID和手动传递用户ID两种方式
3. **错误处理测试**：测试各种异常情况的处理

## 总结

通过以下修正解决了用户ID参数错误：

- ✅ 移除了所有 `'current'` 字符串占位符
- ✅ 集成了Pinia用户状态管理
- ✅ 添加了自动获取当前用户ID的功能
- ✅ 保持了向后兼容性
- ✅ 添加了完善的错误处理
- ✅ 提供了清晰的调用方式

现在所有涉及用户ID的接口都能正确传递Long类型的用户ID，不会再出现参数类型不匹配的错误。
