<template>
	<view class="page-total">
		<view class="tab-list">
			<view class="list" v-for="(item,index) in menuStore.getMenus"
			@click="onTabBar(item,index)" :style="{marginTop: (index == 2) ?  '-18px' : '0px'}"
			:key="index">
				<view class="tab-item-container">
					<image :src="item.acImg" mode="widthFix" v-show="tabBarShow === index" :style="{width: (index == 2) ?  '40px' : '24px',borderRadius: (index == 2) ?  '12px' : '0px'}"></image>
					<image :src="item.img" mode="widthFix" v-show="tabBarShow != index" :style="{width: (index == 2) ?  '40px' : '24px',borderRadius: (index == 2) ?  '12px' : '0px'}"></image>

					<!-- 消息角标 -->
					<view v-if="isMessageTab(item) && unreadCount > 0" class="message-badge">
						{{ unreadCount > 99 ? '99+' : unreadCount }}
					</view>
				</view>

				<!-- background: (index == 2) ?  'red' : '' -->
				<text  :class="{'action':tabBarShow===index}" :style="{marginTop: (index == 2) ?  '4px' : '0px'}">{{item.name}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { useUserStore } from '@/stores/user'
	import { useMenuStore } from '@/stores/menu'
	import { useChatStore } from '@/stores/chat'
	import { ref, onMounted, onUnmounted } from 'vue'

	export default {
		data() {
			return {
				codeheight: 0,
				isOverall: 0,
				phoneModel: '',
				unreadCount: 0,
			};
		},
		props:{
			tabBarShow: {
				type: Number,
				default: 0,
			}
		},
		mounted() {
			this.initMenu()
			this.initSystemInfo()
			this.initUnreadCount()
		},
		unmounted() {
			// 移除事件监听
			uni.$off('unread-count-changed', this.handleUnreadCountChange)
		},
		methods:{
			// 初始化菜单
			async initMenu() {
				const userStore = useUserStore()
				const menuStore = useMenuStore()

				// 获取角色信息
				let roleKey = userStore.roleKey
				if (!roleKey) {
					const roles = uni.getStorageSync('roles') || []
					roleKey = roles[0]?.roleKey || ''
				}

				// 初始化菜单
				await menuStore.initMenus(roleKey)
			},

			// 初始化未读消息计数
			initUnreadCount() {
				const chatStore = useChatStore()

				// 监听未读消息数量变化
				uni.$on('unread-count-changed', this.handleUnreadCountChange)

				// 初始化未读消息数量
				this.unreadCount = chatStore.unreadTotal || 0
				console.log('Tabbar初始化未读消息数量:', this.unreadCount)
			},

			// 处理未读消息数量变化
			handleUnreadCountChange(count) {
				console.log('Tabbar收到未读消息数量变化:', count)
				this.unreadCount = count || 0
			},

			// 判断是否是消息tab
			isMessageTab(item) {
				// 根据菜单名称或路径判断是否是消息相关的tab
				return item.name === '消息' || item.path.includes('message') || item.path.includes('consultation-message')
			},
			
			// 初始化系统信息
			initSystemInfo() {
				try {
				    const res = uni.getSystemInfoSync();
						let that = this;
				    // 获取系统信息
				    uni.getSystemInfo({
				    	success(res) {
				    		console.log(res.brand) //手机牌子
				    		console.log(res.model) //手机型号
				    		console.log(res.screenWidth) //屏幕宽度
				    		console.log(res.screenHeight) //屏幕高度
							that.codeheight = Math.round(res.screenHeight);
							that.phoneModel = res.model
							if(res.model.search('iPhone')){
								that.isOverall = 0;
							}else if(Math.round(res.screenHeight)>740){
							 that.isOverall = 1;
							}
							console.log(that.isOverall);
				    	}
				    });
				} catch (e) {
				    // error
				}
			},
			/**
			 * @param {Object} item
			 * @param {Number} index
			 */
			onTabBar(item,index){
				// 如果点击的是消息tab，清除未读计数
				if (this.isMessageTab(item)) {
					this.unreadCount = 0
					this.chatStore.unreadTotal = 0
					uni.$emit('unread-count-changed', 0)
					console.log('点击消息tab，清除未读计数')
				}

				uni.switchTab({
					url: item.path
				})
			}
		},
		setup() {
			const menuStore = useMenuStore()
			const chatStore = useChatStore()
			return {
				menuStore,
				chatStore
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'cc-myTabbar.scss';
</style>
