<template>
	<view class="test-search-page">
		<view class="header">
			<text class="title">搜索页面测试</text>
		</view>
		
		<!-- 模拟搜索结果分类标签页 -->
		<view class="result-category-tabs">
			<view
				v-for="(tab, index) in resultTabs"
				:key="tab.type"
				:class="['tab-item', { active: currentResultTab === index }]"
				@click="switchResultTab(index)"
			>
				{{ tab.name }}
				<image v-if="currentResultTab === index" class="tab-icon" src="../../static/icon/图层 4.png"></image>
			</view>
		</view>

		<!-- 搜索结果列表 -->
		<scroll-view scroll-y class="result-list-container">
			<view class="result-list">
				<view v-if="currentResultList.length === 0" class="empty-state">
					<image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png" mode="aspectFit"></image>
					<text>暂无搜索结果</text>
				</view>

				<view v-else class="list-container">
					<UniversalListItem
						v-for="item in currentResultList"
						:key="item.id"
						:item="item"
						:type="currentResultType"
						@click="handleItemClick"
					/>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 搜索结果分类标签
const resultTabs = ref([
	{ name: '全部', type: 'all' },
	{ name: '咨询师', type: 'consultant' },
	{ name: '测评', type: 'assessment' },
	{ name: '冥想', type: 'meditation' },
	{ name: '课程', type: 'course' }
])
const currentResultTab = ref(0)

// 模拟搜索结果数据
const consultantResults = ref([
	{
		id: 1,
		name: '张心理咨询师',
		avatar: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
		price: 299,
		originalPrice: 399,
		consultationCount: 1000,
		rating: 4.9,
		description: '专业心理咨询师，擅长情感问题、焦虑症治疗',
		tags: ['情感咨询', '焦虑治疗', '认知行为']
	}
])

const assessmentResults = ref([
	{
		id: 2,
		name: '心理健康测评',
		cover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-cover.png',
		price: 0,
		isFree: true,
		participantCount: 5000,
		description: '全面评估您的心理健康状况',
		tags: ['心理健康', '自我评估']
	}
])

const meditationResults = ref([
	{
		id: 3,
		name: '放松冥想',
		cover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png',
		duration: 15,
		playCount: 2000,
		description: '帮助您放松身心，缓解压力',
		tags: ['放松', '减压', '睡眠']
	}
])

const courseResults = ref([
	{
		id: 4,
		name: '情绪管理课程',
		cover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
		price: 199,
		studentCount: 800,
		lessonCount: 12,
		description: '学会管理情绪，提升生活质量',
		tags: ['情绪管理', '自我成长']
	}
])

// 计算当前搜索结果类型
const currentResultType = computed(() => {
	return resultTabs.value[currentResultTab.value]?.type || 'all';
});

// 计算当前显示的搜索结果列表
const currentResultList = computed(() => {
	const type = currentResultType.value;
	switch (type) {
		case 'consultant':
			return consultantResults.value;
		case 'assessment':
			return assessmentResults.value;
		case 'meditation':
			return meditationResults.value;
		case 'course':
			return courseResults.value;
		default:
			// 全部：合并所有结果
			return [
				...consultantResults.value,
				...assessmentResults.value,
				...meditationResults.value,
				...courseResults.value
			];
	}
});

// 切换搜索结果标签
const switchResultTab = (index) => {
	currentResultTab.value = index
}

// 处理项目点击
const handleItemClick = (item) => {
	console.log('点击了项目:', item)
	uni.showToast({
		title: `点击了: ${item.name}`,
		icon: 'none'
	})
}
</script>

<style lang="scss" scoped>
.test-search-page {
	min-height: 100vh;
	background-color: #fff;
}

.header {
	padding: 32rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
	
	.title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
}

// 搜索结果分类标签样式
.result-category-tabs {
	display: flex;
	background: #fff;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 0 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	
	.tab-item {
		margin-right: 54rpx;
		position: relative;
		font-size: 24rpx;
		color: #8A8788;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		
		&.active {
			font-weight: 500;
			font-size: 28rpx;
			color: #000;
		}
		
		.tab-icon {
			width: 28rpx;
			height: 12rpx;
			margin-top: 8rpx;
		}
	}
}

// 搜索结果内容样式
.result-list-container {
	flex: 1;
	padding: 0 32rpx;
	height: calc(100vh - 200rpx);
}

.result-list {
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 0;
		
		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 32rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
		}
	}
	
	.list-container {
		padding-bottom: 40rpx;
	}
}
</style>
