# 分类接口集成文档

## 概述

本文档描述了如何将固定的分类标签改为从接口动态获取，以及相关的API接口使用方法。

## 新增API接口

### 1. 分类API文件 (`api/category.js`)

#### 主要接口

```javascript
// 获取首页分类标签
export function getHomeCategories()

// 获取咨询师分类
export function getConsultantCategories()

// 获取课程分类
export function getCourseCategories()

// 获取冥想分类
export function getMeditationCategories()

// 获取测评分类
export function getAssessmentCategories()

// 根据分类ID获取对应数据
export function getConsultantsByCategory(categoryId, params)
export function getCoursesByCategory(categoryId, params)
export function getMeditationsByCategory(categoryId, params)
export function getAssessmentsByCategory(categoryId, params)
```

#### 接口说明

| 接口名称 | 请求方式 | 接口地址 | 说明 |
|---------|---------|----------|------|
| getHomeCategories | GET | `/miniapp/category/home` | 获取首页分类标签 |
| getConsultantCategories | GET | `/system/consultant/categories` | 获取咨询师分类 |
| getCourseCategories | GET | `/miniapp/course/categories` | 获取课程分类 |
| getMeditationCategories | GET | `/miniapp/meditation/categories` | 获取冥想分类 |
| getAssessmentCategories | GET | `/miniapp/assessment/categories` | 获取测评分类 |

## 页面改造

### 1. 首页改造 (`pages/index/index.vue`)

#### 主要变更

1. **导入分类API**
```javascript
import { getHomeCategories } from "@/api/category.js";
```

2. **分类标签改为动态获取**
```javascript
// 原来的固定配置
const tabs = ref([
  { name: '咨询师', type: 'consultant' },
  { name: '测评', type: 'assessment' },
  { name: '冥想', type: 'meditation' },
  { name: '课程', type: 'course' }
]);

// 改为动态获取
const tabs = ref([]);
```

3. **新增加载分类方法**
```javascript
const loadCategories = async () => {
  try {
    const res = await getHomeCategories();
    if (res.code === 200 && res.data) {
      tabs.value = res.data;
    } else {
      // 如果接口失败，使用默认分类
      tabs.value = [
        { name: '咨询师', type: 'consultant' },
        { name: '测评', type: 'assessment' },
        { name: '冥想', type: 'meditation' },
        { name: '课程', type: 'course' }
      ];
    }
    
    // 加载默认标签的数据
    if (tabs.value.length > 0) {
      switchTab(0);
    }
  } catch (error) {
    console.error('加载分类标签失败:', error);
    // 使用默认分类作为降级方案
  }
};
```

4. **生命周期调用**
```javascript
onShow(() => {
  getStoreInfo();
  // 加载分类标签
  loadCategories();
});
```

### 2. 分类页面改造 (`pages/classification/simple-index.vue`)

#### 主要变更

1. **导入分类API**
```javascript
import { 
  getHomeCategories, 
  getCourseCategories, 
  getMeditationCategories, 
  getAssessmentCategories,
  getConsultantCategories
} from '@/api/category.js'
```

2. **主分类标签动态获取**
```javascript
const loadMainCategories = async () => {
  try {
    const res = await getHomeCategories()
    if (res.code === 200 && res.data) {
      tabs.value = res.data
    } else {
      // 降级方案
      tabs.value = [
        { name: '咨询师', type: 'consultant' },
        { name: '课程', type: 'course' },
        { name: '测评', type: 'assessment' },
        { name: '冥想', type: 'meditation' }
      ]
    }
    
    if (tabs.value.length > 0) {
      switchTab(0)
    }
  } catch (error) {
    console.error('加载主分类标签失败:', error)
    // 使用默认分类
  }
}
```

3. **子分类动态获取**
```javascript
const loadCategories = async () => {
  try {
    const type = currentTabType.value
    let res = null
    
    switch (type) {
      case 'consultant':
        res = await getConsultantCategories()
        break
      case 'course':
        res = await getCourseCategories()
        break
      case 'meditation':
        res = await getMeditationCategories()
        break
      case 'assessment':
        res = await getAssessmentCategories()
        break
    }
    
    if (res && res.code === 200) {
      categories.value = res.data || []
    } else {
      // 降级方案
      categories.value = getDefaultCategories(type)
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    categories.value = []
  }
}
```

## 数据格式

### 1. 首页分类标签格式

```javascript
// 接口返回格式
{
  "code": 200,
  "data": [
    {
      "name": "咨询师",
      "type": "consultant"
    },
    {
      "name": "测评", 
      "type": "assessment"
    },
    {
      "name": "冥想",
      "type": "meditation"
    },
    {
      "name": "课程",
      "type": "course"
    }
  ]
}
```

### 2. 子分类格式

```javascript
// 冥想分类示例
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "专注"
    },
    {
      "id": 2,
      "name": "放松"
    },
    {
      "id": 3,
      "name": "睡眠"
    },
    {
      "id": 4,
      "name": "减压"
    }
  ]
}
```

## 降级策略

### 1. 接口失败处理

当分类接口调用失败时，系统会自动使用预设的默认分类：

```javascript
// 默认主分类
const defaultTabs = [
  { name: '咨询师', type: 'consultant' },
  { name: '测评', type: 'assessment' },
  { name: '冥想', type: 'meditation' },
  { name: '课程', type: 'course' }
];

// 默认子分类
const defaultCategories = {
  meditation: [
    { id: 1, name: '专注' },
    { id: 2, name: '放松' },
    { id: 3, name: '睡眠' },
    { id: 4, name: '减压' }
  ],
  assessment: [
    { id: 1, name: '性格测试' },
    { id: 2, name: '情感测评' },
    { id: 3, name: '职业测评' },
    { id: 4, name: '心理健康' }
  ]
};
```

### 2. 错误处理

- **网络错误**: 使用默认分类，并在控制台输出错误信息
- **数据格式错误**: 使用默认分类，确保页面正常显示
- **接口返回错误**: 检查code字段，非200时使用默认分类

## 使用方法

### 1. 在新页面中使用

```javascript
import { getHomeCategories } from '@/api/category.js'

// 在组件中
const tabs = ref([])

const loadTabs = async () => {
  try {
    const res = await getHomeCategories()
    if (res.code === 200) {
      tabs.value = res.data
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

onMounted(() => {
  loadTabs()
})
```

### 2. 分类筛选功能

```javascript
import { getMeditationsByCategory } from '@/api/category.js'

const loadDataByCategory = async (categoryId) => {
  try {
    const res = await getMeditationsByCategory(categoryId, {
      page: 1,
      pageSize: 10
    })
    if (res.code === 200) {
      // 处理数据
    }
  } catch (error) {
    console.error('加载分类数据失败:', error)
  }
}
```

## 注意事项

1. **兼容性**: 保持与现有通用组件的兼容性
2. **性能**: 分类数据会在切换标签时缓存，避免重复请求
3. **错误处理**: 所有接口调用都有完整的错误处理和降级方案
4. **用户体验**: 接口失败时不影响页面正常使用

## 测试建议

1. **正常流程**: 测试接口正常返回时的页面显示
2. **异常流程**: 测试接口失败时的降级处理
3. **网络异常**: 测试网络断开时的处理
4. **数据异常**: 测试接口返回异常数据时的处理

## 总结

通过这次改造，实现了：

1. **动态配置**: 分类标签从接口动态获取，便于后台管理
2. **降级保障**: 完善的降级策略确保页面稳定性
3. **扩展性**: 易于添加新的分类类型
4. **维护性**: 统一的接口管理，便于维护和更新

这个改造为项目的灵活性和可维护性提供了重要支撑。
