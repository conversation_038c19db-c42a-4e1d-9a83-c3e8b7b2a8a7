import { defineStore } from 'pinia'
import { listMenuByPermissions } from '@/api/index.js'

// 默认菜单配置
const defaultMenus = [
	{
		index: 0,
		name: '首页',
		img: '/static/tab-icon/home.png',
		acImg: '/static/tab-icon/home-active.png',
		path: '/pages/index/index'
	},
	{
		index: 1,
		name: '分类',
		img: '/static/tab-icon/咨询师.png',
		acImg: '/static/tab-icon/咨询师-active.png',
		path: '/pages/classification/index'
	},
	{
		index: 2,
		name: '预约',
		img: '/static/tab-icon/消息.png',
		acImg: '/static/tab-icon/消息-active.png',
		path: '/pages/appointment/index'
	},
	{
		index: 3,
		name: '测评',
		img: '/static/tab-icon/消息.png',
		acImg: '/static/tab-icon/消息-active.png',
		path: '/pages/explore/index'
	},
	{
		index: 4,
		name: '我的',
		img: '/static/tab-icon/my.png',
		acImg: '/static/tab-icon/my-active.png',
		path: '/pages/my/index'
	},
]

export const useMenuStore = defineStore('menu', {
	state: () => ({
		menus: uni.getStorageSync('menus') || defaultMenus,
	}),

	getters: {
		getMenus: (state) => state.menus,

		// 获取第一个tabBar菜单
		getFirstTabBarMenu: (state) => {
			if (state.menus && state.menus.length > 0) {
				return state.menus[0]
			}
			return null
		},
	},

	actions: {
		// 设置菜单
		setMenus(menus) {
			this.menus = menus
			uni.setStorageSync('menus', menus)
		},

		// 重置为默认菜单
		resetToDefault() {
			this.menus = defaultMenus
			uni.setStorageSync('menus', defaultMenus)
		},

		// 根据角色获取菜单
		async fetchMenusByRole(roleKey) {
			try {
				const res = await listMenuByPermissions(roleKey)
				if (res.code === 200 && res.data) {
					this.setMenus(res.data)
					return true
				}
				return false
			} catch (error) {
				console.error('获取菜单失败:', error)
				return false
			}
		},

		// 初始化菜单
		async initMenus(roleKey) {
			// 如果本地没有存储的菜单，使用默认菜单
			if (!uni.getStorageSync('menus')) {
				this.resetToDefault()
			}
			
			// 如果有角色信息，尝试获取对应的菜单
			if (roleKey) {
				const success = await this.fetchMenusByRole(roleKey)
				if (!success) {
					console.log('获取角色菜单失败，使用本地菜单')
				}
			}
		}
	}
}) 