<template>
  <view class="flexible-schedule-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">灵活排班设置</text>
      <text class="page-desc">为每一天单独设置工作时间</text>
    </view>

    <!-- 日期选择器 -->
    <view class="date-selector">
      <view class="week-navigation">
        <button class="nav-btn" @click="previousWeek">
          <image src="/static/icon/arrow-left.png" mode="aspectFit"></image>
        </button>
        <text class="week-range">{{ weekRangeText }}</text>
        <button class="nav-btn" @click="nextWeek">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </button>
      </view>

      <view class="week-days">
        <view class="day-item" v-for="(day, index) in currentWeekDays" :key="index" :class="{
          active: selectedDay === day.dateStr,
          today: day.isToday,
          hasSchedule: day.hasSchedule
        }" @click="selectDay(day)">
          <text class="day-name">{{ day.dayName }}</text>
          <text class="day-date">{{ day.date }}</text>
          <view class="schedule-indicator" v-if="day.hasSchedule"></view>
        </view>
      </view>
    </view>

    <!-- 选中日期的排班设置 -->
    <view class="day-schedule-section" v-if="selectedDay">
      <view class="section-header">
        <text class="section-title">{{ selectedDayInfo.fullDate }} 排班设置</text>
        <view class="header-actions">
          <button class="copy-btn" @click="showCopyModal">复制设置</button>
          <button class="clear-btn" @click="clearDaySchedule">清空</button>
        </view>
      </view>

      <!-- 工作状态开关 -->
      <view class="work-status">
        <view class="status-item">
          <text class="status-label">今日工作</text>
          <switch :checked="daySchedule.isWorking" @change="onWorkStatusChange" />
        </view>
      </view>

      <!-- 时间段设置 -->
      <view class="time-slots-section" v-if="daySchedule.isWorking">
        <view class="slots-header">
          <text class="slots-title">工作时间段</text>
          <button class="add-slot-btn" @click="addTimeSlot">+ 添加时间段</button>
        </view>

        <view class="time-slots">
          <view class="time-slot" v-for="(slot, index) in daySchedule.timeSlots" :key="index">
            <view class="slot-content">
              <view class="time-range">
                <picker mode="time" :value="slot.startTime"
                  @change="(e) => updateSlotTime(index, 'startTime', e.detail.value)">
                  <view class="time-picker">{{ slot.startTime }}</view>
                </picker>
                <text class="time-separator">-</text>
                <picker mode="time" :value="slot.endTime"
                  @change="(e) => updateSlotTime(index, 'endTime', e.detail.value)">
                  <view class="time-picker">{{ slot.endTime }}</view>
                </picker>
              </view>

              <view class="slot-options">
                <view class="option-item">
                  <text class="option-label">咨询类型</text>
                  <view class="consult-types">
                    <view class="type-tag" v-for="type in consultTypes" :key="type.value"
                      :class="{ active: slot.consultTypes.includes(type.value) }"
                      @click="toggleConsultType(index, type.value)">
                      {{ type.label }}
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <button class="remove-slot-btn" @click="removeTimeSlot(index)" v-if="daySchedule.timeSlots.length > 1">
              <image src="/static/icon/delete.png" mode="aspectFit"></image>
            </button>
          </view>
        </view>
      </view>

      <!-- 休息时间设置 -->
      <view class="break-section" v-if="daySchedule.isWorking">
        <view class="break-header">
          <text class="break-title">休息时间</text>
          <button class="add-break-btn" @click="addBreakTime">+ 添加休息</button>
        </view>

        <view class="break-times">
          <view class="break-item" v-for="(breakTime, index) in daySchedule.breakTimes" :key="index">
            <view class="break-time-range">
              <picker mode="time" :value="breakTime.startTime"
                @change="(e) => updateBreakTime(index, 'startTime', e.detail.value)">
                <view class="time-picker">{{ breakTime.startTime }}</view>
              </picker>
              <text class="time-separator">-</text>
              <picker mode="time" :value="breakTime.endTime"
                @change="(e) => updateBreakTime(index, 'endTime', e.detail.value)">
                <view class="time-picker">{{ breakTime.endTime }}</view>
              </picker>
            </view>
            <text class="break-label">{{ breakTime.label || '休息时间' }}</text>
            <button class="remove-break-btn" @click="removeBreakTime(index)">
              <image src="/static/icon/delete.png" mode="aspectFit"></image>
            </button>
          </view>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="save-section">
        <button class="save-btn" @click="saveDaySchedule" :disabled="!hasChanges">
          保存当日设置
        </button>
      </view>
    </view>

    <!-- 复制设置弹窗 -->
    <uni-popup ref="copyModal" type="bottom">
      <view class="copy-modal">
        <view class="modal-header">
          <text class="modal-title">复制排班设置</text>
          <text class="modal-close" @click="closeCopyModal">×</text>
        </view>

        <view class="modal-content">
          <view class="copy-source">
            <text class="copy-label">从以下日期复制设置：</text>
            <text class="source-date">{{ selectedDayInfo.fullDate }}</text>
          </view>

          <view class="copy-targets">
            <text class="targets-label">复制到：</text>
            <view class="target-options">
              <view class="option-group">
                <text class="group-title">快速选择</text>
                <button class="quick-option" @click="selectAllWeekdays">工作日</button>
                <button class="quick-option" @click="selectAllWeekends">周末</button>
                <button class="quick-option" @click="selectAllDays">全部</button>
              </view>

              <view class="day-checkboxes">
                <view class="day-checkbox" v-for="day in currentWeekDays" :key="day.dateStr"
                  v-if="day.dateStr !== selectedDay">
                  <checkbox :value="day.dateStr" :checked="copyTargets.includes(day.dateStr)"
                    @change="(e) => toggleCopyTarget(day.dateStr, e.detail.value)" />
                  <text class="checkbox-label">{{ day.dayName }} {{ day.date }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn-cancel" @click="closeCopyModal">取消</button>
          <button class="btn-confirm" @click="executeCopy" :disabled="copyTargets.length === 0">
            复制设置
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 批量操作 -->
    <view class="batch-actions">
      <button class="batch-btn" @click="showBatchModal">批量设置</button>
      <button class="template-btn" @click="goToTemplates">模板管理</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'

// 响应式数据
const currentWeek = ref(new Date())
const selectedDay = ref('')
const daySchedules = ref({}) // 存储每天的排班数据
const copyTargets = ref([])
const copyModal = ref(null)
const hasChanges = ref(false)

// 咨询类型选项
const consultTypes = ref([
  { label: '线上咨询', value: 'online' },
  { label: '线下咨询', value: 'offline' },
  { label: '电话咨询', value: 'phone' }
])

// 默认时间段结构
const defaultTimeSlot = {
  startTime: '09:00',
  endTime: '17:00',
  consultTypes: ['online', 'offline']
}

// 默认排班结构
const defaultDaySchedule = {
  isWorking: false,
  timeSlots: [{ ...defaultTimeSlot }],
  breakTimes: []
}

// 计算属性
const currentWeekDays = computed(() => {
  const days = []
  const startOfWeek = getStartOfWeek(currentWeek.value)

  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek)
    date.setDate(startOfWeek.getDate() + i)

    const dateStr = formatDate(date)
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

    days.push({
      dateStr,
      date: date.getDate(),
      dayName: dayNames[date.getDay()],
      fullDate: formatFullDate(date),
      isToday: isToday(date),
      hasSchedule: daySchedules.value[dateStr]?.isWorking || false
    })
  }

  return days
})

const weekRangeText = computed(() => {
  const start = currentWeekDays.value[0]
  const end = currentWeekDays.value[6]
  return `${start.fullDate} - ${end.fullDate}`
})

const selectedDayInfo = computed(() => {
  return currentWeekDays.value.find(day => day.dateStr === selectedDay.value)
})

const daySchedule = computed(() => {
  if (!selectedDay.value) return { ...defaultDaySchedule }
  return daySchedules.value[selectedDay.value] || { ...defaultDaySchedule }
})

// 生命周期
onMounted(() => {
  initializeWeek()
  loadScheduleData()
  handlePageParams()
})

onShow(() => {
  loadScheduleData()
})

// 方法
const initializeWeek = () => {
  const today = new Date()
  selectedDay.value = formatDate(today)
}

const getStartOfWeek = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day
  return new Date(d.setDate(diff))
}

const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const formatFullDate = (date) => {
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${month}月${day}日`
}

const isToday = (date) => {
  const today = new Date()
  return formatDate(date) === formatDate(today)
}

// 周导航
const previousWeek = () => {
  const newWeek = new Date(currentWeek.value)
  newWeek.setDate(newWeek.getDate() - 7)
  currentWeek.value = newWeek
}

const nextWeek = () => {
  const newWeek = new Date(currentWeek.value)
  newWeek.setDate(newWeek.getDate() + 7)
  currentWeek.value = newWeek
}

// 日期选择
const selectDay = (day) => {
  selectedDay.value = day.dateStr
  hasChanges.value = false
}

// 工作状态切换
const onWorkStatusChange = (e) => {
  if (!daySchedules.value[selectedDay.value]) {
    daySchedules.value[selectedDay.value] = { ...defaultDaySchedule }
  }
  daySchedules.value[selectedDay.value].isWorking = e.detail.value
  hasChanges.value = true
}

// 时间段管理
const addTimeSlot = () => {
  if (!daySchedules.value[selectedDay.value]) {
    daySchedules.value[selectedDay.value] = { ...defaultDaySchedule }
  }
  daySchedules.value[selectedDay.value].timeSlots.push({ ...defaultTimeSlot })
  hasChanges.value = true
}

const removeTimeSlot = (index) => {
  if (daySchedules.value[selectedDay.value]?.timeSlots.length > 1) {
    daySchedules.value[selectedDay.value].timeSlots.splice(index, 1)
    hasChanges.value = true
  }
}

const updateSlotTime = (index, field, value) => {
  if (daySchedules.value[selectedDay.value]?.timeSlots[index]) {
    daySchedules.value[selectedDay.value].timeSlots[index][field] = value
    hasChanges.value = true
  }
}

const toggleConsultType = (slotIndex, typeValue) => {
  const slot = daySchedules.value[selectedDay.value]?.timeSlots[slotIndex]
  if (slot) {
    const types = slot.consultTypes
    const index = types.indexOf(typeValue)
    if (index > -1) {
      types.splice(index, 1)
    } else {
      types.push(typeValue)
    }
    hasChanges.value = true
  }
}

// 休息时间管理
const addBreakTime = () => {
  if (!daySchedules.value[selectedDay.value]) {
    daySchedules.value[selectedDay.value] = { ...defaultDaySchedule }
  }
  if (!daySchedules.value[selectedDay.value].breakTimes) {
    daySchedules.value[selectedDay.value].breakTimes = []
  }
  daySchedules.value[selectedDay.value].breakTimes.push({
    startTime: '12:00',
    endTime: '13:00',
    label: '午休时间'
  })
  hasChanges.value = true
}

const removeBreakTime = (index) => {
  if (daySchedules.value[selectedDay.value]?.breakTimes) {
    daySchedules.value[selectedDay.value].breakTimes.splice(index, 1)
    hasChanges.value = true
  }
}

const updateBreakTime = (index, field, value) => {
  if (daySchedules.value[selectedDay.value]?.breakTimes[index]) {
    daySchedules.value[selectedDay.value].breakTimes[index][field] = value
    hasChanges.value = true
  }
}

// 清空当日排班
const clearDaySchedule = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空当日的所有排班设置吗？',
    success: (res) => {
      if (res.confirm) {
        daySchedules.value[selectedDay.value] = { ...defaultDaySchedule }
        hasChanges.value = true
      }
    }
  })
}

// 保存当日排班
const saveDaySchedule = async () => {
  try {
    // 验证时间段
    const schedule = daySchedules.value[selectedDay.value]
    if (schedule.isWorking && schedule.timeSlots.length === 0) {
      uni.showToast({
        title: '请至少添加一个工作时间段',
        icon: 'none'
      })
      return
    }

    // 验证时间段有效性
    for (const slot of schedule.timeSlots) {
      if (slot.startTime >= slot.endTime) {
        uni.showToast({
          title: '开始时间不能晚于结束时间',
          icon: 'none'
        })
        return
      }
    }

    // 这里调用API保存数据
    // const res = await saveFlexibleSchedule(selectedDay.value, schedule)

    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    hasChanges.value = false
  } catch (error) {
    console.error('保存排班失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 复制设置相关
const showCopyModal = () => {
  copyTargets.value = []
  copyModal.value?.open()
}

const closeCopyModal = () => {
  copyModal.value?.close()
}

const toggleCopyTarget = (dateStr, checked) => {
  if (checked) {
    if (!copyTargets.value.includes(dateStr)) {
      copyTargets.value.push(dateStr)
    }
  } else {
    const index = copyTargets.value.indexOf(dateStr)
    if (index > -1) {
      copyTargets.value.splice(index, 1)
    }
  }
}

const selectAllWeekdays = () => {
  copyTargets.value = currentWeekDays.value
    .filter(day => day.dateStr !== selectedDay.value && [1, 2, 3, 4, 5].includes(new Date(day.dateStr).getDay()))
    .map(day => day.dateStr)
}

const selectAllWeekends = () => {
  copyTargets.value = currentWeekDays.value
    .filter(day => day.dateStr !== selectedDay.value && [0, 6].includes(new Date(day.dateStr).getDay()))
    .map(day => day.dateStr)
}

const selectAllDays = () => {
  copyTargets.value = currentWeekDays.value
    .filter(day => day.dateStr !== selectedDay.value)
    .map(day => day.dateStr)
}

const executeCopy = () => {
  const sourceSchedule = daySchedules.value[selectedDay.value]
  if (!sourceSchedule) {
    uni.showToast({
      title: '源日期没有排班设置',
      icon: 'none'
    })
    return
  }

  copyTargets.value.forEach(dateStr => {
    daySchedules.value[dateStr] = JSON.parse(JSON.stringify(sourceSchedule))
  })

  uni.showToast({
    title: `已复制到${copyTargets.value.length}天`,
    icon: 'success'
  })

  closeCopyModal()
  hasChanges.value = true
}

// 加载排班数据
const loadScheduleData = async () => {
  try {
    // 这里调用API加载数据
    // const res = await getFlexibleSchedule()
    // daySchedules.value = res.data || {}

    // 临时模拟数据
    console.log('加载排班数据')
  } catch (error) {
    console.error('加载排班数据失败:', error)
  }
}

// 批量操作
const showBatchModal = () => {
  uni.showActionSheet({
    itemList: ['批量设置工作日', '批量设置周末', '批量清空本周'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          batchSetWeekdays()
          break
        case 1:
          batchSetWeekends()
          break
        case 2:
          batchClearWeek()
          break
      }
    }
  })
}

const batchSetWeekdays = () => {
  // 实现批量设置工作日逻辑
  uni.showToast({
    title: '批量设置工作日功能开发中',
    icon: 'none'
  })
}

const batchSetWeekends = () => {
  // 实现批量设置周末逻辑
  uni.showToast({
    title: '批量设置周末功能开发中',
    icon: 'none'
  })
}

const batchClearWeek = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空本周所有排班设置吗？',
    success: (res) => {
      if (res.confirm) {
        currentWeekDays.value.forEach(day => {
          daySchedules.value[day.dateStr] = { ...defaultDaySchedule }
        })
        hasChanges.value = true
        uni.showToast({
          title: '已清空本周排班',
          icon: 'success'
        })
      }
    }
  })
}

const goToTemplates = () => {
  uni.navigateTo({
    url: '/pages/schedule/templates/index'
  })
}

// 应用模板
const applyTemplate = async (templateId) => {
  try {
    // 这里调用API获取模板数据
    // const res = await getScheduleTemplate(templateId)
    // const template = res.data

    // 临时模拟模板数据
    const template = {
      isWorking: true,
      timeSlots: [{
        startTime: '09:00',
        endTime: '17:00',
        consultTypes: ['online', 'offline']
      }],
      breakTimes: [{
        startTime: '12:00',
        endTime: '13:00',
        label: '午休时间'
      }]
    }

    if (selectedDay.value) {
      daySchedules.value[selectedDay.value] = { ...template }
      hasChanges.value = true

      uni.showToast({
        title: '模板应用成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('应用模板失败:', error)
    uni.showToast({
      title: '应用模板失败',
      icon: 'none'
    })
  }
}

// 处理页面参数
const handlePageParams = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.templateId) {
    // 如果有模板ID，应用模板
    applyTemplate(options.templateId)
  }
}
</script>

<style scoped lang="scss">
.flexible-schedule-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

.page-header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  text-align: center;

  .page-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .page-desc {
    font-size: 24rpx;
    color: #666;
  }
}

.date-selector {
  background-color: #fff;
  margin-bottom: 20rpx;

  .week-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .nav-btn {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;

      image {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .week-range {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .week-days {
    display: flex;
    padding: 20rpx 0;

    .day-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx 10rpx;
      position: relative;

      .day-name {
        font-size: 22rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .day-date {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .schedule-indicator {
        position: absolute;
        bottom: 8rpx;
        width: 8rpx;
        height: 8rpx;
        background-color: #1890ff;
        border-radius: 50%;
      }

      &.active {
        background-color: #1890ff;
        border-radius: 12rpx;

        .day-name,
        .day-date {
          color: #fff;
        }

        .schedule-indicator {
          background-color: #fff;
        }
      }

      &.today {
        .day-date {
          color: #1890ff;
          font-weight: bold;
        }
      }
    }
  }
}

.day-schedule-section {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 15rpx;

      .copy-btn,
      .clear-btn {
        padding: 12rpx 20rpx;
        font-size: 24rpx;
        border-radius: 20rpx;
        border: none;
      }

      .copy-btn {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      .clear-btn {
        background-color: #fff2f0;
        color: #ff4d4f;
      }
    }
  }

  .work-status {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-label {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }

  .time-slots-section {
    padding: 30rpx;

    .slots-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .slots-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .add-slot-btn {
        padding: 12rpx 20rpx;
        background-color: #1890ff;
        color: #fff;
        border: none;
        border-radius: 20rpx;
        font-size: 24rpx;
      }
    }

    .time-slots {
      .time-slot {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30rpx;
        padding: 20rpx;
        background-color: #f8f9fa;
        border-radius: 12rpx;

        .slot-content {
          flex: 1;

          .time-range {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;

            .time-picker {
              padding: 15rpx 20rpx;
              background-color: #fff;
              border: 1rpx solid #d9d9d9;
              border-radius: 8rpx;
              font-size: 28rpx;
              min-width: 120rpx;
              text-align: center;
            }

            .time-separator {
              margin: 0 20rpx;
              font-size: 24rpx;
              color: #666;
            }
          }

          .slot-options {
            .option-item {
              .option-label {
                display: block;
                font-size: 24rpx;
                color: #666;
                margin-bottom: 15rpx;
              }

              .consult-types {
                display: flex;
                gap: 10rpx;
                flex-wrap: wrap;

                .type-tag {
                  padding: 8rpx 16rpx;
                  background-color: #fff;
                  border: 1rpx solid #d9d9d9;
                  border-radius: 16rpx;
                  font-size: 22rpx;
                  color: #666;

                  &.active {
                    background-color: #1890ff;
                    color: #fff;
                    border-color: #1890ff;
                  }
                }
              }
            }
          }
        }

        .remove-slot-btn {
          width: 60rpx;
          height: 60rpx;
          background-color: #ff4d4f;
          border: none;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 20rpx;

          image {
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
    }
  }

  .break-section {
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .break-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .break-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .add-break-btn {
        padding: 12rpx 20rpx;
        background-color: #52c41a;
        color: #fff;
        border: none;
        border-radius: 20rpx;
        font-size: 24rpx;
      }
    }

    .break-times {
      .break-item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        padding: 15rpx;
        background-color: #f6ffed;
        border-radius: 8rpx;

        .break-time-range {
          display: flex;
          align-items: center;
          margin-right: 20rpx;

          .time-picker {
            padding: 10rpx 15rpx;
            background-color: #fff;
            border: 1rpx solid #d9d9d9;
            border-radius: 6rpx;
            font-size: 24rpx;
            min-width: 100rpx;
            text-align: center;
          }

          .time-separator {
            margin: 0 15rpx;
            font-size: 22rpx;
            color: #666;
          }
        }

        .break-label {
          flex: 1;
          font-size: 24rpx;
          color: #666;
        }

        .remove-break-btn {
          width: 40rpx;
          height: 40rpx;
          background-color: #ff4d4f;
          border: none;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            width: 20rpx;
            height: 20rpx;
          }
        }
      }
    }
  }

  .save-section {
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .save-btn {
      width: 100%;
      padding: 24rpx 0;
      background-color: #1890ff;
      color: #fff;
      border: none;
      border-radius: 12rpx;
      font-size: 28rpx;
      font-weight: 500;

      &:disabled {
        background-color: #f5f5f5;
        color: #ccc;
      }
    }
  }
}

.copy-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .modal-close {
      font-size: 40rpx;
      color: #999;
    }
  }

  .modal-content {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;

    .copy-source {
      margin-bottom: 30rpx;

      .copy-label {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 10rpx;
      }

      .source-date {
        font-size: 28rpx;
        color: #1890ff;
        font-weight: 500;
      }
    }

    .copy-targets {
      .targets-label {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 20rpx;
      }

      .target-options {
        .option-group {
          margin-bottom: 30rpx;

          .group-title {
            display: block;
            font-size: 24rpx;
            color: #333;
            margin-bottom: 15rpx;
          }

          .quick-option {
            margin-right: 15rpx;
            margin-bottom: 15rpx;
            padding: 12rpx 20rpx;
            background-color: #f5f5f5;
            color: #666;
            border: none;
            border-radius: 20rpx;
            font-size: 24rpx;
          }
        }

        .day-checkboxes {
          .day-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;

            .checkbox-label {
              margin-left: 15rpx;
              font-size: 26rpx;
              color: #333;
            }
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .btn-cancel,
    .btn-confirm {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }

    .btn-cancel {
      background-color: #f5f5f5;
      color: #666;
    }

    .btn-confirm {
      background-color: #1890ff;
      color: #fff;

      &:disabled {
        background-color: #f5f5f5;
        color: #ccc;
      }
    }
  }
}

.batch-actions {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  gap: 20rpx;

  .batch-btn,
  .template-btn {
    flex: 1;
    padding: 24rpx 0;
    border: none;
    border-radius: 12rpx;
    font-size: 28rpx;
    font-weight: 500;
  }

  .batch-btn {
    background-color: #52c41a;
    color: #fff;
  }

  .template-btn {
    background-color: #fff;
    color: #1890ff;
    border: 1rpx solid #1890ff;
  }
}
</style>
