<template>
  <view class="test-page">
    <view class="header">
      <text class="title">收藏功能测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试更新后的收藏系统：</text>
          <text>1. 测试四种类型的收藏：咨询师、课程、冥想、测评</text>
          <text>2. 测试收藏和取消收藏功能</text>
          <text>3. 测试收藏状态检查</text>
          <text>4. 测试收藏列表获取</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">API测试</text>
        <view class="api-tests">
          <button @click="testAddFavorite" class="test-btn">测试添加收藏</button>
          <button @click="testCheckFavorite" class="test-btn">测试检查收藏</button>
          <button @click="testGetFavoriteList" class="test-btn">测试获取收藏列表</button>
          <button @click="testGetFavoriteStats" class="test-btn">测试获取收藏统计</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试结果</text>
        <view class="result-area">
          <text class="result-text">{{ testResult }}</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">页面跳转测试</text>
        <view class="page-tests">
          <button @click="goToFavorites" class="test-btn">跳转到我的收藏</button>
          <button @click="goToCounselorDetail" class="test-btn">跳转到咨询师详情</button>
          <button @click="goToCourseDetail" class="test-btn">跳转到课程详情</button>
          <button @click="goToMeditationDetail" class="test-btn">跳转到冥想详情</button>
          <button @click="goToAssessmentDetail" class="test-btn">跳转到测评详情</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { 
  addFavorite, 
  removeFavorite, 
  checkFavorite, 
  getFavoriteList, 
  getFavoriteStats,
  FAVORITE_TYPES,
  FAVORITE_TYPE_NAMES 
} from '@/api/favorite.js'

// 响应式数据
const testResult = ref('等待测试...')

// 测试数据
const testData = {
  consultant: {
    targetType: FAVORITE_TYPES.CONSULTANT,
    targetId: 1,
    targetTitle: '测试咨询师',
    targetImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/consultant/default-avatar.png'
  },
  course: {
    targetType: FAVORITE_TYPES.COURSE,
    targetId: 1,
    targetTitle: '测试课程',
    targetImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
  },
  meditation: {
    targetType: FAVORITE_TYPES.MEDITATION,
    targetId: 1,
    targetTitle: '测试冥想',
    targetImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
  },
  assessment: {
    targetType: FAVORITE_TYPES.ASSESSMENT,
    targetId: 1,
    targetTitle: '测试测评',
    targetImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
  }
}

// 测试添加收藏
const testAddFavorite = async () => {
  try {
    testResult.value = '正在测试添加收藏...'
    
    const res = await addFavorite(testData.consultant)
    
    if (res.code === 200) {
      testResult.value = `添加收藏成功！\n返回数据: ${JSON.stringify(res.data, null, 2)}`
    } else {
      testResult.value = `添加收藏失败: ${res.msg}`
    }
  } catch (error) {
    testResult.value = `添加收藏出错: ${error.message}`
    console.error('测试添加收藏失败:', error)
  }
}

// 测试检查收藏
const testCheckFavorite = async () => {
  try {
    testResult.value = '正在测试检查收藏...'
    
    const res = await checkFavorite({
      targetType: FAVORITE_TYPES.CONSULTANT,
      targetId: 1
    })
    
    if (res.code === 200) {
      testResult.value = `检查收藏成功！\n是否已收藏: ${res.data ? '是' : '否'}\n返回数据: ${JSON.stringify(res.data, null, 2)}`
    } else {
      testResult.value = `检查收藏失败: ${res.msg}`
    }
  } catch (error) {
    testResult.value = `检查收藏出错: ${error.message}`
    console.error('测试检查收藏失败:', error)
  }
}

// 测试获取收藏列表
const testGetFavoriteList = async () => {
  try {
    testResult.value = '正在测试获取收藏列表...'
    
    const res = await getFavoriteList()
    
    if (res.code === 200) {
      testResult.value = `获取收藏列表成功！\n收藏数量: ${res.data?.length || 0}\n返回数据: ${JSON.stringify(res.data, null, 2)}`
    } else {
      testResult.value = `获取收藏列表失败: ${res.msg}`
    }
  } catch (error) {
    testResult.value = `获取收藏列表出错: ${error.message}`
    console.error('测试获取收藏列表失败:', error)
  }
}

// 测试获取收藏统计
const testGetFavoriteStats = async () => {
  try {
    testResult.value = '正在测试获取收藏统计...'
    
    const res = await getFavoriteStats()
    
    if (res.code === 200) {
      testResult.value = `获取收藏统计成功！\n返回数据: ${JSON.stringify(res.data, null, 2)}`
    } else {
      testResult.value = `获取收藏统计失败: ${res.msg}`
    }
  } catch (error) {
    testResult.value = `获取收藏统计出错: ${error.message}`
    console.error('测试获取收藏统计失败:', error)
  }
}

// 页面跳转测试
const goToFavorites = () => {
  uni.navigateTo({
    url: '/pages/my/my-star/index'
  })
}

const goToCounselorDetail = () => {
  uni.navigateTo({
    url: '/pages/classification/counselor-detail/index?id=1'
  })
}

const goToCourseDetail = () => {
  uni.navigateTo({
    url: '/pages/course/detail/index?courseId=1'
  })
}

const goToMeditationDetail = () => {
  uni.navigateTo({
    url: '/pages/meditation/detail/index?meditationId=1'
  })
}

const goToAssessmentDetail = () => {
  uni.navigateTo({
    url: '/pages/evaluation/detail/index?assessmentId=1'
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background-color: #f5f5f6;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.content {
  .section {
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
      display: block;
    }
    
    .description {
      display: flex;
      flex-direction: column;
      gap: 10rpx;
      
      text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
    }
    
    .api-tests, .page-tests {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      
      .test-btn {
        background: #20a3f3;
        color: #fff;
        border: none;
        border-radius: 10rpx;
        padding: 20rpx;
        font-size: 28rpx;
      }
    }
    
    .result-area {
      background: #f8f9fa;
      border-radius: 10rpx;
      padding: 20rpx;
      min-height: 200rpx;
      
      .result-text {
        font-size: 26rpx;
        color: #333;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style>
