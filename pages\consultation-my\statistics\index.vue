<template>
  <view class="statistics-container">
    <!-- 时间选择器 -->
    <view class="time-selector">
      <view class="selector-tabs">
        <view 
          class="tab-item" 
          v-for="(tab, index) in timeTabs" 
          :key="index"
          :class="{ active: currentTimeTab === index }"
          @click="switchTimeTab(index)"
        >
          {{ tab.label }}
        </view>
      </view>
      <view class="custom-time" v-if="currentTimeTab === timeTabs.length - 1">
        <picker mode="date" :value="customStartDate" @change="onStartDateChange">
          <view class="date-picker">{{ customStartDate || '开始日期' }}</view>
        </picker>
        <text class="separator">至</text>
        <picker mode="date" :value="customEndDate" @change="onEndDateChange">
          <view class="date-picker">{{ customEndDate || '结束日期' }}</view>
        </picker>
      </view>
    </view>

    <!-- 核心指标 -->
    <view class="core-metrics">
      <view class="metrics-header">
        <text class="section-title">核心指标</text>
        <text class="period-text">{{ getCurrentPeriodText() }}</text>
      </view>
      <view class="metrics-grid">
        <view class="metric-item">
          <text class="metric-value">{{ coreMetrics.totalOrders || 0 }}</text>
          <text class="metric-label">总订单数</text>
          <view class="metric-trend" :class="getTrendClass(coreMetrics.ordersTrend)">
            <image :src="getTrendIcon(coreMetrics.ordersTrend)" mode="aspectFit"></image>
            <text>{{ Math.abs(coreMetrics.ordersTrend || 0) }}%</text>
          </view>
        </view>
        <view class="metric-item">
          <text class="metric-value">{{ coreMetrics.totalClients || 0 }}</text>
          <text class="metric-label">服务人数</text>
          <view class="metric-trend" :class="getTrendClass(coreMetrics.clientsTrend)">
            <image :src="getTrendIcon(coreMetrics.clientsTrend)" mode="aspectFit"></image>
            <text>{{ Math.abs(coreMetrics.clientsTrend || 0) }}%</text>
          </view>
        </view>
        <view class="metric-item">
          <text class="metric-value">¥{{ coreMetrics.totalIncome || 0 }}</text>
          <text class="metric-label">总收入</text>
          <view class="metric-trend" :class="getTrendClass(coreMetrics.incomeTrend)">
            <image :src="getTrendIcon(coreMetrics.incomeTrend)" mode="aspectFit"></image>
            <text>{{ Math.abs(coreMetrics.incomeTrend || 0) }}%</text>
          </view>
        </view>
        <view class="metric-item">
          <text class="metric-value">{{ coreMetrics.averageRating || 5.0 }}</text>
          <text class="metric-label">平均评分</text>
          <view class="metric-trend" :class="getTrendClass(coreMetrics.ratingTrend)">
            <image :src="getTrendIcon(coreMetrics.ratingTrend)" mode="aspectFit"></image>
            <text>{{ Math.abs(coreMetrics.ratingTrend || 0) }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 收入分析 -->
    <view class="income-analysis">
      <view class="section-header">
        <text class="section-title">收入分析</text>
      </view>
      <view class="income-chart">
        <!-- 这里可以集成图表组件 -->
        <view class="chart-placeholder">
          <text>收入趋势图</text>
          <text class="chart-desc">{{ getCurrentPeriodText() }}收入变化趋势</text>
        </view>
      </view>
      <view class="income-breakdown">
        <view class="breakdown-item">
          <text class="breakdown-label">咨询收入</text>
          <text class="breakdown-value">¥{{ incomeBreakdown.consultation || 0 }}</text>
          <text class="breakdown-percent">{{ getPercentage(incomeBreakdown.consultation, incomeBreakdown.total) }}%</text>
        </view>
        <view class="breakdown-item">
          <text class="breakdown-label">课程收入</text>
          <text class="breakdown-value">¥{{ incomeBreakdown.course || 0 }}</text>
          <text class="breakdown-percent">{{ getPercentage(incomeBreakdown.course, incomeBreakdown.total) }}%</text>
        </view>
        <view class="breakdown-item">
          <text class="breakdown-label">其他收入</text>
          <text class="breakdown-value">¥{{ incomeBreakdown.other || 0 }}</text>
          <text class="breakdown-percent">{{ getPercentage(incomeBreakdown.other, incomeBreakdown.total) }}%</text>
        </view>
      </view>
    </view>

    <!-- 客户分析 -->
    <view class="client-analysis">
      <view class="section-header">
        <text class="section-title">客户分析</text>
      </view>
      <view class="client-stats">
        <view class="stat-row">
          <view class="stat-item">
            <text class="stat-label">新增客户</text>
            <text class="stat-value">{{ clientStats.newClients || 0 }}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">回访客户</text>
            <text class="stat-value">{{ clientStats.returningClients || 0 }}</text>
          </view>
        </view>
        <view class="stat-row">
          <view class="stat-item">
            <text class="stat-label">客户满意度</text>
            <text class="stat-value">{{ clientStats.satisfaction || 0 }}%</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">平均咨询次数</text>
            <text class="stat-value">{{ clientStats.averageSessions || 0 }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 服务效率 -->
    <view class="efficiency-analysis">
      <view class="section-header">
        <text class="section-title">服务效率</text>
      </view>
      <view class="efficiency-stats">
        <view class="efficiency-item">
          <view class="efficiency-icon">
            <image src="/static/icon/time.png" mode="aspectFit"></image>
          </view>
          <view class="efficiency-info">
            <text class="efficiency-label">平均响应时间</text>
            <text class="efficiency-value">{{ efficiencyStats.responseTime || 0 }}分钟</text>
          </view>
        </view>
        <view class="efficiency-item">
          <view class="efficiency-icon">
            <image src="/static/icon/completion.png" mode="aspectFit"></image>
          </view>
          <view class="efficiency-info">
            <text class="efficiency-label">订单完成率</text>
            <text class="efficiency-value">{{ efficiencyStats.completionRate || 0 }}%</text>
          </view>
        </view>
        <view class="efficiency-item">
          <view class="efficiency-icon">
            <image src="/static/icon/duration.png" mode="aspectFit"></image>
          </view>
          <view class="efficiency-info">
            <text class="efficiency-label">平均咨询时长</text>
            <text class="efficiency-value">{{ efficiencyStats.averageDuration || 0 }}分钟</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 导出报告 -->
    <view class="export-section">
      <button class="export-btn" @click="exportReport">
        <image src="/static/icon/export.png" mode="aspectFit"></image>
        <text>导出数据报告</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getConsultantStatistics, exportStatisticsReport } from '@/api/consultant-app.js'

// 响应式数据
const currentTimeTab = ref(0)
const customStartDate = ref('')
const customEndDate = ref('')

const coreMetrics = ref({})
const incomeBreakdown = ref({})
const clientStats = ref({})
const efficiencyStats = ref({})

// 时间标签
const timeTabs = ref([
  { label: '今日', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' },
  { label: '自定义', value: 'custom' }
])

// 生命周期
onMounted(() => {
  loadStatistics()
})

onShow(() => {
  loadStatistics()
})

// 加载统计数据
const loadStatistics = async () => {
  try {
    const params = {
      period: timeTabs.value[currentTimeTab.value].value,
      startDate: customStartDate.value,
      endDate: customEndDate.value
    }
    
    const res = await getConsultantStatistics(params)
    if (res.code === 200) {
      coreMetrics.value = res.data.coreMetrics || {}
      incomeBreakdown.value = res.data.incomeBreakdown || {}
      clientStats.value = res.data.clientStats || {}
      efficiencyStats.value = res.data.efficiencyStats || {}
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 切换时间标签
const switchTimeTab = (index) => {
  currentTimeTab.value = index
  if (index !== timeTabs.value.length - 1) {
    // 非自定义时间，清空自定义日期
    customStartDate.value = ''
    customEndDate.value = ''
  }
  loadStatistics()
}

// 开始日期选择
const onStartDateChange = (e) => {
  customStartDate.value = e.detail.value
  if (customEndDate.value) {
    loadStatistics()
  }
}

// 结束日期选择
const onEndDateChange = (e) => {
  customEndDate.value = e.detail.value
  if (customStartDate.value) {
    loadStatistics()
  }
}

// 获取当前周期文本
const getCurrentPeriodText = () => {
  if (currentTimeTab.value === timeTabs.value.length - 1) {
    if (customStartDate.value && customEndDate.value) {
      return `${customStartDate.value} 至 ${customEndDate.value}`
    }
    return '请选择时间范围'
  }
  return timeTabs.value[currentTimeTab.value].label
}

// 获取趋势样式类
const getTrendClass = (trend) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

// 获取趋势图标
const getTrendIcon = (trend) => {
  if (trend > 0) return '/static/icon/trend-up.png'
  if (trend < 0) return '/static/icon/trend-down.png'
  return '/static/icon/trend-stable.png'
}

// 计算百分比
const getPercentage = (value, total) => {
  if (!total || total === 0) return 0
  return Math.round((value / total) * 100)
}

// 导出报告
const exportReport = async () => {
  try {
    uni.showLoading({
      title: '生成报告中...'
    })
    
    const params = {
      period: timeTabs.value[currentTimeTab.value].value,
      startDate: customStartDate.value,
      endDate: customEndDate.value
    }
    
    const res = await exportStatisticsReport(params)
    if (res.code === 200) {
      // 这里可以处理文件下载或分享
      uni.showToast({
        title: '报告生成成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('导出报告失败:', error)
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
</script>

<style scoped lang="scss">
.statistics-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.time-selector {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .selector-tabs {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 12rpx;
    padding: 8rpx;
    margin-bottom: 20rpx;
    
    .tab-item {
      flex: 1;
      text-align: center;
      padding: 16rpx 0;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #666;
      
      &.active {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
  
  .custom-time {
    display: flex;
    align-items: center;
    gap: 20rpx;
    
    .date-picker {
      flex: 1;
      padding: 20rpx;
      border: 1rpx solid #d9d9d9;
      border-radius: 8rpx;
      text-align: center;
      font-size: 26rpx;
      color: #333;
    }
    
    .separator {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.core-metrics {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .metrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .period-text {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;
    
    .metric-item {
      text-align: center;
      
      .metric-value {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 8rpx;
      }
      
      .metric-label {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 12rpx;
      }
      
      .metric-trend {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        
        image {
          width: 20rpx;
          height: 20rpx;
        }
        
        text {
          font-size: 22rpx;
        }
        
        &.trend-up {
          color: #52c41a;
        }
        
        &.trend-down {
          color: #ff4d4f;
        }
        
        &.trend-stable {
          color: #666;
        }
      }
    }
  }
}

.income-analysis,
.client-analysis,
.efficiency-analysis {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-header {
    margin-bottom: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.income-chart {
  margin-bottom: 30rpx;
  
  .chart-placeholder {
    height: 300rpx;
    background-color: #f8f8f8;
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    text {
      font-size: 28rpx;
      color: #666;
      
      &.chart-desc {
        font-size: 24rpx;
        margin-top: 8rpx;
      }
    }
  }
}

.income-breakdown {
  .breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .breakdown-label {
      font-size: 28rpx;
      color: #333;
    }
    
    .breakdown-value {
      font-size: 28rpx;
      font-weight: bold;
      color: #1890ff;
    }
    
    .breakdown-percent {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.client-stats {
  .stat-row {
    display: flex;
    gap: 40rpx;
    margin-bottom: 30rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-label {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 12rpx;
      }
      
      .stat-value {
        font-size: 40rpx;
        font-weight: bold;
        color: #1890ff;
      }
    }
  }
}

.efficiency-stats {
  .efficiency-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .efficiency-icon {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f7ff;
      border-radius: 50%;
      margin-right: 20rpx;
      
      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
    
    .efficiency-info {
      flex: 1;
      
      .efficiency-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .efficiency-value {
        font-size: 32rpx;
        font-weight: bold;
        color: #1890ff;
      }
    }
  }
}

.export-section {
  padding: 0 30rpx;
  
  .export-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    padding: 28rpx 0;
    background-color: #1890ff;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 30rpx;
    
    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}
</style>
