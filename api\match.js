import request from '@/utils/request'

// 获取原有问题列表
export function getTroubledDict() {
  return request({
    url: '/troubled/dict',
    method: 'get'
  })
}

// 获取匹配问题列表
export function getMatchQuestions() {
  return request({
    url: '/match/question/list',
    method: 'get'
  })
}

// 获取问题详情
export function getQuestionDetail(questionId) {
  return request({
    url: `/match/question/${questionId}`,
    method: 'get'
  })
}

// 根据选项匹配咨询师
export function matchConsultants(data) {
  return request({
    url: '/match/question/match',
    method: 'post',
    data
  })
}

// 获取选项关联的咨询师
export function getOptionConsultants(optionId) {
  return request({
    url: `/match/question/option/${optionId}/consultants`,
    method: 'get'
  })
}

// 获取所有咨询师简单信息
export function getAllConsultantsSimple() {
  return request({
    url: '/system/consultant/allSimpleList',
    method: 'get'
  })
}
