# 冥想功能实现文档

## 功能概述

本项目已完成冥想系统的前端实现，包括冥想浏览、购买、播放、评价等完整流程。

## 功能模块

### 1. 冥想首页 (`pages/meditation/index.vue`)
- **功能**: 展示所有可用冥想
- **特性**:
  - 分类筛选（全部、专注、放松、睡眠、减压、冥想）
  - 推荐冥想展示
  - 每日冥想推荐
  - 下拉刷新功能
  - 使用小程序自带导航栏

### 2. 冥想详情 (`pages/meditation/detail/index.vue`)
- **功能**: 展示冥想详细信息
- **特性**:
  - 冥想基本信息（封面、标题、时长、播放次数）
  - 两个标签页：介绍、评价
  - 冥想介绍（适合场景、功效说明）
  - 评价列表和评分统计
  - 购买/播放按钮
  - 集成支付弹框

### 3. 冥想播放器 (`pages/meditation/player/index.vue`)
- **功能**: 音频播放和冥想体验
- **特性**:
  - 沉浸式播放界面（背景图+遮罩）
  - 音频播放控制（播放/暂停、快进/快退）
  - 播放进度显示和拖拽
  - 播放速度调节
  - 定时关闭功能
  - 收藏功能
  - 引导文字显示
  - 播放记录自动保存

### 4. 我的冥想 (`pages/meditation/my-meditations/index.vue`)
- **功能**: 管理已购买的冥想
- **特性**:
  - 冥想统计（冥想次数、总时长、完成次数）
  - 冥想状态筛选（已购买、播放记录、收藏）
  - 最近播放记录
  - 快速播放功能
  - 下拉刷新

### 5. 冥想评价 (`pages/meditation/review/index.vue`)
- **功能**: 提交冥想评价
- **特性**:
  - 星级评分
  - 评价标签选择（很放松、效果显著、声音舒缓等）
  - 评价内容输入
  - 匿名评价选项
  - 字数限制和统计

## API接口 (`api/meditation.js`)

已实现的API接口方法：

```javascript
// 冥想相关
getMeditationList(params)          // 获取冥想列表
getMeditationDetail(id)            // 获取冥想详情
playMeditation(id, data)           // 开始播放冥想
getMeditationsByCategory(categoryId) // 根据分类获取冥想

// 订单相关
createMeditationOrder(meditationId) // 创建冥想订单
getPurchasedMeditations()          // 获取已购冥想列表

// 播放记录
getUserMeditationRecords()         // 获取用户冥想记录
getUserMeditationStatistics()      // 获取用户冥想统计

// 评价相关
submitMeditationReview(data)       // 提交冥想评价
getMeditationReviews(meditationId) // 获取冥想评价列表
checkUserReviewed(meditationId)    // 检查用户是否已评价
```

## 页面路由配置

已在 `pages.json` 中添加的路由：

```json
{
  "path": "pages/meditation/detail/index",
  "style": { "navigationBarTitleText": "冥想详情" }
},
{
  "path": "pages/meditation/player/index",
  "style": { "navigationBarTitleText": "冥想播放" }
},
{
  "path": "pages/meditation/my-meditations/index",
  "style": { "navigationBarTitleText": "我的冥想" }
},
{
  "path": "pages/meditation/review/index",
  "style": { "navigationBarTitleText": "冥想评价" }
}
```

## 功能入口

### 1. 首页入口
- 在首页冥想标签下直接展示冥想列表
- 支持网格布局展示
- 点击可直接跳转到冥想详情

### 2. 我的页面入口
- 在"我的"页面添加了"我的冥想"选项
- 可查看已购买的冥想和播放统计

## 技术特点

### 1. 沉浸式体验
- 播放器采用全屏背景图设计
- 半透明遮罩和毛玻璃效果
- 优雅的播放控制界面

### 2. 音频播放
- 使用HTML5 audio标签
- 支持播放进度控制
- 播放速度调节
- 定时关闭功能

### 3. 数据管理
- 播放记录自动保存
- 统计信息实时更新
- 支持离线播放记录

### 4. 用户体验
- 下拉刷新和错误处理
- 加载状态提示
- 流畅的页面跳转
- 响应式设计

## 设计原则遵循

✅ **不使用自定义导航栏** - 所有页面都使用小程序自带导航栏
✅ **集成支付弹框** - 冥想详情页面使用统一的半屏式支付弹框
✅ **支付成功弹窗** - 支付成功使用弹窗提示而非独立页面

## 后端对接

前端已完全按照提供的后端API接口设计：

- 所有API调用都使用统一的 `request` 工具
- 支持 Token 认证
- 错误处理和用户提示
- 数据格式完全匹配后端返回结构

## 功能亮点

### 1. 播放器功能
- **进度控制**: 支持拖拽进度条跳转
- **播放控制**: 15秒快进/快退
- **速度调节**: 0.5x - 2.0x 播放速度
- **定时关闭**: 15/30/45/60分钟定时选项
- **收藏功能**: 一键收藏喜欢的冥想

### 2. 统计功能
- **播放统计**: 总次数、总时长、完成次数
- **播放记录**: 详细的播放历史
- **进度跟踪**: 每次播放的详细记录

### 3. 评价系统
- **多维评价**: 星级评分 + 标签选择 + 文字评价
- **匿名选项**: 支持匿名评价保护隐私
- **评价展示**: 评分统计和评价列表

## 使用流程

1. **浏览冥想**: 首页冥想标签 → 冥想列表
2. **查看详情**: 点击冥想 → 冥想详情页
3. **购买冥想**: 立即购买 → 支付弹框 → 支付成功
4. **开始冥想**: 开始冥想 → 播放器页面 → 沉浸式体验
5. **查看记录**: 我的冥想 → 播放统计和记录
6. **评价冥想**: 冥想详情 → 评价页面 → 提交评价

## 总结

冥想功能已完整实现，包含了从冥想浏览到体验完成的全流程。界面设计优雅，用户体验良好，功能完善，可以直接与后端API对接使用。特别是播放器的沉浸式设计和丰富的播放控制功能，为用户提供了优质的冥想体验。
