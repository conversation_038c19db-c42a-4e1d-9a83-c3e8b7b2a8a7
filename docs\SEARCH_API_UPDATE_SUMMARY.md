# 搜索接口更新总结

## 修改概述

根据后端 MiniAppSearchController 的接口实现，更新了前端的搜索接口，使其与后端接口保持一致。

## 主要修改内容

### 1. 分类搜索接口统一

**文件**: `api/search.js`

#### 修改前
```javascript
// 各个分类使用不同的接口路径
export function searchConsultants(params) {
  return request({
    url: '/system/consultant/search',
    method: 'GET',
    params: params
  })
}

export function searchCourses(params) {
  return request({
    url: '/miniapp/course/search',
    method: 'GET',
    params: params
  })
}

export function searchMeditations(params) {
  return request({
    url: '/miniapp/meditation/search',
    method: 'GET',
    params: params
  })
}

export function searchAssessments(params) {
  return request({
    url: '/miniapp/user/assessment/scales/search',
    method: 'GET',
    params: params
  })
}
```

#### 修改后
```javascript
// 所有分类搜索都使用统一的全局搜索接口，通过 type 参数区分
export function searchConsultants(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'consultant' }
  })
}

export function searchCourses(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'course' }
  })
}

export function searchMeditations(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'meditation' }
  })
}

export function searchAssessments(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'assessment' }
  })
}
```

### 2. 搜索记录保存接口调整

#### 修改前
```javascript
export function saveSearchRecord(params) {
  return request({
    url: '/miniapp/search/record',
    method: 'POST',
    data: params
  })
}
```

#### 修改后
```javascript
export function saveSearchRecord(params) {
  // 后端会在搜索时自动保存记录，这里可以是空实现或调用统计接口
  console.log('搜索记录已在后端自动保存:', params)
  return Promise.resolve({ code: 200, message: '记录已保存' })
}
```

### 3. 相关搜索接口路径修正

#### 修改前
```javascript
export function getRelatedSearches(keyword) {
  return request({
    url: '/miniapp/search/related',
    method: 'GET',
    data: { keyword }
  })
}
```

#### 修改后
```javascript
export function getRelatedSearches(keyword) {
  return request({
    url: '/miniapp/search/associate',
    method: 'GET',
    params: { keyword, limit: 10 }
  })
}
```

### 4. 智能搜索接口适配

#### 修改前
```javascript
export function intelligentSearch(params) {
  return request({
    url: '/miniapp/search/intelligent',
    method: 'POST',
    data: params
  })
}

export function sortSearchResults(params) {
  return request({
    url: '/miniapp/search/sort',
    method: 'POST',
    data: params
  })
}
```

#### 修改后
```javascript
export function intelligentSearch(params) {
  return globalSearch(params)  // 使用全局搜索代替
}

export function sortSearchResults(params) {
  console.log('搜索结果排序功能暂未实现:', params)
  return Promise.resolve({ code: 200, data: params.results || [] })
}
```

## 后端接口对应关系

| 前端接口 | 后端接口 | 说明 |
|---------|---------|------|
| `globalSearch` | `/miniapp/search/global` | 全局搜索 |
| `searchConsultants` | `/miniapp/search/global?type=consultant` | 咨询师搜索 |
| `searchCourses` | `/miniapp/search/global?type=course` | 课程搜索 |
| `searchMeditations` | `/miniapp/search/global?type=meditation` | 冥想搜索 |
| `searchAssessments` | `/miniapp/search/global?type=assessment` | 测评搜索 |
| `getSearchSuggestions` | `/miniapp/search/suggestions` | 搜索建议 |
| `getHotSearches` | `/miniapp/search/hot` | 热门搜索 |
| `getUserSearchHistory` | `/miniapp/search/history` | 搜索历史 |
| `clearUserSearchHistory` | `/miniapp/search/history` (DELETE) | 清空历史 |
| `quickSearch` | `/miniapp/search/quick` | 快速搜索 |
| `getSearchStatistics` | `/miniapp/search/statistics` | 搜索统计 |
| `getSearchTypes` | `/miniapp/search/types` | 搜索类型 |
| `getKeywordAssociate` | `/miniapp/search/associate` | 关键词联想 |

## 接口参数说明

### 全局搜索参数
```javascript
{
  keyword: string,    // 搜索关键词
  type: string,       // 搜索类型：all, consultant, course, meditation, assessment
  pageNum: number,    // 页码，默认1
  pageSize: number    // 每页数量，默认20
}
```

### 热门搜索参数
```javascript
{
  type: string,       // 类型：all, consultant, course, meditation, assessment
  limit: number       // 返回数量，最大50
}
```

### 搜索建议参数
```javascript
{
  keyword: string     // 关键词
}
```

## 优势

1. **接口统一**: 所有分类搜索都使用同一个后端接口，减少维护成本
2. **参数简化**: 通过 type 参数区分搜索类型，接口调用更简洁
3. **自动记录**: 后端在搜索时自动保存搜索记录，前端无需额外调用
4. **向后兼容**: 保持了前端接口的调用方式不变，只修改了内部实现

## 注意事项

1. 所有分类搜索现在都依赖于全局搜索接口
2. 搜索记录会在后端自动保存，前端的 saveSearchRecord 调用变为空操作
3. 智能搜索和排序功能暂时使用全局搜索代替
4. 确保后端 MiniAppSearchController 正确实现了所有接口
5. 注意处理搜索结果的数据格式，确保与前端组件兼容

## 测试建议

1. 测试各个分类的搜索功能是否正常
2. 验证搜索历史和热门搜索功能
3. 检查搜索建议和关键词联想功能
4. 确认搜索记录是否正确保存
5. 测试分页和搜索结果展示
