<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">搜索组件测试</text>
      <text class="test-desc">测试UniversalListItem组件对新数据结构的支持</text>
    </view>

    <!-- 测评数据测试 -->
    <view class="test-section">
      <text class="section-title">测评数据测试</text>
      <UniversalListItem :item="assessmentTestData" type="assessment" @click="handleItemClick" />
    </view>

    <!-- 咨询师数据测试 -->
    <view class="test-section">
      <text class="section-title">咨询师数据测试</text>
      <UniversalListItem :item="consultantTestData" type="consultant" @click="handleItemClick" />
    </view>

    <!-- 课程数据测试 -->
    <view class="test-section">
      <text class="section-title">课程数据测试</text>
      <UniversalListItem :item="courseTestData" type="course" @click="handleItemClick" />
    </view>

    <!-- 冥想数据测试 -->
    <view class="test-section">
      <text class="section-title">冥想数据测试</text>
      <UniversalListItem :item="meditationTestData" type="meditation" @click="handleItemClick" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 测评测试数据（基于您提供的真实数据结构）
const assessmentTestData = ref({
  id: 8,
  type: "assessment",
  title: "交流恐惧自陈量表(PRCA-24)",
  description: "PRCA-24表可能是评估一般交际恐惧的最佳工具，在测量人际交往方面显现出高度的信度及效度",
  coverImage: null,
  relevanceScore: 0,
  createTime: "2025-07-19T17:59:01.000+08:00",
  viewCount: 105,
  rating: 4.5,
  price: "0.00",
  tags: null,
  highlightDescription: null,
  highlightTitle: null,
  extraData: {
    questionCount: 24,
    testCount: 0,
    applicableAge: "成人",
    searchCount: 0,
    paidReportLevel: 3
  },
  assessment: {
    id: 8,
    name: "交流恐惧自陈量表(PRCA-24)",
    code: "PRCA24",
    description: "PRCA-24表可能是评估一般交际恐惧的最佳工具，在测量人际交往方面显现出高度的信度及效度",
    introduction: "McCroskey在1982年建立的新版量表，用于评定四种特定交流场合中的焦虑程度。含四个分量表，分别测量小组讨论、二人交谈、参加会议及当众演讲时的交流恐惧程度",
    questionCount: 24,
    duration: "5-10分钟",
    applicableAge: "成人",
    price: 0,
    free: true,
    imageUrl: null,
    viewCount: 105,
    monthTestCount: 150,
    todayTestCount: 12,
    createTime: "2025-07-19 17:59:01",
    updateTime: "2025-07-25 15:01:53"
  }
})

// 咨询师测试数据
const consultantTestData = ref({
  id: 1,
  type: "consultant",
  title: "张心理咨询师",
  description: "专业心理咨询师，擅长情感咨询、焦虑抑郁治疗",
  coverImage: null,
  relevanceScore: 0.95,
  createTime: "2025-01-01T10:00:00.000+08:00",
  viewCount: 520,
  rating: 4.8,
  price: "200.00",
  consultant: {
    id: 1,
    name: "张心理咨询师",
    avatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/consultant/avatar1.jpg",
    personalTitle: "3",
    personalIntro: "国家二级心理咨询师，从事心理咨询工作8年，擅长认知行为疗法，专注于情感问题、焦虑抑郁等心理困扰的治疗。",
    startYear: "2016",
    serviceCount: 1200,
    rating: 4.8,
    price: 200,
    consultStyles: [
      { dictLabel: "认知行为疗法" },
      { dictLabel: "情感咨询" },
      { dictLabel: "焦虑治疗" }
    ],
    specialties: [
      { name: "情感问题" },
      { name: "焦虑抑郁" },
      { name: "职场压力" }
    ]
  }
})

// 课程测试数据
const courseTestData = ref({
  id: 2,
  type: "course",
  title: "情绪管理与压力缓解",
  description: "学会科学的情绪管理方法，有效缓解生活和工作压力",
  coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/cover1.jpg",
  relevanceScore: 0.88,
  createTime: "2025-01-15T14:00:00.000+08:00",
  viewCount: 890,
  rating: 4.6,
  price: "99.00",
  course: {
    id: 2,
    title: "情绪管理与压力缓解",
    description: "本课程将教授您科学的情绪管理技巧，帮助您更好地应对生活和工作中的各种压力，提升心理健康水平。",
    coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/cover1.jpg",
    price: 99,
    originalPrice: 199,
    free: false,
    chapterCount: 12,
    totalDuration: 480,
    studentCount: 2580,
    rating: 4.6,
    difficulty: "初级",
    tags: '["情绪管理","压力缓解","心理健康"]', // 字符串格式的tags
    categories: ["心理课程", "自我提升"]
  }
})

// 冥想测试数据
const meditationTestData = ref({
  id: 3,
  type: "meditation",
  title: "深度放松冥想",
  description: "通过专业的引导冥想，帮助您快速进入深度放松状态",
  coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/cover1.jpg",
  relevanceScore: 0.82,
  createTime: "2025-01-20T16:00:00.000+08:00",
  viewCount: 1250,
  rating: 4.7,
  price: "0.00",
  meditation: {
    id: 3,
    title: "深度放松冥想",
    description: "这是一段专门设计的深度放松冥想练习，通过专业的语音引导，帮助您释放身心压力，进入宁静平和的状态。",
    coverImage: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/cover1.jpg",
    duration: 15,
    price: 0,
    free: true,
    playCount: 5680,
    favoriteCount: 1250,
    rating: 4.7,
    tags: '["放松","减压","睡眠"]', // 字符串格式的tags
    categories: ["冥想练习", "放松训练"]
  }
})

// 处理点击事件
const handleItemClick = (item) => {
  console.log('点击了项目:', item)
  uni.showToast({
    title: `点击了${item.title || item.name}`,
    icon: 'none'
  })
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.test-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;

  .test-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .test-desc {
    font-size: 24rpx;
    color: #666;
  }
}

.test-section {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;

  .section-title {
    display: block;
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    background-color: #f5f5f5;
    border-bottom: 1rpx solid #e0e0e0;
  }
}
</style>
