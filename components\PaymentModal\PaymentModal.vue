<template>
  <uni-popup ref="popup" type="bottom" :mask-click="false" :z-index="10000">
    <view class="payment-modal">
      <!-- 顶部拖拽条 -->
      <view class="modal-header">
        <view class="drag-bar"></view>
        <view class="header-title">订单支付</view>
        <view class="close-btn" @click="closeModal">
          <uni-icons type="close" size="20" color="#999"></uni-icons>
        </view>
      </view>

      <!-- 可滚动内容区域 -->
      <scroll-view scroll-y class="modal-content">
        <!-- 订单信息 -->
        <view class="order-section">
        <view class="order-header">
          <text class="order-title">订单详情</text>
          <text class="order-no">订单号：{{ orderInfo.orderNo }}</text>
        </view>
        
        <view class="product-info" v-if="orderInfo.product">
          <image :src="orderInfo.product.coverImage || defaultCover" mode="aspectFill" class="product-cover"></image>
          <view class="product-detail">
            <view class="product-title">{{ orderInfo.product.title }}</view>
            <view class="product-desc">{{ orderInfo.product.description }}</view>
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{ orderInfo.product.price }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-section">
        <view class="section-title">选择支付方式</view>
        <view class="method-list">
          <view 
            v-for="method in paymentMethods" 
            :key="method.id"
            :class="['method-item', { active: selectedMethod === method.id }]"
            @click="selectMethod(method.id)"
          >
            <view class="method-info">
              <image :src="method.icon" mode="aspectFit" class="method-icon"></image>
              <text class="method-name">{{ method.name }}</text>
            </view>
            <view class="method-radio">
              <uni-icons 
                v-if="selectedMethod === method.id" 
                type="checkmarkempty" 
                size="20" 
                color="#4CAF50"
              ></uni-icons>
              <view v-else class="radio-empty"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 优惠券 -->
      <view class="coupon-section" v-if="showCoupon">
        <view class="coupon-item" @click="selectCoupon">
          <view class="coupon-info">
            <uni-icons type="gift" size="20" color="#ff6b35"></uni-icons>
            <text>优惠券</text>
          </view>
          <view class="coupon-value">
            <text v-if="selectedCoupon">-¥{{ selectedCoupon.amount }}</text>
            <text v-else>选择优惠券</text>
            <uni-icons type="right" size="16" color="#999"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 价格明细 -->
      <view class="price-section">
        <view class="detail-item">
          <text>商品金额</text>
          <text>¥{{ orderInfo.product?.price || 0 }}</text>
        </view>
        <view class="detail-item" v-if="selectedCoupon">
          <text>优惠券</text>
          <text class="discount">-¥{{ selectedCoupon.amount }}</text>
        </view>
        <view class="detail-item total">
          <text>实付金额</text>
          <text class="total-price">¥{{ finalPrice }}</text>
        </view>
      </view>
      </scroll-view>
      <!-- 底部支付按钮 - 固定在底部 -->
      <view class="bottom-section">
        <view class="price-info">
          <text class="total-label">实付：</text>
          <text class="total-amount">¥{{ finalPrice }}</text>
        </view>
        <button
          class="pay-btn"
          @click="handlePay"
          :disabled="!selectedMethod || paying"
          :loading="paying"
        >
          {{ paying ? '支付中...' : '立即支付' }}
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed } from 'vue'
import { createOrder, requestWxPayment, showPaymentResult, buildOrderData } from '@/utils/payment.js'
import { ORDER_TYPES } from '@/api/payment.js'
// Props
const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => ({})
  },
  showCoupon: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['close', 'pay-success', 'pay-fail'])

// 响应式数据
const popup = ref(null)
const selectedMethod = ref('wechat')
const selectedCoupon = ref(null)
const paying = ref(false)

// 支付方式
const paymentMethods = ref([
  {
    id: 'wechat',
    name: '微信支付',
    icon: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/wechat-pay.png'
  },
  {
    id: 'alipay',
    name: '支付宝',
    icon: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/alipay.png'
  }
])

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'

// 计算属性
const finalPrice = computed(() => {
  const originalPrice = props.orderInfo.product?.price || 0
  const discount = selectedCoupon.value?.amount || 0
  return Math.max(0, originalPrice - discount).toFixed(2)
})

// 方法
const open = () => {
  popup.value?.open()
}

const close = () => {
  popup.value?.close()
}

const closeModal = () => {
  close()
  emit('close')
}

const selectMethod = (methodId) => {
  selectedMethod.value = methodId
}

const selectCoupon = () => {
  // TODO: 打开优惠券选择页面
  uni.showToast({
    title: '优惠券功能开发中',
    icon: 'none'
  })
}

const handlePay = async () => {
  if (!selectedMethod.value) {
    uni.showToast({
      title: '请选择支付方式',
      icon: 'none'
    })
    return
  }

  // 目前只支持微信支付
  if (selectedMethod.value !== 'wechat') {
    uni.showToast({
      title: '暂只支持微信支付',
      icon: 'none'
    })
    return
  }

  try {
    paying.value = true

    // 如果已有订单号，直接支付；否则先创建订单
    let orderNo = props.orderInfo.orderNo

    if (!orderNo) {
      // 创建订单
      const orderResult = await createOrder(
        props.orderInfo.type,
        buildOrderData(
          props.orderInfo.type,
          props.orderInfo.product,
          {
            paymentAmount: finalPrice.value,
            couponId: props.orderInfo.couponId,
            couponDiscount: props.orderInfo.couponDiscount,
            pointsUsed: props.orderInfo.pointsUsed,
            pointsDiscount: props.orderInfo.pointsDiscount
          }
        )
      )

      if (!orderResult.success) {
        throw new Error(orderResult.message)
      }

      orderNo = orderResult.data.orderNo
    }

    // 发起支付
    const result = await requestWxPayment(orderNo)

    // 显示支付结果
    showPaymentResult(result)

    if (result.success) {
      emit('pay-success', {
        orderNo: orderNo,
        orderId: props.orderInfo.orderId || result.orderId,
        method: selectedMethod.value,
        amount: finalPrice.value,
        transactionId: result.transactionId
      })

      // 延迟关闭弹框
      setTimeout(() => {
        closeModal()
      }, 1500)
    }

  } catch (error) {
    console.error('支付失败:', error)

    // 显示支付结果
    showPaymentResult(error)

    emit('pay-fail', error)
  } finally {
    paying.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.payment-modal {
  background: linear-gradient(to bottom, #fff, #fafafa);
  border-radius: 24rpx 24rpx 0 0;
  height: 85vh;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10001;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.modal-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  
  .drag-bar {
    position: absolute;
    top: 12rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 80rpx;
    height: 8rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4rpx;
    opacity: 0.6;
  }

  .header-title {
    font-size: 34rpx;
    font-weight: 600;
    color: #333;
    margin-top: 12rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .close-btn {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.order-section {
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    
    .order-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
    }
    
    .order-no {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .product-info {
    display: flex;
    
    .product-cover {
      width: 120rpx;
      height: 80rpx;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }
    
    .product-detail {
      flex: 1;
      
      .product-title {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .product-desc {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .product-price {
        display: flex;
        align-items: baseline;
        
        .price-symbol {
          font-size: 24rpx;
          color: #ff6b35;
        }
        
        .price-value {
          font-size: 32rpx;
          font-weight: 600;
          color: #ff6b35;
        }
      }
    }
  }
}

.payment-section {
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .method-list {
    .method-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1px solid #f8f8f8;
      
      &:last-child {
        border-bottom: none;
      }
      
      .method-info {
        display: flex;
        align-items: center;
        
        .method-icon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 20rpx;
        }
        
        .method-name {
          font-size: 28rpx;
          color: #333;
        }
      }
      
      .method-radio {
        .radio-empty {
          width: 20rpx;
          height: 20rpx;
          border: 2rpx solid #ddd;
          border-radius: 50%;
        }
      }
    }
  }
}

.coupon-section {
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .coupon-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .coupon-info {
      display: flex;
      align-items: center;
      gap: 12rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .coupon-value {
      display: flex;
      align-items: center;
      gap: 12rpx;
      font-size: 26rpx;
      color: #999;
      
      .discount {
        color: #ff6b35;
      }
    }
  }
}

.price-section {
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 0;
    font-size: 28rpx;
    
    &.total {
      border-top: 1px solid #f0f0f0;
      margin-top: 16rpx;
      padding-top: 24rpx;
      font-weight: 600;
      
      .total-price {
        color: #ff6b35;
        font-size: 32rpx;
      }
    }
    
    .discount {
      color: #ff6b35;
    }
  }
}

.bottom-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  // 安全区域适配
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  
  .price-info {
    .total-label {
      font-size: 26rpx;
      color: #666;
    }
    
    .total-amount {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff6b35;
    }
  }
  
  .pay-btn {
    padding: 24rpx 56rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border: none;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
    }

    &:disabled {
      background: linear-gradient(135deg, #ccc, #999);
      color: #666;
      box-shadow: none;
      transform: none;
    }
  }
}
</style>
