<template>
  <view class="settings-container">
    <!-- 个人信息设置 -->
    <view class="settings-section">
      <view class="section-title">个人信息</view>
      <view class="settings-item" @click="goToPersonalInfo">
        <view class="item-icon">
          <image src="/static/icon/personal-info.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">个人资料</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="goToAccountSecurity">
        <view class="item-icon">
          <image src="/static/icon/security.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">账号与安全</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 工作设置 -->
    <view class="settings-section">
      <view class="section-title">工作设置</view>
      <view class="settings-item" @click="goToWorkSettings">
        <view class="item-icon">
          <image src="/static/icon/work-settings.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">工作时间设置</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="goToNotificationSettings">
        <view class="item-icon">
          <image src="/static/icon/notification.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">消息通知</text>
        <view class="item-extra">
          <switch 
            :checked="notificationEnabled" 
            @change="onNotificationChange"
            color="#1890ff"
          />
        </view>
      </view>
      
      <view class="settings-item" @click="goToPrivacySettings">
        <view class="item-icon">
          <image src="/static/icon/privacy.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">隐私设置</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 应用设置 -->
    <view class="settings-section">
      <view class="section-title">应用设置</view>
      <view class="settings-item" @click="goToLanguageSettings">
        <view class="item-icon">
          <image src="/static/icon/language.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">语言设置</text>
        <view class="item-extra">
          <text class="extra-text">简体中文</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="clearCache">
        <view class="item-icon">
          <image src="/static/icon/cache.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">清理缓存</text>
        <view class="item-extra">
          <text class="extra-text">{{ cacheSize }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="checkUpdate">
        <view class="item-icon">
          <image src="/static/icon/update.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">检查更新</text>
        <view class="item-extra">
          <text class="extra-text">{{ appVersion }}</text>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 帮助与反馈 -->
    <view class="settings-section">
      <view class="section-title">帮助与反馈</view>
      <view class="settings-item" @click="goToHelp">
        <view class="item-icon">
          <image src="/static/icon/help.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">帮助中心</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="goToFeedback">
        <view class="item-icon">
          <image src="/static/icon/feedback.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">意见反馈</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="goToAbout">
        <view class="item-icon">
          <image src="/static/icon/about.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">关于我们</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 法律条款 -->
    <view class="settings-section">
      <view class="section-title">法律条款</view>
      <view class="settings-item" @click="goToUserAgreement">
        <view class="item-icon">
          <image src="/static/icon/agreement.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">用户协议</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="settings-item" @click="goToPrivacyPolicy">
        <view class="item-icon">
          <image src="/static/icon/privacy-policy.png" mode="aspectFit"></image>
        </view>
        <text class="item-title">隐私政策</text>
        <view class="item-extra">
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="showLogoutConfirm">退出登录</button>
    </view>

    <!-- 退出确认弹窗 -->
    <uni-popup ref="logoutPopup" type="dialog" v-if="showLogoutModal">
      <uni-popup-dialog 
        type="confirm" 
        title="确认退出" 
        content="确定要退出登录吗？"
        @confirm="confirmLogout"
        @close="closeLogoutModal"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const notificationEnabled = ref(true)
const cacheSize = ref('12.5MB')
const appVersion = ref('1.0.0')
const showLogoutModal = ref(false)

// 生命周期
onMounted(() => {
  loadSettings()
})

// 加载设置信息
const loadSettings = () => {
  // 获取缓存大小
  getCacheSize()
  
  // 获取应用版本
  getAppVersion()
  
  // 获取通知设置
  getNotificationSettings()
}

// 获取缓存大小
const getCacheSize = () => {
  // 这里可以调用实际的缓存大小计算方法
  cacheSize.value = '12.5MB'
}

// 获取应用版本
const getAppVersion = () => {
  // 获取应用版本信息
  appVersion.value = '1.0.0'
}

// 获取通知设置
const getNotificationSettings = () => {
  // 从本地存储获取通知设置
  const setting = uni.getStorageSync('notificationEnabled')
  notificationEnabled.value = setting !== false
}

// 通知开关变化
const onNotificationChange = (e) => {
  notificationEnabled.value = e.detail.value
  uni.setStorageSync('notificationEnabled', e.detail.value)
  
  uni.showToast({
    title: e.detail.value ? '已开启通知' : '已关闭通知',
    icon: 'success'
  })
}

// 跳转到个人资料
const goToPersonalInfo = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/personal-info/index'
  })
}

// 跳转到账号安全
const goToAccountSecurity = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/account-security/index'
  })
}

// 跳转到工作设置
const goToWorkSettings = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/work-settings/index'
  })
}

// 跳转到通知设置
const goToNotificationSettings = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/notification-settings/index'
  })
}

// 跳转到隐私设置
const goToPrivacySettings = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/privacy-settings/index'
  })
}

// 跳转到语言设置
const goToLanguageSettings = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/language-settings/index'
  })
}

// 清理缓存
const clearCache = () => {
  uni.showModal({
    title: '清理缓存',
    content: '确定要清理应用缓存吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '清理中...'
        })
        
        // 模拟清理缓存
        setTimeout(() => {
          uni.hideLoading()
          cacheSize.value = '0MB'
          uni.showToast({
            title: '清理完成',
            icon: 'success'
          })
        }, 1500)
      }
    }
  })
}

// 检查更新
const checkUpdate = () => {
  uni.showLoading({
    title: '检查中...'
  })
  
  // 模拟检查更新
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '已是最新版本',
      icon: 'success'
    })
  }, 1500)
}

// 跳转到帮助中心
const goToHelp = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/help/index'
  })
}

// 跳转到意见反馈
const goToFeedback = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/feedback/index'
  })
}

// 跳转到关于我们
const goToAbout = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/about/index'
  })
}

// 跳转到用户协议
const goToUserAgreement = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/user-agreement/index'
  })
}

// 跳转到隐私政策
const goToPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/privacy-policy/index'
  })
}

// 显示退出确认
const showLogoutConfirm = () => {
  showLogoutModal.value = true
}

// 关闭退出弹窗
const closeLogoutModal = () => {
  showLogoutModal.value = false
}

// 确认退出登录
const confirmLogout = async () => {
  try {
    await userStore.logout()
    
    uni.showToast({
      title: '已退出登录',
      icon: 'success'
    })
    
    // 跳转到登录页面
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  } catch (error) {
    console.error('退出登录失败:', error)
    uni.showToast({
      title: '退出失败',
      icon: 'none'
    })
  } finally {
    closeLogoutModal()
  }
}
</script>

<style scoped lang="scss">
.settings-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.settings-section {
  margin-bottom: 20rpx;
  
  .section-title {
    padding: 30rpx 30rpx 20rpx;
    font-size: 26rpx;
    color: #999;
  }
  
  .settings-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-icon {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      
      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
    
    .item-title {
      flex: 1;
      font-size: 30rpx;
      color: #333;
    }
    
    .item-extra {
      display: flex;
      align-items: center;
      
      .extra-text {
        font-size: 26rpx;
        color: #666;
        margin-right: 10rpx;
      }
      
      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
    
    &:active {
      background-color: #f5f5f5;
    }
  }
}

.logout-section {
  padding: 40rpx 30rpx;
  
  .logout-btn {
    width: 100%;
    padding: 28rpx 0;
    background-color: #ff4d4f;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 30rpx;
    font-weight: bold;
    
    &:active {
      background-color: #d9363e;
    }
  }
}
</style>
