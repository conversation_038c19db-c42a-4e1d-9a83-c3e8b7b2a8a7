# 排班设置功能增强

## 更新背景

原有的排班设置只能统一设置工作日和工作时间，无法满足灵活排班的需求。现在直接在现有的排班设置页面中改成每天都能设置不一样的时间段，支持更灵活的排班管理。

## 功能对比

### 原有功能
- ❌ 只能选择固定的工作日（如周一到周五）
- ❌ 所有工作日使用相同的时间段
- ❌ 无法设置多个时间段
- ❌ 缺乏灵活性

### 新功能
- ✅ 每天可以独立设置是否工作
- ✅ 每天可以设置不同的时间段
- ✅ 每天可以设置多个时间段
- ✅ 支持快速设置和批量操作
- ✅ 支持复制功能

## 核心功能

### 1. 每日独立设置
每天都有独立的开关和时间段设置：
```javascript
dailySchedules: [
  // 周一：全天工作
  { isWorking: true, timeSlots: [{ startTime: '09:00', endTime: '18:00' }] },
  // 周二：分段工作
  { isWorking: true, timeSlots: [
    { startTime: '09:00', endTime: '12:00' },
    { startTime: '14:00', endTime: '18:00' }
  ]},
  // 周三：休息
  { isWorking: false, timeSlots: [] },
  // ... 其他天
]
```

### 2. 多时间段支持
每天可以设置多个不连续的时间段：
- 上午：09:00-12:00
- 下午：14:00-18:00
- 晚上：19:00-21:00

### 3. 快速操作功能

#### 快速设置按钮
- **9-18点**：快速设置为09:00-18:00
- **8-17点**：快速设置为08:00-17:00
- **复制上一天**：复制前一天的设置

#### 批量操作
- **设置工作日(9-18点)**：一键设置周一到周五为09:00-18:00
- **设置周末(10-17点)**：一键设置周六周日为10:00-17:00
- **清空所有**：清空所有排班设置

### 4. 智能验证
- 验证时间段的有效性（开始时间 < 结束时间）
- 确保至少有一个工作日
- 确保模板名称不为空

## 界面设计

### 1. 每日排班卡片
```vue
<view class="day-schedule">
  <view class="day-header">
    <text class="day-name">周一</text>
    <switch :checked="isWorking" @change="toggleDayWorking" />
  </view>
  
  <view class="day-content" v-if="isWorking">
    <!-- 时间段列表 -->
    <!-- 添加时间段按钮 -->
    <!-- 快速设置按钮 -->
  </view>
</view>
```

### 2. 时间段管理
- 时间选择器：选择开始和结束时间
- 删除按钮：删除多余的时间段
- 添加按钮：添加新的时间段

### 3. 批量操作区域
- 三个批量操作按钮
- 清晰的功能说明

## 数据结构变化

### 原有数据结构
```javascript
templateForm: {
  name: '',
  remark: '',
  workDays: [1, 2, 3, 4, 5], // 工作日数组
  startTime: '09:00',        // 统一开始时间
  endTime: '18:00'           // 统一结束时间
}
```

### 新数据结构
```javascript
templateForm: {
  name: '',
  remark: '',
  dailySchedules: [
    // 7个元素，对应周一到周日
    { isWorking: true, timeSlots: [{ startTime: '09:00', endTime: '18:00' }] },
    { isWorking: true, timeSlots: [{ startTime: '09:00', endTime: '18:00' }] },
    // ... 其他天
  ]
}
```

## 核心方法

### 1. 日程管理方法
```javascript
// 切换某天的工作状态
const toggleDayWorking = (dayIndex, isWorking) => {
  templateForm.value.dailySchedules[dayIndex].isWorking = isWorking
}

// 更新时间段
const updateTimeSlot = (dayIndex, slotIndex, field, value) => {
  templateForm.value.dailySchedules[dayIndex].timeSlots[slotIndex][field] = value
}

// 添加时间段
const addTimeSlot = (dayIndex) => {
  templateForm.value.dailySchedules[dayIndex].timeSlots.push({
    startTime: '09:00',
    endTime: '18:00'
  })
}

// 删除时间段
const removeTimeSlot = (dayIndex, slotIndex) => {
  if (templateForm.value.dailySchedules[dayIndex].timeSlots.length > 1) {
    templateForm.value.dailySchedules[dayIndex].timeSlots.splice(slotIndex, 1)
  }
}
```

### 2. 快速操作方法
```javascript
// 快速设置时间
const setQuickTime = (dayIndex, startTime, endTime) => {
  templateForm.value.dailySchedules[dayIndex].timeSlots = [{
    startTime,
    endTime
  }]
}

// 复制上一天的设置
const copyFromPrevious = (dayIndex) => {
  if (dayIndex > 0) {
    const previousDay = templateForm.value.dailySchedules[dayIndex - 1]
    templateForm.value.dailySchedules[dayIndex] = {
      isWorking: previousDay.isWorking,
      timeSlots: JSON.parse(JSON.stringify(previousDay.timeSlots))
    }
  }
}

// 批量设置工作日
const setAllWorkingDays = (startTime, endTime) => {
  templateForm.value.dailySchedules.forEach((day, index) => {
    if (index < 5) { // 周一到周五
      day.isWorking = true
      day.timeSlots = [{ startTime, endTime }]
    }
  })
}
```

### 3. 数据转换方法
```javascript
// 保存时转换为后端格式
const saveTemplate = async () => {
  const templateItems = []
  
  templateForm.value.dailySchedules.forEach((daySchedule, dayIndex) => {
    if (daySchedule.isWorking) {
      daySchedule.timeSlots.forEach(slot => {
        templateItems.push({
          dayOfWeek: dayIndex + 1, // 1-7对应周一到周日
          startTime: slot.startTime + ':00',
          endTime: slot.endTime + ':00',
          centerId: 1
        })
      })
    }
  })
  
  // 发送到后端...
}
```

## 样式设计

### 1. 响应式布局
- 移动端友好的卡片设计
- 清晰的视觉层次
- 合适的间距和圆角

### 2. 交互反馈
- 开关状态的视觉反馈
- 按钮的点击效果
- 时间段的高亮显示

### 3. 颜色系统
- 主色调：#1890ff（蓝色）
- 成功色：#52c41a（绿色）
- 警告色：#faad14（橙色）
- 危险色：#ff4d4f（红色）

## 兼容性处理

### 1. 数据迁移
- 编辑现有模板时，将旧格式转换为新格式
- 保持对现有API的兼容性
- 优雅降级处理

### 2. 向后兼容
- 保存时仍然生成标准的templateItems格式
- 显示时支持新的分组显示
- 编辑时支持多时间段恢复

## 使用场景

### 1. 灵活排班
- **医生**：上午门诊，下午手术，晚上值班
- **咨询师**：分时段接待不同类型的客户
- **培训师**：上午理论课，下午实践课

### 2. 特殊安排
- **周末半天工作**：周六上午工作，下午休息
- **错峰排班**：避开高峰期，设置特殊时间段
- **季节性调整**：根据季节调整工作时间

### 3. 多地点工作
- **上午A地点**：09:00-12:00
- **下午B地点**：14:00-18:00
- **晚上在线咨询**：19:00-21:00

## 测试验证

### 1. 功能测试
- ✅ 每日开关控制
- ✅ 多时间段添加/删除
- ✅ 快速设置功能
- ✅ 批量操作功能
- ✅ 复制功能

### 2. 数据测试
- ✅ 保存和加载
- ✅ 编辑现有模板
- ✅ 数据验证
- ✅ 错误处理

### 3. 界面测试
- ✅ 响应式布局
- ✅ 交互反馈
- ✅ 样式一致性
- ✅ 用户体验

## 总结

本次更新将原有的固定排班模式升级为灵活的每日独立排班模式，大大提升了排班设置的灵活性和实用性。主要改进包括：

1. **灵活性提升**：每天可以独立设置，支持多时间段
2. **操作便捷**：提供快速设置、批量操作、复制等功能
3. **界面优化**：清晰的卡片式布局，良好的用户体验
4. **数据兼容**：保持与现有系统的兼容性
5. **功能完善**：智能验证、错误处理、状态管理

这个更新能够满足各种复杂的排班需求，为用户提供更加灵活和强大的排班管理工具。
