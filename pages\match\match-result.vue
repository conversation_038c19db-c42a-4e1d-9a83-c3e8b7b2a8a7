<template>
	<view class="match-result">
		<view class="success-tip">
			<image class="success-icon" mode="aspectFit"></image>
			<view class="tip-text">已为您匹配到最适合的咨询师</view>
		</view>
		
		<swiper class="counselor-swiper" vertical @change="handleSwiperChange" :current="currentIndex">
			<swiper-item v-for="(item, index) in counselorList" :key="index">
				<view class="counselor-card" :style="{ transform: `scale(${1 - index * 0.05})`, opacity: 1 - index * 0.2 }" @click="handleViewDetailWithCounselor(item)" hover-class="card-hover">
					<view class="basic-info">
						<view class="left">
							<image class="avatar" :src="item.imageUrl" mode="aspectFill"></image>
							<view class="name-info">
								<view class="name">{{item.name}}</view>
								<view class="title">{{getTitleText(item.personalTitle)}}</view>
							</view>
						</view>
						<view class="right">
							<view class="match-rate">
								<text class="rate">{{item.matchRate}}%</text>
								<text class="label">匹配度</text>
							</view>
						</view>
					</view>
					
					<view class="divider"></view>
					
					<view class="detail-info">
						<view class="info-item" v-if="item.expertises && item.expertises.length">
							<text class="label">擅长领域：</text>
							<view class="tags">
								<text v-for="(expertise, idx) in item.expertises.slice(0, 3)" :key="idx" class="tag">{{expertise.typeName}}</text>
							</view>
						</view>
						<!-- <view class="info-item">
							<text class="label">咨询价格：</text>
							<view class="price">
								<text class="amount">¥{{item.price}}</text>
								<text class="unit">/60分钟</text>
							</view>
						</view> -->
					</view>
				</view>
			</swiper-item>
		</swiper>
		
		<view class="swipe-tip" v-if="counselorList.length > 1">
			<text class="tip-text">上滑查看更多咨询师</text>
			<text class="arrow">↑</text>
		</view>
		
		<view class="action-buttons">
			<button class="btn secondary" @click="handleViewDetail">查看详情</button>
			<button class="btn primary" @click="handleMakeAppointment">立即预约</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 咨询师数据
const allCounselors = ref([]);
const currentIndex = ref(0);

// 当前显示的咨询师列表（最多5个）
const counselorList = computed(() => {
	return allCounselors.value.slice(0, 5);
});

// 当前选中的咨询师
const currentCounselor = computed(() => {
	return counselorList.value[currentIndex.value] || null;
});

// 获取职称文本
const getTitleText = (titleCode) => {
	const titles = {
		'1': '初级心理咨询师',
		'2': '中级心理咨询师',
		'3': '高级心理咨询师',
		'4': '资深心理咨询师'
	};
	return titles[titleCode] || '心理咨询师';
};

// 处理滑动切换
const handleSwiperChange = (e) => {
	currentIndex.value = e.detail.current;
};

// 初始化数据
const initData = () => {
	const matchedConsultants = uni.getStorageSync('matchedConsultants');
	if (matchedConsultants && matchedConsultants.length > 0) {
		// 设置第一个咨询师的匹配度为98%
		const baseMatchRate = 98;
		
		// 处理所有咨询师数据，添加匹配度
		allCounselors.value = matchedConsultants.map((item, index) => {
			// 第一个保持98%，后面的随机减少但不超过10%
			const reduction = index === 0 ? 0 : Math.floor(Math.random() * 10) + 1;
			return {
				...item,
				matchRate: baseMatchRate - reduction
			};
		});
		
		// 按匹配度降序排序
		allCounselors.value.sort((a, b) => b.matchRate - a.matchRate);
	} else {
		// 如果没有匹配结果，返回上一页
		uni.showToast({
			title: '未找到匹配的咨询师',
			icon: 'none'
		});
		setTimeout(() => {
			uni.navigateBack();
		}, 1500);
	}
};

// 查看详情
const handleViewDetail = () => {
	if (currentCounselor.value) {
		uni.navigateTo({
			url: `/pages/classification/counselor-detail/index?id=${currentCounselor.value.id}`
		});
	}
};

// 立即预约
const handleMakeAppointment = () => {
	if (currentCounselor.value) {
		uni.navigateTo({
			url: `/pages/appointment/index?counselorId=${currentCounselor.value.id}`
		});
	}
};

// 查看详情并传递咨询师信息
const handleViewDetailWithCounselor = (counselor) => {
	uni.navigateTo({
		url: `/pages/classification/counselor-detail/index?id=${counselor.id}`
	});
};

onMounted(() => {
	initData();
});
</script>

<style lang="scss" scoped>
.match-result {
	min-height: calc(100vh - 64rpx);
	background: linear-gradient(180deg, #EEF1FC 0%, #F8F9FC 100%);
	padding: 32rpx;
	display: flex;
	flex-direction: column;
	
	.success-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
		
		.success-icon {
			width: 120rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
		}
		
		.tip-text {
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
		}
	}
	
	.counselor-swiper {
		flex: 1;
		height: calc(100vh - 400rpx);
	}
	
	.counselor-card {
		background: #fff;
		border-radius: 24rpx;
		padding: 32rpx;
		margin-bottom: 20rpx;
		transition: all 0.3s ease;
		cursor: pointer;
		
		&.card-hover {
			background: #f9f9f9;
		}
		
		.basic-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.left {
				display: flex;
				align-items: center;
				
				.avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 60rpx;
					margin-right: 24rpx;
				}
				
				.name-info {
					.name {
						font-size: 32rpx;
						font-weight: 500;
						color: #333;
						margin-bottom: 8rpx;
					}
					
					.title {
						font-size: 26rpx;
						color: #666;
					}
				}
			}
			
			.right {
				.match-rate {
					text-align: center;
					
					.rate {
						font-size: 40rpx;
						font-weight: 600;
						color: #5e72e4;
						margin-bottom: 4rpx;
					}
					
					.label {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
		
		.divider {
			height: 2rpx;
			background: #f0f0f0;
			margin: 32rpx 0;
		}
		
		.detail-info {
			.info-item {
				margin-bottom: 24rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 12rpx;
					display: block;
				}
				
				.tags {
					display: flex;
					flex-wrap: wrap;
					gap: 16rpx;
					
					.tag {
						padding: 8rpx 24rpx;
						background: rgba(94, 114, 228, 0.1);
						color: #5e72e4;
						border-radius: 28rpx;
						font-size: 26rpx;
					}
				}
				
				.price {
					.amount {
						font-size: 40rpx;
						font-weight: 600;
						color: #ff6b6b;
					}
					
					.unit {
						font-size: 26rpx;
						color: #666;
						margin-left: 8rpx;
					}
				}
			}
		}
	}
	
	.swipe-tip {
		text-align: center;
		margin: 20rpx 0;
		
		.tip-text {
			font-size: 24rpx;
			color: #999;
		}
		
		.arrow {
			display: block;
			font-size: 32rpx;
			color: #999;
			animation: bounce 1.5s infinite;
		}
	}
	
	.action-buttons {
		display: flex;
		gap: 24rpx;
		margin-top: 20rpx;
		
		.btn {
			flex: 1;
			height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
			
			&.primary {
				background: #5e72e4;
				color: #fff;
				
				&:active {
					opacity: 0.9;
				}
			}
			
			&.secondary {
				background: rgba(94, 114, 228, 0.1);
				color: #5e72e4;
				
				&:active {
					background: rgba(94, 114, 228, 0.2);
				}
			}
		}
	}
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-10rpx);
	}
	60% {
		transform: translateY(-5rpx);
	}
}
</style> 