# 修正后的预约咨询弹框实现文档

## 修正内容

根据您的要求，已经修正了以下内容：

1. ✅ **复用预约页面的时间表** - 使用 `pages/appointment/index.vue` 的时间表逻辑
2. ✅ **咨询方式使用字典** - 从 `sys_consult_type` 字典获取选项
3. ✅ **支付方式改为只有微信支付** - 移除其他支付方式选项

## 🔧 核心修正

### **1. 时间表复用**

#### **模板结构修正**
```vue
<!-- 修正前：自定义时间表结构 -->
<view class="time-grid">
  <view class="time-period" v-for="item in timeInfo" :key="item.id">
    <view class="period-title">
      <image :src="iconUrl" />
      {{ item.rangeName }}
    </view>
    <view class="time-row">
      <view
        v-for="slot in item.slots"
        :key="slot.id"
        :class="{ 'is-active': selectedTimeSlots.indexOf(slot.fullDate + slot.time) !== -1 }"
        @click="handleTimeSlotSelect(slot)"
      >
        {{ slot.time }}
      </view>
    </view>
  </view>
</view>

<!-- 修正后：复用预约页面的时间表结构 -->
<view class="time-table">
  <view class="time-period" v-for="item in timeInfo" :key="item.id">
    <view class="period-header">
      <image
        class="period-icon"
        :src="`https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/${encodeURIComponent(item.rangeName)}.png`"
      />
      <text class="period-name">{{ item.rangeName }}</text>
    </view>
    <view class="time-slots">
      <view
        v-for="(hour, hourIndex) in item.slots"
        :key="hourIndex"
        :class="{
          'time-slot': true,
          'selected': selectTime.includes(hour.fullDate + ' ' + hour.time),
          'disabled': hour.timeStatus === '已约满' || hour.timeStatus === '已过期'
        }"
        @click="handleHour(hour)"
      >
        <text class="time-text">{{ hour.time }}</text>
        <text class="status-text" v-if="hour.timeStatus">{{ hour.timeStatus }}</text>
      </view>
    </view>
  </view>
</view>
```

#### **时间选择逻辑修正**
```javascript
// 修正前：自定义时间选择逻辑
const selectedTimeSlots = ref([]);
const handleTimeSlotSelect = (slot) => {
  const fullTime = slot.fullDate + slot.time;
  const index = selectedTimeSlots.value.indexOf(fullTime);
  if (index === -1) {
    selectedTimeSlots.value.push(fullTime);
  } else {
    selectedTimeSlots.value.splice(index, 1);
  }
};

// 修正后：复用预约页面的时间选择逻辑
const selectTime = ref([]);
const handleHour = (hour) => {
  // 检查时间槽是否已禁用
  if (hour.timeStatus === '已约满' || hour.timeStatus === '已过期') {
    uni.showToast({
      title: "该时段不可选",
      icon: "none",
      duration: 2000
    });
    return;
  }

  const fullTime = hour.fullDate + ' ' + hour.time;
  
  // 切换选择状态
  const index = selectTime.value.indexOf(fullTime);
  if (index === -1) {
    selectTime.value.push(fullTime);
  } else {
    selectTime.value.splice(index, 1);
  }
};
```

### **2. 咨询方式字典获取**

#### **数据结构修正**
```javascript
// 修正前：硬编码咨询方式选项
const consultationTypes = ref([
  { label: '线上咨询', value: 'online' },
  { label: '线下咨询', value: 'offline' },
  { label: '电话咨询', value: 'phone' }
]);

// 修正后：从字典获取咨询方式选项
const consultationTypes = ref([]);

// 加载咨询方式字典
const loadConsultationTypes = async () => {
  try {
    const res = await useDict('sys_consult_type');
    if (res && res.sys_consult_type) {
      consultationTypes.value = res.sys_consult_type.map(item => ({
        label: item.label,
        value: item.value
      }));
    }
  } catch (error) {
    console.error('加载咨询方式字典失败:', error);
    // 使用默认值
    consultationTypes.value = [
      { label: '线上咨询', value: 'online' },
      { label: '线下咨询', value: 'offline' }
    ];
  }
};
```

#### **页面加载时获取字典**
```javascript
onLoad(async (options) => {
  try {
    // 获取字典数据
    await getPsyConsultantLevel();
    await loadConsultationTypes(); // 新增：加载咨询方式字典
    counselorId.value = options.id;
    // 其他初始化逻辑...
  } catch (error) {
    console.error('页面初始化失败:', error);
  }
});
```

### **3. 支付方式简化**

#### **支付方式选项修正**
```javascript
// 修正前：多种支付方式
const paymentMethods = ref([
  { label: '微信支付', value: 'wechat' },
  { label: '支付宝', value: 'alipay' },
  { label: '余额支付', value: 'balance' }
]);

// 修正后：只有微信支付
const paymentMethods = ref([
  { label: '微信支付', value: 'wechat' }
]);

// 表单默认值也设置为微信支付
const appointmentForm = ref({
  consultationType: '',
  appointmentDate: '',
  paymentMethod: 'wechat', // 默认微信支付
  selectedTimes: []
});
```

#### **表单验证简化**
```javascript
// 修正后：由于只有微信支付，可以简化验证逻辑
const confirmAppointment = () => {
  // 验证表单
  if (!appointmentForm.value.consultationType) {
    uni.showToast({
      title: '请选择咨询方式',
      icon: 'none'
    });
    return;
  }

  if (selectTime.value.length === 0) {
    uni.showToast({
      title: '请选择预约时间',
      icon: 'none'
    });
    return;
  }

  // 支付方式验证可以省略，因为默认就是微信支付

  // 构建预约数据
  const appointmentData = {
    consultantId: counselorId.value,
    consultantName: name.value,
    consultationType: appointmentForm.value.consultationType,
    paymentMethod: appointmentForm.value.paymentMethod, // 固定为 'wechat'
    selectedTimes: selectTime.value.map(time => {
      const [date, timeStr] = time.split(' ');
      return { date, time: timeStr };
    })
  };

  // 跳转到订单页面
  uni.navigateTo({
    url: `/pages/consultation/order/index?consultantId=${counselorId.value}&appointmentData=${encodeURIComponent(JSON.stringify(appointmentData))}`
  });

  closeAppointmentPopup();
};
```

## 🎨 样式修正

### **时间表样式**
```scss
// 修正后：与预约页面保持一致的时间表样式
.time-table {
  .time-period {
    margin-bottom: 32rpx;
    
    .period-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      
      .period-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }
      
      .period-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
      }
    }
    
    .time-slots {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
    }
    
    .time-slot {
      position: relative;
      padding: 16rpx 24rpx;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      border: 2rpx solid transparent;
      min-width: 120rpx;
      text-align: center;
      
      &.selected {
        background-color: #e8f6ff;
        border-color: #20a3f3;
      }
      
      &.disabled {
        background-color: #f0f0f0;
        color: #ccc;
        opacity: 0.6;
      }
      
      .time-text {
        font-size: 26rpx;
        color: #333;
        display: block;
      }
      
      .status-text {
        font-size: 20rpx;
        color: #999;
        display: block;
        margin-top: 4rpx;
      }
    }
  }
}
```

## 📱 用户体验改进

### **1. 数据一致性**
- ✅ 时间选择逻辑与预约页面完全一致
- ✅ 时间数据格式统一（`fullDate + ' ' + time`）
- ✅ 状态判断逻辑一致

### **2. 字典数据动态加载**
- ✅ 咨询方式从后端字典动态获取
- ✅ 支持字典数据更新
- ✅ 有降级处理机制

### **3. 支付流程简化**
- ✅ 默认选择微信支付
- ✅ 减少用户选择步骤
- ✅ 简化表单验证逻辑

## 🔄 数据流程

### **完整的预约流程**
1. 用户点击"预约咨询"按钮
2. 弹框打开，加载咨询方式字典数据
3. 用户选择咨询方式（从字典获取的选项）
4. 用户选择预约日期（复用原有日期选择器）
5. 用户选择时间段（复用预约页面的时间表逻辑）
6. 支付方式默认为微信支付
7. 点击确认预约，进行表单验证
8. 验证通过后，打包数据跳转到订单页面

### **数据传递格式**
```javascript
const appointmentData = {
  consultantId: 123,
  consultantName: "张医生",
  consultationType: "online", // 从字典获取的值
  paymentMethod: "wechat", // 固定为微信支付
  selectedTimes: [
    { date: "2024-01-15", time: "09:00" },
    { date: "2024-01-15", time: "10:00" }
  ]
};
```

## ✅ 修正效果

1. **时间表一致性**: 与预约页面完全一致的时间选择体验
2. **数据动态性**: 咨询方式从后端字典动态获取，便于管理
3. **流程简化**: 支付方式固定为微信支付，减少用户选择步骤
4. **代码复用**: 最大化复用预约页面的时间选择逻辑
5. **用户体验**: 统一的交互模式和视觉效果

现在预约弹框完全符合您的要求，使用了正确的预约页面时间表，咨询方式从字典获取，支付方式只有微信支付！
