<template>
  <view class="evaluation">
    <view class="evaluation-container">
      <!-- 左侧分类 -->
      <scroll-view class="category-list" scroll-y>
        <view 
          class="category-item" 
          v-for="item in categoryList" 
          :key="item.id"
          :class="{ active: currentCategory.id === item.id }"
          @click="switchCategory(item)"
        >
          <text>{{item.title}}</text>
        </view>
      </scroll-view>
      
      <!-- 右侧列表 -->
      <scroll-view class="assessment-list" scroll-y>
        <view class="list-item" v-for="item in currentList" :key="item.id" @click="viewDetail(item.id)">
          <image v-if="item.coverImage" class="item-image" :src="item.coverImage" mode="aspectFill"></image>
          <view class="item-main">
            <text class="title">{{item.name}}</text>
            <view class="desc" v-if="item.description">{{item.description}}</view>
            <view class="tags" v-if="item.tags">
              <text class="tag" v-for="tag in item.tags.split(',')" :key="tag">{{tag}}</text>
            </view>
          </view>
          <view class="item-footer">
            <view class="footer-left">
              <text class="question-count">{{item.questionCount}}道题</text>
              <text class="time-limit" v-if="item.timeLimit">{{Math.ceil(item.timeLimit/60)}}分钟</text>
              <view v-if="item.testRecord" class="status-badge" :class="getStatusClass(item.testRecord.status)">
                <text class="status-text">{{getStatusText(item.testRecord.status)}}</text>
              </view>
            </view>
            <view class="footer-right">
              <view class="price" v-if="item.payMode === 1">
                <text class="price-text">¥{{item.currentPrice || item.originalPrice}}</text>
              </view>
              <view class="free-tag" v-else>
                <text class="free-text">免费</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 加载状态 -->
        <view class="loading" v-if="loading">
          <text>加载中...</text>
        </view>
        <!-- 空状态 -->
        <view class="empty" v-if="!loading && currentList.length === 0">
          <text>暂无测评量表</text>
        </view>
      </scroll-view>
    </view> 
    <cc-myTabbar :tabBarShow="3"></cc-myTabbar>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { listAssessment, getAssessmentRecords, getPopularAssessments, getLatestAssessments } from '@/api/evaluation'
import { onLoad, onReady } from "@dcloudio/uni-app";

const categoryList = ref([
  { id: 'all', title: '全部', type: 'all' },
  { id: 'hot', title: '热门', type: 'hot' },
  { id: 'latest', title: '最新', type: 'latest' }
])
const currentCategory = ref({ id: 'all', title: '全部', type: 'all' })
const loading = ref(false)
const testRecords = ref([])
const assessmentList = ref([])

// 计算当前分类下的列表
const currentList = computed(() => {
  // 将测评记录信息合并到列表中
  return assessmentList.value.map(item => {
    const record = testRecords.value.find(record => record.scaleId === item.id)
    return {
      ...item,
      testRecord: record
    }
  })
})

onLoad(() => {
  getList()
  getTestRecordsList()
})

onReady(() => {
	uni.hideTabBar();
});

// 切换分类
const switchCategory = async (category) => {
  if (!category || currentCategory.value.id === category.id) return
  currentCategory.value = category
  await getList()
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'status-pending',    // 进行中
    1: 'status-pending',    // 暂停
    2: 'status-completed'   // 已完成
  }
  return classMap[status] || 'status-pending'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    0: '进行中',
    1: '暂停中',
    2: '已完成'
  }
  return textMap[status] || '未开始'
}

// 获取测评记录列表
const getTestRecordsList = async () => {
  try {
    const res = await getAssessmentRecords()
    if (res.code === 200) {
      testRecords.value = res.data || []
    }
  } catch (error) {
    console.error('获取测评记录失败:', error)
  }
}

// 获取列表数据
const getList = async () => {
  if (loading.value) return
  loading.value = true
  try {
    let res

    // 根据当前分类获取不同的数据
    switch (currentCategory.value.type) {
      case 'hot':
        res = await getPopularAssessments(20)
        break
      case 'latest':
        res = await getLatestAssessments(20)
        break
      default:
        res = await listAssessment({
          pageNum: 1,
          pageSize: 50
        })
        break
    }

    if (res.code === 200) {
      assessmentList.value = res.data || []
    } else {
      uni.showToast({
        title: res.msg || '获取列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    uni.showToast({
      title: '获取列表失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = (id) => {
  // 查找对应的测评记录
  const record = testRecords.value.find(record => record.scaleId === id)
  if (record) {
    // 如果有记录，传递recordId
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${id}&recordId=${record.id}&status=${record.status}`
    })
  } else {
    // 没有记录，正常跳转
    uni.navigateTo({
      url: `/pages/evaluation/detail/index?id=${id}`
    })
  }
}
</script>

<style lang="scss" scoped>
.evaluation {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.evaluation-container {
  display: flex;
  height: 100vh;
}

.category-list {
  width: 200rpx;
  height: 100%;
  background-color: #f8f8f8;
  
  .category-item {
    padding: 30rpx 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1rpx solid #eee;
    
    &.active {
      color: #409eff;
      background-color: #fff;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 32rpx;
        background-color: #409eff;
      }
    }
  }
}

.assessment-list {
  flex: 1;
  width: calc(100% - 240rpx);
  height: 100%;
  padding: 0rpx 20rpx 0 20rpx;
  background-color: #fff;
  
  .list-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    .item-image {
      width: 100%;
      height: 300rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
    }

    .item-main {
      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .desc {
        font-size: 28rpx;
        color: #666;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        margin-bottom: 10rpx;
      }

      .tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .tag {
          padding: 4rpx 8rpx;
          background-color: #f0f0f0;
          color: #666;
          font-size: 22rpx;
          border-radius: 4rpx;
        }
      }
    }

    .item-footer {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .footer-left {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .question-count {
          font-size: 24rpx;
          color: #409eff;
          font-weight: bold;
        }

        .time-limit {
          font-size: 24rpx;
          color: #999;
        }

        .status-badge {
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          font-size: 22rpx;

          &.status-pending {
            background-color: #fff7e6;
            .status-text {
              color: #fa8c16;
            }
          }

          &.status-completed {
            background-color: #f6ffed;
            .status-text {
              color: #52c41a;
            }
          }
        }
      }

      .footer-right {
        .price {
          .price-text {
            font-size: 28rpx;
            color: #ff4d4f;
            font-weight: bold;
          }
        }

        .free-tag {
          .free-text {
            font-size: 24rpx;
            color: #52c41a;
            font-weight: bold;
          }
        }
      }
    }

    .loading {
      text-align: center;
      padding: 40rpx 0;
      color: #999;
      font-size: 28rpx;
    }
  }

  .empty {
    text-align: center;
    padding: 100rpx 0;
    color: #999;
  }
}
</style>