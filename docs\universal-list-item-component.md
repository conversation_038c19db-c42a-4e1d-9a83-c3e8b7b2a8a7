# 通用列表项组件文档

## 概述

`UniversalListItem` 是一个通用的列表项组件，采用您提供的咨询师样式设计，可以适用于咨询师、课程、冥想、测评等不同类型的数据展示。

## 组件特性

### 🎯 统一的视觉设计
- **左右布局**: 左侧140rpx图片，右侧自适应内容
- **一致样式**: 所有类型使用相同的布局和样式规范
- **响应式**: 自动适配不同屏幕尺寸

### 🔧 智能数据适配
- **自动识别**: 根据type参数自动适配不同数据结构
- **容错处理**: 优雅处理缺失字段和异常数据
- **多格式支持**: 支持不同的标签和数据格式

### 📱 优秀的用户体验
- **图片容错**: 自动处理图片加载失败
- **文本截断**: 长文本自动省略
- **滚动标签**: 标签过多时支持横向滚动

## 使用方法

### 基本用法

```vue
<template>
  <UniversalListItem
    :item="itemData"
    :type="'consultant'"
    @click="handleItemClick"
  />
</template>

<script setup>
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

const itemData = {
  // 数据对象
}

const handleItemClick = (item) => {
  // 处理点击事件
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| item | Object | - | 数据对象（必填） |
| type | String | 'consultant' | 类型：'consultant', 'course', 'meditation', 'assessment' |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | item | 点击列表项时触发，返回数据对象 |

## 数据结构适配

### 1. 咨询师 (consultant)

```javascript
const consultantData = {
  id: 1,
  name: "张医生",                    // 显示名称
  avatar: "头像URL",                 // 头像图片
  personalTitle: "3",               // 职业等级 (1-4)
  province: "北京市",               // 省份
  city: "朝阳区",                   // 城市
  district: "望京",                 // 区域
  address: "北京市朝阳区",          // 完整地址（备用）
  personalIntro: "专业心理咨询师",   // 个人介绍
  consultStyles: [                  // 咨询风格/专业领域
    { dictLabel: "认知行为疗法" },
    { dictLabel: "家庭治疗" }
  ],
  startYear: "2015",               // 从业开始年份
  serviceCount: 1000,              // 服务人次
  totalCases: 500,                 // 累计案例
  price: 299,                      // 价格
  isFree: false                    // 是否免费
}
```

### 2. 课程 (course)

```javascript
const courseData = {
  id: 1,
  title: "心理学入门课程",          // 课程标题
  name: "心理学入门课程",           // 课程名称（备用）
  coverImage: "封面图片URL",        // 封面图片
  cover: "封面图片URL",             // 封面图片（备用）
  description: "课程描述",          // 课程描述
  intro: "课程简介",               // 课程简介（备用）
  tags: ["心理学", "入门"],        // 标签数组
  categories: ["基础课程"],         // 分类（备用）
  studentCount: 1200,              // 学习人数
  lessonCount: 20,                 // 课时数
  duration: 45,                    // 时长（分钟）
  price: 199,                      // 价格
  isFree: false                    // 是否免费
}
```

### 3. 冥想 (meditation)

```javascript
const meditationData = {
  id: 1,
  title: "正念冥想",               // 冥想标题
  name: "正念冥想",                // 冥想名称（备用）
  coverImage: "封面图片URL",        // 封面图片
  description: "冥想描述",          // 冥想描述
  summary: "冥想摘要",             // 冥想摘要（备用）
  tags: ["正念", "放松"],          // 标签
  playCount: 5000,                 // 播放次数
  duration: 15,                    // 时长（分钟）
  favoriteCount: 800,              // 收藏人数
  price: 9.9,                      // 价格
  isFree: true                     // 是否免费
}
```

### 4. 测评 (assessment)

```javascript
const assessmentData = {
  id: 1,
  title: "心理健康测评",           // 测评标题
  name: "心理健康测评",            // 测评名称（备用）
  coverImage: "封面图片URL",        // 封面图片
  description: "测评描述",          // 测评描述
  intro: "测评介绍",               // 测评介绍（备用）
  tags: ["心理健康", "自测"],      // 标签
  completionCount: 3000,           // 完成人数
  questionCount: 30,               // 题目数量
  duration: 10,                    // 预计时长（分钟）
  price: 29.9,                     // 价格
  isFree: false                    // 是否免费
}
```

## 组件内部逻辑

### 图片处理

```javascript
// 获取图片URL，支持多种字段名
const getImageUrl = () => {
  return props.item.avatar || 
         props.item.coverImage || 
         props.item.cover || 
         defaultImages[props.type]
}

// 图片加载失败时的容错处理
const handleImageError = (e) => {
  e.target.src = defaultImages[props.type]
}
```

### 等级标识

```javascript
// 咨询师等级转换
const getPsy_consultant_level = (level) => {
  const levelMap = {
    '1': '初级咨询师',
    '2': '中级咨询师', 
    '3': '高级咨询师',
    '4': '专家咨询师'
  }
  return levelMap[level] || level || ''
}

// 通用等级获取
const getGradeText = () => {
  if (props.type === 'consultant') {
    return getPsy_consultant_level(props.item.personalTitle)
  }
  
  return props.item.level || 
         props.item.difficulty || 
         props.item.grade || ''
}
```

### 标签处理

```javascript
// 智能标签提取
const getTagList = () => {
  let tags = []
  
  if (props.type === 'consultant') {
    // 咨询师专业领域
    if (props.item.consultStyles && Array.isArray(props.item.consultStyles)) {
      tags = props.item.consultStyles.map(style => 
        style.dictLabel || style.name || style
      )
    }
  } else {
    // 其他类型的标签
    tags = props.item.tags || props.item.categories || []
  }
  
  // 处理不同格式
  if (typeof tags === 'string') {
    try {
      tags = JSON.parse(tags)
    } catch (e) {
      tags = tags.split(',').filter(Boolean)
    }
  }
  
  return tags.slice(0, 3) // 最多显示3个
}
```

### 统计信息

```javascript
// 根据类型生成不同的统计信息
const getStatsText = () => {
  if (props.type === 'consultant') {
    const experience = new Date().getFullYear() - parseInt(props.item.startYear)
    let stats = []
    if (experience > 0) stats.push(`从业${experience}年`)
    if (props.item.serviceCount) stats.push(`服务${props.item.serviceCount}人次`)
    if (props.item.totalCases) stats.push(`累计${props.item.totalCases}个案例`)
    return stats.join(', ')
  }
  
  // 其他类型的统计逻辑...
}
```

## 样式特点

### 布局结构
```scss
.universal-list-item {
  width: calc(100% - 40rpx);
  padding: 10rpx 20rpx;
  display: flex;
  
  .item-left {
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
  }
  
  .item-right {
    width: calc(100% - 180rpx);
  }
}
```

### 视觉层次
- **主标题**: 36rpx, 粗体
- **等级标签**: 24rpx, 橙色背景
- **描述文本**: 24rpx, 灰色
- **标签**: 22rpx, 浅灰背景
- **统计信息**: 24rpx, 浅灰色
- **价格**: 30rpx, 红色, 粗体

### 交互效果
- **文本截断**: 长文本自动省略
- **标签滚动**: 横向滚动查看更多标签
- **图片容错**: 加载失败显示默认图片

## 使用示例

### 在分类页面中使用

```vue
<template>
  <view class="content-list">
    <UniversalListItem
      v-for="item in categoryData"
      :key="item.id"
      :item="item"
      :type="currentType"
      @click="handleItemClick"
    />
  </view>
</template>

<script setup>
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

const currentType = ref('consultant') // 当前类型
const categoryData = ref([]) // 分类数据

const handleItemClick = (item) => {
  // 根据类型跳转到不同页面
  switch (currentType.value) {
    case 'consultant':
      uni.navigateTo({ url: `/pages/consultant/detail?id=${item.id}` })
      break
    case 'course':
      uni.navigateTo({ url: `/pages/course/detail?id=${item.id}` })
      break
    // ... 其他类型
  }
}
</script>
```

### 在首页中使用

```vue
<template>
  <view class="home-section">
    <view class="section-title">推荐咨询师</view>
    <UniversalListItem
      v-for="consultant in consultants"
      :key="consultant.id"
      :item="consultant"
      type="consultant"
      @click="goToConsultantDetail"
    />
  </view>
</template>
```

## 扩展性

### 添加新类型
1. 在 `type` 参数中添加新类型
2. 在各个方法中添加对应的处理逻辑
3. 添加默认图片URL

### 自定义样式
组件使用scoped样式，可以通过以下方式自定义：

```vue
<style>
/* 全局覆盖 */
.universal-list-item .item-right .name {
  color: #custom-color;
}
</style>
```

### 添加新字段
在对应的方法中添加新字段的处理逻辑即可，组件会自动适配。

## 总结

这个通用列表项组件具有以下优势：

1. **统一设计**: 所有类型使用一致的视觉设计
2. **智能适配**: 自动识别和处理不同类型的数据
3. **高度复用**: 一个组件适用于多种场景
4. **易于维护**: 集中管理样式和逻辑
5. **良好扩展**: 容易添加新类型和新功能
6. **用户友好**: 优秀的交互体验和容错处理

通过这个组件，您可以在整个应用中保持一致的列表项展示效果，同时大大减少代码重复和维护成本。
