# HorizontalScrollList 组件

一个可复用的横向滚动列表组件，支持咨询师、课程、冥想和测评等不同类型的内容展示。

## 功能特性

- 横向滚动展示
- 统一的卡片样式 (580rpx × 176rpx)
- 左右布局：左侧头像 (114rpx × 114rpx) + 右侧内容
- 标题单行显示，描述两行显示并支持省略号
- 支持多种内容类型：assessment、consultant、course、meditation
- 自动适配不同类型的数据字段
- 图片加载失败自动使用默认图片

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | - | 列表标题（必填） |
| items | Array | [] | 数据列表 |
| type | String | 'assessment' | 内容类型：'assessment'、'consultant'、'course'、'meditation' |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| item-click | 点击列表项时触发 | (item) 被点击的项目数据 |

## 使用示例

### 1. 测评相关列表
```vue
<template>
  <HorizontalScrollList 
    title="相关测评" 
    :items="relatedAssessments" 
    type="assessment" 
    @item-click="handleAssessmentClick" 
  />
</template>

<script setup>
import HorizontalScrollList from '@/components/HorizontalScrollList/HorizontalScrollList.vue'

const relatedAssessments = ref([
  {
    id: 1,
    name: '心理健康测评',
    imageUrl: 'https://example.com/image1.jpg',
    description: '专业的心理健康评估工具，帮助您了解自己的心理状态'
  }
])

const handleAssessmentClick = (item) => {
  uni.navigateTo({
    url: `/pages/evaluation/detail/index?id=${item.id}`
  })
}
</script>
```

### 2. 咨询师列表
```vue
<template>
  <HorizontalScrollList 
    title="推荐咨询师" 
    :items="consultants" 
    type="consultant" 
    @item-click="handleConsultantClick" 
  />
</template>

<script setup>
const consultants = ref([
  {
    id: 1,
    name: '张医生',
    avatar: 'https://example.com/avatar1.jpg',
    personalIntro: '资深心理咨询师，擅长情感咨询和职业规划'
  }
])

const handleConsultantClick = (item) => {
  uni.navigateTo({
    url: `/pages/consultant/detail/index?id=${item.id}`
  })
}
</script>
```

### 3. 课程列表
```vue
<template>
  <HorizontalScrollList 
    title="相关课程" 
    :items="courses" 
    type="course" 
    @item-click="handleCourseClick" 
  />
</template>

<script setup>
const courses = ref([
  {
    id: 1,
    title: '情绪管理课程',
    coverImage: 'https://example.com/course1.jpg',
    description: '学习如何有效管理情绪，提升生活质量'
  }
])

const handleCourseClick = (item) => {
  uni.navigateTo({
    url: `/pages/course/detail/index?id=${item.id}`
  })
}
</script>
```

### 4. 冥想列表
```vue
<template>
  <HorizontalScrollList 
    title="推荐冥想" 
    :items="meditations" 
    type="meditation" 
    @item-click="handleMeditationClick" 
  />
</template>

<script setup>
const meditations = ref([
  {
    id: 1,
    title: '放松冥想',
    coverImage: 'https://example.com/meditation1.jpg',
    description: '通过深度放松练习，缓解压力和焦虑'
  }
])

const handleMeditationClick = (item) => {
  uni.navigateTo({
    url: `/pages/meditation/detail/index?id=${item.id}`
  })
}
</script>
```

## 数据字段映射

组件会根据 `type` 自动适配不同的数据字段：

### 图片字段优先级
- consultant: avatar → headImage → imageUrl
- assessment: imageUrl → coverImage → cover
- course: coverImage → imageUrl → cover
- meditation: coverImage → imageUrl → cover

### 标题字段优先级
- consultant: name → realName → title
- assessment: name → title → scaleName
- course: title → name → courseName
- meditation: title → name

### 描述字段优先级
- consultant: personalIntro → introduction → description → summary
- assessment: description → introduction → summary
- course: description → introduction → summary
- meditation: description → introduction → summary

## 样式说明

- 卡片尺寸：580rpx × 176rpx
- 内边距：32rpx
- 头像尺寸：114rpx × 114rpx
- 圆角：8rpx
- 背景色：#FFFFFF
- 标题：28rpx，#000000，单行显示
- 描述：24rpx，#8A8788，两行显示，超出显示省略号
- 行高：36rpx
