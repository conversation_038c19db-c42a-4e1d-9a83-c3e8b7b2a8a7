<template>
  <view class="help-container">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-input">
        <image src="/static/icon/search.png" mode="aspectFit"></image>
        <input 
          type="text" 
          placeholder="搜索帮助内容" 
          v-model="searchKeyword"
          @input="onSearchInput"
        />
      </view>
    </view>

    <!-- 常见问题分类 -->
    <view class="category-section">
      <view class="section-title">常见问题</view>
      <view class="category-grid">
        <view 
          class="category-item" 
          v-for="category in helpCategories" 
          :key="category.id"
          @click="goToCategory(category)"
        >
          <view class="category-icon">
            <image :src="category.icon" mode="aspectFit"></image>
          </view>
          <text class="category-name">{{ category.name }}</text>
          <text class="category-count">{{ category.count }}个问题</text>
        </view>
      </view>
    </view>

    <!-- 热门问题 -->
    <view class="hot-questions">
      <view class="section-title">热门问题</view>
      <view class="question-list">
        <view 
          class="question-item" 
          v-for="question in hotQuestions" 
          :key="question.id"
          @click="goToQuestion(question)"
        >
          <view class="question-content">
            <text class="question-title">{{ question.title }}</text>
            <text class="question-summary">{{ question.summary }}</text>
          </view>
          <view class="question-meta">
            <text class="view-count">{{ question.viewCount }}次查看</text>
            <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="section-title">快捷操作</view>
      <view class="action-list">
        <view class="action-item" @click="contactSupport">
          <view class="action-icon">
            <image src="/static/icon/customer-service.png" mode="aspectFit"></image>
          </view>
          <view class="action-info">
            <text class="action-title">联系客服</text>
            <text class="action-desc">在线客服为您解答疑问</text>
          </view>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
        
        <view class="action-item" @click="submitFeedback">
          <view class="action-icon">
            <image src="/static/icon/feedback.png" mode="aspectFit"></image>
          </view>
          <view class="action-info">
            <text class="action-title">意见反馈</text>
            <text class="action-desc">告诉我们您的建议</text>
          </view>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
        
        <view class="action-item" @click="viewUserGuide">
          <view class="action-icon">
            <image src="/static/icon/guide.png" mode="aspectFit"></image>
          </view>
          <view class="action-info">
            <text class="action-title">使用指南</text>
            <text class="action-desc">了解平台功能使用方法</text>
          </view>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
        
        <view class="action-item" @click="viewPolicies">
          <view class="action-icon">
            <image src="/static/icon/policy.png" mode="aspectFit"></image>
          </view>
          <view class="action-info">
            <text class="action-title">政策条款</text>
            <text class="action-desc">查看用户协议和隐私政策</text>
          </view>
          <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="search-results" v-if="searchKeyword && searchResults.length > 0">
      <view class="section-title">搜索结果</view>
      <view class="result-list">
        <view 
          class="result-item" 
          v-for="result in searchResults" 
          :key="result.id"
          @click="goToQuestion(result)"
        >
          <text class="result-title">{{ result.title }}</text>
          <text class="result-content">{{ result.content }}</text>
          <text class="result-category">{{ result.categoryName }}</text>
        </view>
      </view>
    </view>

    <!-- 搜索无结果 -->
    <view class="no-results" v-if="searchKeyword && searchResults.length === 0 && !searching">
      <image src="/static/icon/no-search-result.png" mode="aspectFit"></image>
      <text class="no-results-text">未找到相关内容</text>
      <text class="no-results-tip">试试其他关键词或联系客服</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getHelpCategories, getHotQuestions, searchHelpContent } from '@/api/consultant-app.js'

// 响应式数据
const searchKeyword = ref('')
const searching = ref(false)
const helpCategories = ref([])
const hotQuestions = ref([])
const searchResults = ref([])

// 生命周期
onMounted(() => {
  loadHelpData()
})

// 加载帮助数据
const loadHelpData = async () => {
  try {
    // 并行加载分类和热门问题
    const [categoriesRes, questionsRes] = await Promise.all([
      getHelpCategories(),
      getHotQuestions({ limit: 10 })
    ])
    
    if (categoriesRes.code === 200) {
      helpCategories.value = categoriesRes.data || []
    }
    
    if (questionsRes.code === 200) {
      hotQuestions.value = questionsRes.data || []
    }
  } catch (error) {
    console.error('加载帮助数据失败:', error)
  }
}

// 搜索输入
const onSearchInput = () => {
  clearTimeout(onSearchInput.timer)
  onSearchInput.timer = setTimeout(() => {
    if (searchKeyword.value.trim()) {
      searchHelp()
    } else {
      searchResults.value = []
    }
  }, 500)
}

// 搜索帮助内容
const searchHelp = async () => {
  if (!searchKeyword.value.trim()) return
  
  searching.value = true
  
  try {
    const res = await searchHelpContent({
      keyword: searchKeyword.value.trim(),
      limit: 20
    })
    
    if (res.code === 200) {
      searchResults.value = res.data || []
    }
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败',
      icon: 'none'
    })
  } finally {
    searching.value = false
  }
}

// 跳转到分类页面
const goToCategory = (category) => {
  uni.navigateTo({
    url: `/pages/consultation-my/help/category/index?categoryId=${category.id}&categoryName=${category.name}`
  })
}

// 跳转到问题详情
const goToQuestion = (question) => {
  uni.navigateTo({
    url: `/pages/consultation-my/help/question/index?questionId=${question.id}`
  })
}

// 联系客服
const contactSupport = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/help/contact/index'
  })
}

// 提交反馈
const submitFeedback = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/feedback/index'
  })
}

// 查看使用指南
const viewUserGuide = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/help/guide/index'
  })
}

// 查看政策条款
const viewPolicies = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/help/policies/index'
  })
}
</script>

<style scoped lang="scss">
.help-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.search-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .search-input {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 25rpx;
    padding: 0 20rpx;
    height: 70rpx;
    
    image {
      width: 30rpx;
      height: 30rpx;
      margin-right: 15rpx;
    }
    
    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
  }
}

.category-section,
.hot-questions,
.quick-actions {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
}

.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  
  .category-item {
    text-align: center;
    padding: 30rpx 20rpx;
    background-color: #f8f8f8;
    border-radius: 16rpx;
    
    .category-icon {
      width: 80rpx;
      height: 80rpx;
      margin: 0 auto 20rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .category-name {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .category-count {
      font-size: 24rpx;
      color: #666;
    }
    
    &:active {
      background-color: #f0f0f0;
    }
  }
}

.question-list,
.action-list {
  .question-item,
  .action-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background-color: #f5f5f5;
    }
  }
}

.question-item {
  .question-content {
    flex: 1;
    margin-right: 20rpx;
    
    .question-title {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
      line-height: 1.4;
    }
    
    .question-summary {
      font-size: 24rpx;
      color: #666;
      line-height: 1.4;
    }
  }
  
  .question-meta {
    display: flex;
    align-items: center;
    gap: 12rpx;
    
    .view-count {
      font-size: 22rpx;
      color: #999;
    }
    
    image {
      width: 20rpx;
      height: 20rpx;
    }
  }
}

.action-item {
  .action-icon {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f7ff;
    border-radius: 50%;
    margin-right: 20rpx;
    
    image {
      width: 40rpx;
      height: 40rpx;
    }
  }
  
  .action-info {
    flex: 1;
    
    .action-title {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .action-desc {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  > image {
    width: 24rpx;
    height: 24rpx;
  }
}

.search-results {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .result-list {
    .result-item {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .result-title {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 8rpx;
        font-weight: 500;
      }
      
      .result-content {
        display: block;
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
        margin-bottom: 8rpx;
      }
      
      .result-category {
        font-size: 22rpx;
        color: #1890ff;
        background-color: #e6f7ff;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
      
      &:active {
        background-color: #f5f5f5;
      }
    }
  }
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .no-results-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 12rpx;
  }
  
  .no-results-tip {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
