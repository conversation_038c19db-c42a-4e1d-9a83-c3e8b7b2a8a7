<template>
	<view class="container">
		<view class="section">
			<text class="section-title">咨询师</text>
			<UniversalListItem 
				:item="consultantData" 
				type="consultant"
				@click="handleItemClick"
			/>
		</view>

		<view class="section">
			<text class="section-title">冥想课程</text>
			<UniversalListItem 
				:item="meditationData" 
				type="meditation"
				@click="handleItemClick"
			/>
		</view>

		<view class="section">
			<text class="section-title">心理测评</text>
			<UniversalListItem 
				:item="assessmentData" 
				type="assessment"
				@click="handleItemClick"
			/>
		</view>

		<view class="section">
			<text class="section-title">心理课程</text>
			<UniversalListItem 
				:item="courseData" 
				type="course"
				@click="handleItemClick"
			/>
		</view>
	</view>
</template>

<script setup>
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 咨询师数据
const consultantData = {
	name: '张艺萌',
	personalTitle: '2',
	personalIntro: '国家二级心理咨询师，四川临床心理学研究院咨询师，擅长心理研究，从业15年...',
	startYear: 2009,
	serviceCount: 1000,
	consultStyles: [
		{ dictLabel: '亲子教育' },
		{ dictLabel: '情绪压力' },
		{ dictLabel: '婚恋焦虑' }
	],
	price: 29.9,
	originalPrice: 38.0,
	avatar: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'
}

// 冥想数据
const meditationData = {
	name: '3D环绕减压冥想',
	duration: 23,
	playCount: 1000,
	description: '独家高质量疗愈音乐体系，来自一流学府音乐治疗，医学心理学专业课程，数致放松助眠...',
	tags: ['冥想', '净化心灵', '音乐疗愈'],
	isFree: true,
	coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
}

// 测评数据
const assessmentData = {
	scaleName: 'MBTI人格测试',
	description: 'MBTI是迈尔斯-布里格斯类型指标（Myers-Briggs Type Indicator）的简称，它是一种基于卡尔·荣格（Carl Jung）的心理类型理论设计的性格测...',
	questionCount: 23,
	completionCount: 1000,
	grade: 'R31',
	categories: [{ categoryName: '专业型' }],
	isFree: true,
	cover: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png'
}

// 课程数据
const courseData = {
	title: '和焦虑对话',
	lessonCount: 12,
	studentCount: 1000,
	description: '独家高质量疗愈音乐体系，来自一流学府音乐治疗，医学心理学专业课程，数致放松助眠...',
	tags: ['收藏', '情绪疗愈', '情绪压力', '婚恋焦虑'],
	price: 2990.0,
	imageUrl: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
}

const handleItemClick = (item) => {
	console.log('点击了项目:', item)
	uni.showToast({
		title: `点击了: ${item.name || item.title || item.scaleName}`,
		icon: 'none'
	})
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.section {
	margin-bottom: 40rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.section-title {
	display: block;
	padding: 20rpx;
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	background-color: #f8f8f8;
	border-bottom: 1rpx solid #eee;
}
</style>
