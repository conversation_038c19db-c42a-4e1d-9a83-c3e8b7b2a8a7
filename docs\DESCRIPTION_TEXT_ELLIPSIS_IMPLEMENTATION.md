# 描述信息两行省略号实现总结

## 修改概述

修改了 UniversalListItem 组件中的描述信息显示，使其只显示两行文本，超出部分显示省略号。

## 主要修改内容

### 1. 模板结构调整

**文件**: `components/UniversalListItem/UniversalListItem.vue`

#### 修改前
```vue
<!-- 描述信息 -->
<view class="info">
    <rich-text v-if="item.highlightDescription" :nodes="getDescription()"></rich-text>
    <text v-else>{{ getDescription() }}</text>
</view>
```

#### 修改后
```vue
<!-- 描述信息 -->
<view class="info">
    <rich-text v-if="item.highlightDescription" :nodes="getDescription()" class="description-text"></rich-text>
    <text v-else class="description-text">{{ getDescription() }}</text>
</view>
```

### 2. 样式重构

#### 修改前
```scss
.info {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;        // 单行显示
    font-size: 20rpx;
    color: #666;
    margin-bottom: 16rpx;
    line-height: 1.4;
}
```

#### 修改后
```scss
.info {
    margin-bottom: 16rpx;
    
    .description-text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;      // WebKit 浏览器两行限制
        line-clamp: 2;              // 标准属性两行限制
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 20rpx;
        color: #666;
        line-height: 1.4;
        word-break: break-all;      // 强制换行，避免长单词撑开布局
    }
}
```

## 技术实现

### 多行文本省略的CSS实现

使用了现代CSS的多行文本省略技术：

1. **`display: -webkit-box`**: 将元素设置为弹性盒子布局
2. **`-webkit-box-orient: vertical`**: 设置盒子方向为垂直
3. **`-webkit-line-clamp: 2`**: WebKit内核浏览器的行数限制
4. **`line-clamp: 2`**: 标准CSS属性的行数限制
5. **`overflow: hidden`**: 隐藏超出的内容
6. **`text-overflow: ellipsis`**: 在截断处显示省略号

### 兼容性处理

- **WebKit前缀**: 支持WebKit内核浏览器（Chrome、Safari、微信小程序等）
- **标准属性**: 添加标准的 `line-clamp` 属性以提高未来兼容性
- **word-break**: 使用 `break-all` 确保长单词能够正确换行

## 显示效果

### 短文本（不超过两行）
```
国家二级心理咨询师，社会工作师。在某民营医院担任心理科组长兼资深心理咨询师。
```

### 长文本（超过两行）
```
国家二级心理咨询师，社会工作师。在某民营医院担任心理科组长兼资深心理咨询师，逾十年心理咨询经验。为儿童青少年、成年来访及伴侣们提供5000+小时的咨询服务。擅长领域：自闭症、多动症等特殊儿童心理健康，情绪压力、婚姻家庭、职场发展、亲子教育...
```

### 高亮文本支持
对于包含搜索关键词高亮的文本，同样支持两行省略：
```
国家二级心理咨询师，社会工作师。在某民营医院担任心理科组长兼资深心理咨询师，逾十年心理咨询经验。为儿童青少年、成年来访及伴侣们提供5000+小时的咨询服务。擅长领域：自闭症、多动症等特殊儿童心理健康，<em>情绪</em>压力...
```

## 样式特点

1. **行高控制**: `line-height: 1.4` 确保文本行间距适中
2. **字体大小**: `font-size: 20rpx` 保持与设计一致
3. **颜色**: `color: #666` 使用次要文本颜色
4. **换行策略**: `word-break: break-all` 强制长单词换行
5. **省略号**: 自动在第二行末尾显示省略号

## 适用场景

- **搜索结果列表**: 显示咨询师、课程、冥想、测评的描述信息
- **卡片组件**: 任何需要限制描述文本行数的场景
- **列表项**: 保持列表项高度一致性

## 浏览器兼容性

| 浏览器 | 支持情况 | 说明 |
|--------|---------|------|
| Chrome | ✅ 完全支持 | 支持 -webkit-line-clamp |
| Safari | ✅ 完全支持 | 支持 -webkit-line-clamp |
| Firefox | ⚠️ 部分支持 | 较新版本支持 line-clamp |
| 微信小程序 | ✅ 完全支持 | 基于WebKit内核 |
| uni-app | ✅ 完全支持 | 跨平台兼容 |

## 注意事项

1. **高度计算**: 两行文本的高度约为 `2 * line-height * font-size`
2. **内容截断**: 确保重要信息在前两行内显示
3. **响应式**: 在不同屏幕宽度下行数保持一致
4. **性能**: CSS实现，无JavaScript计算，性能优秀
5. **可访问性**: 省略的内容对屏幕阅读器仍然可访问

## 测试建议

1. 测试不同长度的描述文本显示效果
2. 验证高亮文本的省略号显示
3. 检查在不同设备宽度下的显示效果
4. 确认省略号在第二行末尾正确显示
5. 测试包含特殊字符和emoji的文本处理

## 扩展性

如果需要调整显示行数，只需修改CSS中的行数值：

```scss
.description-text {
    -webkit-line-clamp: 3;  // 改为3行
    line-clamp: 3;          // 改为3行
}
```

这种实现方式具有良好的可维护性和扩展性。
