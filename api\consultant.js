/**
 * 咨询师相关接口
 * 包含用户端和咨询师端接口
 */
import request from '@/utils/request.js'

// ==================== 咨询师端专用接口 ====================

/**
 * 获取咨询师端导航菜单
 */
export function getConsultantTabbar() {
  return request({
    url: '/miniapp/consultant/tabbar',
    method: 'GET'
  })
}

/**
 * 获取咨询师订单列表
 * @param {Object} params - 查询参数
 */
export function getConsultantOrders(params = {}) {
  return request({
    url: '/miniapp/consultant/orderManage',
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询师订单详情
 * @param {string} orderId - 订单ID
 */
export function getConsultantOrderDetail(orderId) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}`,
    method: 'GET'
  })
}

/**
 * 更新订单状态
 * @param {string} orderId - 订单ID
 * @param {Object} data - 更新数据
 */
export function updateOrderStatus(orderId, data) {
  return request({
    url: `/miniapp/consultant/orderManage/${orderId}/status`,
    method: 'PUT',
    data
  })
}

/**
 * 获取咨询师咨询记录列表
 * @param {Object} params - 查询参数
 */
export function getConsultantRecords(params = {}) {
  return request({
    url: '/miniapp/consultant/record',
    method: 'GET',
    data: params
  })
}

/**
 * 创建咨询记录
 * @param {Object} data - 咨询记录数据
 */
export function createConsultationRecord(data) {
  return request({
    url: '/miniapp/consultant/record',
    method: 'POST',
    data
  })
}

/**
 * 更新咨询记录
 * @param {string} recordId - 记录ID
 * @param {Object} data - 更新数据
 */
export function updateConsultationRecord(recordId, data) {
  return request({
    url: `/miniapp/consultant/record/${recordId}`,
    method: 'PUT',
    data
  })
}

/**
 * 获取咨询师评价管理列表
 * @param {Object} params - 查询参数
 */
export function getConsultantReviewManage(params = {}) {
  return request({
    url: '/miniapp/consultant/reviewManage',
    method: 'GET',
    data: params
  })
}

/**
 * 回复评价
 * @param {string} reviewId - 评价ID
 * @param {Object} data - 回复数据
 */
export function replyToReview(reviewId, data) {
  return request({
    url: `/miniapp/consultant/reviewManage/${reviewId}/reply`,
    method: 'POST',
    data
  })
}

/**
 * 获取咨询师个人信息
 */
export function getConsultantProfile() {
  return request({
    url: '/miniapp/consultant/profile',
    method: 'GET'
  })
}

/**
 * 更新咨询师个人信息
 * @param {Object} data - 个人信息数据
 */
export function updateConsultantProfile(data) {
  return request({
    url: '/miniapp/consultant/profile',
    method: 'PUT',
    data
  })
}

/**
 * 获取咨询师钱包信息
 */
export function getConsultantWallet() {
  return request({
    url: '/miniapp/consultant/wallet',
    method: 'GET'
  })
}

/**
 * 获取咨询师收入流水
 * @param {Object} params - 查询参数
 */
export function getConsultantIncomeHistory(params = {}) {
  return request({
    url: '/miniapp/consultant/wallet/income',
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询师日程安排
 * @param {Object} params - 查询参数
 */
export function getConsultantSchedule(params = {}) {
  return request({
    url: '/miniapp/consultant/schedule',
    method: 'GET',
    data: params
  })
}

/**
 * 设置咨询师可用时间
 * @param {Object} data - 时间设置数据
 */
export function setConsultantAvailableTime(data) {
  return request({
    url: '/miniapp/consultant/schedule/available',
    method: 'POST',
    data
  })
}

/**
 * 获取咨询师统计数据
 */
export function getConsultantStatistics() {
  return request({
    url: '/miniapp/consultant/statistics',
    method: 'GET'
  })
}

// ==================== 咨询师基础信息 ====================
/**
 * 获取可用的咨询师列表
 * 分类页面直接返回全部咨询师
 * @param {Object} params - 查询参数
 */
export function listAvailableConsultants(params = {}) {
  // 如果有时间筛选条件，使用时间筛选接口
  if (params.date && params.startTime && params.endTime) {
    return request({
      url: '/miniapp/timeSlot/counselors',
      method: 'GET',
      data: params
    })
  }

  // 分类页面直接返回全部咨询师列表
  return request({
    url: '/system/consultant/list',
    method: 'GET',
    data: params
  })
}

/**
 * 搜索咨询师
 * 使用匹配问题搜索接口
 * @param {Object} params - 搜索参数
 */
export function searchConsultants(params) {
  return request({
    url: '/miniapp/user/match/question/search',
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询师详情
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantDetail(consultantId) {
  return request({
    url: `/system/consultant/detail/${consultantId}`,
    method: 'GET'
  })
}

/**
 * 根据选项获取关联咨询师
 * @param {number} optionId - 选项ID
 */
export function getConsultantsByOption(optionId) {
  return request({
    url: `/miniapp/user/match/question/option/${optionId}/consultants`,
    method: 'GET'
  })
}

/**
 * 获取推荐咨询师
 */
export function getRecommendConsultants() {
  return request({
    url: '/miniapp/user/match/question/recommend',
    method: 'GET'
  })
}

/**
 * 获取热门咨询师
 */
export function getHotConsultants() {
  return request({
    url: '/miniapp/user/match/question/hot',
    method: 'GET'
  })
}

// ==================== 咨询师评价相关 ====================
/**
 * 获取咨询师评价列表
 * @param {number} consultantId - 咨询师ID
 * @param {Object} params - 查询参数
 */
export function getConsultantReviews(consultantId, params = {}) {
  return request({
    url: `/miniapp/user/consultant/review/consultant/${consultantId}`,
    method: 'GET',
    data: params
  })
}

/**
 * 获取咨询师评价统计
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantReviewStatistics(consultantId) {
  return request({
    url: `/miniapp/user/consultant/review/statistics/${consultantId}`,
    method: 'GET'
  })
}

/**
 * 获取咨询师高分评价
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantHighRatingReviews(consultantId) {
  return request({
    url: `/miniapp/user/consultant/review/highRating`,
    method: 'GET',
    data: { consultantId }
  })
}

/**
 * 获取咨询师最新评价
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantLatestReviews(consultantId) {
  return request({
    url: `/miniapp/user/consultant/review/latest`,
    method: 'GET',
    data: { consultantId }
  })
}

/**
 * 获取咨询师评价类型统计
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantReviewTypeStatistics(consultantId) {
  return request({
    url: `/miniapp/user/consultant/review/typeStatistics/${consultantId}`,
    method: 'GET'
  })
}

// ==================== 咨询订单相关 ====================
/**
 * 创建咨询订单
 * @param {Object} orderData - 订单数据
 */
export function createConsultantOrder(orderData) {
  return request({
    url: '/miniapp/user/consultant/order/create',
    method: 'POST',
    data: orderData
  })
}

/**
 * 检查咨询师时间段可用性
 * @param {Object} params - 检查参数
 */
export function checkConsultantAvailable(params) {
  return request({
    url: '/miniapp/user/consultant/order/checkAvailable',
    method: 'GET',
    params: params
  })
}

/**
 * 获取咨询师订单统计
 * @param {number} consultantId - 咨询师ID
 */
export function getConsultantOrderStatistics(consultantId) {
  return request({
    url: '/miniapp/user/consultant/order/statistics',
    method: 'GET',
    params: { consultantId }
  })
}

// ==================== 匹配相关 ====================
/**
 * 根据问题选项匹配咨询师
 * @param {Object} matchData - 匹配数据
 */
export function matchConsultantsByOptions(matchData) {
  return request({
    url: '/miniapp/user/match/question/match',
    method: 'POST',
    data: matchData
  })
}

/**
 * 快速匹配咨询师（专门用于匹配结果）
 * @param {Object} quickMatchData - 快速匹配数据
 */
export function quickMatchConsultants(quickMatchData = {}) {
  return request({
    url: '/miniapp/user/match/question/quickMatch',
    method: 'POST',
    data: quickMatchData
  })
}

/**
 * 根据匹配问题获取咨询师（专门用于匹配功能）
 * @param {Object} matchData - 匹配数据，包含选项ID等
 */
export function getConsultantsByMatch(matchData) {
  return request({
    url: '/miniapp/user/match/question/match',
    method: 'POST',
    data: matchData
  })
}

/**
 * 获取匹配统计信息
 */
export function getMatchStatistics() {
  return request({
    url: '/miniapp/user/match/question/statistics',
    method: 'GET'
  })
}
