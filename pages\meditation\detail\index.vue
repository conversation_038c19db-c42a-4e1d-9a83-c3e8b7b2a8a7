<template>
  <view class="meditation-detail">
    <!-- 冥想封面和基本信息 -->
    <view class="meditation-header">
      <image :src="meditationDetail.coverImage || defaultCover" mode="aspectFill"></image>
      <view class="meditation-overlay">
        <view class="meditation-title">{{ meditationDetail.title }}</view>
        <view class="meditation-subtitle">{{ meditationDetail.subtitle }}</view>
        <view class="meditation-stats">
          <text>{{ meditationDetail.duration }}分钟</text>
          <text>{{ meditationDetail.playCount }}次播放</text>
          <text v-if="meditationDetail.isFree === 1 || meditationDetail.price == 0">免费</text>
          <text v-else>¥{{ meditationDetail.price }}</text>
        </view>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', { active: currentTab === index }]"
        @click="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y>
      <!-- 冥想介绍 -->
      <view v-if="currentTab === 0" class="tab-content">
        <view class="section">
          <view class="section-title">冥想介绍</view>
          <view class="meditation-description">{{ meditationDetail.description }}</view>
        </view>
        
        <view class="section">
          <view class="section-title">适合场景</view>
          <view class="suitable-scenes">
            <view v-for="(scene, index) in meditationDetail.suitableScenes" :key="index" class="scene-item">
              <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
              <text>{{ scene }}</text>
            </view>
          </view>
        </view>

        <view class="section">
          <view class="section-title">功效说明</view>
          <view class="benefits">{{ meditationDetail.benefits }}</view>
        </view>
      </view>

      <!-- 冥想评价 -->
      <view v-if="currentTab === 1" class="tab-content">
        <view class="rating-summary">
          <view class="rating-score">{{ meditationDetail.rating || 0 }}</view>
          <view class="rating-stars">
            <uni-rate :value="meditationDetail.rating || 0" readonly size="16"></uni-rate>
          </view>
          <view class="rating-count">{{ reviewList.length }}条评价</view>
        </view>

        <view class="review-list">
          <view v-for="review in reviewList" :key="review.id" class="review-item">
            <view class="review-header">
              <image :src="review.userAvatar || defaultAvatar" class="user-avatar"></image>
              <view class="user-info">
                <view class="user-name">{{ review.userName }}</view>
                <view class="review-date">{{ formatDate(review.createTime) }}</view>
              </view>
              <uni-rate :value="review.rating" readonly size="12"></uni-rate>
            </view>
            <view class="review-content">{{ review.content }}</view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <UniversalGoodsNav
      style="z-index: 1;"
      page-type="meditation"
      :detail-data="meditationDetail"
      :purchased="meditationDetail.purchased"
      :price="meditationDetail.price"
      :favorited="meditationDetail.favorited"
      :favorite-id="meditationDetail.favoriteId"
      @favorite="handleFavorite"
      @contact-service="handleContactService"
      @share="handleShare"
      @main-action="handleMainAction"
    />

    <!-- 支付弹框 -->
    <PaymentModal 
      ref="paymentModal"
      :order-info="orderInfo"
      @close="onPaymentClose"
      @pay-success="onPaymentSuccess"
      @pay-fail="onPaymentFail"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getMeditationDetail,
  getMeditationReviews,
  createMeditationOrder
} from '@/api/meditation'
import { useUserStore } from '@/stores/user'
import PaymentModal from '@/components/PaymentModal/PaymentModal.vue'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'
// 添加转发支持
import { getCurrentInstance, onUnmounted } from 'vue'


// 响应式数据
const meditationDetail = ref({})
const reviewList = ref([])
const tabs = ['介绍', '评价']
const currentTab = ref(0)
const meditationId = ref(null)
const paymentModal = ref(null)
const orderInfo = ref({})

// 默认图片
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 处理收藏事件
const handleFavorite = (favoriteData) => {
  meditationDetail.value.favorited = favoriteData.favorited
  meditationDetail.value.favoriteId = favoriteData.favoriteId
}

// 处理客服事件
const handleContactService = () => {
  console.log('联系客服')
}

// 处理分享事件
const handleShare = (shareConfig) => {
  console.log('分享配置:', shareConfig)
  uni.showToast({
    title: '转发成功',
    icon: 'success'
  })
}

// 处理主要操作事件
const handleMainAction = ({ pageType }) => {
  if (!meditationDetail.value.purchased && meditationDetail.value.isFree !== 1 && meditationDetail.value.price > 0) {
    buyMeditation()
  } else {
    startMeditation()
  }
}

// 方法
const switchTab = (index) => {
  currentTab.value = index

  // 切换到评价页面时加载评价列表
  if (index === 1 && reviewList.value.length === 0) {
    loadReviewList()
  }
}

const loadMeditationDetail = async () => {
  try {
    const res = await getMeditationDetail(meditationId.value)
    if (res.code === 200) {
      meditationDetail.value = res.data
    } else {
      uni.showToast({
        title: res.msg || '获取冥想详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取冥想详情失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const loadReviewList = async () => {
  try {
    const res = await getMeditationReviews(meditationId.value)
    if (res.code === 200) {
      reviewList.value = res.data || []
    }
  } catch (error) {
    console.error('获取评价列表失败:', error)
  }
}

const buyMeditation = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    uni.showLoading({ title: '创建订单中...' })
    
    const res = await createMeditationOrder(meditationId.value)
    
    uni.hideLoading()
    
    if (res.code === 200) {
      // 设置订单信息并打开支付弹框
      orderInfo.value = {
        orderNo: res.data.orderNo,
        product: meditationDetail.value
      }
      paymentModal.value?.open()
    } else {
      uni.showToast({
        title: res.msg || '创建订单失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const startMeditation = () => {
  uni.navigateTo({
    url: `/pages/meditation/player/index?id=${meditationId.value}`
  })
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 支付弹框事件处理
const onPaymentClose = () => {
  console.log('支付弹框关闭')
}

const onPaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)

  // 显示支付成功提示
  uni.showToast({
    title: '购买成功',
    icon: 'success',
    duration: 2000
  })

  // 更新冥想购买状态
  meditationDetail.value.purchased = true

  // 重新加载冥想详情以获取最新状态
  setTimeout(() => {
    loadMeditationDetail()
  }, 500)
}

const onPaymentFail = (error) => {
  console.log('支付失败:', error)
}

// 生命周期
onLoad((options) => {
  meditationId.value = options.id
  if (meditationId.value) {
    loadMeditationDetail()
  }
})


const shareConfig = ref(null)

// 监听转发事件
uni.$on('triggerShare', (config) => {
  shareConfig.value = config
  // #ifdef MP-WEIXIN
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none'
  })
  // #endif
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('triggerShare')
})
</script>

<script>
// 支持转发的页面配置
export default {
  onShareAppMessage() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        path: shareConfig.path,
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const meditationDetail = currentInstance?.ctx?.meditationDetail
    if (meditationDetail) {
      return {
        title: `推荐冥想：${meditationDetail.title}`,
        path: `pages/meditation/detail/index?id=${meditationDetail.id}`,
        imageUrl: meditationDetail.coverImage
      }
    }

    return {
      title: '熙桓心理冥想',
      path: 'pages/index/index'
    }
  },

  onShareTimeline() {
    const currentInstance = getCurrentInstance()
    const shareConfig = currentInstance?.ctx?.shareConfig

    if (shareConfig) {
      return {
        title: shareConfig.title,
        query: '',
        imageUrl: shareConfig.imageUrl
      }
    }

    // 默认分享配置
    const meditationDetail = currentInstance?.ctx?.meditationDetail
    if (meditationDetail) {
      return {
        title: `推荐冥想：${meditationDetail.title}`,
        query: `id=${meditationDetail.id}`,
        imageUrl: meditationDetail.coverImage
      }
    }

    return {
      title: '熙桓心理冥想'
    }
  }
}
</script>

<style lang="scss" scoped>
.meditation-detail {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.meditation-header {
  position: relative;
  height: 500rpx;

  image {
    width: 100%;
    height: 100%;
  }

  .meditation-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 40rpx 32rpx;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: #fff;

    .meditation-title {
      font-size: 40rpx;
      font-weight: 600;
      margin-bottom: 12rpx;
    }

    .meditation-subtitle {
      font-size: 28rpx;
      opacity: 0.9;
      margin-bottom: 20rpx;
    }

    .meditation-stats {
      display: flex;
      gap: 32rpx;
      font-size: 26rpx;
      opacity: 0.8;
    }
  }
}

.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 32rpx 0;
    font-size: 30rpx;
    color: #666;
    position: relative;

    &.active {
      color: #ff6b35;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #ff6b35;
        border-radius: 2rpx;
      }
    }
  }
}

.content-area {
  flex: 1;
  padding-bottom: 160rpx;
}

.tab-content {
  padding: 32rpx;
}

.section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .meditation-description {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
  }

  .suitable-scenes {
    .scene-item {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 16rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  .benefits {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
  }
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;

  .rating-score {
    font-size: 48rpx;
    font-weight: 600;
    color: #ff6b35;
  }

  .rating-count {
    font-size: 26rpx;
    color: #999;
  }
}

.review-list {
  .review-item {
    padding: 32rpx;
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 16rpx;

    .review-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .user-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .user-info {
        flex: 1;

        .user-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .review-date {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .review-content {
      font-size: 28rpx;
      line-height: 1.6;
      color: #666;
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;

  .meditation-info {
    .duration-info {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 8rpx;
    }

    .price-info {
      display: flex;
      align-items: baseline;

      .price-symbol {
        font-size: 24rpx;
        color: #ff6b35;
      }

      .price-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #ff6b35;
      }
    }
  }

  .action-buttons {
    .buy-button, .play-button {
      padding: 20rpx 48rpx;
      border-radius: 40rpx;
      font-size: 30rpx;
      font-weight: 600;
      border: none;
    }

    .buy-button {
      background-color: #ff6b35;
      color: #fff;
    }

    .play-button {
      background-color: #4CAF50;
      color: #fff;
    }
  }
}
</style>
