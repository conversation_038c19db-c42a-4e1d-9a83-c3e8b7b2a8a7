<template>
  <view class="test-page">
    <view class="header">
      <text class="title">课程详情测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试选项</text>
        <view class="test-buttons">
          <button class="test-btn" @click="testWithId">使用课程ID测试</button>
          <button class="test-btn" @click="testWithoutId">使用测试数据</button>
          <button class="test-btn" @click="testVideoPlayer">测试视频播放器</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">说明</text>
        <view class="description">
          <text>1. "使用课程ID测试" - 跳转到课程详情页面，传入课程ID 1000</text>
          <text>2. "使用测试数据" - 跳转到课程详情页面，不传ID，使用内置测试数据</text>
          <text>3. "测试视频播放器" - 直接测试网络视频播放功能</text>
          <text>✨ 新特性：大章节为渐变目录，点击可展开/收起；小章节为视频，点击可播放</text>
          <text>🎨 界面已全面美化：渐变背景、毛玻璃效果、流畅动画</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">功能特性</text>
        <view class="features">
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>课程基本信息显示</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>渐变目录式章节结构</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>流畅的展开/收起动画</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>高清网络视频播放</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>毛玻璃效果控制栏</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>试听/购买权限控制</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>通用购物车组件</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>支付功能集成</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">数据结构示例</text>
        <view class="data-example">
          <text class="code">
{
  "id": 1000,
  "title": "情绪管理入门",
  "summary": "学会识别和调节自己的情绪",
  "price": 199,
  "isFree": 1,
  "instructor": {
    "name": "张老师",
    "title": "资深心理咨询师"
  },
  "chapters": [
    {
      "id": 2000,
      "level": 1,
      "chapterTitle": "第一章 情绪认知",
      "children": [...]
    }
  ]
}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 测试方法
const testWithId = () => {
  uni.navigateTo({
    url: '/pages/course/detail/index?id=1000'
  })
}

const testWithoutId = () => {
  uni.navigateTo({
    url: '/pages/course/detail/index'
  })
}

const testVideoPlayer = () => {
  const videoUrl = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/video/%E6%88%91%E6%98%AF%E5%A6%82%E4%BD%95%E5%9C%A8B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6%E7%9A%84_%E4%B8%A8%E5%BF%83%E7%90%86%E5%AD%A6%E4%BB%8E%E5%85%A5%E9%97%A8%E5%88%B0%E5%85%A5%E5%9C%9F%20%E2%80%94%E2%80%94%20B%E7%AB%99%E8%87%AA%E5%AD%A6%E5%BF%83%E7%90%86%E5%AD%A6.mp4'
  const title = '1.1 情绪的基本类型'
  
  uni.navigateTo({
    url: `/pages/course/video-player/index?videoUrl=${encodeURIComponent(videoUrl)}&title=${encodeURIComponent(title)}&chapterId=2001&courseId=1000`
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #fff;
  padding: 40rpx 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background-color: #007aff;
    color: #fff;
    font-size: 28rpx;
    border: none;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.features {
  .feature-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.data-example {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 24rpx;
  
  .code {
    font-family: monospace;
    font-size: 24rpx;
    color: #666;
    white-space: pre-wrap;
    line-height: 1.4;
  }
}
</style>
