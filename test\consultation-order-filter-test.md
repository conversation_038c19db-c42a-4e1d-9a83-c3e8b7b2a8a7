# 咨询师订单筛选功能测试用例

## 测试环境准备

1. 确保后端接口 `/miniapp/consultant/orderManage/filterByPriceAndDate` 已部署
2. 确保咨询师账号已登录
3. 确保有测试订单数据

## 功能测试用例

### 1. 筛选按钮显示测试
**测试步骤**:
1. 打开咨询师订单管理页面 (`pages/consultation-order/index.vue`)
2. 检查搜索栏右侧是否显示"筛选"按钮

**预期结果**: 
- 筛选按钮正常显示
- 按钮样式正确（蓝色背景，白色文字）

### 2. 筛选弹窗打开测试
**测试步骤**:
1. 点击"筛选"按钮
2. 观察弹窗是否从底部弹出

**预期结果**: 
- 弹窗从底部平滑弹出
- 弹窗内容完整显示
- 遮罩层正确显示

### 3. 价格筛选测试
**测试步骤**:
1. 打开筛选弹窗
2. 在"最低价格"输入框输入 "100"
3. 在"最高价格"输入框输入 "500"
4. 点击"应用筛选"

**预期结果**: 
- 只显示价格在100-500元之间的订单
- 页面重新加载数据
- 弹窗自动关闭

### 4. 日期筛选测试
**测试步骤**:
1. 打开筛选弹窗
2. 选择开始日期为 "2024-01-01"
3. 选择结束日期为 "2024-12-31"
4. 点击"应用筛选"

**预期结果**: 
- 只显示创建时间在指定日期范围内的订单
- 数据正确筛选
- 弹窗关闭

### 5. 排序功能测试
**测试步骤**:
1. 打开筛选弹窗
2. 选择排序方式为"支付金额"
3. 选择排序顺序为"降序"
4. 点击"应用筛选"

**预期结果**: 
- 订单按支付金额从高到低排序
- 排序结果正确

### 6. 组合筛选测试
**测试步骤**:
1. 打开筛选弹窗
2. 设置价格范围：100-500
3. 设置日期范围：2024-01-01 到 2024-12-31
4. 选择排序：按支付金额降序
5. 点击"应用筛选"

**预期结果**: 
- 同时满足价格、日期条件的订单
- 按支付金额降序排列
- 分页功能正常

### 7. 参数验证测试
**测试步骤**:
1. 打开筛选弹窗
2. 设置最低价格为 "500"，最高价格为 "100"
3. 点击"应用筛选"

**预期结果**: 
- 显示错误提示："最小价格不能大于最大价格"
- 不执行筛选操作
- 弹窗保持打开状态

### 8. 日期验证测试
**测试步骤**:
1. 打开筛选弹窗
2. 设置开始日期为 "2024-12-31"，结束日期为 "2024-01-01"
3. 点击"应用筛选"

**预期结果**: 
- 显示错误提示："开始日期不能晚于结束日期"
- 不执行筛选操作

### 9. 重置功能测试
**测试步骤**:
1. 打开筛选弹窗
2. 设置各种筛选条件
3. 点击"重置"按钮

**预期结果**: 
- 所有筛选条件清空
- 恢复默认值
- 弹窗保持打开状态

### 10. 弹窗关闭测试
**测试步骤**:
1. 打开筛选弹窗
2. 分别测试以下关闭方式：
   - 点击右上角"×"按钮
   - 点击遮罩层
   - 点击"应用筛选"后自动关闭

**预期结果**: 
- 所有方式都能正确关闭弹窗
- 关闭动画流畅

## 接口测试用例

### 1. 筛选接口调用测试
**测试数据**:
```javascript
{
  minPrice: 100,
  maxPrice: 500,
  startDate: "2024-01-01",
  endDate: "2024-12-31",
  status: "已完成",
  pageNum: 1,
  pageSize: 10,
  orderBy: "paymentAmount",
  sortOrder: "desc"
}
```

**预期响应**:
```javascript
{
  code: 200,
  msg: "操作成功",
  data: {
    orders: [...],
    totalCount: 25,
    totalPages: 3,
    currentPage: 1,
    pageSize: 10,
    hasNext: true,
    hasPrevious: false,
    filterSummary: {...}
  }
}
```

### 2. 分页测试
**测试步骤**:
1. 设置筛选条件
2. 滚动到页面底部触发加载更多
3. 检查是否正确加载下一页数据

**预期结果**: 
- 正确加载下一页数据
- 页码参数正确传递
- 数据不重复

## 性能测试

### 1. 响应时间测试
- 筛选操作响应时间应小于2秒
- 弹窗打开/关闭动画流畅
- 大数据量下的筛选性能

### 2. 内存使用测试
- 长时间使用不出现内存泄漏
- 弹窗组件正确销毁

## 兼容性测试

### 1. 平台兼容性
- 微信小程序
- H5浏览器
- App端

### 2. 设备兼容性
- 不同屏幕尺寸
- 不同操作系统版本

## 错误处理测试

### 1. 网络异常测试
- 网络断开时的处理
- 接口超时的处理
- 服务器错误的处理

### 2. 数据异常测试
- 空数据的处理
- 异常数据格式的处理

## 测试通过标准

1. 所有功能测试用例通过
2. 接口调用正确，数据返回符合预期
3. 用户体验良好，操作流畅
4. 错误处理完善，提示信息清晰
5. 性能满足要求
6. 兼容性良好

## 注意事项

1. 测试前确保后端接口已正确实现
2. 测试数据应覆盖各种边界情况
3. 注意测试不同状态下的订单筛选
4. 验证筛选结果的准确性
5. 检查分页功能的正确性
