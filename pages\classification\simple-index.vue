<template>
	<view class="classification-page">
		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-bar" @click="goToSearch">
				<uni-icons type="search" size="16" color="#999"></uni-icons>
				<text class="search-placeholder">搜索咨询师、课程、测评...</text>
			</view>
		</view>

		<!-- 分类标签 -->
		<view class="category-tabs">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index"
				:class="['tab-item', { active: currentTab === index }]"
				@click="switchTab(index)"
			>
				{{ tab.name }}
			</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-section" v-if="showFilters">
			<view class="filter-row">
				<view class="filter-label">分类:</view>
				<scroll-view class="filter-scroll" scroll-x>
					<view 
						v-for="category in categories" 
						:key="category.id"
						:class="['filter-item', { active: selectedCategory === category.id }]"
						@click="selectCategory(category.id)"
					>
						{{ category.name }}
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 内容列表 -->
		<scroll-view
			class="content-list"
			scroll-y
			:refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
			<view class="tab-content">
				<view v-if="currentList.length === 0" class="empty-state">
					<image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png" mode="aspectFit"></image>
					<text>暂无数据</text>
				</view>

				<view v-else class="list-container">
					<UniversalListItem
						v-for="item in currentList"
						:key="item.id"
						:item="item"
						:type="currentTabType"
						@click="handleItemClick"
					/>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getlist } from '@/api/index.js'
import { getCourseList } from '@/api/course.js'
import { getMeditationList, getMeditationsByCategory } from '@/api/meditation.js'
import { listAssessment, getAssessmentsByCategory } from '@/api/evaluation.js'
import { getCategoryTree } from '@/api/category.js'
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'

// 响应式数据
const counselorList = ref([])
const courseList = ref([])
const assessmentList = ref([])
const meditationList = ref([])
const categories = ref([])

// 加载状态
const refreshing = ref(false)

// 分类标签
const tabs = ref([])
const currentTab = ref(0)

// 筛选相关
const showFilters = ref(true)
const selectedCategory = ref(null)

// 计算属性
const currentTabType = computed(() => {
	return tabs.value[currentTab.value]?.type || 'consultant'
})

const currentList = computed(() => {
	const type = currentTabType.value
	switch (type) {
		case 'consultant':
			return counselorList.value
		case 'course':
			return courseList.value
		case 'assessment':
			return assessmentList.value
		case 'meditation':
			return meditationList.value
		default:
			return []
	}
})

// 方法
const goToSearch = () => {
	uni.navigateTo({
		url: '/pages/search/index'
	})
}

const switchTab = (index) => {
	currentTab.value = index
	selectedCategory.value = null

	// 根据标签页加载对应数据
	const type = tabs.value[index]?.type
	switch (type) {
		case 'consultant':
			if (counselorList.value.length === 0) {
				loadCounselorList()
			}
			break
		case 'course':
			if (courseList.value.length === 0) {
				loadCourseList()
			}
			break
		case 'assessment':
			if (assessmentList.value.length === 0) {
				loadAssessmentList()
			}
			break
		case 'meditation':
			if (meditationList.value.length === 0) {
				loadMeditationList()
			}
			break
	}

	// 加载对应的分类数据
	loadCategories()
}

const selectCategory = (categoryId) => {
	selectedCategory.value = categoryId

	// 根据分类加载数据
	const type = currentTabType.value
	switch (type) {
		case 'meditation':
			loadMeditationsByCategory(categoryId)
			break
		case 'assessment':
			loadAssessmentsByCategory(categoryId)
			break
		// 其他类型可以根据需要添加分类筛选
	}
}

const handleItemClick = (item) => {
	const type = currentTabType.value
	
	switch (type) {
		case 'consultant':
			uni.navigateTo({
				url: `/pages/classification/counselor-detail/index?id=${item.id}`
			})
			break
		case 'course':
			uni.navigateTo({
				url: `/pages/course/detail/index?id=${item.id}`
			})
			break
		case 'meditation':
			uni.navigateTo({
				url: `/pages/meditation/detail/index?id=${item.id}`
			})
			break
		case 'assessment':
			uni.navigateTo({
				url: `/pages/evaluation/detail/index?id=${item.id}`
			})
			break
	}
}

// 数据加载方法
// 存储完整的分类数据
const fullCategoryData = ref([])

// 加载主分类标签
const loadMainCategories = async () => {
	try {
		const res = await getCategoryTree()
		if (res.code === 200 && res.data && res.data.categories) {
			// 存储完整的分类数据
			fullCategoryData.value = res.data.categories

			// 将分类数据转换为标签格式
			tabs.value = res.data.categories.map(category => {
				// 根据分类名称映射到对应的类型
				let type = 'consultant' // 默认类型
				switch (category.categoryName) {
					case '咨询师':
						type = 'consultant'
						break
					case '课程':
						type = 'course'
						break
					case '冥想':
						type = 'meditation'
						break
					case '测评':
						type = 'assessment'
						break
				}

				return {
					name: category.categoryName,
					type: type,
					categoryId: category.categoryId,
					children: category.children || []
				}
			})
		} else {
			// 如果接口失败，使用默认分类
			tabs.value = [
				{ name: '咨询师', type: 'consultant' },
				{ name: '课程', type: 'course' },
				{ name: '测评', type: 'assessment' },
				{ name: '冥想', type: 'meditation' }
			]
		}

		// 加载默认标签的数据
		if (tabs.value.length > 0) {
			switchTab(0)
		}
	} catch (error) {
		console.error('加载主分类标签失败:', error)
		// 使用默认分类
		tabs.value = [
			{ name: '咨询师', type: 'consultant' },
			{ name: '课程', type: 'course' },
			{ name: '测评', type: 'assessment' },
			{ name: '冥想', type: 'meditation' }
		]

		// 加载默认标签的数据
		if (tabs.value.length > 0) {
			switchTab(0)
		}
	}
}

const loadCounselorList = async () => {
	try {
		const res = await getlist()
		if (res.code === 200) {
			counselorList.value = res.data || []
		}
	} catch (error) {
		console.error('加载咨询师列表失败:', error)
	}
}

const loadCourseList = async () => {
	try {
		const res = await getCourseList()
		if (res.code === 200) {
			courseList.value = res.data || []
		}
	} catch (error) {
		console.error('加载课程列表失败:', error)
	}
}

const loadAssessmentList = async () => {
	try {
		const res = await listAssessment({
			pageNum: 1,
			pageSize: 20
		})
		if (res.code === 200) {
			assessmentList.value = res.data || []
		}
	} catch (error) {
		console.error('加载测评列表失败:', error)
	}
}

const loadMeditationList = async () => {
	try {
		const res = await getMeditationList()
		if (res.code === 200) {
			meditationList.value = res.data || []
		}
	} catch (error) {
		console.error('加载冥想列表失败:', error)
	}
}

const loadMeditationsByCategory = async (categoryId) => {
	try {
		const res = await getMeditationsByCategory(categoryId)
		if (res.code === 200) {
			meditationList.value = res.data || []
		}
	} catch (error) {
		console.error('根据分类加载冥想失败:', error)
	}
}

const loadAssessmentsByCategory = async (categoryId) => {
	try {
		const res = await getAssessmentsByCategory(categoryId)
		if (res.code === 200) {
			assessmentList.value = res.data || []
		}
	} catch (error) {
		console.error('根据分类加载测评失败:', error)
	}
}

const loadCategories = () => {
	try {
		const currentTabData = tabs.value[currentTab.value]
		if (currentTabData && currentTabData.children) {
			// 从本地数据获取二级分类
			categories.value = currentTabData.children.map(child => ({
				id: child.categoryId,
				name: child.categoryName,
				categoryId: child.categoryId
			}))
		} else {
			// 如果没有二级分类，使用默认分类
			const type = currentTabType.value
			switch (type) {
				case 'meditation':
					categories.value = [
						{ id: 1, name: '专注' },
						{ id: 2, name: '放松' },
						{ id: 3, name: '睡眠' },
						{ id: 4, name: '减压' }
					]
					break
				case 'assessment':
					categories.value = [
						{ id: 1, name: '性格测试' },
						{ id: 2, name: '情感测评' },
						{ id: 3, name: '职业测评' },
						{ id: 4, name: '心理健康' }
					]
					break
				default:
					categories.value = []
			}
		}
	} catch (error) {
		console.error('加载分类失败:', error)
		categories.value = []
	}
}

// 下拉刷新
const onRefresh = async () => {
	refreshing.value = true

	const type = currentTabType.value

	if (selectedCategory.value) {
		// 如果有选中分类，按分类刷新
		switch (type) {
			case 'meditation':
				await loadMeditationsByCategory(selectedCategory.value)
				break
			case 'assessment':
				await loadAssessmentsByCategory(selectedCategory.value)
				break
		}
	} else {
		// 否则刷新全部数据
		switch (type) {
			case 'consultant':
				await loadCounselorList()
				break
			case 'course':
				await loadCourseList()
				break
			case 'assessment':
				await loadAssessmentList()
				break
			case 'meditation':
				await loadMeditationList()
				break
		}
	}

	refreshing.value = false
}

// 生命周期
onLoad(() => {
	// 加载主分类标签
	loadMainCategories()
})
</script>

<style lang="scss" scoped>
.classification-page {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.search-section {
	padding: 24rpx 32rpx;
	background-color: #fff;

	.search-bar {
		display: flex;
		align-items: center;
		padding: 20rpx 24rpx;
		background-color: #f8f8f8;
		border-radius: 40rpx;
		gap: 16rpx;

		.search-placeholder {
			font-size: 28rpx;
			color: #999;
		}
	}
}

.category-tabs {
	display: flex;
	background-color: #fff;
	padding: 24rpx 32rpx;
	margin-bottom: 16rpx;

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 16rpx 0;
		font-size: 28rpx;
		color: #666;
		position: relative;

		&.active {
			color: #ff6b35;
			font-weight: 600;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background-color: #ff6b35;
				border-radius: 2rpx;
			}
		}
	}
}

.filter-section {
	background-color: #fff;
	padding: 24rpx 32rpx;
	margin-bottom: 16rpx;

	.filter-row {
		display: flex;
		align-items: center;

		.filter-label {
			font-size: 28rpx;
			color: #333;
			margin-right: 24rpx;
			flex-shrink: 0;
		}

		.filter-scroll {
			flex: 1;
			white-space: nowrap;

			.filter-item {
				display: inline-block;
				padding: 12rpx 24rpx;
				margin-right: 16rpx;
				background-color: #f8f8f8;
				color: #666;
				border-radius: 20rpx;
				font-size: 26rpx;

				&.active {
					background-color: #ff6b35;
					color: #fff;
				}
			}
		}
	}
}

.content-list {
	flex: 1;
	padding: 0 32rpx;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 32rpx;
	text-align: center;

	image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 32rpx;
	}

	text {
		font-size: 28rpx;
		color: #999;
	}
}

.list-container {
	padding: 0;
}
</style>
