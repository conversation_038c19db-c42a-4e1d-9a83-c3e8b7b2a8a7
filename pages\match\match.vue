<template>
	<view class="match-container">
		<view class="content">
			<view class="intro-card">
				<view class="new-tag">全新上线!</view>
				<view class="title">心理咨询师偏好评估系统</view>
				<!-- <view class="description">
					本系统 由熙桓心理×国内外心理治疗偏好领域权威学者共同研发，历经两年多的试用，该系统在在权威性、实用性和有效性方面均处于国际&国内领先水平。
				</view> -->
				<view class="stats">
					95%的来访者表示匹配到了理想的咨询师，且体验到了 更省钱、更高效 的咨询服务。
				</view>
				<view class="note">
					*通过5大维度收集您的选择偏好，使用最前沿的匹配算法来提升心理咨询效果。
				</view>
			</view>
			
			<view class="question-card" v-if="showQuestion">
				<view class="question-title">
					你寻求心理咨询帮助的原因是哪个方面？
				</view>
				<view class="options-grid">
					<view 
						v-for="(option, index) in options" 
						:key="index"
						class="option-item"
						:class="{ 'selected': selectedOption === index }"
						@click="selectOption(index)"
					>
						{{ option }}
					</view>
				</view>
				<button class="confirm-btn" :disabled="selectedOption === null" @click="nextStep">确定</button>
			</view>
		</view>
		
		<button class="start-btn" v-if="!showQuestion" @click="startMatch">开始匹配</button>
	</view>
</template>

<script setup>
import { ref } from 'vue';

const showQuestion = ref(false);
const selectedOption = ref(null);
const options = [
	'个人成长',
	'情绪管理',
	'婚姻家庭',
	'心理健康',
	'恋爱心理',
	'人际关系',
	'亲子教育',
	'职场心理',
	'性心理',
	'不确定'
];

const startMatch = () => {
	uni.navigateTo({
		url: '/pages/match/match-detail'
	});
};

const selectOption = (index) => {
	selectedOption.value = index;
};

const nextStep = () => {
	// TODO: 处理下一步逻辑
	uni.showToast({
		title: '选择成功',
		icon: 'success'
	});
};

const goBack = () => {
	uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.match-container {
	min-height: 100vh;
	background: #f8f9fc;
	padding: 0 32rpx;
	
	.content {
		padding-top: 32rpx;
	}
	
	.intro-card {
		background: #fff;
		border-radius: 24rpx;
		padding: 40rpx;
		margin-bottom: 32rpx;
		
		.new-tag {
			display: inline-block;
			background: rgba(94, 114, 228, 0.1);
			color: #5e72e4;
			padding: 8rpx 24rpx;
			border-radius: 32rpx;
			font-size: 26rpx;
			margin-bottom: 24rpx;
		}
		
		.title {
			font-size: 40rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 32rpx;
			line-height: 1.4;
		}
		
		.description {
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 32rpx;
		}
		
		.stats {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 24rpx;
		}
		
		.note {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.question-card {
		background: #fff;
		border-radius: 24rpx;
		padding: 40rpx;
		
		.question-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 40rpx;
			text-align: center;
		}
		
		.options-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 24rpx;
			margin-bottom: 40rpx;
		}
		
		.option-item {
			height: 88rpx;
			border: 2rpx solid #e5e7eb;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #333;
			transition: all 0.3s ease;
			
			&.selected {
				background: #5e72e4;
				color: #fff;
				border-color: #5e72e4;
				transform: translateX(20rpx);
			}
			
			&:active {
				opacity: 0.8;
			}
		}
	}
	
	.start-btn, .confirm-btn {
		width: 100%;
		height: 88rpx;
		background: #5e72e4;
		color: #fff;
		border-radius: 44rpx;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 60rpx;
		border: none;
		
		&:disabled {
			background: #ccc;
		}
		
		&:active {
			opacity: 0.9;
		}
	}
}
</style> 