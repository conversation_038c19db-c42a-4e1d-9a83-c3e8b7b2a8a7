# API接口规范化重构文档

## 重构目标

将项目中所有原生的 `uni.$http` 调用替换为统一的API接口文件调用方式，提高代码的可维护性和规范性。

## 重构内容

### 1. 新建API接口文件

#### `api/explore.js` - 探索页面相关接口
```javascript
import request from '../utils/request'

// 获取冥想列表
export function getExploreMeditationList(params) {
  return request({
    url: '/miniapp/meditation/list',
    method: 'get',
    params
  })
}

// 获取冥想分类
export function getMeditationCategories() {
  return request({
    url: '/miniapp/meditation/categories',
    method: 'get'
  })
}

// 获取测评列表
export function getAssessmentList(params) {
  return request({
    url: '/miniapp/assessment/list',
    method: 'get',
    params
  })
}

// 获取测评分类
export function getAssessmentCategories() {
  return request({
    url: '/miniapp/assessment/categories',
    method: 'get'
  })
}

// 获取推荐内容
export function getRecommendedContent(params) {
  return request({
    url: '/miniapp/explore/recommended',
    method: 'get',
    params
  })
}

// 搜索内容
export function searchContent(params) {
  return request({
    url: '/miniapp/explore/search',
    method: 'get',
    params
  })
}
```

### 2. 修改页面调用方式

#### 修改前（原生调用）
```javascript
// ❌ 不推荐的原生调用方式
const res = await uni.$http.get('/miniapp/meditation/list', { params })
const meditationRes = await uni.$http.get('/miniapp/meditation/categories')
```

#### 修改后（API接口调用）
```javascript
// ✅ 推荐的API接口调用方式
import { getExploreMeditationList, getMeditationCategories } from '@/api/explore'

const res = await getExploreMeditationList(params)
const meditationRes = await getMeditationCategories()
```

### 3. 具体修改的文件

#### `pages/explore/index.vue`
- **修改内容**: 
  - 导入explore相关API接口
  - 替换 `uni.$http.get('/miniapp/meditation/list', { params })` 为 `getExploreMeditationList(params)`
  - 替换 `uni.$http.get('/miniapp/meditation/categories')` 为 `getMeditationCategories()`

- **修改前**:
```javascript
// 原生HTTP调用
const res = await uni.$http.get('/miniapp/meditation/list', { params })
const meditationRes = await uni.$http.get('/miniapp/meditation/categories')
```

- **修改后**:
```javascript
// 导入API接口
import { getExploreMeditationList, getMeditationCategories } from '@/api/explore'

// 使用API接口调用
const res = await getExploreMeditationList(params)
const meditationRes = await getMeditationCategories()
```

## 重构优势

### 1. **代码规范性**
- 统一的API调用方式
- 集中管理所有接口
- 便于代码审查和维护

### 2. **可维护性**
- 接口变更时只需修改API文件
- 避免在多个页面重复修改
- 减少出错概率

### 3. **类型安全**
- 明确的函数签名
- 参数类型检查
- IDE智能提示支持

### 4. **复用性**
- 接口可在多个页面复用
- 避免重复定义相同接口
- 提高开发效率

### 5. **错误处理**
- 统一的错误处理机制
- 集中的请求拦截器
- 一致的响应格式

## 现有API接口文件

项目中已有的API接口文件：

### `api/index.js` - 通用接口
- 城市信息获取
- 轮播图列表
- 字典数据
- 困扰数据
- 咨询师列表

### `api/my.js` - 用户相关接口
- 微信登录
- 用户信息获取
- 手机验证码
- 手机号登录
- 用户信息编辑
- 收藏管理

### `api/course.js` - 课程相关接口
- 课程列表
- 课程详情
- 课程购买
- 学习进度
- 课程评价

### `api/meditation.js` - 冥想相关接口
- 冥想列表
- 冥想详情
- 播放记录
- 订单管理
- 评价系统

### `api/evaluation.js` - 测评相关接口
- 测评列表
- 测评分类
- 测评详情
- 测评结果

### `api/explore.js` - 探索页面接口（新增）
- 冥想列表获取
- 冥想分类
- 测评列表
- 推荐内容
- 搜索功能

### `api/common.js` - 公共接口
- 时间获取
- 咨询师时间
- 通用工具接口

## 统一请求工具

所有API接口都使用统一的 `utils/request.js` 工具：

### 特性
- **Token自动管理**: 自动添加认证头
- **白名单机制**: 部分接口无需Token
- **错误处理**: 统一的错误提示和处理
- **请求缓存**: 字典等数据的缓存机制
- **请求拦截**: 登录状态检查和跳转

### 使用方式
```javascript
import request from '../utils/request'

export function apiFunction(params) {
  return request({
    url: '/api/endpoint',
    method: 'get', // 或 'post', 'put', 'delete'
    params, // GET请求参数
    data    // POST请求数据
  })
}
```

## 最佳实践

### 1. **API文件组织**
- 按功能模块划分API文件
- 每个文件专注于特定业务领域
- 使用清晰的函数命名

### 2. **函数命名规范**
- 使用动词+名词的形式：`getUserInfo`, `createOrder`
- 保持命名的一致性和可读性
- 避免缩写，使用完整单词

### 3. **参数处理**
- 明确区分 `params`（GET参数）和 `data`（POST数据）
- 使用对象传参，提高可读性
- 添加必要的参数验证

### 4. **错误处理**
- 在API层面进行基础错误处理
- 在页面层面处理业务逻辑错误
- 提供友好的用户提示

## 总结

通过这次API接口规范化重构，项目的代码质量和可维护性得到了显著提升。所有的HTTP请求都通过统一的API接口文件管理，避免了原生调用的分散和不规范问题。

这种架构为后续的功能开发和维护提供了良好的基础，也符合现代前端开发的最佳实践。
