# 价格区域显示修复总结

## 修改概述

修复了 UniversalListItem 组件中价格区域的显示格式，按照设计要求调整了不同部分的字号和样式。

## 修改内容

### 1. 模板结构调整

**文件**: `components/UniversalListItem/UniversalListItem.vue`

#### 原始结构
```vue
<view v-if="!item.isFree && item.price">
    <text class="currency-symbol">¥</text>
    <text class="price-value">{{ item.price }}</text>
    <text class="unit">{{ getPriceUnit() }}</text>
</view>
```

#### 新结构
```vue
<view v-if="!item.isFree && item.price" class="price-main">
    <text class="currency-symbol">¥</text>
    <text class="price-integer">{{ getPriceInteger() }}</text>
    <text v-if="getPriceDecimal()" class="price-dot">.</text>
    <text v-if="getPriceDecimal()" class="price-decimal">{{ getPriceDecimal() }}</text>
    <text class="price-slash">/</text>
    <text class="price-unit">{{ getPriceUnit() }}</text>
</view>
```

### 2. 新增方法

#### 获取价格整数部分
```javascript
const getPriceInteger = () => {
    if (!props.item.price) return ''
    const price = parseFloat(props.item.price)
    return Math.floor(price).toString()
}
```

#### 获取价格小数部分
```javascript
const getPriceDecimal = () => {
    if (!props.item.price) return ''
    const price = parseFloat(props.item.price)
    const decimal = price - Math.floor(price)
    if (decimal === 0) return ''
    // 保留两位小数，去掉末尾的0
    return decimal.toFixed(2).substring(2).replace(/0+$/, '')
}
```

#### 修改价格单位方法
```javascript
const getPriceUnit = () => {
    const unitMap = {
        consultant: '节',    // 去掉了斜杠前缀
        course: '课程',
        meditation: '次',
        assessment: '次'
    }

    return props.item.priceUnit || unitMap[props.type] || '次'
}
```

### 3. 样式调整

#### 价格区域样式
```scss
.price-box {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .price-main {
        display: flex;
        align-items: baseline;
        color: #A04571;

        .currency-symbol {
            font-size: 24rpx;        // ¥符号字号
            margin-right: 2rpx;
        }

        .price-integer {
            font-weight: bold;
            font-size: 36rpx;        // 小数点之前的数字字号
        }

        .price-dot {
            font-size: 44rpx;        // 小数点字号
            font-weight: bold;
            margin: 0 1rpx;
        }

        .price-decimal {
            font-size: 28rpx;        // 小数点之后的数字字号
            font-weight: bold;
        }

        .price-slash {
            font-size: 24rpx;        // 斜杠字号
            margin: 0 2rpx;
        }

        .price-unit {
            font-size: 24rpx;        // 单位字号
        }
    }

    .original-price {
        font-size: 24rpx;
        color: #ACA8AA;              // 原价颜色
        text-decoration: line-through;
        margin-top: 4rpx;
    }
}
```

## 字号规范

根据设计要求，价格区域各部分的字号如下：

| 元素 | 字号 | 说明 |
|------|------|------|
| ¥符号 | 24rpx | 货币符号 |
| 整数部分 | 36rpx | 小数点之前的数字 |
| 小数点 | 44rpx | 小数点符号 |
| 小数部分 | 28rpx | 小数点之后的数字 |
| 斜杠 | 24rpx | 分隔符 |
| 单位 | 24rpx | 价格单位 |
| 原价 | 24rpx | 带删除线的原价 |

## 显示效果

### 整数价格
- 显示：¥299/节
- 不显示小数点和小数部分

### 带小数价格
- 显示：¥299.90/节
- 小数点字号最大(44rpx)，突出显示

### 原价显示
- 颜色：#ACA8AA
- 带删除线效果
- 字号：24rpx

## 功能特点

1. **智能小数处理**: 只有当价格包含小数时才显示小数点和小数部分
2. **去除末尾零**: 自动去除小数部分末尾的零（如 .90 显示为 .9）
3. **统一样式**: 所有价格元素使用统一的颜色 #A04571
4. **原价对比**: 原价使用不同颜色和删除线效果
5. **响应式布局**: 价格元素基线对齐，保持视觉协调

## 兼容性

- 支持整数和小数价格
- 支持免费商品显示
- 支持原价对比显示
- 兼容不同类型的商品（咨询师、课程、冥想、测评）

## 注意事项

1. 价格数据需要是数字类型或可转换为数字的字符串
2. 小数部分最多保留两位
3. 单位映射已去除斜杠前缀，在模板中单独添加斜杠
4. 原价颜色已更新为设计要求的 #ACA8AA
