<template>
  <view class="container">
    <text class="page-title">HorizontalScrollList 测试页面</text>
    
    <!-- 测试相关测评 -->
    <HorizontalScrollList 
      title="相关测评" 
      :items="testAssessments" 
      type="assessment" 
      @item-click="handleItemClick" 
    />
    
    <!-- 测试咨询师 -->
    <HorizontalScrollList 
      title="推荐咨询师" 
      :items="testConsultants" 
      type="consultant" 
      @item-click="handleItemClick" 
    />
    
    <!-- 测试课程 -->
    <HorizontalScrollList 
      title="相关课程" 
      :items="testCourses" 
      type="course" 
      @item-click="handleItemClick" 
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import HorizontalScrollList from '@/components/HorizontalScrollList/HorizontalScrollList.vue'

// 测试数据 - 包含长文本来测试换行和省略号
const testAssessments = ref([
  {
    id: 1,
    name: '焦虑自评量表(SAS)',
    imageUrl: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
    description: '焦虑自评量表(Self-Rating Anxiety Scale，SAS)是一个用于评估个体焦虑水平的标准化心理测评工具，由William W.K. Zung于1971年编制。该量表包含20个项目，涵盖了焦虑的认知、情感、行为和躯体症状等多个维度，能够有效识别和量化焦虑症状的严重程度。'
  },
  {
    id: 2,
    name: '抑郁自评量表(SDS)',
    imageUrl: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
    description: '抑郁自评量表是一个广泛使用的心理健康评估工具，用于筛查和评估抑郁症状的严重程度。'
  },
  {
    id: 3,
    name: 'MBTI职业性格测试',
    imageUrl: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/assessment/default-cover.png',
    description: '基于荣格心理类型理论的性格测试，帮助了解个人的性格偏好和职业适应性。'
  }
])

const testConsultants = ref([
  {
    id: 1,
    name: '张心理咨询师',
    avatar: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
    personalIntro: '资深心理咨询师，拥有15年丰富的临床经验，擅长认知行为疗法、人本主义疗法和家庭系统治疗。专业领域包括焦虑抑郁、情感问题、职业规划、亲子关系等。曾在多家三甲医院心理科工作，具有深厚的理论基础和丰富的实践经验。'
  },
  {
    id: 2,
    name: '李医生',
    avatar: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png',
    personalIntro: '专业心理治疗师，专注于青少年心理健康和家庭治疗。'
  }
])

const testCourses = ref([
  {
    id: 1,
    title: '情绪管理与压力缓解课程',
    coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    description: '这是一门综合性的情绪管理课程，旨在帮助学员掌握科学有效的情绪调节技巧和压力管理方法。课程内容涵盖情绪认知理论、压力源识别、放松训练、认知重构、正念冥想等多个模块，通过理论学习与实践练习相结合的方式，提升个人的情绪智力和心理韧性。'
  },
  {
    id: 2,
    title: '正念冥想入门',
    coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png',
    description: '学习正念冥想的基本技巧，培养专注力和内心平静。'
  }
])

const handleItemClick = (item) => {
  console.log('点击了项目:', item)
  uni.showToast({
    title: `点击了: ${item.name || item.title}`,
    icon: 'none',
    duration: 2000
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  display: block;
}
</style>
