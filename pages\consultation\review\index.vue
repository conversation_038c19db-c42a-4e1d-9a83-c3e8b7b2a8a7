<template>
  <view class="consultation-review">
    <!-- 咨询信息 -->
    <view class="consultation-info">
      <view class="consultant-info">
        <image :src="consultationRecord.consultantAvatar || defaultAvatar" class="consultant-avatar"></image>
        <view class="consultant-details">
          <view class="consultant-name">{{ consultationRecord.consultantName }}</view>
          <view class="consultation-meta">
            <text>{{ getConsultationTypeName(consultationRecord.consultationType) }}</text>
            <text>{{ formatDateTime(consultationRecord.startTime) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 评价表单 -->
    <view class="review-form">
      <!-- 评分 -->
      <view class="form-section">
        <view class="section-title">咨询评分</view>
        <view class="rating-container">
          <uni-rate 
            v-model="reviewForm.rating" 
            :size="32" 
            :value="reviewForm.rating"
            @change="onRatingChange"
          ></uni-rate>
          <text class="rating-text">{{ getRatingText(reviewForm.rating) }}</text>
        </view>
      </view>

      <!-- 评价标签 -->
      <view class="form-section">
        <view class="section-title">咨询感受</view>
        <view class="tag-list">
          <view 
            v-for="tag in reviewTags" 
            :key="tag.id"
            :class="['tag-item', { active: selectedTags.includes(tag.id) }]"
            @click="toggleTag(tag.id)"
          >
            {{ tag.name }}
          </view>
        </view>
      </view>

      <!-- 评价内容 -->
      <view class="form-section">
        <view class="section-title">评价内容</view>
        <textarea 
          v-model="reviewForm.content"
          class="review-textarea"
          placeholder="分享您的咨询体验，帮助其他用户了解这位咨询师..."
          :maxlength="500"
          show-confirm-bar
        ></textarea>
        <view class="char-count">{{ reviewForm.content.length }}/500</view>
      </view>

      <!-- 是否匿名 -->
      <view class="form-section">
        <view class="anonymous-option">
          <text>匿名评价</text>
          <switch 
            :checked="reviewForm.anonymous"
            @change="onAnonymousChange"
          ></switch>
        </view>
      </view>

      <!-- 是否推荐 -->
      <view class="form-section">
        <view class="recommend-option">
          <text>推荐给其他用户</text>
          <switch 
            :checked="reviewForm.recommend"
            @change="onRecommendChange"
          ></switch>
        </view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="bottom-bar">
      <button 
        class="submit-btn" 
        :disabled="!canSubmit"
        @click="submitReview"
      >
        提交评价
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getRecordDetails, submitConsultationReview } from '@/api/consultation'
import { useUserStore } from '@/stores/user'

// 响应式数据
const consultationRecord = ref({})
const recordId = ref(null)
const reviewForm = ref({
  rating: 5,
  content: '',
  anonymous: false,
  recommend: true,
  tags: []
})
const selectedTags = ref([])

// 评价标签
const reviewTags = ref([
  { id: 1, name: '专业耐心' },
  { id: 2, name: '效果显著' },
  { id: 3, name: '沟通顺畅' },
  { id: 4, name: '方法有效' },
  { id: 5, name: '态度友善' },
  { id: 6, name: '准时守约' },
  { id: 7, name: '环境舒适' },
  { id: 8, name: '值得推荐' }
])

// 默认头像
const defaultAvatar = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/default-avatar.png'

const userStore = useUserStore()

// 计算属性
const canSubmit = computed(() => {
  return reviewForm.value.rating > 0 && reviewForm.value.content.trim().length >= 10
})

// 方法
const loadConsultationRecord = async () => {
  try {
    const res = await getRecordDetails(recordId.value)
    if (res.code === 200) {
      consultationRecord.value = res.data
    } else {
      uni.showToast({
        title: res.msg || '获取咨询记录失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取咨询记录失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const onRatingChange = (value) => {
  reviewForm.value.rating = value
}

const getRatingText = (rating) => {
  const texts = ['', '很差', '较差', '一般', '满意', '非常满意']
  return texts[rating] || ''
}

const toggleTag = (tagId) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
  
  // 更新表单数据
  reviewForm.value.tags = selectedTags.value
}

const onAnonymousChange = (e) => {
  reviewForm.value.anonymous = e.detail.value
}

const onRecommendChange = (e) => {
  reviewForm.value.recommend = e.detail.value
}

const submitReview = async () => {
  if (!userStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  if (!canSubmit.value) {
    uni.showToast({
      title: '请完善评价信息',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({ title: '提交中...' })
    
    const submitData = {
      recordId: recordId.value,
      consultantId: consultationRecord.value.consultantId,
      rating: reviewForm.value.rating,
      content: reviewForm.value.content.trim(),
      anonymous: reviewForm.value.anonymous,
      recommend: reviewForm.value.recommend,
      tags: JSON.stringify(selectedTags.value.map(id => {
        const tag = reviewTags.value.find(t => t.id === id)
        return tag ? tag.name : ''
      }).filter(Boolean))
    }
    
    const res = await submitConsultationReview(submitData)
    
    uni.hideLoading()
    
    if (res.code === 200) {
      uni.showToast({
        title: '评价提交成功',
        icon: 'success'
      })
      
      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.msg || '提交失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('提交评价失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 工具方法
const formatDateTime = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const getConsultationTypeName = (type) => {
  const types = {
    'psychological': '心理咨询',
    'emotional': '情感咨询',
    'career': '职场咨询',
    'family': '家庭咨询'
  }
  return types[type] || type
}

// 生命周期
onLoad((options) => {
  recordId.value = options.recordId
  if (recordId.value) {
    loadConsultationRecord()
  }
})
</script>

<style lang="scss" scoped>
.consultation-review {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.consultation-info {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  
  .consultant-info {
    display: flex;
    align-items: center;
    
    .consultant-avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 24rpx;
    }
    
    .consultant-details {
      flex: 1;
      
      .consultant-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .consultation-meta {
        display: flex;
        gap: 24rpx;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.review-form {
  .form-section {
    background-color: #fff;
    margin-bottom: 16rpx;
    padding: 32rpx;
    
    .section-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .rating-container {
      display: flex;
      align-items: center;
      gap: 24rpx;
      
      .rating-text {
        font-size: 28rpx;
        color: #ff6b35;
        font-weight: 600;
      }
    }
    
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      
      .tag-item {
        padding: 16rpx 32rpx;
        background-color: #f8f8f8;
        color: #666;
        border-radius: 40rpx;
        font-size: 26rpx;
        transition: all 0.3s;
        
        &.active {
          background-color: #ff6b35;
          color: #fff;
        }
      }
    }
    
    .review-textarea {
      width: 100%;
      min-height: 200rpx;
      padding: 24rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      line-height: 1.6;
      border: none;
      resize: none;
    }
    
    .char-count {
      text-align: right;
      font-size: 24rpx;
      color: #999;
      margin-top: 12rpx;
    }
    
    .anonymous-option, .recommend-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
  
  .submit-btn {
    width: 100%;
    padding: 24rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 600;
    
    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }
}
</style>
