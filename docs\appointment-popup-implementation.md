# 预约咨询弹框实现文档

## 功能概述

将咨询师详情页面的"预约咨询"按钮改为弹框形式，包含咨询方式、预约日期、支付方式字段和时间表选择功能。

## 🎯 实现的功能

### **1. 弹框基本结构**
- ✅ 底部弹出式弹框
- ✅ 标题栏和关闭按钮
- ✅ 滚动内容区域
- ✅ 底部操作按钮

### **2. 表单字段**
- ✅ **咨询方式选择**: 线上咨询、线下咨询、电话咨询
- ✅ **预约日期选择**: 复用预约页面的日期滚动选择
- ✅ **可约时间选择**: 复用预约页面的时间表组件
- ✅ **支付方式选择**: 微信支付、支付宝、余额支付

### **3. 交互功能**
- ✅ 单选按钮样式和交互
- ✅ 时间段多选功能
- ✅ 表单验证
- ✅ 数据传递到订单页面

## 🔧 核心实现

### **1. 响应式数据结构**
```javascript
// 预约弹框相关数据
const appointmentPopup = ref(null);
const appointmentForm = ref({
  consultationType: '', // 咨询方式
  appointmentDate: '', // 预约日期
  paymentMethod: '', // 支付方式
  selectedTimes: [] // 选择的时间段
});

// 咨询方式选项
const consultationTypes = ref([
  { label: '线上咨询', value: 'online' },
  { label: '线下咨询', value: 'offline' },
  { label: '电话咨询', value: 'phone' }
]);

// 支付方式选项
const paymentMethods = ref([
  { label: '微信支付', value: 'wechat' },
  { label: '支付宝', value: 'alipay' },
  { label: '余额支付', value: 'balance' }
]);

// 时间选择相关
const selectedTimeSlots = ref([]);
```

### **2. 弹框模板结构**
```vue
<uni-popup ref="appointmentPopup" type="bottom" :mask-click="false">
  <view class="appointment-popup">
    <!-- 弹框头部 -->
    <view class="popup-header">
      <text class="popup-title">预约咨询</text>
      <uni-icons type="close" size="20" @click="closeAppointmentPopup" />
    </view>
    
    <!-- 滚动内容区域 -->
    <scroll-view class="popup-content" scroll-y>
      <!-- 咨询方式选择 -->
      <view class="form-section">
        <view class="section-title">咨询方式</view>
        <view class="radio-group">
          <!-- 单选项 -->
        </view>
      </view>

      <!-- 预约日期选择 -->
      <view class="form-section">
        <view class="section-title">预约日期</view>
        <scroll-view class="date-scroll" scroll-x>
          <!-- 日期选择项 -->
        </scroll-view>
      </view>

      <!-- 可约时间选择 -->
      <view class="form-section">
        <view class="section-title">可约时间</view>
        <view class="time-grid">
          <!-- 时间段选择 -->
        </view>
      </view>

      <!-- 支付方式选择 -->
      <view class="form-section">
        <view class="section-title">支付方式</view>
        <view class="radio-group">
          <!-- 支付方式选项 -->
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作按钮 -->
    <view class="popup-footer">
      <button class="cancel-btn" @click="closeAppointmentPopup">取消</button>
      <button class="confirm-btn" @click="confirmAppointment">确认预约</button>
    </view>
  </view>
</uni-popup>
```

### **3. 核心交互方法**

#### **打开弹框**
```javascript
const handleMakeAppointment = () => {
  nextTick(() => {
    appointmentPopup.value.open();
  });
};
```

#### **选择咨询方式**
```javascript
const selectConsultationType = (type) => {
  appointmentForm.value.consultationType = type;
};
```

#### **选择支付方式**
```javascript
const selectPaymentMethod = (method) => {
  appointmentForm.value.paymentMethod = method;
};
```

#### **时间段选择**
```javascript
const handleTimeSlotSelect = (slot) => {
  const fullTime = slot.fullDate + slot.time;
  
  // 检查时间槽是否已禁用
  if (slot.isDisabled || slot.timeStatus === '已约满' || slot.timeStatus === '已过期') {
    uni.showToast({
      title: "该时段不可选",
      icon: "none",
      duration: 2000
    });
    return;
  }

  // 切换选择状态
  const index = selectedTimeSlots.value.indexOf(fullTime);
  if (index === -1) {
    selectedTimeSlots.value.push(fullTime);
  } else {
    selectedTimeSlots.value.splice(index, 1);
  }
};
```

#### **表单验证和提交**
```javascript
const confirmAppointment = () => {
  // 验证表单
  if (!appointmentForm.value.consultationType) {
    uni.showToast({
      title: '请选择咨询方式',
      icon: 'none'
    });
    return;
  }

  if (selectedTimeSlots.value.length === 0) {
    uni.showToast({
      title: '请选择预约时间',
      icon: 'none'
    });
    return;
  }

  if (!appointmentForm.value.paymentMethod) {
    uni.showToast({
      title: '请选择支付方式',
      icon: 'none'
    });
    return;
  }

  // 构建预约数据并跳转
  const appointmentData = {
    consultantId: counselorId.value,
    consultantName: name.value,
    consultationType: appointmentForm.value.consultationType,
    paymentMethod: appointmentForm.value.paymentMethod,
    selectedTimes: selectedTimeSlots.value.map(time => {
      const [date, timeStr] = time.split(' ');
      return { date, time: timeStr };
    })
  };

  uni.navigateTo({
    url: `/pages/consultation/order/index?consultantId=${counselorId.value}&appointmentData=${encodeURIComponent(JSON.stringify(appointmentData))}`
  });

  closeAppointmentPopup();
};
```

## 🎨 样式设计

### **1. 弹框整体样式**
```scss
.appointment-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
```

### **2. 表单区域样式**
```scss
.form-section {
  margin: 32rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
```

### **3. 单选按钮样式**
```scss
.radio-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  
  &.active {
    background-color: #e8f6ff;
    border-color: #20a3f3;
  }
}
```

### **4. 时间选择样式**
```scss
.time-slot {
  padding: 16rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
  min-width: 120rpx;
  text-align: center;
  
  &.is-active {
    background-color: #e8f6ff;
    border-color: #20a3f3;
  }
  
  &.is-disabled {
    background-color: #f0f0f0;
    color: #ccc;
    opacity: 0.6;
  }
}
```

## 📱 用户体验

### **1. 交互流程**
1. 用户点击"预约咨询"按钮
2. 弹框从底部滑出
3. 用户依次选择咨询方式、预约日期、时间段、支付方式
4. 点击"确认预约"进行表单验证
5. 验证通过后跳转到订单页面

### **2. 视觉反馈**
- ✅ 选中状态有明显的颜色变化
- ✅ 禁用状态有灰色显示
- ✅ 表单验证有toast提示
- ✅ 按钮有点击反馈

### **3. 数据传递**
- ✅ 将选择的数据打包传递给订单页面
- ✅ 包含咨询师信息和预约详情
- ✅ 支持多个时间段选择

## 🔄 复用的组件

### **1. 日期选择**
- 复用预约页面的日期滚动选择器
- 支持横向滚动
- 显示星期和日期

### **2. 时间表**
- 复用预约页面的时间段选择组件
- 按时间段分组显示
- 支持多选和状态显示

### **3. uni-popup组件**
- 使用uni-ui的popup组件
- 底部弹出效果
- 支持遮罩和关闭

## ✅ 实现效果

1. **功能完整**: 包含所有要求的字段和功能
2. **交互流畅**: 弹框动画和选择反馈自然
3. **样式统一**: 与整体设计风格保持一致
4. **代码复用**: 最大化复用现有的时间选择组件
5. **数据完整**: 完整传递预约信息到下一页面

现在用户可以在咨询师详情页面直接通过弹框完成预约信息的填写，提升了用户体验和操作效率！
