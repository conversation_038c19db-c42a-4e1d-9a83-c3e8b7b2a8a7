# TimeTable 时间表组件

这是一个通用的时间表组件，可以在预约页面、咨询师详情页面和订单弹框中复用。

## 功能特性

- 支持日期选择
- 支持时间段选择
- 支持多种显示模式
- 支持时间区间选择（预约页面模式）
- 响应式设计

## 使用方法

### 1. 导入组件

```javascript
import TimeTable from "@/components/TimeTable/TimeTable.vue";
```

### 2. 在模板中使用

```vue
<template>
  <TimeTable 
    :dateInfo="dateInfo"
    :timeInfo="timeInfo" 
    :selectTime="selectTime" 
    :timeIntervals="timeIntervals"
    @timeSelect="handleHour"
    @dateSelect="handleSelectTime"
    @scroll="scroll"
    mode="appointment"
    :showDateSelector="true"
  />
</template>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| timeInfo | Array | [] | 时间段信息数组 |
| dateInfo | Array | [] | 日期信息数组 |
| selectTime | Array | [] | 已选择的时间数组 |
| timeIntervals | Array | [] | 时间区间数组（仅预约模式使用） |
| mode | String | 'legacy' | 显示模式：'appointment'(预约页面), 'popup'(弹框), 'legacy'(旧版) |
| showDateSelector | Boolean | false | 是否显示日期选择器 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| timeSelect | hour | 选择时间段时触发 |
| dateSelect | item, index | 选择日期时触发 |
| scroll | event | 滚动时触发 |

## 显示模式

### appointment 模式
- 用于预约页面
- 支持时间区间选择
- 显示完整的时间网格
- 支持区间开始、中间、结束状态

### popup 模式
- 用于弹框中的时间选择
- 简化的时间选择界面
- 适合在有限空间中使用

### legacy 模式
- 兼容旧版本的时间选择器
- 三列布局的时间选择
- 保持原有的交互方式

## 数据格式

### timeInfo 格式
```javascript
[
  {
    id: 1,
    rangeName: "上午",
    slots: [
      {
        id: 1,
        time: "09:00",
        fullDate: "2024-01-01",
        timeStatus: "可预约",
        isDisabled: false
      }
    ]
  }
]
```

### dateInfo 格式
```javascript
[
  {
    data: "2024-01-01",
    shortDate: "01月01日",
    weekDay: "周一",
    isToday: true
  }
]
```

## 样式定制

组件使用了 scoped 样式，如需定制样式，可以通过以下方式：

1. 在父组件中使用 `:deep()` 选择器
2. 修改组件内部的 CSS 变量
3. 通过 props 传递自定义类名

## 注意事项

1. 确保传入的数据格式正确
2. 不同模式下需要的 props 可能不同
3. 事件处理函数需要在父组件中正确实现
4. 时间区间功能仅在 appointment 模式下可用
