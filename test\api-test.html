<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咨询师订单接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        button {
            background-color: #1890ff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
        }
    </style>
</head>
<body>
    <h1>咨询师订单接口测试</h1>
    
    <!-- 基础订单列表测试 -->
    <div class="test-section">
        <h3>1. 获取订单列表</h3>
        <div class="form-group">
            <label>订单状态:</label>
            <select id="orderStatus">
                <option value="">全部</option>
                <option value="已支付">待确认</option>
                <option value="待咨询">待咨询</option>
                <option value="咨询中">咨询中</option>
                <option value="已完成">已完成</option>
                <option value="已取消">已取消</option>
            </select>
        </div>
        <button onclick="testGetOrders()">获取订单列表</button>
        <div id="ordersResult" class="result"></div>
    </div>

    <!-- 筛选接口测试 -->
    <div class="test-section">
        <h3>2. 价格和日期筛选</h3>
        <div class="form-group">
            <label>最低价格:</label>
            <input type="number" id="minPrice" placeholder="例如: 100">
        </div>
        <div class="form-group">
            <label>最高价格:</label>
            <input type="number" id="maxPrice" placeholder="例如: 500">
        </div>
        <div class="form-group">
            <label>开始日期:</label>
            <input type="date" id="startDate">
        </div>
        <div class="form-group">
            <label>结束日期:</label>
            <input type="date" id="endDate">
        </div>
        <div class="form-group">
            <label>排序字段:</label>
            <select id="orderBy">
                <option value="createTime">创建时间</option>
                <option value="paymentAmount">支付金额</option>
                <option value="scheduledTime">预约时间</option>
                <option value="paymentTime">支付时间</option>
                <option value="updateTime">更新时间</option>
            </select>
        </div>
        <div class="form-group">
            <label>排序顺序:</label>
            <select id="sortOrder">
                <option value="desc">降序</option>
                <option value="asc">升序</option>
            </select>
        </div>
        <button onclick="testFilterOrders()">筛选订单</button>
        <div id="filterResult" class="result"></div>
    </div>

    <!-- 状态统计测试 -->
    <div class="test-section">
        <h3>3. 订单状态统计</h3>
        <button onclick="testStatusCount()">获取状态统计</button>
        <div id="statusResult" class="result"></div>
    </div>

    <!-- 订单操作测试 -->
    <div class="test-section">
        <h3>4. 订单操作</h3>
        <div class="form-group">
            <label>订单ID:</label>
            <input type="text" id="orderId" placeholder="输入订单ID">
        </div>
        <div class="form-group">
            <label>拒绝原因:</label>
            <input type="text" id="rejectReason" placeholder="拒绝原因">
        </div>
        <button onclick="testConfirmOrder()">确认订单</button>
        <button onclick="testRejectOrder()">拒绝订单</button>
        <div id="operationResult" class="result"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080';
        
        // 模拟登录token（实际使用时需要真实的token）
        const mockToken = 'your-consultant-token-here';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(BASE_URL + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${mockToken}`,
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误: ${result.error}`;
            }
        }

        async function testGetOrders() {
            const status = document.getElementById('orderStatus').value;
            const params = new URLSearchParams();
            if (status) params.append('status', status);
            
            const result = await makeRequest(`/miniapp/consultant/orderManage/myOrders?${params}`);
            displayResult('ordersResult', result);
        }

        async function testFilterOrders() {
            const params = new URLSearchParams();
            
            const minPrice = document.getElementById('minPrice').value;
            const maxPrice = document.getElementById('maxPrice').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const orderBy = document.getElementById('orderBy').value;
            const sortOrder = document.getElementById('sortOrder').value;
            
            if (minPrice) params.append('minPrice', minPrice);
            if (maxPrice) params.append('maxPrice', maxPrice);
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            params.append('orderBy', orderBy);
            params.append('sortOrder', sortOrder);
            params.append('pageNum', '1');
            params.append('pageSize', '10');
            
            const result = await makeRequest(`/miniapp/consultant/orderManage/filterByPriceAndDate?${params}`);
            displayResult('filterResult', result);
        }

        async function testStatusCount() {
            const result = await makeRequest('/miniapp/consultant/orderManage/statusCount');
            displayResult('statusResult', result);
        }

        async function testConfirmOrder() {
            const orderId = document.getElementById('orderId').value;
            if (!orderId) {
                alert('请输入订单ID');
                return;
            }
            
            const result = await makeRequest(`/miniapp/consultant/orderManage/${orderId}/confirm`, {
                method: 'POST'
            });
            displayResult('operationResult', result);
        }

        async function testRejectOrder() {
            const orderId = document.getElementById('orderId').value;
            const rejectReason = document.getElementById('rejectReason').value;
            
            if (!orderId) {
                alert('请输入订单ID');
                return;
            }
            if (!rejectReason) {
                alert('请输入拒绝原因');
                return;
            }
            
            const params = new URLSearchParams();
            params.append('rejectReason', rejectReason);
            
            const result = await makeRequest(`/miniapp/consultant/orderManage/${orderId}/reject?${params}`, {
                method: 'POST'
            });
            displayResult('operationResult', result);
        }

        // 页面加载时设置默认日期
        window.onload = function() {
            const today = new Date();
            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        };
    </script>
</body>
</html>
