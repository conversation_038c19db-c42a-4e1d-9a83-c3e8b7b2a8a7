// import disLogin from "./login.js";
import {
	setToken,
	getToken,
	removeToken
} from "@/utils/auth.js";
import {
	useUserStore
} from "@/stores/user";

// 请求缓存
const requestCache = new Map();
const CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000; // 24小时过期

// 判断是否是字典请求
const isDictRequest = (url) => {
    return url.includes('/system/dict/data/type/');
};

// 获取缓存key
const getCacheKey = (obj) => {
    const { url, method, data, params } = obj;
    return `${method}_${url}_${JSON.stringify(data || params || '')}`;
};

// 检查缓存是否有效
const isValidCache = (cacheData) => {
    return cacheData && (Date.now() - cacheData.timestamp < CACHE_EXPIRE_TIME);
};

let request = async function(obj) {
	let {
		url,
		data,
		params,
		method,
		Authorization
	} = obj;

	// 检查字典请求缓存
	if (isDictRequest(url)) {
		const cacheKey = getCacheKey(obj);
		const cachedResponse = requestCache.get(cacheKey);
		if (isValidCache(cachedResponse)) {
			return Promise.resolve(cachedResponse.data);
		}
	}

	if (data) {
		for (let key in data) {
			if (data[key] === undefined || data[key] === null || data[key] === "") {
				delete data[key]
			}
		}
	};
	const userStore = useUserStore();

	// 白名单（不需要 Token 验证的接口）
	const whiteList = [
		'/wx/login', // 登录接口
		'/sms/sendCode', // 公共接口（前缀匹配）
		"/phoneLogin",
		"/wx/phone/login"
	];

	// 检查当前 URL 是否在白名单中
	const isWhiteListed = whiteList.some(path => {
		return url.startsWith(path); // 支持前缀匹配
	});

	const token = userStore.token || obj.Authorization || getToken();
	// 非白名单接口需要 Token
	if (!isWhiteListed) {
		if (!token) {
			uni.navigateTo({
				url: '/pages/login/login'
			});
			userStore.clearUser()
			return Promise.reject('未登录');
		}
	}

	let paramsData = data ? data : params ? params : '';
	url = url.indexOf("http") == 0 ? url : 'http://localhost:8080' + url;
	// url = url.indexOf("http") == 0 ? url : 'https://xhxlzx.cn:8081/prod-api' + url;
	return new Promise((resovel, reject) => {
		uni.request({
			sslVerify: false,
			url: url,
			header: {
				'content-type': "application/json",
				'Authorization': `Bearer ${token}` // Bearer 后面有空格
			},
			data: paramsData,
			method,
			timeout: 20000000,
			success: (res) => {
				let data = null; // 解密数据
				let code = null; // 未设置状态码则默认成功状态
				let msg = null; // 获取错误信息
				const isk999 = res?.data?.k == "999" ? true : false
				if (isk999) {
					data = JSON.parse(decryptS);
					code = data.code || 200;
					msg = data.msg || '未知异常';
				} else {
					data = res.data;
					code = data.code || 200;
					msg = data.msg || '未知异常';
				}
				if (code == 401) {
					uni.navigateTo({
						url: '/pages/login/login'
					});
					userStore.clearUser()
					uni.showToast({
						title: '登录已过期，请重新登录',
						position: "bottom",
						icon: "none"
					});
				} else if (code == 200) {
					// 缓存字典请求的响应
					if (isDictRequest(url)) {
						requestCache.set(getCacheKey(obj), {
							data: data,
							timestamp: Date.now()
						});
					}
					resovel(data);
				} else {
					uni.showToast({
						title: msg || '未知异常',
						position: "bottom",
						icon: "none"
					});
					reject(msg)
				}
			},
			fail: (resp) => {
				console.error(resp);
				uni.showToast({
					title: '请求出错',
					position: "bottom",
					icon: "none"
				});
				reject(resp);
			}
		})
	})
};

// 清除缓存
request.clearCache = () => {
    requestCache.clear();
};

// 移除过期缓存
request.clearExpiredCache = () => {
    const now = Date.now();
    for (const [key, value] of requestCache.entries()) {
        if (!isValidCache(value)) {
            requestCache.delete(key);
        }
    }
};

export default request;