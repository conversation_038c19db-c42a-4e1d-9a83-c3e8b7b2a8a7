<template>
  <view class="test-page">
    <view class="header">
      <text class="title">通用购物车组件测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">当前测试类型：{{ currentPageType }}</text>
        <view class="type-buttons">
          <button 
            v-for="type in pageTypes" 
            :key="type.value"
            :class="['type-btn', { active: currentPageType === type.value }]"
            @click="switchPageType(type.value)"
          >
            {{ type.label }}
          </button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">状态控制</text>
        <view class="controls">
          <label class="control-item">
            <switch :checked="testData.purchased" @change="togglePurchased" />
            <text>已购买/已完成</text>
          </label>
          <label class="control-item">
            <switch :checked="testData.favorited" @change="toggleFavorited" />
            <text>已收藏</text>
          </label>
          <label class="control-item">
            <switch :checked="testData.isFree" @change="toggleFree" />
            <text>免费（仅冥想）</text>
          </label>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">价格设置</text>
        <input 
          class="price-input"
          type="number"
          :value="testData.price"
          @input="updatePrice"
          placeholder="请输入价格"
        />
      </view>
      
      <view class="section">
        <text class="section-title">详情数据</text>
        <view class="data-display">
          <text>{{ JSON.stringify(testData, null, 2) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 通用购物车组件 -->
    <UniversalGoodsNav
      :page-type="currentPageType"
      :detail-data="testData"
      :purchased="testData.purchased"
      :price="testData.price"
      :favorited="testData.favorited"
      :favorite-id="testData.favoriteId"
      @favorite="handleFavorite"
      @contact-service="handleContactService"
      @share="handleShare"
      @main-action="handleMainAction"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import UniversalGoodsNav from '@/components/UniversalGoodsNav/UniversalGoodsNav.vue'

// 页面类型选项
const pageTypes = [
  { value: 'assessment', label: '测评' },
  { value: 'meditation', label: '冥想' },
  { value: 'course', label: '课程' },
  { value: 'counselor', label: '咨询师' }
]

// 当前页面类型
const currentPageType = ref('assessment')

// 测试数据
const testData = ref({
  id: 1,
  title: '测试标题',
  price: 99,
  purchased: false,
  favorited: false,
  favoriteId: null,
  isFree: false,
  description: '这是一个测试描述',
  coverImage: 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'
})

// 切换页面类型
const switchPageType = (type) => {
  currentPageType.value = type
  // 根据类型更新测试数据
  switch (type) {
    case 'assessment':
      testData.value.title = '心理健康测评'
      testData.value.price = 29
      break
    case 'meditation':
      testData.value.title = '放松冥想练习'
      testData.value.price = 19
      break
    case 'course':
      testData.value.title = '心理学基础课程'
      testData.value.price = 199
      break
    case 'counselor':
      testData.value.title = '专业心理咨询师'
      testData.value.price = 300
      break
  }
}

// 切换购买状态
const togglePurchased = (e) => {
  testData.value.purchased = e.detail.value
}

// 切换收藏状态
const toggleFavorited = (e) => {
  testData.value.favorited = e.detail.value
  testData.value.favoriteId = e.detail.value ? 'test-favorite-id' : null
}

// 切换免费状态
const toggleFree = (e) => {
  testData.value.isFree = e.detail.value
}

// 更新价格
const updatePrice = (e) => {
  testData.value.price = parseFloat(e.detail.value) || 0
}

// 事件处理
const handleFavorite = (favoriteData) => {
  console.log('收藏事件:', favoriteData)
  testData.value.favorited = favoriteData.favorited
  testData.value.favoriteId = favoriteData.favoriteId
  uni.showToast({
    title: favoriteData.favorited ? '收藏成功' : '取消收藏成功',
    icon: 'none'
  })
}

const handleContactService = () => {
  console.log('联系客服')
  uni.showToast({
    title: '正在连接客服...',
    icon: 'none'
  })
}

const handleShare = (shareConfig) => {
  console.log('分享配置:', shareConfig)
  uni.showToast({
    title: '分享成功',
    icon: 'success'
  })
}

const handleMainAction = (actionData) => {
  console.log('主要操作:', actionData)
  const { pageType } = actionData
  
  let message = ''
  switch (pageType) {
    case 'assessment':
      message = testData.value.purchased ? '查看测评报告' : '开始测评'
      break
    case 'meditation':
      message = (testData.value.purchased || testData.value.isFree) ? '开始冥想' : '购买冥想'
      break
    case 'course':
      message = testData.value.purchased ? '开始学习' : '购买课程'
      break
    case 'counselor':
      message = '预约咨询'
      break
  }
  
  uni.showToast({
    title: message,
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.header {
  background-color: #fff;
  padding: 40rpx 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.type-buttons {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  
  .type-btn {
    flex: 1;
    min-width: 120rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx solid #ddd;
    background-color: #fff;
    color: #666;
    font-size: 28rpx;
    
    &.active {
      border-color: #007aff;
      background-color: #007aff;
      color: #fff;
    }
  }
}

.controls {
  .control-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.price-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.data-display {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 24rpx;
  
  text {
    font-size: 24rpx;
    color: #666;
    font-family: monospace;
    white-space: pre-wrap;
  }
}
</style>
