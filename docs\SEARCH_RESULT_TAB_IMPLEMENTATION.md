# 搜索页面结果分类标签实现总结

## 修改概述

将搜索页面的搜索结果改成类似首页的tab分类，使用 UniversalListItem 组件来显示列表。

## 主要修改内容

### 1. 搜索结果UI结构调整

**文件**: `pages/search/search.vue`

#### 原始结构
```vue
<!-- 搜索结果组件 -->
<SearchResult
    v-else
    :categories="searchCategories"
    :items="searchResults"
    :showCategories="searchType === 'all' && searchCategories.length > 0"
    @itemClick="goToDetail"
    @showMore="handleShowMore"
/>
```

#### 新结构
```vue
<!-- 搜索结果内容 -->
<view v-else class="result-content">
    <!-- 搜索结果分类标签页 -->
    <view class="result-category-tabs">
        <view
            v-for="(tab, index) in resultTabs"
            :key="tab.type"
            :class="['tab-item', { active: currentResultTab === index }]"
            @click="switchResultTab(index)"
        >
            {{ tab.name }}
            <image v-if="currentResultTab === index" class="tab-icon" src="../../static/icon/图层 4.png"></image>
        </view>
    </view>

    <!-- 搜索结果列表 -->
    <scroll-view scroll-y class="result-list-container">
        <view class="result-list">
            <view v-if="currentResultList.length === 0" class="empty-state">
                <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-list.png" mode="aspectFit"></image>
                <text>暂无搜索结果</text>
            </view>

            <view v-else class="list-container">
                <UniversalListItem
                    v-for="item in currentResultList"
                    :key="item.id"
                    :item="item"
                    :type="currentResultType"
                    @click="goToDetail"
                />
            </view>
        </view>
    </scroll-view>
</view>
```

### 2. 组件导入调整

#### 原始导入
```javascript
import SearchResult from '@/components/SearchResult/SearchResult.vue'
```

#### 新导入
```javascript
import UniversalListItem from '@/components/UniversalListItem/UniversalListItem.vue'
```

### 3. 数据结构调整

#### 新增变量
```javascript
// 搜索结果分类标签
const resultTabs = ref([
    { name: '全部', type: 'all' },
    { name: '咨询师', type: 'consultant' },
    { name: '测评', type: 'assessment' },
    { name: '冥想', type: 'meditation' },
    { name: '课程', type: 'course' }
])
const currentResultTab = ref(0)

// 分类搜索结果
const consultantResults = ref([])
const assessmentResults = ref([])
const meditationResults = ref([])
const courseResults = ref([])
```

#### 新增计算属性
```javascript
// 计算当前搜索结果类型
const currentResultType = computed(() => {
    return resultTabs.value[currentResultTab.value]?.type || 'all';
});

// 计算当前显示的搜索结果列表
const currentResultList = computed(() => {
    const type = currentResultType.value;
    switch (type) {
        case 'consultant':
            return consultantResults.value;
        case 'assessment':
            return assessmentResults.value;
        case 'meditation':
            return meditationResults.value;
        case 'course':
            return courseResults.value;
        default:
            // 全部：合并所有结果
            return [
                ...consultantResults.value,
                ...assessmentResults.value,
                ...meditationResults.value,
                ...courseResults.value
            ];
    }
});
```

### 4. 搜索逻辑重构

#### 原始搜索逻辑
- 根据 `searchType` 调用不同的搜索接口
- 单一类型搜索或全局搜索

#### 新搜索逻辑
- 并行搜索所有类型的数据
- 分别存储到不同的结果数组中
- 通过标签切换显示不同类型的结果

```javascript
// 并行搜索所有类型的数据
const searchPromises = [
    searchConsultants({
        keyword: searchKeyword.value,
        pageNum: 1,
        pageSize: 20
    }),
    searchAssessments({
        keyword: searchKeyword.value,
        pageNum: 1,
        pageSize: 20
    }),
    searchMeditations({
        keyword: searchKeyword.value,
        pageNum: 1,
        pageSize: 20
    }),
    searchCourses({
        keyword: searchKeyword.value,
        pageNum: 1,
        pageSize: 20
    })
]

const results = await Promise.allSettled(searchPromises)
```

### 5. 新增方法

```javascript
// 切换搜索结果标签
const switchResultTab = (index) => {
    currentResultTab.value = index
}
```

### 6. 样式调整

#### 新增样式
```scss
// 搜索结果分类标签样式
.result-category-tabs {
    display: flex;
    background: #fff;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .tab-item {
        margin-right: 54rpx;
        position: relative;
        font-size: 24rpx;
        color: #8A8788;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 0;
        
        &.active {
            font-weight: 500;
            font-size: 28rpx;
            color: #000;
        }
        
        .tab-icon {
            width: 28rpx;
            height: 12rpx;
            margin-top: 8rpx;
        }
    }
}

// 搜索结果内容样式
.result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200rpx);
}

.result-list-container {
    flex: 1;
    padding: 0 32rpx;
}

.result-list {
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 120rpx 0;
        
        image {
            width: 200rpx;
            height: 200rpx;
            margin-bottom: 32rpx;
        }
        
        text {
            font-size: 28rpx;
            color: #999;
        }
    }
    
    .list-container {
        padding-bottom: 40rpx;
    }
}
```

## 测试页面

创建了测试页面 `pages/test-search/test-search.vue` 来验证新的搜索结果展示效果。

## 功能特点

1. **分类标签**: 提供全部、咨询师、测评、冥想、课程五个分类标签
2. **统一组件**: 使用 UniversalListItem 组件统一显示不同类型的搜索结果
3. **并行搜索**: 一次搜索获取所有类型的结果，提升用户体验
4. **标签切换**: 用户可以快速切换查看不同类型的搜索结果
5. **空状态处理**: 当某个分类没有搜索结果时显示空状态提示

## 优势

1. **用户体验**: 类似首页的tab设计，用户操作更加熟悉
2. **性能优化**: 一次搜索获取所有结果，减少重复请求
3. **组件复用**: 使用统一的 UniversalListItem 组件，保持界面一致性
4. **响应式设计**: 支持不同屏幕尺寸的适配

## 注意事项

1. 确保 UniversalListItem 组件能正确处理不同类型的数据
2. 搜索接口需要支持并行调用
3. 注意处理搜索失败的情况
4. 保持与首页tab样式的一致性
