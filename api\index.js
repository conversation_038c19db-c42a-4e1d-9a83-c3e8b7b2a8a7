import request from "../utils/request";

// 获取城市信息
export function getCityInformation(data) {
	return request({
		url: "https://restapi.amap.com/v3/config/district",
		method: 'get',
		data
	})
}

// 获取轮播图列表
export function getBannerList() {
	return request({
		url: '/wechat/imageResource/all',
		method: 'get'
	})
}

// 获取字典
export function getDict(dictType) {
	return request({
		url: '/system/dict/data/type/' + dictType,
		method: 'get',
	})
}

// 获取困扰
export function getTroubledDict() {
	return request({
		url: '/system/expertise/tree',
		method: 'get',
	})
}

// 查询咨询师列表
export function getlist() {
	return request({
		url: '/system/consultant/list',
		method: 'get',
	})
}

// 根据角色查询菜单列表
export function listMenuByPermissions(role) {
	console.log(role, 'role')
	return request({
		url: '/wechat/tabbar/listByPermissions',
		method: 'get',
		params: { permissions: role }
	})
}

// 微信手机号快速登录
export function wxPhoneLogin(code) {
	return request({
		url: '/wx/phone/login',
		method: 'post',
		data: {
			code
		}
	})
}