import request from '../utils/request'

// 获取冥想列表
export function getMeditationList(params) {
  return request({
    url: '/miniapp/meditation/list',
    method: 'get',
    params
  })
}

// 获取冥想详情
export function getMeditationDetail(id) {
  return request({
    url: `/miniapp/meditation/${id}`,
    method: 'get'
  })
}

// 开始播放冥想
export function playMeditation(id, data) {
  return request({
    url: `/miniapp/meditation/play/${id}`,
    method: 'post',
    data
  })
}

// 创建冥想订单
export function createMeditationOrder(meditationId) {
  return request({
    url: `/miniapp/meditation/order/${meditationId}`,
    method: 'post'
  })
}

// 提交冥想评价
export function submitMeditationReview(data) {
  return request({
    url: '/miniapp/meditation/review',
    method: 'post',
    data
  })
}

// 获取冥想评价列表
export function getMeditationReviews(meditationId) {
  return request({
    url: `/miniapp/meditation/reviews/${meditationId}`,
    method: 'get'
  })
}

// 检查用户是否已评价
export function checkUserReviewed(meditationId) {
  return request({
    url: `/miniapp/meditation/checkReviewed/${meditationId}`,
    method: 'get'
  })
}

// 获取用户已购冥想列表
export function getPurchasedMeditations() {
  return request({
    url: '/miniapp/meditation/purchased',
    method: 'get'
  })
}

// 获取用户冥想记录列表
export function getUserMeditationRecords() {
  return request({
    url: '/miniapp/meditation/records',
    method: 'get'
  })
}

// 获取用户冥想统计信息
export function getUserMeditationStatistics() {
  return request({
    url: '/miniapp/meditation/statistics',
    method: 'get'
  })
}

// 根据分类ID查询冥想列表
export function getMeditationsByCategory(categoryId) {
  return request({
    url: `/miniapp/meditation/category/${categoryId}`,
    method: 'get'
  })
}
