<template>
  <view class="test-page">
    <view class="header">
      <text class="title">测评分页功能测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试测评的分页加载功能：</text>
          <text>1. 首页测评Tab支持上拉加载更多</text>
          <text>2. 分类页面测评支持分页加载</text>
          <text>3. API接口支持pageNum和pageSize参数</text>
          <text>4. 下拉刷新重置分页状态</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">分页参数配置</text>
        <view class="config-list">
          <view class="config-item">
            <text class="config-label">首页分页大小：</text>
            <text class="config-value">10条/页</text>
          </view>
          <view class="config-item">
            <text class="config-label">分类页分页大小：</text>
            <text class="config-value">20条/页</text>
          </view>
          <view class="config-item">
            <text class="config-label">测评页分页大小：</text>
            <text class="config-value">50条/页</text>
          </view>
          <view class="config-item">
            <text class="config-label">加载方式：</text>
            <text class="config-value">上拉加载更多</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试入口</text>
        <view class="test-buttons">
          <button class="test-btn" @click="goToHomePage">首页测评Tab</button>
          <button class="test-btn" @click="goToClassificationPage">分类页面</button>
          <button class="test-btn" @click="goToEvaluationPage">专门测评页面</button>
          <button class="test-btn" @click="testAPI">测试分页API</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">API测试结果</text>
        <view class="api-result" v-if="apiResult">
          <view class="result-item">
            <text class="result-label">请求参数：</text>
            <text class="result-value">{{ apiParams }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">返回状态：</text>
            <text class="result-value" :class="{ 'success': apiResult.code === 200, 'error': apiResult.code !== 200 }">
              {{ apiResult.code === 200 ? '成功' : '失败' }}
            </text>
          </view>
          <view class="result-item">
            <text class="result-label">数据条数：</text>
            <text class="result-value">{{ apiResult.data ? apiResult.data.length : 0 }}条</text>
          </view>
          <view class="result-item" v-if="apiResult.total">
            <text class="result-label">总数据量：</text>
            <text class="result-value">{{ apiResult.total }}条</text>
          </view>
        </view>
        <view v-else class="no-result">
          <text>点击"测试分页API"查看结果</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">功能特性</text>
        <view class="features">
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>支持分页参数：pageNum, pageSize</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>首页测评Tab上拉加载更多</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>分类页面支持分页加载</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>下拉刷新重置分页状态</text>
          </view>
          <view class="feature-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>加载更多时数据追加显示</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试步骤</text>
        <view class="steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <text>点击"首页测评Tab"进入首页</text>
          </view>
          <view class="step-item">
            <view class="step-number">2</view>
            <text>切换到测评Tab</text>
          </view>
          <view class="step-item">
            <view class="step-number">3</view>
            <text>向下滚动到底部触发加载更多</text>
          </view>
          <view class="step-item">
            <view class="step-number">4</view>
            <text>观察是否加载了更多测评数据</text>
          </view>
          <view class="step-item">
            <view class="step-number">5</view>
            <text>下拉刷新测试重置功能</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { listAssessment } from '@/api/evaluation.js'

// 响应式数据
const apiResult = ref(null)
const apiParams = ref('')

// 测试方法
const goToHomePage = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

const goToClassificationPage = () => {
  uni.navigateTo({
    url: '/pages/classification/index'
  })
}

const goToEvaluationPage = () => {
  uni.navigateTo({
    url: '/pages/evaluation/index'
  })
}

const testAPI = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 5
    }
    apiParams.value = JSON.stringify(params)
    
    uni.showLoading({
      title: '测试中...'
    })
    
    const res = await listAssessment(params)
    apiResult.value = res
    
    uni.hideLoading()
    
    if (res.code === 200) {
      uni.showToast({
        title: '测试成功',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: '测试失败',
        icon: 'error'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('API测试失败:', error)
    apiResult.value = {
      code: 500,
      message: error.message || '请求失败'
    }
    uni.showToast({
      title: '测试失败',
      icon: 'error'
    })
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #ff6b35, #ff8a50);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.config-list {
  .config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .config-label {
      font-size: 28rpx;
      color: #333;
    }
    
    .config-value {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #ff6b35, #ff8a50);
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  }
}

.api-result {
  .result-item {
    display: flex;
    align-items: flex-start;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    .result-label {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      min-width: 160rpx;
    }
    
    .result-value {
      flex: 1;
      font-size: 28rpx;
      color: #666;
      word-break: break-all;
      
      &.success {
        color: #4CAF50;
        font-weight: 600;
      }
      
      &.error {
        color: #f44336;
        font-weight: 600;
      }
    }
  }
}

.no-result {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.features {
  .feature-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.steps {
  .step-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 20rpx;
    
    .step-number {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #ff6b35, #ff8a50);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 600;
      flex-shrink: 0;
    }
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}
</style>
