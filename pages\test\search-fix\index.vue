<template>
  <view class="test-page">
    <view class="header">
      <text class="title">搜索功能测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试搜索功能的修复：</text>
          <text>1. 清空搜索框时应清空搜索结果</text>
          <text>2. 热门搜索数据正确显示</text>
          <text>3. 搜索后返回页面状态正确</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试步骤</text>
        <view class="steps">
          <view class="step-item">
            <view class="step-number">1</view>
            <text>点击下方按钮进入搜索页面</text>
          </view>
          <view class="step-item">
            <view class="step-number">2</view>
            <text>查看热门搜索是否正确显示</text>
          </view>
          <view class="step-item">
            <view class="step-number">3</view>
            <text>输入关键词进行搜索</text>
          </view>
          <view class="step-item">
            <view class="step-number">4</view>
            <text>清空搜索框，确认搜索结果消失</text>
          </view>
          <view class="step-item">
            <view class="step-number">5</view>
            <text>点击热门搜索项测试</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试入口</text>
        <view class="test-buttons">
          <button class="test-btn" @click="goToSearch">进入搜索页面</button>
          <button class="test-btn" @click="goToSearchWithKeyword">搜索"心理咨询师"</button>
          <button class="test-btn" @click="goToConsultantSearch">搜索咨询师</button>
          <button class="test-btn" @click="goToCourseSearch">搜索课程</button>
          <button class="test-btn" @click="goToMeditationSearch">搜索冥想</button>
          <button class="test-btn" @click="goToAssessmentSearch">搜索测评</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">修复内容</text>
        <view class="fix-list">
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>清空搜索框时清空搜索结果和状态</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>输入框变化时智能清空搜索状态</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>热门搜索数据格式优化</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>添加测试热门搜索数据</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>热门搜索按热度排序显示</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">热门搜索数据示例</text>
        <view class="hot-data-display">
          <view class="hot-item-demo" v-for="(item, index) in hotSearchDemo" :key="index">
            <text class="rank">{{ index + 1 }}</text>
            <text class="keyword">{{ item.keyword }}</text>
            <text class="count">{{ item.searchCount }}次</text>
            <text class="type">{{ getTypeText(item.searchType) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 热门搜索示例数据
const hotSearchDemo = ref([
  {
    keyword: "心理咨询师",
    searchType: "all",
    searchCount: 150,
    hotScore: 1500
  },
  {
    keyword: "失眠",
    searchType: "all", 
    searchCount: 121,
    hotScore: 1210
  },
  {
    keyword: "焦虑症",
    searchType: "all",
    searchCount: 110,
    hotScore: 1100
  },
  {
    keyword: "婚姻咨询师",
    searchType: "consultant",
    searchCount: 80,
    hotScore: 800
  },
  {
    keyword: "心理学课程",
    searchType: "course",
    searchCount: 55,
    hotScore: 550
  },
  {
    keyword: "放松冥想",
    searchType: "meditation",
    searchCount: 45,
    hotScore: 450
  },
  {
    keyword: "心理健康测评",
    searchType: "assessment",
    searchCount: 35,
    hotScore: 350
  }
])

// 获取搜索类型文本
const getTypeText = (type) => {
  const typeMap = {
    'all': '全部',
    'consultant': '咨询师',
    'course': '课程',
    'meditation': '冥想',
    'assessment': '测评'
  }
  return typeMap[type] || '全部'
}

// 测试方法
const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search'
  })
}

const goToSearchWithKeyword = () => {
  uni.navigateTo({
    url: '/pages/search/search?keyword=心理咨询师'
  })
}

const goToConsultantSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?type=consultant'
  })
}

const goToCourseSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?type=course'
  })
}

const goToMeditationSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?type=meditation'
  })
}

const goToAssessmentSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search?type=assessment'
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.steps {
  .step-item {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 20rpx;
    
    .step-number {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 600;
      flex-shrink: 0;
    }
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  }
}

.fix-list {
  .fix-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.hot-data-display {
  .hot-item-demo {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .rank {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      font-weight: 600;
    }
    
    .keyword {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
    
    .count {
      font-size: 24rpx;
      color: #666;
    }
    
    .type {
      font-size: 22rpx;
      color: #999;
      background: #f0f0f0;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
    }
  }
}
</style>
