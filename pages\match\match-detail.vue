<template>
	<view class="match-detail">
		<view class="content">
			<view class="tip-text">
				我们将为您精准匹配合适的咨询师，为提升咨询效果助力。
			</view>
			
			<swiper class="questions-swiper" :current="currentIndex" @change="handleSwiperChange">
				<swiper-item v-for="(question, index) in questions" :key="question.id">
					<view class="question-card" :class="{'slide-left': index < currentIndex, 'slide-right': index > currentIndex}">
						<view class="question-title">
							{{ question.title }}
							<text v-if="question.isRequired === '1'" class="required">*</text>
						</view>
						
						<scroll-view scroll-y class="options-scroll">
							<view class="options-grid" :class="{
								'grid-2': question.id === 6,
								'grid-3': question.id === 7,
								'vertical': question.id !== 6 && question.id !== 7
							}">
								<view 
									v-for="option in filteredOptions" 
									:key="option.id"
									class="option-item"
									:class="{ 
										'selected': question.type === 'checkbox' 
											? selectedOptions[question.id]?.optionIds?.includes(option.id)
											: selectedOptions[question.id]?.optionId === option.id
									}"
									@click="selectOption(question, option)"
								>
									<text>{{ option.optionText }}</text>
									<text v-if="option.warningText" class="warning-text">{{ option.warningText }}</text>
								</view>
							</view>
						</scroll-view>
					</view>
				</swiper-item>
			</swiper>
		</view>
		
		<view class="button-group">
			<button 
				class="btn back-btn" 
				v-if="currentIndex > 0"
				@click="prevQuestion"
			>
				上一题
			</button>
			<button 
				class="btn confirm-btn"
				:disabled="!canNext"
				@click="nextQuestion"
			>
				{{ isLastQuestion ? '完成' : '下一题' }}
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getMatchQuestions, matchConsultants } from '@/api/match.js';
import { onLoad } from '@dcloudio/uni-app';

const questions = ref([]); // 所有问题
const currentIndex = ref(0); // 当前问题索引
const selectedOptions = ref({}); // 选中的选项 {questionId: {optionId, valueCode, tagType, recommendTag}}

// 计算当前问题
const currentQuestion = computed(() => {
	const question = questions.value[currentIndex.value];
	if (!question) return null;
	
	// 如果是子问题，检查父问题是否已选择
	if (question.parentId !== null && question.parentId !== 0) {
		const parentQuestion = questions.value.find(q => q.id === question.parentId);
		if (parentQuestion && selectedOptions.value[parentQuestion.id]) {
			return question;
		}
		return null;
	}
	console.log(question);
	
	return question;
});

// 是否是最后一个问题
const isLastQuestion = computed(() => {
	const currentQuestionIndex = questions.value.findIndex(q => q.id === currentQuestion.value?.id);
	console.log('当前题目索引:', currentQuestionIndex, '总题目数:', questions.value.length);
	if (currentQuestionIndex === -1) return true;

	// 检查后面是否还有可显示的问题
	for (let i = currentQuestionIndex + 1; i < questions.value.length; i++) {
		const question = questions.value[i];
		if (question.parentId === null || question.parentId === 0 ||
			(question.parentId !== null && question.parentId !== 0 && selectedOptions.value[question.parentId])) {
			console.log('找到后续题目:', question.title);
			return false;
		}
	}
	console.log('没有找到后续题目，判断为最后一题');
	return true;
});

// 是否可以进入下一题
const canNext = computed(() => {
	const current = currentQuestion.value;
	if (!current) return false;
	
	// 如果是必填题且没有选择选项，则不能进入下一题
	if (current.isRequired === '1') {
		if (current.type === 'checkbox') {
			return selectedOptions.value[current.id]?.optionIds?.length > 0;
		} else {
			return !!selectedOptions.value[current.id]?.optionId;
		}
	}
	return true;
});

// 计算当前问题的选项
const filteredOptions = computed(() => {
	const question = currentQuestion.value;
	if (!question) return [];
	
	// 如果是子问题，根据父问题的选择过滤选项
	if (question.parentId !== 0 && question.parentId !== null) {
		const parentSelection = selectedOptions.value[question.parentId];
		if (!parentSelection) return [];
		
		// 过滤出parentId等于父问题选中选项id的选项
		return question.options.filter(option => {
			return option.parentId === parentSelection.optionId;
		});
	}
	
	return question.options || [];
});

// 获取问题列表
const getQuestions = async () => {
	try {
		const res = await getMatchQuestions();
		if (res.code === 200) {
			// 按sort字段排序
			questions.value = res.rows;
		}
	} catch (error) {
		console.error('获取问题失败：', error);
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		});
	}
};

// 选择选项
const selectOption = (question, option) => {
	console.log('选择选项:', question.type, option.optionText, '是否最后一题:', isLastQuestion.value);
	if (question.type === 'checkbox') {
		// 多选题处理
		if (!selectedOptions.value[question.id]) {
			selectedOptions.value[question.id] = {
				optionIds: [],
				valueCode: [],
				tagType: [],
				recommendTag: []
			};
		}
		
		const index = selectedOptions.value[question.id].optionIds.indexOf(option.id);
		if (index === -1) {
			// 添加选项
			selectedOptions.value[question.id].optionIds.push(option.id);
			selectedOptions.value[question.id].valueCode.push(option.valueCode);
			selectedOptions.value[question.id].tagType.push(option.tagType);
			selectedOptions.value[question.id].recommendTag.push(option.recommendTag);
		} else {
			// 移除选项
			selectedOptions.value[question.id].optionIds.splice(index, 1);
			selectedOptions.value[question.id].valueCode.splice(index, 1);
			selectedOptions.value[question.id].tagType.splice(index, 1);
			selectedOptions.value[question.id].recommendTag.splice(index, 1);
		}
	} else {
		// 单选题处理
		selectedOptions.value[question.id] = {
			optionId: option.id,
			valueCode: option.valueCode,
			tagType: option.tagType,
			recommendTag: option.recommendTag
		};
		
		// 如果选择了新的选项，清除子问题的选择
		const childQuestion = questions.value.find(q => q.parentId === question.id);
		if (childQuestion) {
			delete selectedOptions.value[childQuestion.id];
		}
		
		// 单选题选择后自动进入下一题
		if (!isLastQuestion.value && (question.type === 'radio' || question.type === 'single')) {
			console.log('准备自动跳转到下一题');
			setTimeout(() => {
				nextQuestion();
			}, 300);
		} else {
			console.log('不自动跳转:', '是否最后一题:', isLastQuestion.value, '题目类型:', question.type);
		}
	}
};

// 处理滑动切换
const handleSwiperChange = (e) => {
	const newIndex = e.detail.current;
	// 检查是否可以切换到目标问题
	const targetQuestion = questions.value[newIndex];
	if (targetQuestion && targetQuestion.parentId !== null && targetQuestion.parentId !== 0) {
		const parentQuestion = questions.value.find(q => q.id === targetQuestion.parentId);
		if (!parentQuestion || !selectedOptions.value[parentQuestion.id]) {
			// 如果父问题未选择，回退到当前问题
			currentIndex.value = currentIndex.value;
			return;
		}
	}
	currentIndex.value = newIndex;
};

// 上一题
const prevQuestion = () => {
	if (currentIndex.value > 0) {
		let prevIndex = currentIndex.value - 1;
		// 找到前一个可显示的问题
		while (prevIndex >= 0) {
			const question = questions.value[prevIndex];
			if (question.parentId === null || question.parentId === 0 ||
				(question.parentId !== null && question.parentId !== 0 && selectedOptions.value[question.parentId])) {
				currentIndex.value = prevIndex;
				break;
			}
			prevIndex--;
		}
	}
};

// 下一题
const nextQuestion = () => {
	if (!canNext.value) return;
	
	if (isLastQuestion.value) {
		// 完成匹配，收集所有选中的选项ID
		const answers = [];
		
		// 遍历所有问题
		questions.value.forEach(question => {
			const answer = selectedOptions.value[question.id];
			if (!answer) return;
			
			if (question.type === 'checkbox' && answer.optionIds?.length) {
				// 复选题：optionId作为数组提交
				answers.push({
					questionId: question.id,
					optionId: answer.optionIds // 直接提交选项ID数组
				});
			} else if (answer.optionId) {
				// 单选题：直接添加答案
				answers.push({
					questionId: question.id,
					optionId: answer.optionId
				});
			}
		});

		// 调用匹配接口
		matchConsultants(answers).then(res => {
			if (res.code === 200) {
				// 将匹配结果存储到本地，供下一页使用
				uni.setStorageSync('matchedConsultants', res.data);
				// 跳转到匹配结果页
				uni.navigateTo({
					url: '/pages/match/match-loading'
				});
			} else {
				uni.showToast({
					title: '匹配失败',
					icon: 'none'
				});
			}
		}).catch(error => {
			console.error('匹配失败：', error);
			uni.showToast({
				title: '匹配失败',
				icon: 'none'
			});
		});
	} else {
		let nextIndex = currentIndex.value + 1;
		// 找到下一个可显示的问题
		while (nextIndex < questions.value.length) {
			const question = questions.value[nextIndex];
			if (question.parentId === null || question.parentId === 0 ||
				(question.parentId !== null && question.parentId !== 0 && selectedOptions.value[question.parentId])) {
				currentIndex.value = nextIndex;
				break;
			}
			nextIndex++;
		}
	}
};

// 页面加载时获取数据
onLoad(() => {
	getQuestions();
});
</script>

<style lang="scss" scoped>
.match-detail {
	min-height: 100vh;
	background: linear-gradient(180deg, #EEF1FC 0%, #F8F9FC 100%);
	overflow: hidden;
	
	.content {
		padding: 32rpx;
		position: relative;
		height: calc(100vh - 64rpx - 120rpx);
		
		.tip-text {
			font-size: 28rpx;
			color: #666;
			text-align: center;
			margin-bottom: 32rpx;
			position: relative;
			z-index: 2;
		}
		
		.questions-swiper {
			height: calc(100% - 100rpx);
		}
	}
	
	.question-card {
		background: #fff;
		border-radius: 24rpx;
		padding: 32rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		transition: all 0.3s ease;
		
		&.slide-left {
			transform: translateX(-100%);
			opacity: 0;
		}
		
		&.slide-right {
			transform: translateX(100%);
			opacity: 0;
		}
		
		.question-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 32rpx;
			text-align: center;
			
			.required {
				color: #ff4d4f;
				margin-left: 8rpx;
			}
		}
		
		.options-scroll {
			flex: 1;
			overflow: hidden;
		}
		
		.options-grid {
			display: grid;
			gap: 20rpx;
			padding-bottom: 20rpx;
			
			&.grid-2 {
				grid-template-columns: repeat(2, 1fr);
				
				.option-item {
					height: 80rpx;
					padding: 0 20rpx;
				}
			}
			
			&.grid-3 {
				grid-template-columns: repeat(3, 1fr);
				
				.option-item {
					height: 80rpx;
					padding: 0 16rpx;
					font-size: 24rpx;
				}
			}
			
			&.vertical {
				grid-template-columns: 1fr;
				
				.option-item {
					min-height: 72rpx;
					padding: 12rpx;
				}
			}
		}
		
		.option-item {
			border: 2rpx solid #e5e7eb;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			color: #333;
			transition: all 0.3s ease;
			text-align: center;
			flex-direction: column;
			
			&.selected {
				background: #5e72e4;
				color: #fff;
				border-color: #5e72e4;
			}
			
			&:active {
				opacity: 0.8;
			}
			
			.warning-text {
				font-size: 24rpx;
				color: #ff4d4f;
				margin-top: 8rpx;
			}
		}
	}
	
	.button-group {
		position: fixed;
		bottom: 32rpx;
		left: 32rpx;
		right: 32rpx;
		z-index: 2;
		display: flex;
		gap: 24rpx;
		
		.btn {
			flex: 1;
			height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: none;
			
			&.back-btn {
				background: rgba(94, 114, 228, 0.1);
				color: #5e72e4;
			}
			
			&.confirm-btn {
				background: #5e72e4;
				color: #fff;
				
				&:disabled {
					background: #ccc;
				}
			}
			
			&:active {
				opacity: 0.9;
			}
		}
	}
}
</style> 