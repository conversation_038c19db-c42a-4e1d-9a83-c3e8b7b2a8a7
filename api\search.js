/**
 * 统一搜索接口
 * 支持咨询师、课程、冥想、测评的搜索功能
 */
import request from '@/utils/request.js'

// ==================== 统一搜索接口 ====================
/**
 * 全局搜索（搜索所有类型的内容）
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.type - 搜索类型：all, consultant, course, meditation, assessment
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 */
export function globalSearch(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: params  // GET请求使用params而不是data
  })
}

/**
 * 获取搜索建议
 * @param {string} keyword - 关键词
 */
export function getSearchSuggestions(keyword) {
  return request({
    url: '/miniapp/search/suggestions',
    method: 'GET',
    params: { keyword }  // GET请求使用params而不是data
  })
}

/**
 * 获取热门搜索
 * @param {Object} params - 参数对象
 * @param {string} params.type - 类型：all, consultant, course, meditation, assessment
 * @param {number} params.limit - 返回数量，最大50
 */
export function getHotSearches(params = {}) {
  return request({
    url: '/miniapp/search/hot',
    method: 'GET',
    params: params  // GET请求使用params而不是data
  })
}

// ==================== 分类搜索接口 ====================
/**
 * 搜索咨询师
 * @param {Object} params - 搜索参数
 */
export function searchConsultants(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'consultant' }
  })
}

/**
 * 搜索课程
 * @param {Object} params - 搜索参数
 */
export function searchCourses(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'course' }
  })
}

/**
 * 搜索冥想
 * @param {Object} params - 搜索参数
 */
export function searchMeditations(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'meditation' }
  })
}

/**
 * 搜索测评
 * @param {Object} params - 搜索参数
 */
export function searchAssessments(params) {
  return request({
    url: '/miniapp/search/global',
    method: 'GET',
    params: { ...params, type: 'assessment' }
  })
}

// ==================== 搜索历史和统计 ====================
/**
 * 保存搜索记录
 * @param {Object} params - 搜索记录参数
 */
export function saveSearchRecord(params) {
  // 后端会在搜索时自动保存记录，这里可以是空实现或调用统计接口
  console.log('搜索记录已在后端自动保存:', params)
  return Promise.resolve({ code: 200, message: '记录已保存' })
}

/**
 * 获取用户搜索历史
 * @param {number} limit - 限制数量
 */
export function getUserSearchHistory(limit = 10) {
  return request({
    url: '/miniapp/search/history',
    method: 'GET',
    params: { limit }  // GET请求使用params
  })
}

/**
 * 清空用户搜索历史
 */
export function clearUserSearchHistory() {
  return request({
    url: '/miniapp/search/history',
    method: 'DELETE'
  })
}

/**
 * 快速搜索
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.type - 搜索类型
 * @param {number} params.limit - 返回数量，最大10
 */
export function quickSearch(params) {
  return request({
    url: '/miniapp/search/quick',
    method: 'GET',
    params: params
  })
}

/**
 * 获取搜索统计信息
 * @param {string} type - 搜索类型
 */
export function getSearchStatistics(type = 'all') {
  return request({
    url: '/miniapp/search/statistics',
    method: 'GET',
    params: { type }
  })
}

/**
 * 获取搜索类型列表
 */
export function getSearchTypes() {
  return request({
    url: '/miniapp/search/types',
    method: 'GET'
  })
}

/**
 * 获取关键词联想
 * @param {Object} params - 参数
 * @param {string} params.keyword - 关键词
 * @param {number} params.limit - 返回数量
 */
export function getKeywordAssociate(params) {
  return request({
    url: '/miniapp/search/associate',
    method: 'GET',
    params: params
  })
}

// ==================== 智能搜索接口 ====================
/**
 * 智能搜索（基于用户画像和偏好）
 * 注意：后端暂未实现此接口，使用全局搜索代替
 * @param {Object} params - 搜索参数
 */
export function intelligentSearch(params) {
  return globalSearch(params)
}

/**
 * 搜索结果排序
 * 注意：后端暂未实现此接口，返回原始结果
 * @param {Object} params - 排序参数
 */
export function sortSearchResults(params) {
  console.log('搜索结果排序功能暂未实现:', params)
  return Promise.resolve({ code: 200, data: params.results || [] })
}

/**
 * 获取相关搜索
 * @param {string} keyword - 关键词
 */
export function getRelatedSearches(keyword) {
  return request({
    url: '/miniapp/search/associate',
    method: 'GET',
    params: { keyword, limit: 10 }
  })
}
