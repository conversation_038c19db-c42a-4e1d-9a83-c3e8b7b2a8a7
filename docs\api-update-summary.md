# 测评相关接口更新总结

## 概述

根据后端提供的新接口文档，已完成对前端测评相关API接口的全面更新。本次更新涉及6个主要模块，共计45个接口。

## 更新的接口模块

### 1. 量表浏览相关接口 (8个)

| 序号 | 接口名称 | 函数名 | 新接口路径 | 原接口路径 |
|------|----------|--------|------------|------------|
| 1 | 查询启用的量表列表 | `listAssessment()` | `/miniapp/user/assessment/scales` | `/miniapp/user/assessment/scale/list` |
| 2 | 查询热门量表 | `getPopularAssessments()` | `/miniapp/user/assessment/scales/hot` | `/miniapp/user/assessment/scale/hot` |
| 3 | 查询最新量表 | `getLatestAssessments()` | `/miniapp/user/assessment/scales/latest` | `/miniapp/user/assessment/scale/latest` |
| 4 | 根据分类查询量表 | `getAssessmentsByCategory()` | `/miniapp/user/assessment/scales/category/{categoryId}` | `/miniapp/assessment/category/{categoryId}` |
| 5 | 搜索量表 | `searchAssessments()` | `/miniapp/user/assessment/scales/search` | `/miniapp/user/assessment/scale/search` |
| 6 | 获取量表详情 | `getAssessment()` | `/miniapp/user/assessment/scales/{id}` | `/miniapp/user/assessment/scale/{id}` |
| 7 | 查询用户收藏的量表 | `getFavoriteAssessments()` | `/miniapp/user/assessment/favorites/{userId}` | `/miniapp/user/assessment/scale/favorite` |
| 8 | 查询相似量表推荐 | `getRecommendedAssessments()` | `/miniapp/user/assessment/recommendations/{scaleId}` | `/miniapp/user/assessment/scale/recommend` |

### 2. 测评流程相关接口 (11个)

| 序号 | 接口名称 | 函数名 | 新接口路径 |
|------|----------|--------|------------|
| 1 | 检查用户是否可以开始测评 | `checkCanStartAssessment()` | `/miniapp/user/assessment/check-can-start` |
| 2 | 开始测评 | `startAssessment()` | `/miniapp/user/assessment/start` |
| 3 | 获取测评题目 | `getAssessmentQuestions()` | `/miniapp/user/assessment/questions/{recordId}` |
| 4 | 获取下一题 | `getNextQuestion()` | `/miniapp/user/assessment/next-question/{recordId}` |
| 5 | 获取上一题 | `getPreviousQuestion()` | `/miniapp/user/assessment/previous-question/{recordId}` |
| 6 | 保存答题记录 | `saveAnswerRecord()` | `/miniapp/user/assessment/answer` |
| 7 | 查询答题进度 | `getAssessmentProgress()` | `/miniapp/user/assessment/progress/{recordId}` |
| 8 | 暂停测评 | `pauseAssessment()` | `/miniapp/user/assessment/pause/{recordId}` |
| 9 | 恢复测评 | `resumeAssessment()` | `/miniapp/user/assessment/resume/{recordId}` |
| 10 | 完成测评 | `completeAssessment()` | `/miniapp/user/assessment/complete/{recordId}` |
| 11 | 取消测评 | `cancelAssessment()` | `/miniapp/user/assessment/cancel/{recordId}` |

### 3. 测评结果相关接口 (2个)

| 序号 | 接口名称 | 函数名 | 新接口路径 |
|------|----------|--------|------------|
| 1 | 查询测评结果 | `getAssessmentResult()` | `/miniapp/user/assessment/result/{recordId}` |
| 2 | 生成测评报告 | `generateAssessmentReport()` | `/miniapp/user/assessment/report/{recordId}` |

### 4. 测评记录管理相关接口 (5个)

| 序号 | 接口名称 | 函数名 | 新接口路径 |
|------|----------|--------|------------|
| 1 | 查询用户的测评记录 | `getAssessmentRecords()` | `/miniapp/user/assessment/records/{userId}` |
| 2 | 查询用户最近的测评记录 | `getRecentAssessmentRecords()` | `/miniapp/user/assessment/records/{userId}/recent` |
| 3 | 查询用户未完成的测评记录 | `getIncompleteAssessmentRecords()` | `/miniapp/user/assessment/records/{userId}/incomplete` |
| 4 | 查询用户已完成的测评记录 | `getCompletedAssessmentRecords()` | `/miniapp/user/assessment/records/{userId}/completed` |
| 5 | 查询用户测评统计 | `getAssessmentStats()` | `/miniapp/user/assessment/stats/{userId}` |

### 5. 测评订单相关接口 (8个)

| 序号 | 接口名称 | 函数名 | 新接口路径 |
|------|----------|--------|------------|
| 1 | 查询用户订单列表 | `getUserOrderList()` | `/miniapp/user/order/list` |
| 2 | 查询订单详情 | `getOrderDetail()` | `/miniapp/user/order/{id}` |
| 3 | 创建订单 | `createAssessmentOrder()` | `/miniapp/user/order/create` |
| 4 | 支付订单 | `payOrder()` | `/miniapp/user/order/pay` |
| 5 | 取消订单 | `cancelOrder()` | `/miniapp/user/order/cancel` |
| 6 | 申请退款 | `refundOrder()` | `/miniapp/user/order/refund` |
| 7 | 查询待支付订单 | `getPendingOrders()` | `/miniapp/user/order/pending` |
| 8 | 查询已支付订单 | `getPaidOrders()` | `/miniapp/user/order/paid` |

### 6. 测评评价相关接口 (11个)

| 序号 | 接口名称 | 函数名 | 新接口路径 |
|------|----------|--------|------------|
| 1 | 提交测评评价 | `submitAssessmentReview()` | `/miniapp/user/assessment/review` |
| 2 | 获取量表评价列表 | `getScaleReviews()` | `/miniapp/user/assessment/reviews/{scaleId}` |
| 3 | 获取用户评价列表 | `getUserReviews()` | `/miniapp/user/assessment/reviews/user` |
| 4 | 获取评价详情 | `getReviewDetail()` | `/miniapp/user/assessment/review/{id}` |
| 5 | 根据记录获取评价 | `getReviewByRecord()` | `/miniapp/user/assessment/review/record/{recordId}` |
| 6 | 检查评价权限 | `checkReviewPermission()` | `/miniapp/user/assessment/review/check` |
| 7 | 获取量表评价统计 | `getScaleReviewStats()` | `/miniapp/user/assessment/review/stats/{scaleId}` |
| 8 | 获取用户评价统计 | `getUserReviewStats()` | `/miniapp/user/assessment/review/stats/user` |
| 9 | 获取评价摘要 | `getReviewSummary()` | `/miniapp/user/assessment/review/summary/{scaleId}` |
| 10 | 获取热门评价 | `getHotReviews()` | `/miniapp/user/assessment/review/hot` |
| 11 | 搜索评价 | `searchReviews()` | `/miniapp/user/assessment/review/search` |

## 兼容性处理

为了确保现有页面的正常运行，保留了以下兼容性函数：

- `startTest()` → `startAssessment()`
- `submitAnswer()` → `saveAnswerRecord()`
- `completeTest()` → `completeAssessment()`
- `getTestResult()` → `getAssessmentResult()`
- `getUserTestRecords()` → `getAssessmentRecords()`
- `getTestRecordDetail()` → `getAssessmentResult()`
- `getTestHistory()` → `getAssessmentRecords()`
- `submitReview()` → `submitAssessmentReview()`
- `saveAnswer()` → `saveAnswerRecord()`
- `submitTest()` → `completeAssessment()`
- `getTestReport()` → `generateAssessmentReport()`

## 主要变化

1. **统一路径前缀**: 所有接口统一使用 `/miniapp/user/assessment/` 前缀
2. **规范化命名**: 接口路径和函数名更加规范和一致
3. **增强功能**: 新增了权限检查、进度管理、暂停恢复等功能
4. **参数调整**: 部分接口的参数结构有所调整，特别是需要传递userId的接口

## 注意事项

1. 使用新接口的页面需要注意参数传递方式的变化
2. 涉及userId的接口需要确保正确传递用户ID
3. 建议逐步迁移到新接口，并测试兼容性函数的正确性
4. 部分旧的系统接口路径（如 `/system/test/`）仍保留在兼容函数中

## 其他文件更新

### payment.js
- 更新了 `createAssessmentOrder()` 接口路径：`/miniapp/order/assessment/create` → `/miniapp/user/order/create`
- 更新了 `getAssessmentOrderList()` 接口路径：`/miniapp/order/assessment/list` → `/miniapp/user/order/list`

### my.js
- 更新了 `getTestRecords()` 接口路径：`/system/test/records` → `/miniapp/user/assessment/records/{userId}`

### favorite.js
- 已支持测评收藏功能（targetType: 4）
- 接口路径正确，无需更新

## 验证结果

✅ **已完成的更新**：
- [x] 量表浏览相关接口 (8个)
- [x] 测评流程相关接口 (11个)
- [x] 测评结果相关接口 (2个)
- [x] 测评记录管理接口 (5个)
- [x] 测评订单相关接口 (8个)
- [x] 测评评价相关接口 (11个)
- [x] payment.js 中的测评订单接口 (2个)
- [x] my.js 中的测评记录接口 (1个)

✅ **兼容性处理**：
- [x] 保留了所有旧版本函数的兼容性映射
- [x] 确保现有页面不会因接口更新而出错

✅ **接口路径验证**：
- [x] 所有新接口路径与后端文档完全一致
- [x] 统一使用 `/miniapp/user/assessment/` 前缀
- [x] 参数传递方式符合新的接口规范

## 下一步工作

1. **测试验证**：
   - 在开发环境中测试所有新接口的功能正确性
   - 验证兼容性函数是否正常工作
   - 检查现有页面是否受到影响

2. **页面更新**：
   - 逐步更新相关页面组件以使用新接口
   - 特别注意需要传递userId的接口调用
   - 更新错误处理逻辑以适应新的响应格式

3. **文档维护**：
   - 更新API文档和注释
   - 创建接口迁移指南
   - 记录已知问题和解决方案

4. **性能优化**：
   - 移除不再使用的兼容性函数
   - 优化接口调用逻辑
   - 减少冗余的API请求
