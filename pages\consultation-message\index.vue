<template>
  <view class="message-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <image src="/static/icon/search.png" mode="aspectFit"></image>
        <input
          type="text"
          placeholder="搜索来访者"
          v-model="searchKeyword"
          @input="onSearchInput"
        />
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="conversationList.length === 0 && !loading">
      <image src="/static/icon/empty-message.png" mode="aspectFit"></image>
      <text>暂无消息</text>
    </view>

    <!-- 会话列表 -->
    <scroll-view
      class="conversation-list"
      scroll-y
      v-else
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view
        class="conversation-item"
        v-for="item in conversationList"
        :key="item.conversationId"
        @click="goToChat(item)"
      >
        <view class="avatar-container">
          <image class="avatar" :src="item.clientAvatar || item.userAvatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
          <view class="badge" v-if="getUnreadCount(item) > 0">{{ getUnreadCount(item) }}</view>
          <view class="online-status" v-if="item.isOnline"></view>
        </view>
        <view class="conversation-info">
          <view class="top-row">
            <text class="nickname">{{ item.clientName || item.userName || '用户' }}</text>
            <text class="time">{{ formatTime(item.lastMessageTime) }}</text>
          </view>
          <view class="bottom-row">
            <text class="last-message" :class="{'message-withdrawn': isWithdrawn(item.lastMessage)}">
              {{ formatLastMessage(item) }}
            </text>
            <view class="message-type" v-if="item.lastMessageType">
              <text>{{ getMessageTypeText(item.lastMessageType) }}</text>
            </view>
          </view>
        </view>
        <view class="conversation-actions">
          <view class="action-btn" @click.stop="markAsRead(item)" v-if="getUnreadCount(item) > 0">
            <image src="/static/icon/mark-read.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 快捷回复模板 -->
    <view class="quick-reply-fab" @click="showQuickReplyModal = true">
      <image src="/static/icon/quick-reply.png" mode="aspectFit"></image>
    </view>

    <!-- 快捷回复模板弹窗 -->
    <view class="quick-reply-modal" v-if="showQuickReplyModal" @click="closeQuickReplyModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">快捷回复模板</text>
          <text class="modal-close" @click="closeQuickReplyModal">×</text>
        </view>
        <view class="modal-body">
          <view class="template-list">
            <view
              class="template-item"
              v-for="(template, index) in quickReplyTemplates"
              :key="index"
              @click="useTemplate(template)"
            >
              <text class="template-title">{{ template.title }}</text>
              <text class="template-content">{{ template.content }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <cc-myTabbar :tabBarShow="3"></cc-myTabbar>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useChatStore } from '@/stores/chat'
import { useUserStore } from '@/stores/user'
import { onShow } from '@dcloudio/uni-app'

const chatStore = useChatStore()
const userStore = useUserStore()
const conversationList = computed(() => chatStore.conversationList)
const isConsultant = ref(true) // 咨询师端标识

// 响应式数据
const searchKeyword = ref('')
const loading = ref(false)
const refreshing = ref(false)
const showQuickReplyModal = ref(false)

// 快捷回复模板
const quickReplyTemplates = ref([
  {
    title: '问候语',
    content: '您好，我是您的咨询师，很高兴为您服务。'
  },
  {
    title: '预约确认',
    content: '您的预约已确认，请按时参加咨询。如有问题请及时联系我。'
  },
  {
    title: '咨询提醒',
    content: '温馨提醒：您的咨询时间即将到来，请提前5分钟准备。'
  },
  {
    title: '咨询结束',
    content: '本次咨询已结束，感谢您的信任。如有疑问请随时联系我。'
  },
  {
    title: '作业布置',
    content: '根据今天的咨询内容，建议您完成以下练习...'
  },
  {
    title: '关怀问候',
    content: '最近感觉怎么样？有什么需要帮助的地方吗？'
  }
])

// 页面加载时初始化数据
onMounted(async () => {
  console.log('咨询师消息页面加载，用户信息:', userStore.profile)
  console.log('用户ID:', userStore.userId)

  // 确保WebSocket连接
  if (userStore.userId && (!chatStore.wsConnected || !uni.webSocketTask)) {
    console.log('初始化咨询师WebSocket连接')
    chatStore.initWebSocket(userStore.userId)
  }

  await loadConversations()
})

// 每次显示页面时刷新数据
onShow(async () => {
  console.log('咨询师消息页面显示，用户信息:', userStore.profile)
  console.log('用户ID:', userStore.userId)

  // 确保WebSocket连接
  if (userStore.userId && (!chatStore.wsConnected || !uni.webSocketTask)) {
    console.log('重新初始化咨询师WebSocket连接')
    chatStore.initWebSocket(userStore.userId)
  }

  // 清除未读消息计数（咨询师进入消息页面表示已查看）
  chatStore.unreadTotal = 0;
  uni.$emit('unread-count-changed', 0);
  console.log('咨询师进入消息页面，清除未读计数');

  await loadConversations()
})

// 加载会话列表
const loadConversations = async () => {
  loading.value = true
  try {
    // 咨询师端加载会话列表
    await chatStore.getConversationList(isConsultant.value)
    console.log('咨询师端会话列表:', conversationList.value)
    await chatStore.getUnreadCount()
  } catch (error) {
    console.error('加载会话列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 搜索输入
const onSearchInput = () => {
  // 防抖处理
  clearTimeout(onSearchInput.timer)
  onSearchInput.timer = setTimeout(() => {
    filterConversations()
  }, 300)
}

// 筛选会话
const filterConversations = () => {
  if (!searchKeyword.value.trim()) {
    loadConversations()
    return
  }

  // 根据关键词筛选会话
  // 这里可以调用store的筛选方法或直接重新加载数据
  loadConversations()
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  loadConversations().finally(() => {
    refreshing.value = false
  })
}

// 获取未读消息数量
const getUnreadCount = (conversation) => {
  // 咨询师端查看咨询师未读数量
  return conversation.consultantUnreadCount || 0
}

// 格式化最后消息时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''

  const messageDate = new Date(timestamp)
  const now = new Date()

  // 同一天显示时间
  if (messageDate.toDateString() === now.toDateString()) {
    return messageDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }

  // 一周内显示星期几
  const dayDiff = Math.floor((now - messageDate) / (1000 * 60 * 60 * 24))
  if (dayDiff < 7) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weekdays[messageDate.getDay()]
  }

  // 超过一周显示日期
  return messageDate.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
}

// 判断消息是否已撤回
const isWithdrawn = (message) => {
  if (!message) return false
  try {
    const parsedMessage = JSON.parse(message)
    return parsedMessage.type === 'withdraw_notification'
  } catch (e) {
    return false
  }
}

// 格式化最后一条消息
const formatLastMessage = (conversation) => {
  if (!conversation.lastMessage) return ''

  try {
    const parsedMessage = JSON.parse(conversation.lastMessage)
    if (parsedMessage.type === 'withdraw_notification') {
      return '消息已撤回'
    }
    return parsedMessage.content || ''
  } catch (e) {
    return conversation.lastMessage
  }
}

// 获取消息类型文本
const getMessageTypeText = (type) => {
  const typeMap = {
    'text': '文本',
    'image': '图片',
    'voice': '语音',
    'file': '文件'
  }
  return typeMap[type] || ''
}

// 标记为已读
const markAsRead = async (conversation) => {
  try {
    await chatStore.markConversationAsRead(conversation.conversationId)
    loadConversations()
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

// 跳转到聊天页面
const goToChat = (conversation) => {
  console.log('咨询师端跳转到聊天页面，会话数据:', conversation);
  console.log('客户ID:', conversation.clientId);
  console.log('咨询师ID:', userStore.profile.userId);

  // 检查必要的参数
  if (!conversation.conversationId) {
    console.error('会话ID缺失');
    uni.showToast({
      title: '会话信息错误',
      icon: 'none'
    });
    return;
  }

  // 获取客户ID，尝试多个可能的字段名
  const clientId = conversation.clientId || conversation.userId || conversation.customerId;

  if (!clientId) {
    console.error('客户ID缺失，会话数据:', conversation);
    uni.showToast({
      title: '客户信息错误',
      icon: 'none'
    });
    return;
  }

  const clientName = conversation.clientName || conversation.userName || conversation.customerName || '用户';
  const clientAvatar = conversation.clientAvatar || conversation.userAvatar || conversation.customerAvatar;

  console.log('跳转参数 - 客户ID:', clientId, '客户名称:', clientName);

  uni.navigateTo({
    url: `/pages/my/my-message/chat/index?conversationId=${conversation.conversationId}&userId=${clientId}&consultantId=${userStore.profile.userId}&nickname=${clientName}&clientAvatar=${clientAvatar}&isConsultant=true`
  })
}

// 使用模板
const useTemplate = (template) => {
  // 复制到剪贴板
  uni.setClipboardData({
    data: template.content,
    success: () => {
      uni.showToast({
        title: '已复制到剪贴板',
        icon: 'success'
      })
      closeQuickReplyModal()
    }
  })
}

// 关闭快捷回复模板弹窗
const closeQuickReplyModal = () => {
  showQuickReplyModal.value = false
}
</script>

<style scoped lang="scss">
.message-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.search-bar {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;

  .search-input {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 25rpx;
    padding: 0 20rpx;
    height: 70rpx;

    image {
      width: 30rpx;
      height: 30rpx;
      margin-right: 15rpx;
    }

    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  text {
    color: #999;
    font-size: 28rpx;
  }
}

.conversation-list {
  height: calc(100vh - 140rpx);

  .conversation-item {
    display: flex;
    padding: 30rpx 20rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .avatar-container {
      position: relative;
      margin-right: 20rpx;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #f0f0f0;
      }

      .badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        background-color: #ff4d4f;
        color: #fff;
        font-size: 20rpx;
        min-width: 32rpx;
        height: 32rpx;
        border-radius: 16rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6rpx;
      }

      .online-status {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 20rpx;
        height: 20rpx;
        background-color: #52c41a;
        border: 2rpx solid #fff;
        border-radius: 50%;
      }
    }

    .conversation-info {
      flex: 1;
      overflow: hidden;

      .top-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;

        .nickname {
          font-size: 30rpx;
          font-weight: 700;
          color: #333;
        }

        .time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .bottom-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .last-message {
          font-size: 26rpx;
          color: #666;
          max-width: 400rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &.message-withdrawn {
            color: #999;
            font-style: italic;
          }
        }

        .message-type {
          text {
            font-size: 22rpx;
            color: #999;
            background-color: #f5f5f5;
            padding: 2rpx 8rpx;
            border-radius: 8rpx;
          }
        }
      }
    }

    .conversation-actions {
      display: flex;
      align-items: center;

      .action-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 30rpx;
          height: 30rpx;
        }
      }
    }

    &:active {
      background-color: #f0f0f0;
    }
  }
}

.quick-reply-fab {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);

  image {
    width: 40rpx;
    height: 40rpx;
  }
}

.quick-reply-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 9999;

  .modal-content {
    width: 100%;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    max-height: 80vh;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .modal-close {
        font-size: 40rpx;
        color: #999;
      }
    }

    .modal-body {
      padding: 20rpx 30rpx 30rpx;
      max-height: 60vh;
      overflow-y: auto;

      .template-list {
        .template-item {
          padding: 20rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .template-title {
            display: block;
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
          }

          .template-content {
            font-size: 26rpx;
            color: #666;
            line-height: 1.4;
          }

          &:active {
            background-color: #f5f5f5;
          }
        }
      }
    }
  }
}
</style>