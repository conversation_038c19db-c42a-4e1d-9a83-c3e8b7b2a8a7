# InfoModal 通用信息展示弹窗组件

## 概述

`InfoModal` 是一个通用的信息展示弹窗组件，可以用于显示测评须知、课程介绍、服务条款等各种类型的信息内容。组件支持动态内容配置，具有良好的可扩展性和复用性。

## 功能特性

- ✅ **动态内容**: 支持多个内容区块，每个区块可以有标题和内容
- ✅ **灵活配置**: 可配置标题、注意事项、底部按钮等
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **滚动支持**: 内容过长时支持滚动查看
- ✅ **按钮样式**: 支持主要和次要两种按钮样式
- ✅ **事件处理**: 支持关闭和按钮点击事件

## 使用方法

### 基本用法

```vue
<template>
  <view>
    <button @click="showModal">显示信息</button>
    
    <InfoModal
      ref="infoModal"
      :title="modalTitle"
      :sections="modalSections"
      :notice="modalNotice"
      :show-button="true"
      :button-text="'我知道了'"
      @close="handleClose"
      @button-click="handleButtonClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import InfoModal from '@/components/InfoModal/InfoModal.vue'

const infoModal = ref(null)

// 测评须知示例
const modalTitle = '测评须知'
const modalSections = [
  {
    title: '测评目的',
    content: '本测评旨在帮助与者了解自己的心理状态与特质，包括但不限于情绪状态、性格倾向、应对策略等。'
  },
  {
    title: '测评对象',
    content: '本测评适用于年龄在 16 岁及以上的个人。若参与者年龄较小，需要在父母或法定监护人的指导下进行。'
  },
  {
    title: '测评后处理',
    content: '评估报告包括各项心理指标的解读和说明，您可以下载报告永久保存。'
  }
]
const modalNotice = '如果您在测评过程中感到不适，可以随时停止。测评不能替代专业的心理诊断。'

const showModal = () => {
  infoModal.value?.open()
}

const handleClose = () => {
  console.log('弹窗关闭')
}

const handleButtonClick = () => {
  console.log('按钮点击')
  infoModal.value?.close()
}
</script>
```

### 不同类型的使用示例

#### 1. 测评须知
```javascript
const assessmentInfo = {
  title: '测评须知',
  sections: [
    {
      title: '测评目的',
      content: '本测评旨在帮助参与者了解自己的心理状态与特质...'
    },
    {
      title: '测评对象',
      content: '本测评适用于年龄在 16 岁及以上的个人...'
    },
    {
      title: '测评后处理',
      content: '评估报告包括各项心理指标的解读和说明...'
    }
  ],
  notice: '如果您在测评过程中感到不适，可以随时停止。测评不能替代专业的心理诊断。'
}
```

#### 2. 课程介绍
```javascript
const courseInfo = {
  title: '课程详情',
  sections: [
    {
      title: '课程简介',
      content: '本课程将带您深入了解心理健康的重要性...'
    },
    {
      title: '适用人群',
      content: '适合对心理学感兴趣的初学者和专业人士...'
    },
    {
      title: '学习收获',
      content: '完成课程后，您将掌握基本的心理调节技巧...'
    }
  ],
  notice: '请确保在安静的环境中学习，以获得最佳效果。'
}
```

#### 3. 服务条款
```javascript
const termsInfo = {
  title: '服务条款',
  sections: [
    {
      title: '服务内容',
      content: '我们提供专业的心理咨询和测评服务...'
    },
    {
      title: '用户权利',
      content: '用户有权随时终止服务，并要求删除个人信息...'
    },
    {
      title: '免责声明',
      content: '本服务仅供参考，不能替代专业医疗建议...'
    }
  ]
}
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | '信息详情' | 弹窗标题 |
| sections | Array | [] | 内容区块数组，每个对象包含 title 和 content |
| notice | String | '' | 注意事项文本 |
| showButton | Boolean | false | 是否显示底部按钮 |
| buttonText | String | '确定' | 按钮文字 |
| buttonType | String | 'primary' | 按钮类型：'primary' \| 'secondary' |
| buttonDisabled | Boolean | false | 按钮是否禁用 |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| close | 弹窗关闭时触发 | - |
| button-click | 底部按钮点击时触发 | - |

## Methods 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| open | 打开弹窗 | - |
| close | 关闭弹窗 | - |

## 样式定制

组件使用了项目统一的设计规范，包括：
- 主色调：#ff6b35
- 渐变按钮：linear-gradient(135deg, #667eea, #764ba2)
- 圆角：24rpx
- 阴影效果

如需自定义样式，可以通过 CSS 变量或覆盖样式类来实现。

## 实际应用场景

### 在测评页面中使用
```vue
<!-- pages/assessment/detail.vue -->
<template>
  <view class="assessment-page">
    <button @click="showAssessmentNotice">查看测评须知</button>

    <InfoModal
      ref="noticeModal"
      :title="'测评须知'"
      :sections="noticeSections"
      :notice="noticeText"
      :show-button="true"
      :button-text="'开始测评'"
      @button-click="startAssessment"
    />
  </view>
</template>

<script setup>
import InfoModal from '@/components/InfoModal/InfoModal.vue'

const noticeModal = ref(null)
const noticeSections = [
  // ... 测评须知内容
]

const showAssessmentNotice = () => {
  noticeModal.value?.open()
}

const startAssessment = () => {
  // 开始测评逻辑
  uni.navigateTo({
    url: '/pages/assessment/questions'
  })
  noticeModal.value?.close()
}
</script>
```

### 在课程页面中使用
```vue
<!-- pages/course/detail.vue -->
<template>
  <view class="course-page">
    <button @click="showCourseDetail">课程详情</button>

    <InfoModal
      ref="courseModal"
      :title="courseInfo.title"
      :sections="courseInfo.sections"
      :show-button="true"
      :button-text="'立即购买'"
      @button-click="purchaseCourse"
    />
  </view>
</template>
```

## 使用配置文件

为了简化使用，组件提供了预设的配置文件 `config.js`，包含常用的信息展示配置。

### 使用预设配置
```vue
<template>
  <view>
    <button @click="showAssessmentNotice">测评须知</button>

    <InfoModal
      ref="infoModal"
      :title="currentConfig.title"
      :sections="currentConfig.sections"
      :notice="currentConfig.notice"
      :show-button="currentConfig.showButton"
      :button-text="currentConfig.buttonText"
      :button-type="currentConfig.buttonType"
      @button-click="handleButtonClick"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import InfoModal from '@/components/InfoModal/InfoModal.vue'
import { getInfoConfig } from '@/components/InfoModal/config.js'

const infoModal = ref(null)
const currentConfig = ref({})

const showAssessmentNotice = () => {
  // 使用预设的测评须知配置
  currentConfig.value = getInfoConfig('assessment')
  infoModal.value?.open()
}

const handleButtonClick = () => {
  // 处理按钮点击
  console.log('开始测评')
  infoModal.value?.close()
}
</script>
```

### 创建自定义配置
```vue
<script setup>
import { createInfoConfig } from '@/components/InfoModal/config.js'

const showCustomInfo = () => {
  const customConfig = createInfoConfig({
    title: '自定义标题',
    sections: [
      {
        title: '第一部分',
        content: '这是自定义的内容...'
      }
    ],
    notice: '自定义注意事项',
    showButton: true,
    buttonText: '自定义按钮',
    buttonType: 'primary'
  })

  currentConfig.value = customConfig
  infoModal.value?.open()
}
</script>
```

### 可用的预设配置类型
- `assessment` - 测评须知
- `consultation` - 咨询须知
- `meditation` - 冥想课程
- `terms` - 服务条款
- `privacy` - 隐私政策
- `purchase` - 购买须知

## 数据结构示例

### 完整的测评须知数据
```javascript
export const ASSESSMENT_NOTICE = {
  title: '测评须知',
  sections: [
    {
      title: '测评目的',
      content: '本测评旨在帮助参与者了解自己的心理状态与特质，包括但不限于情绪状态、性格倾向、应对策略等。'
    },
    {
      title: '测评对象',
      content: '本测评适用于年龄在 16 岁及以上的个人。若参与者年龄较小，需要在父母或法定监护人的指导下进行。'
    },
    {
      title: '测评后处理',
      content: '评估报告包括各项心理指标的解读和说明，您可以下载报告永久保存。'
    }
  ],
  notice: '如果您在测评过程中感到不适，可以随时停止。测评不能替代专业的心理诊断。',
  showButton: true,
  buttonText: '开始测评',
  buttonType: 'primary'
}
```

## 注意事项

1. 确保项目中已安装 uni-ui 组件库
2. sections 数组中的每个对象都应包含 content 字段，title 字段可选
3. 长文本内容会自动支持滚动
4. 组件会自动适配不同屏幕尺寸
5. 建议在使用前先测试不同长度的内容以确保显示效果
6. 可以通过配置不同的 sections 数据来适应各种业务场景
7. 组件支持动态更新内容，适合在同一页面展示不同类型的信息
