<template>
  <view class="my-courses-page">
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <view class="stat-number">{{ courseList.length }}</view>
        <view class="stat-label">已购课程</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ completedCount }}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ totalStudyTime }}</view>
        <view class="stat-label">学习时长(分钟)</view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="['tab-item', { active: currentFilter === index }]"
        @click="switchFilter(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 课程列表 -->
    <scroll-view 
      class="course-list" 
      scroll-y 
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view v-if="filteredCourseList.length === 0" class="empty-state">
        <image src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/empty-course.png" mode="aspectFit"></image>
        <text>暂无课程</text>
        <button class="browse-btn" @click="goToBrowse">去浏览课程</button>
      </view>

      <view v-else class="course-items">
        <view 
          v-for="course in filteredCourseList" 
          :key="course.id"
          class="course-item"
          @click="goToCourseDetail(course.id)"
        >
          <view class="course-cover">
            <image :src="course.coverImage || defaultCover" mode="aspectFill"></image>
            <view class="progress-overlay">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: course.progress + '%' }"></view>
              </view>
              <text class="progress-text">{{ course.progress }}%</text>
            </view>
          </view>
          
          <view class="course-info">
            <view class="course-title">{{ course.title }}</view>
            <view class="course-meta">
              <text>{{ course.chapterCount }}个章节</text>
              <text>{{ course.duration }}分钟</text>
            </view>
            <view class="course-status">
              <view v-if="course.progress === 100" class="status-completed">
                <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
                <text>已完成</text>
              </view>
              <view v-else-if="course.progress > 0" class="status-learning">
                <uni-icons type="play-filled" size="16" color="#ff6b35"></uni-icons>
                <text>学习中</text>
              </view>
              <view v-else class="status-not-started">
                <uni-icons type="circle" size="16" color="#ccc"></uni-icons>
                <text>未开始</text>
              </view>
            </view>
          </view>
          
          <view class="course-actions">
            <button 
              v-if="course.progress === 100" 
              class="action-btn review-btn" 
              @click.stop="goToReview(course.id)"
            >
              评价
            </button>
            <button 
              v-else 
              class="action-btn continue-btn" 
              @click.stop="continueLearning(course)"
            >
              {{ course.progress > 0 ? '继续学习' : '开始学习' }}
            </button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPurchasedCourses, getUserProgress } from '@/api/course'
import { useUserStore } from '@/stores/user'

// 响应式数据
const courseList = ref([])
const refreshing = ref(false)
const filterTabs = ref([
  { name: '全部', value: 'all' },
  { name: '学习中', value: 'learning' },
  { name: '已完成', value: 'completed' },
  { name: '未开始', value: 'not_started' }
])
const currentFilter = ref(0)

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/course/default-cover.png'

const userStore = useUserStore()

// 计算属性
const filteredCourseList = computed(() => {
  const filter = filterTabs.value[currentFilter.value].value
  
  if (filter === 'all') {
    return courseList.value
  } else if (filter === 'learning') {
    return courseList.value.filter(course => course.progress > 0 && course.progress < 100)
  } else if (filter === 'completed') {
    return courseList.value.filter(course => course.progress === 100)
  } else if (filter === 'not_started') {
    return courseList.value.filter(course => course.progress === 0)
  }
  
  return courseList.value
})

const completedCount = computed(() => {
  return courseList.value.filter(course => course.progress === 100).length
})

const totalStudyTime = computed(() => {
  return courseList.value.reduce((total, course) => {
    return total + Math.round((course.duration || 0) * (course.progress || 0) / 100)
  }, 0)
})

// 方法
const switchFilter = (index) => {
  currentFilter.value = index
}

const loadPurchasedCourses = async () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  try {
    const res = await getPurchasedCourses()
    if (res.code === 200) {
      courseList.value = res.data || []
      
      // 为每个课程加载学习进度
      await loadAllProgress()
    } else {
      uni.showToast({
        title: res.msg || '获取课程列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取已购课程失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const loadAllProgress = async () => {
  for (let course of courseList.value) {
    try {
      const res = await getUserProgress(course.id)
      if (res.code === 200) {
        const progressList = res.data || []
        
        // 计算总体进度
        if (progressList.length > 0) {
          const totalProgress = progressList.reduce((sum, p) => sum + (p.progressPercent || 0), 0)
          course.progress = Math.round(totalProgress / progressList.length)
        } else {
          course.progress = 0
        }
      }
    } catch (error) {
      console.error(`获取课程${course.id}进度失败:`, error)
      course.progress = 0
    }
  }
}

const onRefresh = async () => {
  refreshing.value = true
  await loadPurchasedCourses()
  refreshing.value = false
}

const goToCourseDetail = (courseId) => {
  uni.navigateTo({
    url: `/pages/course/detail/index?id=${courseId}`
  })
}

const continueLearning = (course) => {
  // 找到第一个未完成的章节
  uni.navigateTo({
    url: `/pages/course/detail/index?id=${course.id}&tab=1`
  })
}

const goToReview = (courseId) => {
  uni.navigateTo({
    url: `/pages/course/review/index?courseId=${courseId}`
  })
}

const goToBrowse = () => {
  uni.navigateTo({
    url: '/pages/course/index'
  })
}

// 生命周期
onLoad(() => {
  loadPurchasedCourses()
})
</script>

<style lang="scss" scoped>
.my-courses-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.stats-section {
  display: flex;
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-number {
      font-size: 48rpx;
      font-weight: 600;
      color: #ff6b35;
      margin-bottom: 8rpx;
    }
    
    .stat-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;
    
    &.active {
      color: #ff6b35;
      font-weight: 600;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #ff6b35;
        border-radius: 2rpx;
      }
    }
  }
}

.course-list {
  flex: 1;
  padding: 0 32rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 32rpx;
  text-align: center;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .browse-btn {
    padding: 20rpx 48rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 28rpx;
  }
}

.course-items {
  .course-item {
    display: flex;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    
    .course-cover {
      position: relative;
      width: 200rpx;
      height: 120rpx;
      border-radius: 12rpx;
      overflow: hidden;
      margin-right: 24rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
      
      .progress-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 12rpx;
        background: linear-gradient(transparent, rgba(0,0,0,0.6));
        
        .progress-bar {
          height: 4rpx;
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 2rpx;
          overflow: hidden;
          margin-bottom: 8rpx;
          
          .progress-fill {
            height: 100%;
            background-color: #fff;
          }
        }
        
        .progress-text {
          font-size: 20rpx;
          color: #fff;
        }
      }
    }
    
    .course-info {
      flex: 1;
      
      .course-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .course-meta {
        display: flex;
        gap: 24rpx;
        font-size: 24rpx;
        color: #999;
        margin-bottom: 16rpx;
      }
      
      .course-status {
        .status-completed, .status-learning, .status-not-started {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 24rpx;
        }
        
        .status-completed {
          color: #4CAF50;
        }
        
        .status-learning {
          color: #ff6b35;
        }
        
        .status-not-started {
          color: #ccc;
        }
      }
    }
    
    .course-actions {
      display: flex;
      align-items: center;
      
      .action-btn {
        padding: 16rpx 32rpx;
        border: none;
        border-radius: 40rpx;
        font-size: 26rpx;
        
        &.continue-btn {
          background-color: #ff6b35;
          color: #fff;
        }
        
        &.review-btn {
          background-color: #f8f8f8;
          color: #666;
        }
      }
    }
  }
}
</style>
