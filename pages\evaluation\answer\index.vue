<template>
  <view class="answer-page">
    <!-- 阻止返回拦截器 -->
    <view v-if="showBackInterceptor">
      <page-container :show="showBackInterceptor" :overlay="false" @beforeleave="beforeLeave"></page-container>
    </view>

    <!-- 顶部进度 -->
    <view class="progress-bar">
      <view class="progress-text">{{currentIndex + 1}}/{{questions.length}}</view>
      <view class="progress-outer">
        <view
          class="progress-inner"
          :style="{width: (currentIndex + 1) / questions.length * 100 + '%'}"
        ></view>
      </view>
    </view>

    <!-- 题目内容 -->
    <view class="questions-container">
      <view
        class="question-content"
        v-if="currentQuestion"
        :class="{'slide-left': slideDirection === 'left', 'slide-right': slideDirection === 'right'}"
        @animationend="onAnimationEnd"
      >
        <view class="question-title">
          <text class="number">Q{{currentQuestion.questionNo || (currentIndex + 1)}}.</text>
          <text>{{currentQuestion.content}}</text>
          <text v-if="currentQuestion.isRequired" class="required">*</text>
        </view>

        <!-- 选项列表 -->
        <view class="options-list">
          <view
            class="option-item"
            v-for="option in currentQuestion.optionList"
            :key="option.id"
            :class="{'selected': selectedOption === option.id}"
            @click="selectOption(option)"
          >
            <text class="option-text">{{option.displayText || option.optionText}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-btns">
      <button
        class="prev-btn"
        v-if="currentIndex > 0"
        @click="prevQuestion"
      >上一题</button>
      <button
        class="save-btn"
        v-if="Object.keys(answers).length > 0"
        @click="saveAnswers"
      >保存答案</button>
      <button
        class="next-btn"
        v-if="currentIndex < questions.length - 1"
        @click="nextQuestion"
      >下一题</button>
      <button
        class="submit-btn"
        v-if="currentIndex === questions.length - 1"
        :disabled="!selectedOption"
        @click="submitAnswerFn"
      >提交答案</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import {
  startAssessment,
  getAssessmentQuestions,
  getAssessmentAnswers,
  saveAnswerRecord,
  completeAssessment,
  getAssessmentResult,
  getAssessmentProgress
} from '@/api/evaluation'
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
const questions = ref([])
const currentIndex = ref(0)
const selectedOption = ref('')
const sessionId = ref('')
const recordId = ref(null) // 保留兼容性
const answers = ref({})
const assessmentId = ref('')
const slideDirection = ref('')
const testStarted = ref(false)
const totalQuestions = ref(0)
const showBackInterceptor = ref(true) // 返回拦截器状态

// 获取当前题目
const currentQuestion = computed(() => questions.value[currentIndex.value])

// 开始测评
const startTestFn = async (id) => {
  try {
    assessmentId.value = id

    // 使用新的开始测评API
    const startRes = await startAssessment(Number(id))
    if (startRes.code === 200) {
      recordId.value = startRes.data // 新API返回的是recordId
      testStarted.value = true

      // 获取题目列表
      const questionsRes = await getAssessmentQuestions(recordId.value)
      if (questionsRes.code === 200) {
        // 处理题目数据，确保选项数据结构正确
        questions.value = questionsRes.data || []
        totalQuestions.value = questions.value.length



        // 初始化当前题目的选中选项
        if (questions.value.length > 0) {
          const currentQuestionId = questions.value[currentIndex.value].id
          selectedOption.value = answers.value[currentQuestionId] || ''
        }
      } else {
        uni.showToast({
          title: questionsRes.msg || '获取题目失败',
          icon: 'none'
        })
      }
    } else {
      uni.showToast({
        title: startRes.msg || '开始测评失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('开始测评失败:', error)
    uni.showToast({
      title: '开始测评失败',
      icon: 'none'
    })
  }
}

// 继续测评（使用已有的recordId）
const continueTestFn = async (id, existingRecordId) => {
  try {
    assessmentId.value = id
    recordId.value = existingRecordId

    // 获取题目列表
    const questionsRes = await getAssessmentQuestions(existingRecordId)
    if (questionsRes.code === 200) {
      // 处理题目数据，确保选项数据结构正确
      questions.value = questionsRes.data || []
      totalQuestions.value = questions.value.length

      // 获取答题历史记录
      try {
        const answersRes = await getAssessmentAnswers(existingRecordId)

        if (answersRes.code === 200 && answersRes.data) {
          // 将答题历史记录转换为 answers 对象格式
          const answerHistory = answersRes.data
          answers.value = {}

          answerHistory.forEach(answer => {
            // 根据实际的数据结构调整字段名
            const questionId = answer.questionId || answer.question_id
            const optionId = answer.optionId || answer.option_id
            if (questionId && optionId) {
              answers.value[questionId] = optionId
            }
          })
        }
      } catch (error) {
        console.error('获取答题历史失败:', error)
      }

      // 获取答题进度
      try {
        const progressRes = await getAssessmentProgress(existingRecordId)
        if (progressRes.code === 200) {
          const progress = progressRes.data
          currentIndex.value = (progress.current_question_no || progress.currentQuestionNo) - 1 || 0

          // 设置当前选中选项（如果有的话）
          if (questions.value.length > 0 && currentIndex.value < questions.value.length) {
            const currentQuestionId = questions.value[currentIndex.value].id
            selectedOption.value = answers.value[currentQuestionId] || ''
          }
        }
      } catch (error) {
        console.error('获取答题进度失败:', error)
      }
    } else {
      uni.showToast({
        title: questionsRes.msg || '获取题目失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('恢复测评失败:', error)
    uni.showToast({
      title: '恢复测评失败',
      icon: 'none'
    })
  }
}

// 动画结束后清除动画类
const onAnimationEnd = () => {
  slideDirection.value = ''
}

// 选择选项
const selectOption = async (option) => {
  selectedOption.value = option.id
  // 保存答案到本地
  const currentQuestionId = currentQuestion.value.id
  answers.value[currentQuestionId] = option.id

  // 使用新的提交答案API
  try {
    await saveAnswerRecord(
      recordId.value,
      currentQuestionId,
      option.id,
      option.optionText,
      30 // 可以记录实际用时
    )
  } catch (error) {
    console.error('提交答案失败:', error)
    uni.showToast({
      title: '保存答案失败',
      icon: 'none'
    })
  }

  // 自动跳转到下一题，延迟让用户看到选中效果
  if (currentIndex.value < questions.value.length - 1) {
    setTimeout(() => {
      nextQuestion()
    }, 500)
  }
}

// 保存答案（新版本已在选择选项时自动保存）
const saveAnswers = async () => {
  try {
    uni.showToast({
      title: '答案已自动保存',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    slideDirection.value = 'right'
    setTimeout(() => {
      currentIndex.value--
      // 恢复之前的选择
      const questionId = questions.value[currentIndex.value].id
      selectedOption.value = answers.value[questionId] || ''
    }, 400) // 等待动画完成
  }
}

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < questions.value.length - 1) {
    slideDirection.value = 'left'
    setTimeout(() => {
      currentIndex.value++
      // 恢复之前的选择
      const questionId = questions.value[currentIndex.value].id
      selectedOption.value = answers.value[questionId] || ''
    }, 400) // 等待动画完成
  } else if (!selectedOption.value) {
    uni.showToast({
      title: '请先选择答案',
      icon: 'none'
    })
  }
}

// 完成测评
const submitAnswerFn = async () => {
  try {
    // 检查是否所有题目都已回答
    const unansweredIndex = questions.value.findIndex(q => !answers.value[q.id])
    if (unansweredIndex !== -1) {
      uni.showModal({
        title: '提示',
        content: '还有题目未完成，即将跳转到未完成题目',
        showCancel: false,
        success: () => {
          currentIndex.value = unansweredIndex
          slideDirection.value = unansweredIndex > currentIndex.value ? 'left' : 'right'
        }
      })
      return
    }

    // 使用新的完成测评API
    const res = await completeAssessment(recordId.value)
    if (res.code === 200) {
      // 跳转到测评结果页面
      uni.redirectTo({
        url: '/pages/evaluation/report/index?recordId=' + recordId.value
      })
    } else {
      uni.showToast({
        title: res.msg || '完成测评失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('完成测评失败:', error)
    uni.showToast({
      title: '完成测评失败',
      icon: 'none'
    })
  }
}

// 返回拦截处理
const beforeLeave = () => {
  // 临时关闭拦截器以显示弹窗
  showBackInterceptor.value = false

  uni.showModal({
    title: '确定要退出测评吗？',
    content: '退出后当前答题进度将会丢失',
    confirmText: '确定退出',
    cancelText: '继续答题',
    success: (res) => {
      if (res.confirm) {
        // 用户确认退出，执行跳转
        const pages = getCurrentPages()
        if (pages.length === 1) {
          // 返回到首页
          uni.switchTab({
            url: '/pages/index/index'
          })
        } else {
          // 返回上一页
          uni.navigateBack({
            delta: 1
          })
        }
      } else {
        // 用户取消，重新启用拦截器
        showBackInterceptor.value = true
      }
    },
    fail: () => {
      // 弹窗失败时也要重新启用拦截器
      showBackInterceptor.value = true
    }
  })
}

// 页面加载
onLoad((options) => {
  if (options.id) {
    if (options.recordId) {
      // 继续测评（使用recordId）
      continueTestFn(options.id, options.recordId)
    } else {
      // 开始新测评
      startTestFn(options.id)
    }
    uni.setNavigationBarTitle({
      title: decodeURIComponent(options.title || '心理测评')
    });
  }
})

// 页面卸载时清理拦截器
onUnload(() => {
  showBackInterceptor.value = false
})
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}
.answer-page {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;

  .progress-bar {
    background: #fff;
    padding: 20rpx;
    border-radius: 12rpx;
    margin-bottom: 30rpx;

    .progress-text {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
    }

    .progress-outer {
      height: 6rpx;
      background-color: #eee;
      border-radius: 3rpx;
      overflow: hidden;
    }

    .progress-inner {
      height: 100%;
      background-color: #409eff;
      transition: width 0.3s;
    }
  }

  .questions-container {
    position: relative;
    overflow: hidden;
    min-height: 60vh;
  }

  .question-content {
    background: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    
    &.slide-left {
      animation: slideLeft 0.4s ease-in;
    }
    
    &.slide-right {
      animation: slideRight 0.4s ease-in;
    }

    .question-title {
      font-size: 32rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 40rpx;

      .number {
        color: #409eff;
        margin-right: 10rpx;
        font-weight: bold;
      }

      .required {
        color: #ff4d4f;
        margin-left: 8rpx;
        font-weight: bold;
      }
    }

    .options-list {
      .option-item {
        background: #f8f8f8;
        padding: 30rpx;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        transition: all 0.3s;

        &.selected {
          background: #409eff;
          .option-text {
            color: #fff;
          }
        }

        .option-text {
          font-size: 30rpx;
          color: #333;
          line-height: 1.5;
          transition: color 0.3s;
        }
      }
    }
  }

  @keyframes slideLeft {
    0% {
      transform: translateX(0);
      opacity: 1;
    }
    30% {
      transform: translateX(-10%);
      opacity: 0.9;
    }
    100% {
      transform: translateX(-100%);
      opacity: 0;
    }
  }

  @keyframes slideRight {
    0% {
      transform: translateX(0);
      opacity: 1;
    }
    30% {
      transform: translateX(10%);
      opacity: 0.9;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .footer-btns {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 30rpx;
    background: #fff;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    button {
      flex: 1;
      margin: 0 10rpx;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 30rpx;
      border-radius: 40rpx;

      &.prev-btn {
        background: #f5f5f5;
        color: #666;
      }

      &.save-btn {
        background: #67c23a;
        color: #fff;
      }

      &.next-btn {
        background: #409eff;
        color: #fff;
      }

      &.submit-btn {
        background: #409eff;
        color: #fff;

        &:disabled {
          opacity: 0.6;
        }
      }
    }
  }
}
</style> 