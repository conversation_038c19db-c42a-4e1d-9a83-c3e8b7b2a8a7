<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">弹窗事件冒泡测试</text>
      <text class="test-desc">测试开关和时间选择器的事件冒泡问题修复</text>
    </view>

    <button class="open-modal-btn" @click="showModal = true">
      打开测试弹窗
    </button>

    <!-- 测试弹窗 -->
    <view class="test-modal" v-if="showModal" @click="closeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">测试弹窗</text>
          <text class="modal-close" @click="closeModal">×</text>
        </view>

        <view class="modal-body">
          <text class="section-title">测试项目</text>
          
          <!-- 开关测试 -->
          <view class="test-item">
            <text class="test-label">开关测试（应该不会关闭弹窗）:</text>
            <switch :checked="switchValue" @change="onSwitchChange" @click.stop />
          </view>
          
          <!-- 时间选择器测试 -->
          <view class="test-item">
            <text class="test-label">时间选择器测试:</text>
            <view class="time-picker-container" @click.stop>
              <picker mode="time" :value="timeValue" @change="onTimeChange">
                <view class="time-picker">{{ timeValue || '选择时间' }}</view>
              </picker>
            </view>
          </view>
          
          <!-- 普通按钮测试 -->
          <view class="test-item">
            <text class="test-label">普通按钮测试:</text>
            <button class="test-button" @click.stop="onButtonClick">点击我</button>
          </view>
          
          <!-- 状态显示 -->
          <view class="status-section">
            <text class="status-title">当前状态:</text>
            <text class="status-text">开关: {{ switchValue ? '开启' : '关闭' }}</text>
            <text class="status-text">时间: {{ timeValue || '未选择' }}</text>
            <text class="status-text">按钮点击次数: {{ clickCount }}</text>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn-close" @click="closeModal">关闭弹窗</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const showModal = ref(false)
const switchValue = ref(false)
const timeValue = ref('09:00')
const clickCount = ref(0)

const closeModal = () => {
  showModal.value = false
  console.log('弹窗被关闭')
}

const onSwitchChange = (e) => {
  switchValue.value = e.detail.value
  console.log('开关状态改变:', e.detail.value)
}

const onTimeChange = (e) => {
  timeValue.value = e.detail.value
  console.log('时间改变:', e.detail.value)
}

const onButtonClick = () => {
  clickCount.value++
  console.log('按钮被点击，次数:', clickCount.value)
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.test-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
  
  .test-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .test-desc {
    font-size: 24rpx;
    color: #666;
  }
}

.open-modal-btn {
  width: 100%;
  padding: 20rpx 0;
  background-color: #1890ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.test-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .modal-content {
    width: 90%;
    max-width: 600rpx;
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .modal-close {
        font-size: 40rpx;
        color: #999;
        cursor: pointer;
      }
    }
    
    .modal-body {
      padding: 30rpx;
      
      .section-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .test-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        
        .test-label {
          font-size: 26rpx;
          color: #333;
          flex: 1;
        }
        
        .time-picker-container {
          .time-picker {
            padding: 12rpx 20rpx;
            border: 1rpx solid #d9d9d9;
            border-radius: 6rpx;
            font-size: 26rpx;
            background-color: #fff;
            min-width: 120rpx;
            text-align: center;
          }
        }
        
        .test-button {
          padding: 12rpx 24rpx;
          background-color: #52c41a;
          color: #fff;
          border: none;
          border-radius: 6rpx;
          font-size: 24rpx;
        }
      }
      
      .status-section {
        margin-top: 30rpx;
        padding: 20rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
        
        .status-title {
          display: block;
          font-size: 26rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 15rpx;
        }
        
        .status-text {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
        }
      }
    }
    
    .modal-footer {
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #f0f0f0;
      
      .btn-close {
        width: 100%;
        padding: 16rpx 0;
        background-color: #ff4d4f;
        color: #fff;
        border: none;
        border-radius: 6rpx;
        font-size: 26rpx;
      }
    }
  }
}
</style>
