<template>
	<view class="content">
		<view class="consultant">
			<view class="address" v-if="storeInfo">
				<view class="left">
					<view class="top">{{storeInfo.name}} {{storeInfo.branchName}}</view>
					<view class="bottom">{{storeInfo.address}}</view>
				</view>
				<view class="right" @click="toMap">
					<view class="top">
						<image mode="scaleToFill" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/icon/%E6%96%B9%E4%BD%8D.png"></image>
					</view>
					<view class="bottom" v-if="distance">距您{{distance}}km</view>
				</view>
			</view>
			<view class="make-appointment-box">
				<view class="scroll">
					<EnhancedTimeTable
						ref="enhancedTimeTableRef"
						:showDateSelector="true"
						:dayRange="6"
						:showMessage="true"
						:showActionButtons="true"
						message="请选择咨询时间，每次咨询时长为1小时,不在当前时间表内请下单后与客服预约时间"
						@timeChange="handleTimeChange"
						@intervalChange="handleIntervalChange"
						@confirm="handleConfirm"
						@dateChange="handleDateChange"
					/>
				</view>
			</view>
		</view>
		<cc-myTabbar :tabBarShow="2"></cc-myTabbar>
	</view>
</template>

<script setup>
import { ref } from "vue";
import { onShow, onReady } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user.js";
import { getStore } from "@/api/store.js";
import EnhancedTimeTable from "@/components/EnhancedTimeTable/EnhancedTimeTable.vue";

onReady(() => {
	uni.hideTabBar();
});
onShow(() => {
	getStoreInfo();
});

const selectTime = ref([]);
const storeInfo = ref(null);
const distance = ref(null);
const user = useUserStore();

// 增强版时间表组件引用
const enhancedTimeTableRef = ref(null);

// 注意：所有时间处理逻辑已移至 EnhancedTimeTable 组件内部

// 计算两点之间的距离
const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // 地球半径，单位km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const d = R * c;
    return d.toFixed(2); // 保留两位小数
};

// 获取用户位置
const getUserLocation = () => {
    return new Promise((resolve, reject) => {
        uni.getLocation({
            type: 'gcj02',
            success: (res) => {
                resolve(res);
            },
            fail: (err) => {
                console.error('获取位置失败：', err);
                reject(err);
            }
        });
    });
};

// 获取门店信息
const getStoreInfo = async () => {
	try {
		const res = await getStore(1); // 获取ID为1的门店信息
		if (res.code === 200) {
			storeInfo.value = res.data.store;
			// 获取用户位置并计算距离
			try {
				const location = await getUserLocation();
				distance.value = calculateDistance(
					location.latitude,
					location.longitude,
					storeInfo.value.latitude,
					storeInfo.value.longitude
				);
			} catch (locationError) {
				console.log('用户未授权位置信息或获取位置失败');
				distance.value = null;
			}
		} else {
			uni.showToast({
				title: '获取门店信息失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('获取门店信息错误：', error);
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		});
	}
};

const toMap = () => {
	if (!storeInfo.value) return;
	uni.openLocation({
		longitude: Number(storeInfo.value.longitude),
		latitude: Number(storeInfo.value.latitude),
		name: storeInfo.value.mapName,
		address: storeInfo.value.mapAddress,
		fail: (err) => console.log("导航失败:", err),
	});
};

// 增强版时间表事件处理
const handleTimeChange = (selectedTimes) => {
	selectTime.value = selectedTimes;
	console.log('时间选择变化:', selectedTimes);
};

const handleIntervalChange = (intervals) => {
	console.log('时间区间变化:', intervals);
};

const handleDateChange = (dateItem, index) => {
	console.log('日期选择变化:', dateItem, index);
};

const handleConfirm = (data) => {
	console.log('确认选择:', data);

	// 保存选择的时间到store
	user.handleSelectTime(data.selectTime);
	// 保存格式化后的时间参数到store
	user.setSelectedTimeParams(data.timeParams);

	console.log('Store state after setting params:', {
		selectTime: user.$state.selectTime,
		selectedTimeParams: user.$state.selectedTimeParams,
		shouldFilterByTime: user.$state.shouldFilterByTime
	});

	uni.switchTab({
		url: `/pages/classification/index`
	});
};

// 保留原有的 handleSelect 方法以防其他地方调用
const handleSelect = () => {
	// 获取增强版时间表的选择结果
	if (enhancedTimeTableRef.value) {
		enhancedTimeTableRef.value.confirmSelection();
	} else {
		uni.showToast({ title: "请选择预约时间", icon: "none" });
	}
};
</script>

<style lang="scss" scoped>
.content {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;

	.consultant {
		width: calc(100% - 40rpx);
		padding: 20rpx;
		.address {
			width: calc(100% - 40rpx);
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left {
				.top {
					font-size: 34rpx;
					font-weight: 700;
					margin-bottom: 10rpx;
				}
				.bottom {
					font-size: 24rpx;
					color: #a2a2a2;
				}
			}
			.right {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				position: relative;
				.top {
					image {
						width: 36rpx;
						height: 36rpx;
						margin-right: 10rpx;
					}
				}
				.bottom {
					font-size: 22rpx;
					color: #a2a2a2;
				}
				&:before {
					content: "";
					width: 2rpx;
					height: 100rpx;
					position: absolute;
					left: -30rpx;
					background-color: #dddddd;
				}
			}
		}
		.visitor {
			width: 100%;
			height: 160rpx;
			border: 1px dashed #ccc;
			margin: 0 auto;
			margin-top: 20rpx;
			border-radius: 20rpx;
			.plus {
				display: flex;
				justify-content: center;
				flex-direction: column;
				margin-top: 14rpx;
				align-items: center;
				text {
					margin-top: 8rpx;
					text-align: center;
					color: #666b74;
				}
			}
		}
		.msg {
			font-size: 26rpx;
			margin-top: 20rpx;
		}
		.consultation-box {
			margin-top: 20px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.consultation-type {
				width: 48%;
				height: 100rpx;
				background-color: #f2f4f5;
				border-radius: 14rpx;
				text-align: center;
				line-height: 100rpx;
				font-size: 30rpx;
				color: #656a73;
				border: 2px solid #f2f4f5;
			}
			.individual {
				background-color: #eaf8ff;
				color: #01a6fe;
				border: 2px solid #01a6fe;
				font-weight: 700;
			}
			.consultation-method {
				background-color: #eaf8ff;
				color: #01a6fe;
				border: 2px solid #01a6fe;
				font-weight: 700;
			}
		}
		.time-grid {
			width: 100%;
			border: 1px solid #eee;
			border-radius: 10rpx;
			background-color: #fff;
			margin-bottom: 20rpx;
			
			.time-period {
				padding: 20rpx;
				border-bottom: 1px solid #eee;
				
				&:last-child {
					border-bottom: none;
				}
				
				.period-title {
					display: flex;
					align-items: center;
					color: #262626;
					font-size: 28rpx;
					margin-bottom: 20rpx;
					
					image {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
					}
				}
				
				.time-row {
					display: flex;
					flex-wrap: wrap;
					.time-slot {
						width: 20%;
						height: 70rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						transition: all 0.3s ease;
						position: relative;
						cursor: pointer;
						
						.time-text {
							font-size: 26rpx;
							color: #333;
							margin-bottom: 6rpx;
						}
						
						.status-text {
							font-size: 22rpx;
							color: #999;
						}
						
						&:hover:not(.is-disabled) {
							border-color: #1890ff;
						}
						
						&.is-disabled {
							background-color: #f5f5f5;
							cursor: not-allowed;
							
							.time-text {
								color: #999;
							}
						}
						
						&.is-active {
							background-color: #52b5f9;
							border-color: #1890ff;
							
							.time-text {
								color: #fff;
							}
						}
						
						&.is-start {
							border-top-right-radius: 0;
							border-bottom-right-radius: 0;
						}
						
						&.is-middle {
							border-radius: 0;
							border-left: none;
							border-right: none;
							background-color: #52b5f9;
							
							&:after {
								content: '';
								position: absolute;
								left: 0;
								right: 0;
								top: -1px;
								bottom: -1px;
								background-color: #52b5f9;
								z-index: -1;
							}
						}
						
						&.is-end {
							border-top-left-radius: 0;
							border-bottom-left-radius: 0;
						}
					}
				}
			}
		}
	}
	.make-appointment-box {
		margin-top: 20rpx;
		margin-bottom: 140rpx;
		.make-appointment-top {
			position: relative;
			background: linear-gradient(to bottom, #e3f3ff, #fff);
			border-radius: 30rpx 30rpx 0 0;
		}

		.scroll {
			.scroll-bottom {
				width: 100%;
				height: 80rpx;
				background-color: #fff;
				margin-bottom: 20rpx;

				.scroll-bottom-btn {
					width: 80%;
					height: 100%;
					background-color: #52b5f9;
					border-radius: 100rpx;
					line-height: 80rpx;
					font-size: 30rpx;

					&:active {
						opacity: 0.9;
					}
				}
			}
		}
	}
}

.time-slot {
	.availability-text {
		font-size: 20rpx;
		color: #999;
		margin-top: 4rpx;
	}
	
	&.is-active {
		.availability-text {
			color: #fff;
		}
	}
}
</style>
