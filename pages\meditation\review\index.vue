<template>
  <view class="review-page">
    <!-- 冥想信息 -->
    <view class="meditation-info">
      <image :src="meditationInfo.coverImage || defaultCover" mode="aspectFill" class="meditation-cover"></image>
      <view class="meditation-detail">
        <view class="meditation-title">{{ meditationInfo.title }}</view>
        <view class="meditation-meta">
          <text>{{ meditationInfo.duration }}分钟</text>
          <text v-if="meditationInfo.isFree">免费</text>
          <text v-else>¥{{ meditationInfo.price }}</text>
        </view>
      </view>
    </view>

    <!-- 评价表单 -->
    <view class="review-form">
      <!-- 评分 -->
      <view class="form-section">
        <view class="section-title">冥想评分</view>
        <view class="rating-container">
          <uni-rate 
            v-model="reviewForm.rating" 
            :size="32" 
            :value="reviewForm.rating"
            @change="onRatingChange"
          ></uni-rate>
          <text class="rating-text">{{ getRatingText(reviewForm.rating) }}</text>
        </view>
      </view>

      <!-- 评价标签 -->
      <view class="form-section">
        <view class="section-title">冥想感受</view>
        <view class="tag-list">
          <view 
            v-for="tag in reviewTags" 
            :key="tag.id"
            :class="['tag-item', { active: selectedTags.includes(tag.id) }]"
            @click="toggleTag(tag.id)"
          >
            {{ tag.name }}
          </view>
        </view>
      </view>

      <!-- 评价内容 -->
      <view class="form-section">
        <view class="section-title">评价内容</view>
        <textarea 
          v-model="reviewForm.content"
          class="review-textarea"
          placeholder="分享你的冥想体验，帮助其他用户了解这个冥想..."
          :maxlength="500"
          show-confirm-bar
        ></textarea>
        <view class="char-count">{{ reviewForm.content.length }}/500</view>
      </view>

      <!-- 是否匿名 -->
      <view class="form-section">
        <view class="anonymous-option">
          <text>匿名评价</text>
          <switch 
            :checked="reviewForm.anonymous"
            @change="onAnonymousChange"
          ></switch>
        </view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="bottom-bar">
      <button 
        class="submit-btn" 
        :disabled="!canSubmit"
        @click="submitReview"
      >
        提交评价
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getMeditationDetail, submitMeditationReview } from '@/api/meditation'
import { useUserStore } from '@/stores/user'

// 响应式数据
const meditationInfo = ref({})
const meditationId = ref(null)
const reviewForm = ref({
  rating: 5,
  content: '',
  anonymous: false,
  tags: []
})
const selectedTags = ref([])

// 评价标签
const reviewTags = ref([
  { id: 1, name: '很放松' },
  { id: 2, name: '效果显著' },
  { id: 3, name: '声音舒缓' },
  { id: 4, name: '引导清晰' },
  { id: 5, name: '时长合适' },
  { id: 6, name: '容易入睡' },
  { id: 7, name: '减压明显' },
  { id: 8, name: '专注提升' }
])

// 默认封面图
const defaultCover = 'https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/meditation/default-cover.png'

const userStore = useUserStore()

// 计算属性
const canSubmit = computed(() => {
  return reviewForm.value.rating > 0 && reviewForm.value.content.trim().length >= 10
})

// 方法
const loadMeditationInfo = async () => {
  try {
    const res = await getMeditationDetail(meditationId.value)
    if (res.code === 200) {
      meditationInfo.value = res.data
    } else {
      uni.showToast({
        title: res.msg || '获取冥想信息失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取冥想信息失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

const onRatingChange = (value) => {
  reviewForm.value.rating = value
}

const getRatingText = (rating) => {
  const texts = ['', '很差', '较差', '一般', '满意', '非常满意']
  return texts[rating] || ''
}

const toggleTag = (tagId) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
  
  // 更新表单数据
  reviewForm.value.tags = selectedTags.value
}

const onAnonymousChange = (e) => {
  reviewForm.value.anonymous = e.detail.value
}

const submitReview = async () => {
  if (!userStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  if (!canSubmit.value) {
    uni.showToast({
      title: '请完善评价信息',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({ title: '提交中...' })
    
    const submitData = {
      meditationId: meditationId.value,
      rating: reviewForm.value.rating,
      content: reviewForm.value.content.trim(),
      anonymous: reviewForm.value.anonymous,
      tags: selectedTags.value.join(',')
    }
    
    const res = await submitMeditationReview(submitData)
    
    uni.hideLoading()
    
    if (res.code === 200) {
      uni.showToast({
        title: '评价提交成功',
        icon: 'success'
      })
      
      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.msg || '提交失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('提交评价失败:', error)
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    })
  }
}

// 生命周期
onLoad((options) => {
  meditationId.value = options.meditationId
  if (meditationId.value) {
    loadMeditationInfo()
  }
})
</script>

<style lang="scss" scoped>
.review-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.meditation-info {
  display: flex;
  padding: 32rpx;
  background-color: #fff;
  margin-bottom: 16rpx;
  
  .meditation-cover {
    width: 120rpx;
    height: 80rpx;
    border-radius: 8rpx;
    margin-right: 24rpx;
  }
  
  .meditation-detail {
    flex: 1;
    
    .meditation-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 12rpx;
    }
    
    .meditation-meta {
      display: flex;
      gap: 24rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
}

.review-form {
  .form-section {
    background-color: #fff;
    margin-bottom: 16rpx;
    padding: 32rpx;
    
    .section-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .rating-container {
      display: flex;
      align-items: center;
      gap: 24rpx;
      
      .rating-text {
        font-size: 28rpx;
        color: #ff6b35;
        font-weight: 600;
      }
    }
    
    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      
      .tag-item {
        padding: 16rpx 32rpx;
        background-color: #f8f8f8;
        color: #666;
        border-radius: 40rpx;
        font-size: 26rpx;
        transition: all 0.3s;
        
        &.active {
          background-color: #ff6b35;
          color: #fff;
        }
      }
    }
    
    .review-textarea {
      width: 100%;
      min-height: 200rpx;
      padding: 24rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      font-size: 28rpx;
      line-height: 1.6;
      border: none;
      resize: none;
    }
    
    .char-count {
      text-align: right;
      font-size: 24rpx;
      color: #999;
      margin-top: 12rpx;
    }
    
    .anonymous-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
  
  .submit-btn {
    width: 100%;
    padding: 24rpx;
    background-color: #ff6b35;
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 600;
    
    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }
}
</style>
