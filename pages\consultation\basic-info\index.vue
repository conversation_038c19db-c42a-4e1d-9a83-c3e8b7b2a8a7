<template>
  <view class="basic-info-page">
    <view class="header">
      <text class="title">填写基本信息</text>
      <text class="subtitle">为了更好地为您提供咨询服务，请填写以下信息</text>
    </view>
    
    <view class="form-container">
      <uni-forms ref="formRef" :model="formData" :rules="rules" label-width="160rpx">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>
          
          <uni-forms-item label="姓名" name="name" required>
            <uni-easyinput v-model="formData.name" placeholder="请输入您的真实姓名" />
          </uni-forms-item>
          
          <uni-forms-item label="性别" name="gender" required>
            <uni-data-select 
              v-model="formData.gender" 
              :localdata="genderOptions"
              placeholder="请选择性别"
            />
          </uni-forms-item>
          
          <uni-forms-item label="年龄" name="age" required>
            <uni-number-box v-model="formData.age" :min="1" :max="120" />
          </uni-forms-item>
          
          <uni-forms-item label="籍贯" name="hometown">
            <uni-easyinput v-model="formData.hometown" placeholder="请输入籍贯" />
          </uni-forms-item>
          
          <uni-forms-item label="民族" name="ethnicity">
            <uni-easyinput v-model="formData.ethnicity" placeholder="请输入民族" />
          </uni-forms-item>
          
          <uni-forms-item label="宗教信仰" name="religion">
            <uni-easyinput v-model="formData.religion" placeholder="请输入宗教信仰（无则填无）" />
          </uni-forms-item>
          
          <uni-forms-item label="学历" name="education">
            <uni-data-select 
              v-model="formData.education" 
              :localdata="educationOptions"
              placeholder="请选择学历"
            />
          </uni-forms-item>
          
          <uni-forms-item label="从事职业" name="occupation">
            <uni-easyinput v-model="formData.occupation" placeholder="请输入职业" />
          </uni-forms-item>
          
          <uni-forms-item label="婚姻状况" name="maritalStatus">
            <uni-data-select 
              v-model="formData.maritalStatus" 
              :localdata="maritalOptions"
              placeholder="请选择婚姻状况"
            />
          </uni-forms-item>
          
          <uni-forms-item label="是否生育" name="hasChildren">
            <uni-data-select 
              v-model="formData.hasChildren" 
              :localdata="yesNoOptions"
              placeholder="请选择是否生育"
            />
          </uni-forms-item>
        </view>
        
        <!-- 紧急联系人 -->
        <view class="form-section">
          <view class="section-title">紧急联系人</view>
          
          <uni-forms-item label="联系人姓名" name="emergencyName" required>
            <uni-easyinput v-model="formData.emergencyName" placeholder="请输入紧急联系人姓名" />
          </uni-forms-item>
          
          <uni-forms-item label="联系方式" name="emergencyPhone" required>
            <uni-easyinput v-model="formData.emergencyPhone" placeholder="请输入紧急联系人电话" />
          </uni-forms-item>
        </view>
        
        <!-- 健康状况 -->
        <view class="form-section">
          <view class="section-title">健康状况</view>
          
          <uni-forms-item label="既往病史及家族病史" name="medicalHistory">
            <uni-easyinput 
              v-model="formData.medicalHistory" 
              type="textarea" 
              :maxlength="500"
              placeholder="请描述既往病史及家族病史，目前健康状况、是否在服药中、及所服用药物信息"
            />
          </uni-forms-item>
        </view>
        
        <!-- 咨询相关 -->
        <view class="form-section">
          <view class="section-title">咨询相关</view>
          
          <uni-forms-item label="以往咨询经历" name="consultationHistory">
            <uni-easyinput 
              v-model="formData.consultationHistory" 
              type="textarea" 
              :maxlength="500"
              placeholder="是否进行过心理咨询？如有请介绍咨询的效果"
            />
          </uni-forms-item>
          
          <uni-forms-item label="咨询的主要原因" name="consultationReason" required>
            <uni-easyinput 
              v-model="formData.consultationReason" 
              type="textarea" 
              :maxlength="500"
              placeholder="请描述您寻求心理咨询的主要原因"
            />
          </uni-forms-item>
          
          <uni-forms-item label="性格特点与兴趣爱好" name="personalityAndHobbies">
            <uni-easyinput 
              v-model="formData.personalityAndHobbies" 
              type="textarea" 
              :maxlength="500"
              placeholder="请描述您的性格特点与兴趣爱好"
            />
          </uni-forms-item>
          
          <uni-forms-item label="目前的情况" name="currentSituation">
            <uni-easyinput 
              v-model="formData.currentSituation" 
              type="textarea" 
              :maxlength="500"
              placeholder="请描述您目前的情况（工作、家庭、人际关系）"
            />
          </uni-forms-item>
          
          <uni-forms-item label="早年成长环境" name="childhoodEnvironment">
            <uni-easyinput 
              v-model="formData.childhoodEnvironment" 
              type="textarea" 
              :maxlength="500"
              placeholder="请描述您的早年成长环境，以及您认为早期环境中的哪些方面影响了现在的您"
            />
          </uni-forms-item>
          
          <uni-forms-item label="重大生活事件" name="majorLifeEvents">
            <uni-easyinput 
              v-model="formData.majorLifeEvents" 
              type="textarea" 
              :maxlength="500"
              placeholder="近半年内是否发生过重大生活事件，如有请描述"
            />
          </uni-forms-item>
          
          <uni-forms-item label="期待与目标" name="expectations">
            <uni-easyinput 
              v-model="formData.expectations" 
              type="textarea" 
              :maxlength="500"
              placeholder="您期待从咨询中得到什么样的启发与领悟"
            />
          </uni-forms-item>
        </view>
      </uni-forms>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="submit-btn" @click="submitForm" :loading="submitting">
        {{ submitting ? '提交中...' : '提交信息' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 响应式数据
const formRef = ref(null)
const submitting = ref(false)
const orderId = ref(null)

// 表单数据
const formData = reactive({
  name: '',
  gender: '',
  age: null,
  hometown: '',
  ethnicity: '',
  religion: '',
  education: '',
  occupation: '',
  maritalStatus: '',
  hasChildren: '',
  emergencyName: '',
  emergencyPhone: '',
  medicalHistory: '',
  consultationHistory: '',
  consultationReason: '',
  personalityAndHobbies: '',
  currentSituation: '',
  childhoodEnvironment: '',
  majorLifeEvents: '',
  expectations: ''
})

// 选项数据
const genderOptions = [
  { value: 'male', text: '男' },
  { value: 'female', text: '女' }
]

const educationOptions = [
  { value: 'primary', text: '小学' },
  { value: 'junior', text: '初中' },
  { value: 'senior', text: '高中' },
  { value: 'college', text: '大专' },
  { value: 'bachelor', text: '本科' },
  { value: 'master', text: '硕士' },
  { value: 'doctor', text: '博士' }
]

const maritalOptions = [
  { value: 'single', text: '未婚' },
  { value: 'married', text: '已婚' },
  { value: 'divorced', text: '离异' },
  { value: 'widowed', text: '丧偶' }
]

const yesNoOptions = [
  { value: 'yes', text: '是' },
  { value: 'no', text: '否' }
]

// 表单验证规则
const rules = {
  name: {
    rules: [
      { required: true, errorMessage: '请输入姓名' },
      { minLength: 2, maxLength: 10, errorMessage: '姓名长度应为2-10个字符' }
    ]
  },
  gender: {
    rules: [{ required: true, errorMessage: '请选择性别' }]
  },
  age: {
    rules: [
      { required: true, errorMessage: '请输入年龄' },
      { validateFunction: (rule, value, data, callback) => {
        if (value < 1 || value > 120) {
          callback('年龄应在1-120之间')
        }
        return true
      }}
    ]
  },
  emergencyName: {
    rules: [
      { required: true, errorMessage: '请输入紧急联系人姓名' },
      { minLength: 2, maxLength: 10, errorMessage: '姓名长度应为2-10个字符' }
    ]
  },
  emergencyPhone: {
    rules: [
      { required: true, errorMessage: '请输入紧急联系人电话' },
      { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号码' }
    ]
  },
  consultationReason: {
    rules: [
      { required: true, errorMessage: '请填写咨询的主要原因' },
      { minLength: 10, errorMessage: '请详细描述咨询原因，至少10个字符' }
    ]
  }
}

// 提交表单
const submitForm = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    submitting.value = true
    
    // 提交数据到后端
    const res = await uni.request({
      url: `${process.env.VUE_APP_BASE_API}/miniapp/consultation/basicInfo`,
      method: 'POST',
      data: {
        orderId: orderId.value,
        ...formData
      }
    })
    
    if (res.data.code === 200) {
      uni.showToast({
        title: '信息提交成功',
        icon: 'success'
      })
      
      // 延迟跳转到我的咨询页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/consultation/my-consultations/index'
        })
      }, 1500)
    } else {
      uni.showToast({
        title: res.data.msg || '提交失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('表单验证失败或提交失败:', error)
    if (error.errorFields) {
      // 表单验证失败
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none'
      })
    } else {
      // 网络请求失败
      uni.showToast({
        title: '网络请求失败',
        icon: 'none'
      })
    }
  } finally {
    submitting.value = false
  }
}

// 生命周期
onLoad((options) => {
  orderId.value = options.orderId
  if (!orderId.value) {
    uni.showToast({
      title: '订单信息错误',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<style lang="scss" scoped>
.basic-info-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
  }
}

.form-container {
  padding: 32rpx;
}

.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 32rpx;
    padding-bottom: 16rpx;
    border-bottom: 2rpx solid #f0f0f0;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  
  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    border: none;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
    
    &:active {
      transform: scale(0.98);
    }
  }
}
</style>
