<template>
  <view class="wallet-container">
    <!-- 钱包余额卡片 -->
    <view class="balance-card">
      <view class="balance-header">
        <text class="title">账户余额</text>
        <view class="eye-btn" @click="toggleBalanceVisible">
          <image :src="balanceVisible ? '/static/icon/eye-open.png' : '/static/icon/eye-close.png'" mode="aspectFit"></image>
        </view>
      </view>
      <view class="balance-amount">
        <text class="currency">¥</text>
        <text class="amount">{{ balanceVisible ? (walletInfo.balance || 0) : '****' }}</text>
      </view>
      <view class="balance-actions">
        <button class="action-btn withdraw-btn" @click="showWithdrawModal = true">
          <image src="/static/icon/withdraw.png" mode="aspectFit"></image>
          <text>提现</text>
        </button>
        <button class="action-btn detail-btn" @click="goToDetail">
          <image src="/static/icon/detail.png" mode="aspectFit"></image>
          <text>明细</text>
        </button>
      </view>
    </view>

    <!-- 收入统计 -->
    <view class="income-stats">
      <view class="stats-header">
        <text class="title">收入统计</text>
        <view class="period-selector">
          <text 
            class="period-item" 
            v-for="(period, index) in periodOptions" 
            :key="index"
            :class="{ active: currentPeriod === index }"
            @click="switchPeriod(index)"
          >
            {{ period.label }}
          </text>
        </view>
      </view>
      <view class="stats-content">
        <view class="stat-item">
          <text class="stat-label">收入金额</text>
          <text class="stat-value income">¥{{ incomeStats.totalIncome || 0 }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">待结算金额</text>
          <text class="stat-value pending">¥{{ incomeStats.pendingIncome || 0 }}</text>
        </view>
      </view>
    </view>

    <!-- 流水明细 -->
    <view class="transaction-section">
      <view class="section-header">
        <text class="title">流水明细</text>
        <text class="more" @click="goToDetail">查看全部</text>
      </view>
      <view class="transaction-list">
        <view 
          class="transaction-item" 
          v-for="item in transactionList" 
          :key="item.id"
        >
          <view class="transaction-icon">
            <image :src="getTransactionIcon(item.type)" mode="aspectFit"></image>
          </view>
          <view class="transaction-info">
            <text class="transaction-title">{{ item.title }}</text>
            <text class="transaction-time">{{ formatDateTime(item.createTime) }}</text>
          </view>
          <view class="transaction-amount" :class="getAmountClass(item.type)">
            <text>{{ getAmountPrefix(item.type) }}¥{{ item.amount }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="transactionList.length === 0">
        <image src="/static/icon/empty-transaction.png" mode="aspectFit"></image>
        <text>暂无流水记录</text>
      </view>
    </view>

    <!-- 提现弹窗 -->
    <uni-popup ref="withdrawPopup" type="center" v-if="showWithdrawModal">
      <view class="withdraw-modal">
        <view class="modal-header">
          <text class="modal-title">申请提现</text>
          <text class="modal-close" @click="closeWithdrawModal">×</text>
        </view>
        <view class="modal-content">
          <view class="available-balance">
            <text class="label">可提现余额</text>
            <text class="balance">¥{{ walletInfo.withdrawableBalance || 0 }}</text>
          </view>
          <view class="withdraw-form">
            <view class="form-item">
              <text class="form-label">提现金额</text>
              <input 
                class="form-input" 
                type="digit" 
                placeholder="请输入提现金额"
                v-model="withdrawForm.amount"
                @input="onAmountInput"
              />
            </view>
            <view class="form-item">
              <text class="form-label">提现方式</text>
              <picker :range="withdrawMethods" range-key="name" @change="onMethodChange">
                <view class="picker-input">
                  {{ withdrawMethods[withdrawForm.methodIndex].name }}
                  <image src="/static/icon/arrow-down.png" mode="aspectFit"></image>
                </view>
              </picker>
            </view>
          </view>
          <view class="withdraw-tips">
            <text>• 提现将在1-3个工作日内到账</text>
            <text>• 单次提现金额不得超过可提现余额</text>
            <text>• 提现手续费按平台规则收取</text>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn-cancel" @click="closeWithdrawModal">取消</button>
          <button class="btn-confirm" @click="submitWithdraw" :disabled="!canWithdraw">确认提现</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { 
  getConsultantWallet, 
  getConsultantIncomeHistory,
  requestWithdraw 
} from '@/api/consultant-app.js'

// 响应式数据
const balanceVisible = ref(true)
const showWithdrawModal = ref(false)
const currentPeriod = ref(0)
const walletInfo = ref({})
const incomeStats = ref({})
const transactionList = ref([])

// 时间周期选项
const periodOptions = ref([
  { label: '本月', value: 'month' },
  { label: '本周', value: 'week' },
  { label: '今日', value: 'day' }
])

// 提现表单
const withdrawForm = ref({
  amount: '',
  methodIndex: 0
})

// 提现方式
const withdrawMethods = ref([
  { name: '微信钱包', value: 'wechat' },
  { name: '支付宝', value: 'alipay' },
  { name: '银行卡', value: 'bank' }
])

// 计算属性
const canWithdraw = computed(() => {
  const amount = parseFloat(withdrawForm.value.amount)
  return amount > 0 && amount <= (walletInfo.value.withdrawableBalance || 0)
})

// 生命周期
onMounted(() => {
  loadWalletData()
})

onShow(() => {
  loadWalletData()
})

// 加载钱包数据
const loadWalletData = async () => {
  try {
    // 并行加载钱包信息和流水记录
    const [walletRes, historyRes] = await Promise.all([
      getConsultantWallet(),
      getConsultantIncomeHistory({ 
        period: periodOptions.value[currentPeriod.value].value,
        pageSize: 10 
      })
    ])
    
    if (walletRes.code === 200) {
      walletInfo.value = walletRes.data
    }
    
    if (historyRes.code === 200) {
      incomeStats.value = historyRes.data.stats || {}
      transactionList.value = historyRes.data.list || []
    }
  } catch (error) {
    console.error('加载钱包数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 切换余额显示
const toggleBalanceVisible = () => {
  balanceVisible.value = !balanceVisible.value
}

// 切换时间周期
const switchPeriod = (index) => {
  currentPeriod.value = index
  loadIncomeStats()
}

// 加载收入统计
const loadIncomeStats = async () => {
  try {
    const res = await getConsultantIncomeHistory({ 
      period: periodOptions.value[currentPeriod.value].value 
    })
    if (res.code === 200) {
      incomeStats.value = res.data.stats || {}
    }
  } catch (error) {
    console.error('加载收入统计失败:', error)
  }
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hour}:${minute}`
}

// 获取交易图标
const getTransactionIcon = (type) => {
  const iconMap = {
    'income': '/static/icon/income.png',
    'withdraw': '/static/icon/withdraw.png',
    'refund': '/static/icon/refund.png'
  }
  return iconMap[type] || '/static/icon/transaction.png'
}

// 获取金额样式类
const getAmountClass = (type) => {
  return type === 'income' ? 'amount-income' : 'amount-expense'
}

// 获取金额前缀
const getAmountPrefix = (type) => {
  return type === 'income' ? '+' : '-'
}

// 金额输入处理
const onAmountInput = (e) => {
  let value = e.detail.value
  // 限制小数点后两位
  if (value.includes('.')) {
    const parts = value.split('.')
    if (parts[1].length > 2) {
      value = parts[0] + '.' + parts[1].substring(0, 2)
    }
  }
  withdrawForm.value.amount = value
}

// 提现方式选择
const onMethodChange = (e) => {
  withdrawForm.value.methodIndex = e.detail.value
}

// 关闭提现弹窗
const closeWithdrawModal = () => {
  showWithdrawModal.value = false
  withdrawForm.value = {
    amount: '',
    methodIndex: 0
  }
}

// 提交提现申请
const submitWithdraw = async () => {
  if (!canWithdraw.value) {
    uni.showToast({
      title: '请输入正确的提现金额',
      icon: 'none'
    })
    return
  }
  
  try {
    const data = {
      amount: parseFloat(withdrawForm.value.amount),
      method: withdrawMethods.value[withdrawForm.value.methodIndex].value
    }
    
    const res = await requestWithdraw(data)
    if (res.code === 200) {
      uni.showToast({
        title: '提现申请已提交',
        icon: 'success'
      })
      closeWithdrawModal()
      loadWalletData()
    }
  } catch (error) {
    console.error('提现申请失败:', error)
    uni.showToast({
      title: '提现申请失败',
      icon: 'none'
    })
  }
}

// 跳转到详情页面
const goToDetail = () => {
  uni.navigateTo({
    url: '/pages/consultation-my/wallet-detail/index'
  })
}
</script>

<style scoped lang="scss">
.wallet-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  
  .balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .title {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
    
    .eye-btn {
      width: 50rpx;
      height: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      image {
        width: 30rpx;
        height: 30rpx;
        filter: brightness(0) invert(1);
      }
    }
  }
  
  .balance-amount {
    display: flex;
    align-items: baseline;
    margin-bottom: 40rpx;
    
    .currency {
      font-size: 36rpx;
      color: #fff;
      margin-right: 8rpx;
    }
    
    .amount {
      font-size: 72rpx;
      font-weight: bold;
      color: #fff;
    }
  }
  
  .balance-actions {
    display: flex;
    gap: 30rpx;
    
    .action-btn {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx 0;
      background-color: rgba(255, 255, 255, 0.1);
      border: none;
      border-radius: 16rpx;
      color: #fff;
      
      image {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 8rpx;
        filter: brightness(0) invert(1);
      }
      
      text {
        font-size: 26rpx;
      }
      
      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.income-stats {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .period-selector {
      display: flex;
      background-color: #f5f5f5;
      border-radius: 20rpx;
      padding: 4rpx;
      
      .period-item {
        padding: 12rpx 24rpx;
        font-size: 24rpx;
        color: #666;
        border-radius: 16rpx;
        
        &.active {
          background-color: #1890ff;
          color: #fff;
        }
      }
    }
  }
  
  .stats-content {
    display: flex;
    gap: 40rpx;
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-label {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 12rpx;
      }
      
      .stat-value {
        display: block;
        font-size: 40rpx;
        font-weight: bold;
        
        &.income {
          color: #52c41a;
        }
        
        &.pending {
          color: #fa8c16;
        }
      }
    }
  }
}

.transaction-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .more {
      font-size: 26rpx;
      color: #1890ff;
    }
  }
  
  .transaction-list {
    .transaction-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .transaction-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        border-radius: 50%;
        margin-right: 20rpx;
        
        image {
          width: 40rpx;
          height: 40rpx;
        }
      }
      
      .transaction-info {
        flex: 1;
        
        .transaction-title {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .transaction-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .transaction-amount {
        font-size: 30rpx;
        font-weight: bold;
        
        &.amount-income {
          color: #52c41a;
        }
        
        &.amount-expense {
          color: #ff4d4f;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  
  image {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 20rpx;
  }
  
  text {
    color: #999;
    font-size: 26rpx;
  }
}

.withdraw-modal {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .modal-close {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .modal-content {
    padding: 30rpx;
    
    .available-balance {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      background-color: #f5f5f5;
      border-radius: 12rpx;
      margin-bottom: 30rpx;
      
      .label {
        font-size: 26rpx;
        color: #666;
      }
      
      .balance {
        font-size: 30rpx;
        font-weight: bold;
        color: #1890ff;
      }
    }
    
    .withdraw-form {
      .form-item {
        margin-bottom: 30rpx;
        
        .form-label {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 12rpx;
        }
        
        .form-input,
        .picker-input {
          width: 100%;
          padding: 20rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          font-size: 28rpx;
          box-sizing: border-box;
        }
        
        .picker-input {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          image {
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
    }
    
    .withdraw-tips {
      font-size: 24rpx;
      color: #999;
      line-height: 1.6;
      
      text {
        display: block;
        margin-bottom: 8rpx;
      }
    }
  }
  
  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    
    .btn-cancel,
    .btn-confirm {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }
    
    .btn-cancel {
      background-color: #f5f5f5;
      color: #666;
    }
    
    .btn-confirm {
      background-color: #1890ff;
      color: #fff;
      
      &:disabled {
        background-color: #d9d9d9;
        color: #999;
      }
    }
  }
}
</style>
