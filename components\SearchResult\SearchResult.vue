<template>
	<view class="search-result-container">
		<!-- 分类搜索结果 -->
		<view v-if="showCategories && categories.length > 0" class="categories-result">
			<view 
				v-for="category in categories" 
				:key="category.type" 
				class="category-section"
				v-show="category.items && category.items.length > 0"
			>
				<view class="category-header">
					<text class="category-title">{{ category.typeName }}</text>
					<text class="category-count">({{ category.count }})</text>
				</view>
				<view class="category-items">
					<view
						v-for="(item, index) in category.items.slice(0, showAll ? category.items.length : 3)"
						:key="index"
						class="result-item"
						@click="$emit('itemClick', item)"
					>
						<image 
							class="item-image" 
							:src="item.coverImage || item.imageUrl || item.avatar" 
							mode="aspectFill" 
						/>
						<view class="item-info">
							<view class="item-title" v-html="item.highlightTitle || item.title || item.name"></view>
							<view class="item-desc" v-html="item.highlightDescription || item.description || item.specialty"></view>
							<view class="item-meta">
								<text class="item-type">{{ category.typeName }}</text>
								<text class="item-price" v-if="item.price">¥{{ item.price }}</text>
								<text class="item-rating" v-if="item.rating">{{ item.rating }}分</text>
								<text class="item-views" v-if="item.viewCount">{{ formatViewCount(item.viewCount) }}次浏览</text>
							</view>
						</view>
					</view>
				</view>
				<view 
					v-if="category.items.length > 3 && !showAll" 
					class="show-more"
					@click="$emit('showMore', category.type)"
				>
					<text>查看更多{{ category.typeName }}({{ category.items.length - 3 }}+)</text>
					<uni-icons type="arrowright" size="14" color="#666" />
				</view>
			</view>
		</view>

		<!-- 普通搜索结果列表 -->
		<view v-else class="result-list">
			<view
				v-for="(item, index) in items"
				:key="index"
				class="result-item"
				@click="$emit('itemClick', item)"
			>
				<image 
					class="item-image" 
					:src="item.coverImage || item.imageUrl || item.avatar" 
					mode="aspectFill" 
				/>
				<view class="item-info">
					<view class="item-title" v-html="item.highlightTitle || item.title || item.name"></view>
					<view class="item-desc" v-html="item.highlightDescription || item.description || item.specialty"></view>
					<view class="item-meta">
						<text class="item-type">{{ getItemTypeText(item.type) }}</text>
						<text class="item-price" v-if="item.price">¥{{ item.price }}</text>
						<text class="item-rating" v-if="item.rating">{{ item.rating }}分</text>
						<text class="item-views" v-if="item.viewCount">{{ formatViewCount(item.viewCount) }}次浏览</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 无结果状态 -->
		<view v-if="(!categories || categories.length === 0) && (!items || items.length === 0)" class="no-result">
			<image src="/static/images/no-search-result.png" mode="aspectFit" />
			<text class="no-result-text">未找到相关内容</text>
			<text class="no-result-tip">试试其他关键词吧</text>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
	// 分类搜索结果
	categories: {
		type: Array,
		default: () => []
	},
	// 普通搜索结果
	items: {
		type: Array,
		default: () => []
	},
	// 是否显示分类结果
	showCategories: {
		type: Boolean,
		default: false
	},
	// 是否显示全部结果
	showAll: {
		type: Boolean,
		default: false
	}
})

// Emits
const emit = defineEmits(['itemClick', 'showMore'])

// 获取项目类型文本
const getItemTypeText = (type) => {
	const typeMap = {
		consultant: '咨询师',
		course: '课程',
		meditation: '冥想',
		assessment: '测评'
	}
	return typeMap[type] || '未知'
}

// 格式化浏览次数
const formatViewCount = (count) => {
	if (count >= 10000) {
		return (count / 10000).toFixed(1) + '万'
	} else if (count >= 1000) {
		return (count / 1000).toFixed(1) + 'k'
	}
	return count
}
</script>

<style lang="scss" scoped>
.search-result-container {
	width: 100%;
}

.categories-result {
	.category-section {
		margin-bottom: 40rpx;
		
		.category-header {
			display: flex;
			align-items: center;
			padding: 0 32rpx 20rpx;
			border-bottom: 1rpx solid #f0f0f0;
			margin-bottom: 20rpx;
			
			.category-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}
			
			.category-count {
				font-size: 24rpx;
				color: #999;
				margin-left: 8rpx;
			}
		}
		
		.show-more {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			color: #666;
			font-size: 28rpx;
			border-top: 1rpx solid #f0f0f0;
			margin-top: 20rpx;
			
			text {
				margin-right: 8rpx;
			}
		}
	}
}

.result-list,
.category-items {
	.result-item {
		display: flex;
		padding: 24rpx 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.item-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-right: 24rpx;
			flex-shrink: 0;
		}
		
		.item-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.item-title {
				font-size: 30rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.4;
				margin-bottom: 8rpx;
				
				// 高亮样式
				:deep(em) {
					color: #20a3f3;
					font-style: normal;
					background-color: rgba(32, 163, 243, 0.1);
					padding: 0 4rpx;
					border-radius: 4rpx;
				}
			}
			
			.item-desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.4;
				margin-bottom: 12rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				
				// 高亮样式
				:deep(em) {
					color: #20a3f3;
					font-style: normal;
					background-color: rgba(32, 163, 243, 0.1);
					padding: 0 4rpx;
					border-radius: 4rpx;
				}
			}
			
			.item-meta {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				gap: 16rpx;
				
				text {
					font-size: 24rpx;
					color: #999;
				}
				
				.item-type {
					background-color: #f0f0f0;
					padding: 4rpx 12rpx;
					border-radius: 8rpx;
					color: #666;
				}
				
				.item-price {
					color: #ff6b35;
					font-weight: 600;
				}
				
				.item-rating {
					color: #ffa500;
				}
			}
		}
	}
}

.no-result {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	
	image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 40rpx;
		opacity: 0.6;
	}
	
	.no-result-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 16rpx;
	}
	
	.no-result-tip {
		font-size: 28rpx;
		color: #999;
	}
}
</style>
