# 课程统计信息和收费标签实现总结

## 修改概述

为 UniversalListItem 组件添加了以下功能：
1. 课程统计信息显示总章节和学习人数
2. 测评、冥想和课程在标签列表最前面显示收费/免费标签

## 主要修改内容

### 1. 课程统计信息优化

**文件**: `components/UniversalListItem/UniversalListItem.vue`

#### 修改前
```javascript
} else if (props.type === 'course') {
    // 课程统计信息
    if (props.item.lessonCount) {
        stats.push({ value: `${props.item.lessonCount}章`, label: '总章节' })
    }
    if (props.item.studentCount) {
        stats.push({ value: `${props.item.studentCount}+`, label: '学习人数' })
    }
}
```

#### 修改后
```javascript
} else if (props.type === 'course') {
    // 课程统计信息 - 显示总章节和学习人数
    if (props.item.lessonCount || props.item.chapterCount) {
        const count = props.item.lessonCount || props.item.chapterCount || 0
        stats.push({ value: `${count}章`, label: '总章节' })
    }
    if (props.item.studentCount || props.item.enrollCount) {
        const count = props.item.studentCount || props.item.enrollCount || 0
        stats.push({ value: `${count}+`, label: '学习人数' })
    }
}
```

### 2. 收费/免费标签实现

#### 课程标签修改
```javascript
} else if (props.type === 'course') {
    // 课程的标签 - 最前面添加收费/免费标签
    tags = []
    
    // 添加收费/免费标签
    if (props.item.isFree || (props.item.price && parseFloat(props.item.price) === 0)) {
        tags.push({ text: '免费', type: 'free' })
    } else {
        tags.push({ text: '收费', type: 'paid' })
    }
    
    // 添加其他标签
    const otherTags = props.item.tags || props.item.categories || []
    tags = tags.concat(otherTags)
}
```

#### 冥想标签修改
```javascript
} else if (props.type === 'meditation') {
    // 冥想的标签 - 最前面添加收费/免费标签
    tags = []
    
    // 添加收费/免费标签
    if (props.item.isFree || (props.item.price && parseFloat(props.item.price) === 0)) {
        tags.push({ text: '免费', type: 'free' })
    } else {
        tags.push({ text: '收费', type: 'paid' })
    }
    
    // 添加其他标签
    const otherTags = props.item.tags || props.item.categories || []
    tags = tags.concat(otherTags)
}
```

#### 测评标签修改
```javascript
} else if (props.type === 'assessment') {
    // 测评显示收费/免费标签、题目数和预估时间
    tags = []
    
    // 添加收费/免费标签
    if (props.item.isFree || (props.item.price && parseFloat(props.item.price) === 0)) {
        tags.push({ text: '免费', type: 'free' })
    } else {
        tags.push({ text: '收费', type: 'paid' })
    }
    
    if (props.item.questionCount) {
        tags.push({ text: `共${props.item.questionCount}题`, type: 'question' })
    }
    if (props.item.duration) {
        tags.push({ text: `预计${props.item.duration}分钟完成`, type: 'duration' })
    }
    return tags // 直接返回，不需要后续处理
}
```

### 3. 模板样式类添加

#### 修改前
```vue
<text v-for="(tag, index) in getTagList()" :key="index" :class="[
    'professional-tag',
    type === 'assessment' && tag.type === 'question' ? 'assessment-question-tag' : '',
    type === 'assessment' && tag.type === 'duration' ? 'assessment-duration-tag' : ''
]">
    {{ typeof tag === 'object' ? tag.text : tag }}
</text>
```

#### 修改后
```vue
<text v-for="(tag, index) in getTagList()" :key="index" :class="[
    'professional-tag',
    tag.type === 'free' ? 'free-tag' : '',
    tag.type === 'paid' ? 'paid-tag' : '',
    type === 'assessment' && tag.type === 'question' ? 'assessment-question-tag' : '',
    type === 'assessment' && tag.type === 'duration' ? 'assessment-duration-tag' : ''
]">
    {{ typeof tag === 'object' ? tag.text : tag }}
</text>
```

### 4. 样式定义

#### 免费标签样式
```scss
.free-tag {
    background: #F0F2F7;
    font-size: 20rpx;
    color: #455DA0;
}
```

#### 收费标签样式
```scss
.paid-tag {
    background: #F6F1F4;
    font-size: 20rpx;
    color: #A04571;
}
```

## 数据字段支持

### 课程统计信息字段
- `lessonCount` 或 `chapterCount`: 总章节数
- `studentCount` 或 `enrollCount`: 学习人数

### 收费判断字段
- `isFree`: 布尔值，是否免费
- `price`: 价格，为0或null时视为免费

### 标签数据字段
- `tags`: 标签数组
- `categories`: 分类数组（作为标签的备选）

## 显示效果

### 课程统计信息
- 显示格式：`12章` `1000+`
- 标签：`总章节` `学习人数`

### 收费/免费标签
- **免费标签**:
  - 背景色：#F0F2F7
  - 文字色：#455DA0
  - 字号：20rpx
  
- **收费标签**:
  - 背景色：#F6F1F4
  - 文字色：#A04571
  - 字号：20rpx

### 标签顺序
1. 收费/免费标签（最前面）
2. 其他业务标签
3. 测评特有标签（题目数、预估时间）

## 兼容性

1. **向后兼容**: 支持原有的数据字段格式
2. **字段容错**: 支持多种字段名称（如 `lessonCount` 和 `chapterCount`）
3. **数据类型**: 自动处理字符串和数字类型的价格数据
4. **标签格式**: 支持字符串数组和对象数组格式的标签

## 注意事项

1. 收费/免费判断优先使用 `isFree` 字段，其次判断 `price` 是否为0
2. 课程统计信息优先使用 `lessonCount`，备选 `chapterCount`
3. 学习人数优先使用 `studentCount`，备选 `enrollCount`
4. 标签列表最多显示前3个，收费/免费标签不计入此限制
5. 测评类型的标签有特殊处理，直接返回不经过通用处理逻辑

## 测试建议

1. 测试不同价格的课程、冥想、测评的标签显示
2. 验证免费和收费标签的样式是否正确
3. 检查课程统计信息是否正确显示总章节和学习人数
4. 测试标签列表的滚动和显示效果
5. 验证数据字段的容错处理
