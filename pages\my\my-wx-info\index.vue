<template>
	<view class="content">
		<view class="user-info">
			<view class="avatar">
				<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar"><image :src="avatar" mode=""></image></button>
			</view>
			<uni-forms ref="valiForm" :rules="rules" :modelValue="userInfoForm" label-width="80px">
				<uni-forms-item label="昵称" required name="nickname">
					<uni-easyinput type="nickname" v-model="userInfoForm.nickname" placeholder="请输入昵称" />
				</uni-forms-item>
				<uni-forms-item label="关联手机" required name="associatedPhone">
					<uni-easyinput v-model="userInfoForm.associatedPhone" placeholder="请输入关联手机" />
				</uni-forms-item>
			</uni-forms>
			<view class="scroll-bottom">
				<button class="scroll-bottom-btn" type="primary" @click="handleSubmit">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
import { setToken, getToken, removeToken } from "@/utils/auth.js";
export default {
	data() {
		return {
			userInfoForm: {
				nickname: "欢迎来到熙桓心理",
				associatedPhone: "",
			},
			avatar: "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/tab-icon/my-active.png",
			rules: [],
		};
	},
	methods: {
		onChooseAvatar(e) {
			// const { avatarUrl } = e.detail;
			// 获取到用户头像添加到数据库
			uni.getFileSystemManager().saveFile({
				tempFilePath: e.detail.avatarUrl,
				success: (res) => {
					uni.uploadFile({
						//传到服务器
						url: "http://************:8080/system/user/profile/avatar",
						filePath: res.savedFilePath,
						name: "avatarfile",
						formData: {},
						header: {
							Authorization: "Bearer " + getToken(),
						},
						success: (res1) => {
							const data = JSON.parse(res1.data);
							console.log(data);
							if (data.code === 200) {
								// 保存路径到用户信息
								this.avatar = data.imgUrl;
							}
						},
					});
				},
			});
		},
		handleSubmit() {},
	},
};
</script>

<style lang="scss" scoped>
.content {
	width: 100%;
	margin: 20rpx;
	.user-info {
		width: calc(100% - 40rpx);
		.avatar {
			width: 100%;
			height: 150rpx;
			display: flex;
			justify-content: center;
			margin-bottom: 40rpx;
			image {
				width: 150rpx;
				height: 150rpx;
			}
			button {
				width: 150rpx;
				height: 150rpx;
				border-radius: 50%;
				padding: 0;
				opacity: 1;
				background-color: #fff !important;
				border: none;
				color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.scroll-bottom {
		width: 100%;
		height: 80rpx;
		background-color: #fff;
		margin-bottom: 20rpx;
		margin-top: 20rpx;

		.scroll-bottom-btn {
			width: 80%;
			height: 100%;
			background-color: #52b5f9;
			border-radius: 100rpx;
			line-height: 80rpx;
			font-size: 30rpx;
		}
	}
}
</style>
