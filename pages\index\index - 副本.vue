<template>
	<view class="content">
		<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval" :duration="duration">
			<swiper-item>
				<image class="logo-img" mode="aspectFill" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E6%8A%91%E9%83%81%E7%97%87.jpg" @error="error"></image>
			</swiper-item>
			<swiper-item>
				<image class="logo-img" mode="scaleToFill" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E8%BD%AE%E6%92%AD%E5%9B%BE1.png" @error="error"></image>
			</swiper-item>
			<swiper-item>
				<image class="logo-img" mode="heightFix" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E8%BD%AE%E6%92%AD%E5%9B%BE2.png" @error="error"></image>
			</swiper-item>
			<swiper-item>
				<image class="logo-img" mode="heightFix" src="https://xihuanxinli.oss-cn-beijing.aliyuncs.com/wx-material/system/img/%E8%BD%AE%E6%92%AD%E5%9B%BE3.png" @error="error"></image>
			</swiper-item>
		</swiper>
		<view class="title">
			<view class="logo">
				<image class="logo-img" mode="heightFix" src="../../static/logo.png" @error="error"></image>
			</view>
			<view class="search-input">
				<view class="search-input-p">
					<uni-icons class="sarch-icon" type="search" size="18"></uni-icons>
					搜索咨询师
				</view>
			</view>
		</view>
		<!-- <view class="gongge">
			<uni-grid :column="5" :highlight="true" @change="change">
				<uni-grid-item v-for="(item, index) in 5" :index="index" :key="index">
					<view class="grid-item-box" style="background-color: #fff">
						<uni-icons type="image" :size="30" color="#777" />
						<text class="text">文本信息</text>
					</view>
				</uni-grid-item>
			</uni-grid>
		</view> -->

		<view class="gongge">
			<view class="gongge-item" v-for="(item, index) in productList" :index="index" :key="index">
				<view class="grid-item-box" style="background-color: #fff">
					<!-- <uni-icons type="image" :size="30" color="#777" /> -->
					<image class="gongge-icon" mode="heightFix" :src="`../../static/icon/${item.name}.png`" @error="error"></image>
					<text class="text">{{ item.name }}</text>
				</view>
			</view>
		</view>

		<view class="change-consultant">
			<view class="left">
				<view class="image-box">
					<image class="gongge-icon" mode="heightFix" :src="`../../static/img/极速匹配.png`" @error="error"></image>
				</view>
				<view class="text-info">极速匹配</view>
			</view>
			<view class="right" @click="handleToAppointment">
				<view class="image-box">
					<image class="gongge-img" mode="heightFix" :src="`../../static/img/直接预约.png`" @error="error"></image>
				</view>
				<view class="text-info">直接预约</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from "vue";

// 响应式数据
const indicatorDots = ref(true);
const autoplay = ref(true);
const interval = ref(2000);
const duration = ref(500);
const productList = ref([
	{
		id: 6,
		name: "测评",
	},
	{
		id: 2,
		name: "OH卡牌",
	},
	{
		id: 3,
		name: "沙盘",
	},
	{
		id: 4,
		name: "冥想",
	},
	{
		id: 5,
		name: "线下沙龙",
	},
	{
		id: 1,
		name: "催眠",
	},
	{
		id: 7,
		name: "咨询师考证",
	},
	{
		id: 8,
		name: "企业EAP",
		icon: "",
	},
]);

// 方法
const change = () => {
	// 方法逻辑
};

const handleToAppointment = () => {
	uni.navigateTo({
		url: "/pages/appointment/appointment",
	});
};
</script>
<style lang="scss" scoped>
.content {
	.title {
		display: flex;
		height: 100rpx;
		align-items: center;

		.logo {
			margin-right: 40rpx;
			height: 80rpx;
			margin-left: 20rpx;

			.logo-img {
				height: 100%;
			}
		}

		.search-input {
			width: calc(100% - 180rpx);
			height: 50rpx;
			display: flex;
			margin-right: 20rpx;

			.search-input-p {
				width: 100%;
				height: 60rpx;
				border: 2rpx solid #ccc;
				border-radius: 60rpx;
				padding-left: 20rpx;
				font-size: 30rpx;
				color: #ccc;
				display: flex;
				align-items: center;

				.sarch-icon {
					height: 36rpx;
					// margin-right: 10rpx;
				}
			}
		}
	}

	.swiper {
		height: 360rpx;
		padding: 20rpx;

		swiper-item {
			border-radius: 20rpx;
		}

		image {
			width: 100%;
			height: 100%;
		}

		.swiper-item {
			display: block;
			height: 100%;
			line-height: 200rpx;
			text-align: center;
		}
	}

	.gongge {
		padding: 20rpx;
		display: flex;
		flex-wrap: wrap; /* 允许换行 */
		.gongge-item {
			flex: 0 0 25%; /* 每项固定 20% 宽度（100%/5=20%） */
			min-width: 0; /* 防止内容溢出 */
		}
		.grid-item-box {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center; /* 修正属性名应为 justify-content */
			// margin: 10rpx; /* 增加间距（可选） */
			margin-bottom: 20rpx;
			.text {
				font-size: 24rpx;
				margin-top: 4px;
			}
			.gongge-icon {
				width: 80rpx;
				height: 90rpx;
			}
		}
	}
	.change-consultant {
		padding: 20rpx;
		display: flex;
		.left,
		.right {
			width: 50%;
			height: 200rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.image-box {
			width: calc(100% - 160rpx);
			height: 120rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			image {
				width: 100%;
				height: 100%;
			}
			font-weight: 700;
		}
	}
}
</style>
