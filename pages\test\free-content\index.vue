<template>
  <view class="test-page">
    <view class="header">
      <text class="title">免费内容测试</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">测试说明</text>
        <view class="description">
          <text>本页面用于测试免费内容的判断逻辑：</text>
          <text>1. isFree === 1 的内容应显示为免费</text>
          <text>2. price == 0 的内容应显示为免费</text>
          <text>3. 免费内容不应显示支付按钮</text>
          <text>4. 免费内容应可以直接访问</text>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">测试场景</text>
        <view class="test-buttons">
          <button class="test-btn" @click="testFreeCourse">测试免费课程 (isFree=1)</button>
          <button class="test-btn" @click="testZeroPriceCourse">测试零价格课程 (price=0)</button>
          <button class="test-btn" @click="testFreeMeditation">测试免费冥想 (price=0)</button>
          <button class="test-btn" @click="testFreeAssessment">测试免费测评 (isFree=1)</button>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">修复内容</text>
        <view class="fix-list">
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>通用购物车组件支持 isFree===1 和 price==0 判断</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>课程详情页面章节权限判断优化</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>冥想详情页面免费显示逻辑修复</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>视频播放器控制栏美化，倍速按钮移至右下角</text>
          </view>
          <view class="fix-item">
            <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
            <text>所有页面统一免费判断标准</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">判断逻辑</text>
        <view class="logic-display">
          <view class="logic-item">
            <text class="logic-title">免费判断条件：</text>
            <text class="logic-code">isFree === 1 || price == 0</text>
          </view>
          <view class="logic-item">
            <text class="logic-title">需要购买条件：</text>
            <text class="logic-code">!purchased && isFree !== 1 && price > 0</text>
          </view>
          <view class="logic-item">
            <text class="logic-title">按钮文案：</text>
            <text class="logic-code">免费: "免费学习/冥想/测评"</text>
            <text class="logic-code">付费: "立即购买 ¥{price}"</text>
            <text class="logic-code">已购买: "开始学习/冥想" 或 "查看报告"</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 模拟购物车组件 -->
    <view class="mock-goods-nav">
      <text>模拟购物车组件 - 测试层级关系</text>
    </view>
  </view>
</template>

<script setup>
// 测试方法
const testFreeCourse = () => {
  // 模拟免费课程数据
  const courseData = {
    id: 1000,
    title: '免费心理学入门课程',
    isFree: 1,
    price: 199, // 即使有价格，但isFree=1应该显示为免费
    purchased: false
  }
  
  uni.navigateTo({
    url: `/pages/course/detail/index?testData=${encodeURIComponent(JSON.stringify(courseData))}`
  })
}

const testZeroPriceCourse = () => {
  // 模拟零价格课程数据
  const courseData = {
    id: 1001,
    title: '零价格心理学课程',
    isFree: 0,
    price: 0, // 价格为0应该显示为免费
    purchased: false
  }
  
  uni.navigateTo({
    url: `/pages/course/detail/index?testData=${encodeURIComponent(JSON.stringify(courseData))}`
  })
}

const testFreeMeditation = () => {
  // 模拟免费冥想数据
  const meditationData = {
    id: 3,
    title: '免费焦虑缓解冥想',
    isFree: 0,
    price: 0, // 价格为0应该显示为免费
    purchased: false
  }
  
  uni.navigateTo({
    url: `/pages/meditation/detail/index?testData=${encodeURIComponent(JSON.stringify(meditationData))}`
  })
}

const testFreeAssessment = () => {
  // 模拟免费测评数据
  const assessmentData = {
    id: 100,
    title: '免费心理健康测评',
    isFree: 1,
    price: 39, // 即使有价格，但isFree=1应该显示为免费
    purchased: false
  }
  
  uni.navigateTo({
    url: `/pages/evaluation/detail/index?testData=${encodeURIComponent(JSON.stringify(assessmentData))}`
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.header {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  padding: 40rpx 32rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
  }
}

.content {
  padding: 32rpx;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .test-btn {
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: #fff;
    font-size: 28rpx;
    border: none;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  }
}

.fix-list {
  .fix-item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;
    
    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.logic-display {
  .logic-item {
    margin-bottom: 24rpx;
    
    .logic-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .logic-code {
      display: block;
      font-family: monospace;
      font-size: 24rpx;
      color: #666;
      background: #f8f9fa;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      margin-bottom: 4rpx;
    }
  }
}

.mock-goods-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b35, #ff8a50);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
  }
}
</style>
