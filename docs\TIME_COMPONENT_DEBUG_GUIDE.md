# 时间组件API接口修复指南

## 🐛 问题分析

### 错误信息
```
1. TypeError: Cannot read property 'replace' of undefined (已修复)
2. TypeError: Cannot read property 'substring' of undefined (已修复)
```

### 根本原因
使用了错误的咨询师时间表API接口：

**错误的API接口**:
```javascript
// 旧的接口（返回不标准的数据格式）
/wechat/appointment/counselor/${counselorId}?days=7
```

**正确的API接口**:
```javascript
// 新的接口（返回标准的PsyTimeSlotDTO格式）
/system/timeSlot/formatted/${counselorId}?startDate=2025-07-21&endDate=2025-07-27
```

**标准数据格式** (两个接口现在都返回相同格式):
```javascript
{
  dates: [
    {
      date: "2025-07-21",  // 使用 date 字段，ISO格式
      isToday: true,
      shortDate: "7月21日",
      timeRanges: [
        {
          rangeName: "上午",
          endHour: 12,
          slots: [
            {
              slotId: 1,
              startTime: "09:00:00",  // 使用 startTime 字段
              status: 1,
              hasAvailable: true,
              availabilityText: "可预约",
              timeDisplay: "09:00"
            }
          ]
        }
      ],
      weekDay: "今天"
    }
  ]
}
```

## 🔧 修复方案

### 1. 智能字段检测
```javascript
// 兼容两种数据格式
const dateStr = dateData.date || dateData.data;
```

### 2. 多格式日期解析
```javascript
if (dateStr.includes('年')) {
  // 处理 "2025年7月21日" 格式
  const dateMatch = dateStr.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
  if (dateMatch) {
    const [, year, month, day] = dateMatch;
    currentDate = new Date(year, month - 1, day);
    standardDateStr = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
} else {
  // 处理 "2025-07-21" 格式
  currentDate = new Date(dateStr.replace(/-/g, '/'));
  standardDateStr = dateStr;
}
```

### 3. 错误处理增强
```javascript
try {
  // 数据处理逻辑
} catch (error) {
  console.error('处理日期数据时出错:', error, dateData);
  return null;
}
```

## 🧪 测试步骤

### 1. 控制台调试
打开浏览器开发者工具，查看以下日志：
- `获取咨询师时间表，ID: [counselorId]`
- `咨询师时间表原始数据: [data]`
- `处理日期数据: [dateData]`

### 2. 数据验证
确认以下数据结构：
```javascript
// 检查原始API响应
console.log('API响应:', res);
console.log('数据数组:', res.data);

// 检查处理后的数据
console.log('处理后的dateInfo:', dateInfo.value);
console.log('处理后的timeInfo:', timeInfo.value);
```

### 3. 功能测试
1. 打开咨询师详情页面
2. 点击"预约咨询"按钮
3. 检查时间表是否正确显示
4. 尝试选择时间段
5. 验证咨询时长是否自动计算

## 🔍 常见问题排查

### 问题1: counselorId未传递
**检查**: `console.log('获取咨询师时间表，ID:', props.counselorId);`
**解决**: 确保父组件正确传递counselorId属性

### 问题2: API返回数据格式异常
**检查**: `console.log('咨询师时间表原始数据:', data);`
**解决**: 检查API返回的数据结构是否符合预期

### 问题3: 日期解析失败
**检查**: `console.log('处理日期数据:', dateData);`
**解决**: 检查日期格式是否在支持的格式范围内

## 📋 修复文件清单

- `components/EnhancedTimeTable/EnhancedTimeTable.vue` - 主要修复文件
- `pages/classification/counselor-detail/index.vue` - 传递counselorId
- `TIME_COMPONENT_DEBUG_GUIDE.md` - 调试指南

## 🎯 预期结果

修复后应该能够：
1. ✅ 正确解析咨询师时间表数据
2. ✅ 支持多种日期格式
3. ✅ 显示咨询师专属时间表
4. ✅ 自动计算咨询时长
5. ✅ 提供清晰的错误信息
