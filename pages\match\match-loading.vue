<template>
	<view class="match-loading">
		<view class="content">
			<view class="tip-text">
				95%的来访者表示匹配到了理想的咨询师，且体验了更省钱、更高效的咨询服务。
			</view>
			
			<view class="matching-card">
				<view class="title">正在为你匹配咨询师，请稍等！</view>
				
				<view class="avatar-container">
					<view class="circle-animation"></view>
					<view class="avatar">
						<image v-if="currentConsultant" :src="currentConsultant.avatar" mode="aspectFill"></image>
					</view>
				</view>
				
				<view class="steps">
					<view class="step-container" :class="{ 'active': step === 0, 'done': step > 0 }">
						<view class="step-box">
							<view class="step-item">
								<text>问题正在分析中...</text>
							</view>
						</view>
					</view>
					<view class="step-container" :class="{ 'active': step === 1, 'done': step > 1 }">
						<view class="step-box">
							<view class="step-item">
								<text>寻找匹配的咨询师...</text>
							</view>
						</view>
					</view>
					<view class="step-container" :class="{ 'active': step === 2, 'done': step > 2 }">
						<view class="step-box">
							<view class="step-item">
								<text>分析咨询师匹配适度...</text>
							</view>
						</view>
					</view>
					<view class="step-container" :class="{ 'active': step === 3, 'done': step > 3 }">
						<view class="step-box">
							<view class="step-item">
								<text>返回最佳的匹配结果...</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getAllConsultantsSimple } from '@/api/match.js';

const step = ref(0);
const consultants = ref([]);
const currentConsultant = ref(null);
let avatarTimer = null;

// 随机切换咨询师头像
const changeAvatar = () => {
	if (consultants.value.length > 0) {
		const randomIndex = Math.floor(Math.random() * consultants.value.length);
		currentConsultant.value = consultants.value[randomIndex];
	}
};

// 获取咨询师列表
const getConsultants = async () => {
	try {
		const res = await getAllConsultantsSimple();
		if (res.code === 200) {
			consultants.value = res.data;
			changeAvatar(); // 初始化显示一个咨询师头像
			
			// 开始定时切换头像
			avatarTimer = setInterval(() => {
				changeAvatar();
			}, 1000); // 每秒切换一次
		}
	} catch (error) {
		console.error('获取咨询师列表失败：', error);
	}
};

// 模拟匹配进度
const startMatching = () => {
	let timer = setInterval(() => {
		if (step.value < 4) {
			step.value++;
		} else {
			clearInterval(timer);
			if (avatarTimer) {
				clearInterval(avatarTimer); // 清除头像切换定时器
			}
			// 匹配完成后直接跳转
			uni.redirectTo({
				url: '/pages/match/match-result'
			});
		}
	}, 1500);
};

onMounted(() => {
	getConsultants();
	startMatching();
});
</script>

<style lang="scss" scoped>
.match-loading {
	min-height: 100vh;
	background: linear-gradient(180deg, #EEF1FC 0%, #F8F9FC 100%);
	padding: 32rpx;
	.tip-text {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 32rpx;
	}
	
	.matching-card {
		background: #fff;
		border-radius: 24rpx;
		padding: 40rpx;
		
		.title {
			font-size: 36rpx;
			font-weight: 500;
			color: #333;
			text-align: center;
			margin-bottom: 48rpx;
		}
		
		.avatar-container {
			position: relative;
			width: 200rpx;
			height: 200rpx;
			margin: 0 auto 60rpx;
			
			.circle-animation {
				position: absolute;
				top: -20rpx;
				left: -20rpx;
				right: -20rpx;
				bottom: -20rpx;
				border: 4rpx solid #5e72e4;
				border-radius: 50%;
				animation: rotate 2s linear infinite;
				
				&::before {
					content: '';
					position: absolute;
					top: -4rpx;
					left: 50%;
					width: 20rpx;
					height: 20rpx;
					background: #5e72e4;
					border-radius: 50%;
					transform: translateX(-50%);
				}
			}
			
			.avatar {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: #f0f3ff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				overflow: hidden;
				
				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}
		
		.steps {
			.step-container {
				margin-bottom: 32rpx;
				
				.step-box {
					position: relative;
					padding: 24rpx;
					border-radius: 12rpx;
					background: #f8f9fc;
					border: 2rpx solid transparent;
					transition: all 0.3s ease;
					
					&::before {
						content: '';
						position: absolute;
						top: -2rpx;
						left: -2rpx;
						right: -2rpx;
						bottom: -2rpx;
						border: 2rpx solid #5e72e4;
						border-radius: 12rpx;
						opacity: 0;
						transition: all 0.3s ease;
					}
				}
				
				.step-item {
					text {
						font-size: 28rpx;
						color: #666;
						transition: color 0.3s ease;
					}
				}
				
				&.active {
					.step-box {
						border-color: #5e72e4;
						
						&::before {
							opacity: 1;
							animation: borderRotate 2s linear infinite;
						}
					}
					
					.step-item text {
						color: #5e72e4;
					}
				}
				
				&.done {
					.step-box {
						background: rgba(94, 114, 228, 0.1);
						border-color: #5e72e4;
					}
					
					.step-item text {
						color: #5e72e4;
					}
				}
			}
		}
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

@keyframes borderRotate {
	0% {
		clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	}
	25% {
		clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
	}
	50% {
		clip-path: polygon(100% 0, 100% 0, 100% 100%, 100% 100%);
	}
	75% {
		clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%);
	}
	100% {
		clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
	}
}
</style> 