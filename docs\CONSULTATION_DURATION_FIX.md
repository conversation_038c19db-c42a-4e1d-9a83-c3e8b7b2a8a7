# 咨询时长动态计算和时间组件修复

## 问题描述
1. **咨询时长问题**: 在预约咨询功能中，咨询时长应该根据用户选择的预约时间动态计算，而不是让用户手动选择固定的时长选项。
2. **时间组件问题**: EnhancedTimeTable组件在咨询师详情页面中使用了错误的API，获取的是系统通用时间表而不是特定咨询师的时间表。

## 修复内容

### A. 咨询时长动态计算修复

#### 1. 移除手动选择咨询时长的UI
- 删除了重复的咨询时长选择区域（原来有两个相同的选择区域）
- 替换为动态显示咨询时长的区域

#### 2. 实现动态时长计算
- 添加了 `calculatedDuration` 计算属性，根据选择的时间区间自动计算咨询时长
- 添加了 `calculatedDurationText` 计算属性，格式化显示时长文本
- 修改了 `totalAmount` 计算属性，使用动态计算的时长来计算价格

#### 3. 更新事件处理逻辑
- 修改了 `handleAppointmentTimeChange` 和 `handleAppointmentIntervalChange` 函数，添加了时长计算的日志输出
- 移除了 `selectDuration` 函数，因为不再需要手动选择时长
- 更新了 `confirmAppointment` 函数，使用动态计算的时长创建订单

#### 4. 清理代码
- 移除了不再使用的 `duration` 字段从表单重置逻辑中
- 更新了 `handleMakeAppointment` 函数，移除了默认时长设置

### B. 时间组件API修复

#### 1. 修复EnhancedTimeTable组件
- 添加了 `counselorId` 属性支持
- 修改了 `getTimeList` 函数，支持两种API调用模式：
  - 有 `counselorId` 时：调用 `getCounselorTime(counselorId)` 获取特定咨询师的时间表
  - 无 `counselorId` 时：调用 `getFormattedTimeSlots()` 获取系统通用时间表

#### 2. 更新咨询师详情页面
- 给 EnhancedTimeTable 组件传递 `counselorId` 属性
- 移除了页面中重复的 `getTimeList` 调用和相关代码
- 清理了不再使用的导入和函数

## 工作原理

1. **时间选择**: 用户在EnhancedTimeTable组件中选择预约时间
2. **区间计算**: EnhancedTimeTable组件自动将选择的时间点组织成时间区间
3. **时长计算**: `calculatedDuration` 计算属性根据时间区间计算总时长
4. **价格更新**: `totalAmount` 计算属性根据动态时长计算最终价格
5. **订单创建**: 确认预约时使用计算出的时长创建订单

## 计算逻辑

- 每个时间区间默认为1小时（60分钟）
- 如果选择了多个时间区间，总时长为所有区间时长的累加
- 备用计算方法：根据选择的时间点数量估算（每5个时间点=1小时）

## UI改进

- 咨询时长现在以只读方式显示，用户无法手动修改
- 显示格式："{X}小时" 
- 添加了说明文字："根据选择的预约时间自动计算"
- 使用了美观的卡片样式显示时长信息

## API调用对比

### 修复前
- **预约页面**: 调用 `getFormattedTimeSlots()` ✅ 正确
- **咨询师详情页面**: 调用 `getFormattedTimeSlots()` ❌ 错误（应该获取特定咨询师的时间表）

### 修复后
- **预约页面**: 调用 `getFormattedTimeSlots()` ✅ 正确（无counselorId）
- **咨询师详情页面**: 调用 `getCounselorTime(counselorId)` ✅ 正确（有counselorId）

## 测试建议

### 咨询时长测试
1. 选择单个1小时时间段，验证显示"1小时"
2. 选择多个时间段，验证时长累加正确
3. 验证价格计算是否根据动态时长正确更新
4. 测试订单创建时是否使用了正确的时长数据

### 时间组件测试
1. 在预约页面测试时间选择功能是否正常
2. 在咨询师详情页面测试是否显示该咨询师的专属时间表
3. 验证不同咨询师的时间表是否不同
4. 测试时间区间选择和冲突检测功能
