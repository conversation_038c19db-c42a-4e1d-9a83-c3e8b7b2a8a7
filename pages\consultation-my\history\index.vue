<template>
  <view class="history-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <image src="/static/icon/search.png" mode="aspectFit"></image>
        <input 
          type="text" 
          placeholder="搜索来访者姓名" 
          v-model="searchKeyword"
          @input="onSearchInput"
        />
      </view>
      <view class="filter-btn" @click="showFilterModal = true">
        <image src="/static/icon/filter.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-number">{{ statsInfo.totalClients || 0 }}</text>
        <text class="stat-label">服务人数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ statsInfo.totalSessions || 0 }}</text>
        <text class="stat-label">咨询次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ statsInfo.totalHours || 0 }}</text>
        <text class="stat-label">咨询时长(小时)</text>
      </view>
    </view>

    <!-- 咨询历史列表 -->
    <scroll-view 
      class="history-list" 
      scroll-y 
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="history-item" v-for="item in historyList" :key="item.id" @click="goToDetail(item.id)">
        <!-- 来访者信息 -->
        <view class="client-info">
          <image class="avatar" :src="item.clientAvatar || '/static/icon/default-avatar.png'" mode="aspectFill"></image>
          <view class="info">
            <view class="name-row">
              <text class="name">{{ item.clientName }}</text>
              <text class="gender-age">{{ item.clientGender }} · {{ item.clientAge }}岁</text>
            </view>
            <text class="phone">{{ formatPhone(item.clientPhone) }}</text>
          </view>
        </view>

        <!-- 咨询信息 -->
        <view class="consultation-info">
          <view class="info-row">
            <view class="info-item">
              <text class="label">最近咨询：</text>
              <text class="value">{{ formatDateTime(item.lastConsultationTime) }}</text>
            </view>
            <view class="consultation-type">
              <text>{{ item.consultationType }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">咨询次数：</text>
              <text class="value">{{ item.sessionCount }}次</text>
            </view>
            <view class="info-item">
              <text class="label">总时长：</text>
              <text class="value">{{ item.totalDuration }}小时</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="btn-secondary" @click.stop="viewRecord(item)">查看记录</button>
          <button class="btn-primary" @click.stop="contactClient(item)">联系来访者</button>
        </view>
      </view>

      <!-- 加载更多 -->
      <!-- <view class="load-more" v-if="hasMore">
        <text>加载更多...</text>
      </view> -->

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && historyList.length > 0">
        <text>没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="historyList.length === 0 && !loading">
        <image src="/static/icon/empty-history.png" mode="aspectFit"></image>
        <text>暂无咨询历史</text>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom" v-if="showFilterModal">
      <view class="filter-modal">
        <view class="modal-header">
          <text class="modal-title">筛选条件</text>
          <text class="modal-close" @click="closeFilterModal">×</text>
        </view>
        <view class="modal-content">
          <!-- 时间范围 -->
          <view class="filter-section">
            <text class="section-title">时间范围</text>
            <view class="time-range">
              <picker mode="date" :value="filterForm.startDate" @change="onStartDateChange">
                <view class="date-picker">{{ filterForm.startDate || '开始日期' }}</view>
              </picker>
              <text class="separator">至</text>
              <picker mode="date" :value="filterForm.endDate" @change="onEndDateChange">
                <view class="date-picker">{{ filterForm.endDate || '结束日期' }}</view>
              </picker>
            </view>
          </view>

          <!-- 咨询类型 -->
          <view class="filter-section">
            <text class="section-title">咨询类型</text>
            <view class="type-options">
              <view 
                class="type-item" 
                v-for="type in consultationTypes" 
                :key="type.value"
                :class="{ active: filterForm.type === type.value }"
                @click="selectType(type.value)"
              >
                {{ type.label }}
              </view>
            </view>
          </view>

          <!-- 排序方式 -->
          <view class="filter-section">
            <text class="section-title">排序方式</text>
            <view class="sort-options">
              <view 
                class="sort-item" 
                v-for="sort in sortOptions" 
                :key="sort.value"
                :class="{ active: filterForm.sort === sort.value }"
                @click="selectSort(sort.value)"
              >
                {{ sort.label }}
              </view>
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn-reset" @click="resetFilter">重置</button>
          <button class="btn-apply" @click="applyFilter">应用</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getConsultantRecords, getConsultantStatistics } from '@/api/consultant-app.js'

// 响应式数据
const searchKeyword = ref('')
const showFilterModal = ref(false)
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const pageNum = ref(1)
const pageSize = ref(10)

const historyList = ref([])
const statsInfo = ref({})

// 筛选表单
const filterForm = ref({
  startDate: '',
  endDate: '',
  type: '',
  sort: 'time_desc'
})

// 咨询类型选项
const consultationTypes = ref([
  { label: '全部', value: '' },
  { label: '线上咨询', value: 'online' },
  { label: '线下咨询', value: 'offline' },
  { label: '电话咨询', value: 'phone' }
])

// 排序选项
const sortOptions = ref([
  { label: '时间倒序', value: 'time_desc' },
  { label: '时间正序', value: 'time_asc' },
  { label: '咨询次数', value: 'session_count' }
])

// 生命周期
onMounted(() => {
  loadHistoryData()
  loadStatistics()
})

onShow(() => {
  loadHistoryData()
})

// 加载历史数据
const loadHistoryData = async (isRefresh = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    if (isRefresh) {
      pageNum.value = 1
      hasMore.value = true
    }
    
    const params = {
      keyword: searchKeyword.value,
      startDate: filterForm.value.startDate,
      endDate: filterForm.value.endDate,
      type: filterForm.value.type,
      sort: filterForm.value.sort,
      pageNum: pageNum.value,
      pageSize: pageSize.value
    }
    
    const res = await getConsultantRecords(params)
    if (res.code === 200) {
      const newData = res.data.list || []
      
      if (isRefresh) {
        historyList.value = newData
      } else {
        historyList.value.push(...newData)
      }
      
      hasMore.value = newData.length === pageSize.value
    }
  } catch (error) {
    console.error('加载历史数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await getConsultantStatistics()
    if (res.code === 200) {
      statsInfo.value = res.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索输入
const onSearchInput = () => {
  clearTimeout(onSearchInput.timer)
  onSearchInput.timer = setTimeout(() => {
    pageNum.value = 1
    historyList.value = []
    loadHistoryData(true)
  }, 500)
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  loadHistoryData(true)
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    pageNum.value++
    loadHistoryData()
  }
}

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 查看记录
const viewRecord = (item) => {
  uni.navigateTo({
    url: `/pages/consultation-my/client-records/index?clientId=${item.clientId}`
  })
}

// 联系来访者
const contactClient = (item) => {
  uni.navigateTo({
    url: `/pages/my/my-message/chat/index?userId=${item.clientId}&nickname=${item.clientName}&clientAvatar=${item.clientAvatar}&isConsultant=true`
  })
}

// 跳转到详情
const goToDetail = (recordId) => {
  uni.navigateTo({
    url: `/pages/consultation-my/consultation-record/index?recordId=${recordId}`
  })
}

// 开始日期选择
const onStartDateChange = (e) => {
  filterForm.value.startDate = e.detail.value
}

// 结束日期选择
const onEndDateChange = (e) => {
  filterForm.value.endDate = e.detail.value
}

// 选择咨询类型
const selectType = (type) => {
  filterForm.value.type = type
}

// 选择排序方式
const selectSort = (sort) => {
  filterForm.value.sort = sort
}

// 重置筛选
const resetFilter = () => {
  filterForm.value = {
    startDate: '',
    endDate: '',
    type: '',
    sort: 'time_desc'
  }
}

// 应用筛选
const applyFilter = () => {
  closeFilterModal()
  pageNum.value = 1
  historyList.value = []
  loadHistoryData(true)
}

// 关闭筛选弹窗
const closeFilterModal = () => {
  showFilterModal.value = false
}
</script>

<style scoped lang="scss">
.history-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  
  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 25rpx;
    padding: 0 20rpx;
    height: 70rpx;
    margin-right: 20rpx;
    
    image {
      width: 30rpx;
      height: 30rpx;
      margin-right: 15rpx;
    }
    
    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .filter-btn {
    width: 70rpx;
    height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 50%;
    
    image {
      width: 30rpx;
      height: 30rpx;
    }
  }
}

.stats-card {
  display: flex;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #1890ff;
      margin-bottom: 10rpx;
    }
    
    .stat-label {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.history-list {
  height: calc(100vh - 280rpx);
  padding: 0 20rpx;
}

.history-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .client-info {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .info {
      flex: 1;
      
      .name-row {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;
        
        .name {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-right: 20rpx;
        }
        
        .gender-age {
          font-size: 24rpx;
          color: #666;
          background-color: #f5f5f5;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
      }
      
      .phone {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .consultation-info {
    margin-bottom: 20rpx;
    
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-item {
        display: flex;
        align-items: center;
        
        .label {
          font-size: 26rpx;
          color: #666;
          margin-right: 8rpx;
        }
        
        .value {
          font-size: 26rpx;
          color: #333;
        }
      }
      
      .consultation-type {
        text {
          font-size: 24rpx;
          color: #1890ff;
          background-color: #e6f7ff;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
      }
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 20rpx;
    
    button {
      flex: 1;
      padding: 20rpx 0;
      border-radius: 12rpx;
      font-size: 26rpx;
      border: none;
      
      &.btn-primary {
        background-color: #1890ff;
        color: #fff;
      }
      
      &.btn-secondary {
        background-color: #f5f5f5;
        color: #666;
      }
    }
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }
  
  text {
    color: #999;
    font-size: 28rpx;
  }
}

.filter-modal {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .modal-close {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .modal-content {
    padding: 30rpx;
    
    .filter-section {
      margin-bottom: 40rpx;
      
      .section-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .time-range {
        display: flex;
        align-items: center;
        gap: 20rpx;
        
        .date-picker {
          flex: 1;
          padding: 20rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 8rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
        }
        
        .separator {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .type-options,
      .sort-options {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
        
        .type-item,
        .sort-item {
          padding: 16rpx 32rpx;
          border: 1rpx solid #d9d9d9;
          border-radius: 20rpx;
          font-size: 26rpx;
          color: #666;
          
          &.active {
            background-color: #1890ff;
            color: #fff;
            border-color: #1890ff;
          }
        }
      }
    }
  }
  
  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    
    .btn-reset,
    .btn-apply {
      flex: 1;
      padding: 24rpx 0;
      border-radius: 12rpx;
      font-size: 28rpx;
      border: none;
    }
    
    .btn-reset {
      background-color: #f5f5f5;
      color: #666;
    }
    
    .btn-apply {
      background-color: #1890ff;
      color: #fff;
    }
  }
}
</style>
