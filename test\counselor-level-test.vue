<template>
  <view class="test-page">
    <view class="page-title">咨询师等级排序测试</view>
    
    <view class="section">
      <view class="section-title">排序控制</view>
      <view class="sort-controls">
        <button 
          class="sort-btn" 
          :class="{ active: sortBy === 'level' }"
          @click="toggleSort('level')"
        >
          等级排序 {{ sortBy === 'level' ? (sortOrder === 'asc' ? '↑' : '↓') : '' }}
        </button>
        <button 
          class="sort-btn" 
          :class="{ active: sortBy === 'price' }"
          @click="toggleSort('price')"
        >
          价格排序 {{ sortBy === 'price' ? (sortOrder === 'asc' ? '↑' : '↓') : '' }}
        </button>
        <button class="sort-btn" @click="resetSort">重置排序</button>
      </view>
    </view>

    <view class="section">
      <view class="section-title">测试数据 ({{ sortedCounselors.length }}个)</view>
      <view class="counselor-list">
        <view 
          v-for="(counselor, index) in sortedCounselors" 
          :key="counselor.id"
          class="counselor-item"
        >
          <view class="counselor-info">
            <view class="name">{{ counselor.name }}</view>
            <view class="level">{{ getLevelText(counselor.counselorLevel) }}</view>
            <view class="level-value">等级值: {{ counselor.counselorLevel }}</view>
            <view class="price">¥{{ counselor.price }}/节</view>
          </view>
          <view class="sort-info">
            <view class="index">排序: {{ index + 1 }}</view>
            <view class="sort-value">排序值: {{ getLevelSortValue(counselor.counselorLevel) }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 测试数据 - 包含所有等级的咨询师
const testCounselors = ref([
  { id: 1, name: '张医生', counselorLevel: '6', price: 500 }, // 咨询督导
  { id: 2, name: '李医生', counselorLevel: '1', price: 200 }, // 初级咨询师
  { id: 3, name: '王医生', counselorLevel: '4', price: 400 }, // 高级咨询师
  { id: 4, name: '赵医生', counselorLevel: '2', price: 250 }, // 中级咨询师
  { id: 5, name: '刘医生', counselorLevel: '5', price: 450 }, // 资深咨询师
  { id: 6, name: '陈医生', counselorLevel: '0', price: 150 }, // 咨询助理
  { id: 7, name: '杨医生', counselorLevel: '3', price: 350 }, // 成熟咨询师
  { id: 8, name: '周医生', counselorLevel: '1', price: 180 }, // 初级咨询师
  { id: 9, name: '吴医生', counselorLevel: '4', price: 420 }, // 高级咨询师
  { id: 10, name: '郑医生', counselorLevel: '6', price: 550 } // 咨询督导
])

// 排序状态
const sortBy = ref('')
const sortOrder = ref('desc')

// 等级映射
const levelMap = {
  '0': '咨询助理',
  '1': '初级咨询师',
  '2': '中级咨询师',
  '3': '成熟咨询师',
  '4': '高级咨询师',
  '5': '资深咨询师',
  '6': '咨询督导'
}

// 获取等级文本
const getLevelText = (level) => {
  return levelMap[level] || `未知等级(${level})`
}

// 咨询师等级排序映射 - 根据字典数据的 dictSort 字段排序
const getLevelSortValue = (level) => {
  // 根据您提供的字典数据，dictSort 值越大等级越高
  const levelSortMap = {
    '0': 0, // 咨询助理
    '1': 1, // 初级咨询师
    '2': 2, // 中级咨询师
    '3': 3, // 成熟咨询师
    '4': 4, // 高级咨询师
    '5': 5, // 资深咨询师
    '6': 6  // 咨询督导
  }
  
  const levelStr = String(level || '1')
  return levelSortMap[levelStr] || 1 // 默认为初级咨询师
}

// 排序数据的方法
const sortData = (data, field, order) => {
  if (!data || data.length === 0) return []

  return [...data].sort((a, b) => {
    let aValue, bValue

    if (field === 'price') {
      aValue = parseFloat(a.price || 0)
      bValue = parseFloat(b.price || 0)
    } else if (field === 'level') {
      // 使用字典数据的排序值进行等级排序
      aValue = getLevelSortValue(a.counselorLevel)
      bValue = getLevelSortValue(b.counselorLevel)
    } else {
      return 0
    }

    if (order === 'asc') {
      return aValue - bValue
    } else {
      return bValue - aValue
    }
  })
}

// 计算排序后的咨询师列表
const sortedCounselors = computed(() => {
  if (!sortBy.value) return testCounselors.value
  return sortData(testCounselors.value, sortBy.value, sortOrder.value)
})

// 排序切换
const toggleSort = (field) => {
  if (sortBy.value === field) {
    // 如果点击的是当前排序字段，切换排序方向
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    // 如果点击的是新的排序字段，设置为降序
    sortBy.value = field
    sortOrder.value = 'desc'
  }
}

// 重置排序
const resetSort = () => {
  sortBy.value = ''
  sortOrder.value = 'desc'
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 60rpx;
}

.section {
  margin-bottom: 40rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #666;
    margin-bottom: 24rpx;
  }
}

.sort-controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 32rpx;
  
  .sort-btn {
    padding: 20rpx 32rpx;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #666;
    
    &.active {
      background: #007aff;
      color: #fff;
      border-color: #007aff;
    }
  }
}

.counselor-list {
  .counselor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 24rpx;
    margin-bottom: 16rpx;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    
    .counselor-info {
      flex: 1;
      
      .name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .level {
        font-size: 24rpx;
        color: #ff6b35;
        background: #fff4dc;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
        display: inline-block;
        margin-bottom: 8rpx;
      }
      
      .level-value {
        font-size: 22rpx;
        color: #999;
        margin-bottom: 8rpx;
      }
      
      .price {
        font-size: 28rpx;
        color: #e72f2f;
        font-weight: 600;
      }
    }
    
    .sort-info {
      text-align: right;
      
      .index {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .sort-value {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}
</style>
