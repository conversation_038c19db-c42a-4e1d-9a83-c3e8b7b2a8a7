import request from "../utils/request";

export function getWXLogin(code) {
	return request({
		url: "/wx/login?code=" + code,
		method: 'post',
	})
}


// 获取用户详细信息
export function getInfo() {
	return request({
		url: '/getInfo',
		method: 'get'
	})
}

// 根据id获取用户详细信息
export function getUserIdInfo(userId) {
	return request({
		url: '/wx/user/' + userId,
		method: 'get'
	})
}

export function getCode(params) {
	return request({
		url: "/sms/sendCode",
		method: 'get',
		params
	})
}


export function getMobileLogin(data) {
	return request({
		url: "/phoneLogin",
		method: 'post',
		data
	})
}

// 添加用户信息
export function postWxUser(data) {
	return request({
		url: "/wx/user/edit",
		method: 'post',
		data
	})
}


// 获取收藏列表
export function getFavorites(query) {
	return request({
		url: '/psy/favorite/list',
		method: 'get',
		params: query
	})
}

// 获取收藏详情
export function getFavoriteInfo(favoriteId) {
	return request({
		url: `/system/favorite/${favoriteId}`,
		method: 'get'
	})
}

// 新增收藏
export function addFavorite(data) {
	return request({
		url: '/psy/favorite',
		method: 'post',
		data: data
	})
}

// 修改收藏
export function updateFavorite(data) {
	return request({
		url: '/system/favorite',
		method: 'put',
		data: data
	})
}

// 删除收藏
export function removeFavorite(favoriteIds) {
	return request({
		url: `/psy/favorite/${favoriteIds}`,
		method: 'delete'
	})
}

// 检查是否已收藏
export function checkFavorites(params) {
	return request({
		url: '/psy/favorite/check',
		method: 'get',
		params: params
	})
}

// 获取用户的测评记录列表（兼容旧版本，建议使用evaluation.js中的新接口）
export function getTestRecords(userId) {
	// 如果没有传递userId，需要页面传递正确的用户ID
	if (!userId) {
		console.warn('getTestRecords: userId is required')
		return Promise.reject(new Error('用户ID不能为空，请传递正确的userId参数'))
	}
	return request({
		url: `/miniapp/user/assessment/records/${userId}`,
		method: 'get'
	})
}